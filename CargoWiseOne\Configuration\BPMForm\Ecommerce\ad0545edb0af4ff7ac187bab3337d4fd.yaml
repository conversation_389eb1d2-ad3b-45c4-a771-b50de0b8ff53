#transformationVersion: 70.0
#
VZ_PK: ad0545edb0af4ff7ac187bab3337d4fd
VZ_ConfigurationKey: ad0545ed-b0af-4ff7-ac18-7bab3337d4fd
VZ_FormID: ETL - VDV3 - Shipper/Origin/Destination Depot - Intercept Events
VZ_Caption:
  resKey: VZ_Caption|ad0545ed-b0af-4ff7-ac18-7bab3337d4fd
  text: Intercept Events
VZ_FormFactor: DSK
VZ_EntityType: IHVLVItem
VZ_FormType: FRM
VZ_RequiresLogin: true
VZ_Dependencies: >-
  <dependencies xmlns="http://cargowise.com/glow/2014/11/14/dependencies.xsd">
    <expandPath
      path="SPREvents" />
    <calculatedProperty
      path="LastInterceptEventReference" />
    <calculatedProperty
      path="SPREvents" />
  </dependencies>
VZ_FormData: >-
  <form
    id="00c3402d-7cdd-4501-a8d4-b4fce1019f56" xmlns="http://wisetechglobal.com/glow/2017/05/26/form.xsd">
    <placeholder
      name="BackgroundImagePk"
      value="" />
    <placeholder
      name="BackgroundSize"
      value="Auto" />
    <placeholder
      name="BackgroundImageOverride"
      value="" />
    <placeholder
      name="FormLayout"
      value="Splitview" />
    <placeholder
      name="ExpandPaths"
      value="" />
    <placeholder
      name="ScanInputHandling"
      value="NON" />
    <placeholder
      name="RemovePadding"
      value="false" />
    <placeholder
      name="TurnOnPortableOneToOneDesignMode"
      value="false" />
    <placeholder
      name="ContainsDefaultButton"
      value="False" />
    <control
      code="GRP"
      id="8259ee8a-8ed4-49d0-988a-0fc37a55270b"
      width="18"
      height="12">
      <placeholder
        name="CanBeHidden"
        value="True" />
      <placeholder
        name="VisibilityCondition"
        value="" />
      <placeholder
        name="Header"
        value="New Section"
        resid="4ef8b642-78c7-417f-a6d7-f6afa0675f84" />
      <placeholder
        name="HeaderAlignment"
        value="Left" />
      <placeholder
        name="HeaderDisplayMode"
        value="Hidden" />
      <placeholder
        name="BackgroundImagePk"
        value="" />
      <placeholder
        name="BackgroundImageOverride"
        value="" />
      <control
        code="TXT"
        id="2b1c6977-6e89-41f1-92b2-8f94be3f2b9d"
        left="0"
        top="0"
        width="18"
        height="2"
        binding="LastInterceptEventReference">
        <placeholder
          name="IsReadOnly"
          value="True" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="OnSubmit"
          value="" />
        <placeholder
          name="MultiLine"
          value="True" />
        <placeholder
          name="EnableAutoComplete"
          value="False" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="GRD"
        id="ff2bf172-3916-46b5-af85-37f3b6115f84"
        left="0"
        top="2"
        width="18"
        height="9"
        binding="SPREvents">
        <placeholder
          name="IsReadOnly"
          value="False" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="CaptionOverride"
          value="" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="HeaderAlignment"
          value="Left" />
        <placeholder
          name="HeaderDisplayMode"
          value="Visible" />
        <placeholder
          name="AllowAdd"
          value="True" />
        <placeholder
          name="AllowAttach"
          value="True" />
        <placeholder
          name="AllowDetach"
          value="True" />
        <placeholder
          name="AllowInlineEdit"
          value="True" />
        <placeholder
          name="FieldConfiguration"
          value="" />
        <placeholder
          name="DefaultSortFields">
          <xml>
            <ArrayOfFieldSortDefinition xmlns:i="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://schemas.datacontract.org/2004/07/CargoWise.Glow.Infrastructure.Interfaces" />
          </xml>
        </placeholder>
        <placeholder
          name="ShowDocumentsIcon"
          value="True" />
        <placeholder
          name="RowActionsMode"
          value="RowAndMenu" />
        <placeholder
          name="HideGridActions"
          value="False" />
        <placeholder
          name="HideFilters"
          value="False" />
        <placeholder
          name="ActionMenuItems"
          value="" />
        <placeholder
          name="EditFormFlow"
          value="" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
    </control>
  </form>
