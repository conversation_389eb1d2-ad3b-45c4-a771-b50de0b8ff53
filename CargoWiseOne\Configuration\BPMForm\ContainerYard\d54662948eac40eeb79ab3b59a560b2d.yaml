#transformationVersion: 70.0
#
VZ_PK: d54662948eac40eeb79ab3b59a560b2d
VZ_ConfigurationKey: d5466294-8eac-40ee-b79a-b3b59a560b2d
VZ_FormID: CYS Container Yard Survey Landing Page (Mobile)
VZ_Caption:
  resKey: VZ_Caption|d54662948eac40eeb79ab3b59a560b2d
  text: Container Yard Survey Landing Page (Mobile)
VZ_FormFactor: MOB
VZ_EntityType: IWhsWarehouse
VZ_FormType: FRM
VZ_RequiresLogin: true
VZ_MaterialDesign: true
VZ_Dependencies: <dependencies xmlns="http://cargowise.com/glow/2014/11/14/dependencies.xsd" />
VZ_FormData: >-
  <form
    id="56dc6ccf-a9cb-4afc-bad7-04025e2a48f7" xmlns="http://wisetechglobal.com/glow/2017/05/26/form.xsd">
    <placeholder
      name="Style"
      value="margin-top: -16px" />
    <placeholder
      name="MaxWidth"
      value="600px" />
    <placeholder
      name="Width"
      value="100%" />
    <control
      code="BOX"
      id="0341d54d-5dde-4b2a-b0a6-83190afc9574">
      <placeholder
        name="Margin"
        value="mb-9" />
      <control
        code="BOX"
        id="84f06b3e-216e-4f63-b0e4-1ebf6bf43be6">
        <placeholder
          name="Layout"
          value="flex" />
        <placeholder
          name="Margin"
          value="mt-2" />
        <placeholder
          name="FlexJustify"
          value="justify-center" />
        <placeholder
          name="FlexAlign"
          value="align-center" />
        <placeholder
          name="FlexWrap"
          value="flex-wrap" />
        <control
          code="CRD"
          id="ffb4e8bc-1be5-4ac1-8730-ea97145b361a">
          <placeholder
            name="Height"
            value="64" />
          <placeholder
            name="Padding"
            value="10px" />
          <placeholder
            name="Margin"
            value="mx-6 my-2" />
          <placeholder
            name="FormFlowConfiguration">
            <xml>
              <formFlows xmlns="">
                <formFlow
                  newSession="True">ffc587308aca4ad69ad470bcdd04bd2b</formFlow>
              </formFlows>
            </xml>
          </placeholder>
          <placeholder
            name="Style"
            value="border-radius: var(--s-radius-s); border: 1px solid var(--s-neutral-border-weak-default);" />
          <placeholder
            name="MaxWidth"
            value="600px" />
          <placeholder
            name="Width"
            value="100%" />
          <control
            code="BOX"
            id="8ba56af9-ce47-4bf6-b6b7-1097bc800b71">
            <placeholder
              name="Layout"
              value="flex" />
            <placeholder
              name="FlexJustify"
              value="justify-space-between" />
            <placeholder
              name="FlexAlign"
              value="align-center" />
            <placeholder
              name="Height"
              value="64" />
            <placeholder
              name="MaxWidth"
              value="600px" />
            <placeholder
              name="Width"
              value="100%" />
            <control
              code="BOX"
              id="6aa80dc5-ba09-4cb6-b078-a821ad28480d">
              <placeholder
                name="Layout"
                value="flex" />
              <placeholder
                name="FlexAlign"
                value="align-center" />
              <placeholder
                name="Style"
                value="margin-left: 12px;" />
              <placeholder
                name="Width"
                value="220" />
              <placeholder
                name="Height"
                value="40" />
              <control
                code="ICO"
                id="48217091-293a-49fd-8919-23b6dfb2a707">
                <placeholder
                  name="IconName"
                  value="s-icon-truck-loading" />
                <placeholder
                  name="Size"
                  value="xl" />
              </control>
              <control
                code="LBL"
                id="4ac0dd65-152f-4418-b4ff-e26927dca16a">
                <placeholder
                  name="Caption"
                  value="Surveying"
                  resid="15c9aad5-7ce8-449e-99a6-f24e430c78ed" />
                <placeholder
                  name="FontWeight"
                  value="48" />
                <placeholder
                  name="Margin"
                  value="mx-3" />
                <placeholder
                  name="Style"
                  value="font-family: Inter; font-size: 16px; font-weight: 600; line-height: 24px; text-align: left; text-underline-position: from-font; text-decoration-skip-ink: none;" />
                <placeholder
                  name="FormFlowConfiguration">
                  <xml>
                    <formFlows xmlns="">
                      <formFlow
                        newSession="True">ffc587308aca4ad69ad470bcdd04bd2b</formFlow>
                    </formFlows>
                  </xml>
                </placeholder>
              </control>
            </control>
            <control
              code="ICO"
              id="d6ce1cc2-069b-40d8-bd59-3cb9cdbb76ff">
              <placeholder
                name="IconName"
                value="s-icon-caret-right" />
              <placeholder
                name="Style"
                value="margin-right: 12px;" />
              <placeholder
                name="Size"
                value="l" />
            </control>
          </control>
        </control>
      </control>
      <control
        code="BOX"
        id="93dc990c-9279-40e2-b6cf-2a5d489af16b">
        <placeholder
          name="Layout"
          value="flex" />
        <placeholder
          name="FlexWrap"
          value="flex-wrap" />
        <placeholder
          name="FlexJustify"
          value="justify-center" />
        <placeholder
          name="FlexAlign"
          value="align-center" />
        <control
          code="CRD"
          id="40ed9e9e-221c-4a9b-8b19-ed2d5fe06ae2">
          <placeholder
            name="Height"
            value="64" />
          <placeholder
            name="Padding"
            value="10px" />
          <placeholder
            name="Margin"
            value="mx-6 my-2" />
          <placeholder
            name="FormFlowConfiguration">
            <xml>
              <formFlows xmlns="">
                <formFlow
                  newSession="True">62c4cc76af654cb88b4b483417f55eda</formFlow>
              </formFlows>
            </xml>
          </placeholder>
          <placeholder
            name="Style"
            value="border-radius: var(--s-radius-s); border: 1px solid var(--s-neutral-border-weak-default);" />
          <placeholder
            name="MaxWidth"
            value="600px" />
          <placeholder
            name="Width"
            value="100%" />
          <control
            code="BOX"
            id="40322e02-a596-4515-b8f0-68924ca9cb5a">
            <placeholder
              name="Layout"
              value="flex" />
            <placeholder
              name="FlexJustify"
              value="justify-space-between" />
            <placeholder
              name="FlexAlign"
              value="align-center" />
            <placeholder
              name="Height"
              value="64" />
            <placeholder
              name="MaxWidth"
              value="600px" />
            <placeholder
              name="Width"
              value="100%" />
            <control
              code="BOX"
              id="303a6b69-3d1c-4f4d-841d-430140463510">
              <placeholder
                name="Layout"
                value="flex" />
              <placeholder
                name="FlexAlign"
                value="align-center" />
              <placeholder
                name="Style"
                value="margin-left: 12px;" />
              <placeholder
                name="Width"
                value="220" />
              <placeholder
                name="Height"
                value="40" />
              <control
                code="ICO"
                id="c8560c85-b677-4e59-8073-e0734c27ef18">
                <placeholder
                  name="IconName"
                  value="s-icon-container-trailer" />
                <placeholder
                  name="Size"
                  value="xl" />
              </control>
              <control
                code="LBL"
                id="53c50c1a-2565-400c-8b73-b13d5249248d">
                <placeholder
                  name="Caption"
                  value="Inventory"
                  resid="5cb3533c-2c65-47d9-921d-6ce93db29671" />
                <placeholder
                  name="FontWeight"
                  value="48" />
                <placeholder
                  name="Margin"
                  value="mx-3" />
                <placeholder
                  name="Style"
                  value="font-family: Inter; font-size: 16px; font-weight: 600; line-height: 24px; text-align: left; text-underline-position: from-font; text-decoration-skip-ink: none;" />
                <placeholder
                  name="FormFlowConfiguration">
                  <xml>
                    <formFlows xmlns="">
                      <formFlow
                        newSession="True">ff7f9fc6a10240a8bd9a34b994c3e16c</formFlow>
                    </formFlows>
                  </xml>
                </placeholder>
              </control>
            </control>
            <control
              code="ICO"
              id="eede9928-3841-436d-ad46-90d520dcd165">
              <placeholder
                name="IconName"
                value="s-icon-caret-right" />
              <placeholder
                name="Style"
                value="margin-right: 12px;" />
              <placeholder
                name="Size"
                value="l" />
            </control>
          </control>
        </control>
      </control>
      <control
        code="BOX"
        id="09613a53-b391-4a10-bfd3-7dff97325e34">
        <placeholder
          name="Layout"
          value="flex" />
        <placeholder
          name="FlexJustify"
          value="justify-center" />
        <placeholder
          name="FlexAlign"
          value="align-center" />
        <placeholder
          name="FlexWrap"
          value="flex-wrap" />
        <control
          code="CRD"
          id="18900d9f-3787-4e23-95e8-ec29b937655c">
          <placeholder
            name="Height"
            value="64" />
          <placeholder
            name="Padding"
            value="10px" />
          <placeholder
            name="Margin"
            value="mx-6 my-2" />
          <placeholder
            name="FormFlowConfiguration">
            <xml>
              <formFlows xmlns="">
                <formFlow
                  newSession="True">02f761070b104b5f8c7c04d958f51317</formFlow>
              </formFlows>
            </xml>
          </placeholder>
          <placeholder
            name="Style"
            value="border-radius: var(--s-radius-s); border: 1px solid var(--s-neutral-border-weak-default);" />
          <placeholder
            name="MaxWidth"
            value="600px" />
          <placeholder
            name="Width"
            value="100%" />
          <control
            code="BOX"
            id="97705c53-b4b1-4d51-b628-d05c5bbee7c6">
            <placeholder
              name="Layout"
              value="flex" />
            <placeholder
              name="FlexJustify"
              value="justify-space-between" />
            <placeholder
              name="FlexAlign"
              value="align-center" />
            <placeholder
              name="Height"
              value="64" />
            <placeholder
              name="MaxWidth"
              value="600px" />
            <placeholder
              name="Width"
              value="100%" />
            <control
              code="BOX"
              id="6bc2e6f2-a64f-429d-8e01-7467a415577e">
              <placeholder
                name="Layout"
                value="flex" />
              <placeholder
                name="FlexAlign"
                value="align-center" />
              <placeholder
                name="Style"
                value="margin-left: 12px;" />
              <placeholder
                name="Width"
                value="220" />
              <placeholder
                name="Height"
                value="40" />
              <control
                code="ICO"
                id="b474956f-f381-47e3-8b72-7f446386929c">
                <placeholder
                  name="IconName"
                  value="s-icon-task" />
                <placeholder
                  name="Size"
                  value="xl" />
              </control>
              <control
                code="LBL"
                id="58f00965-23ee-4e99-9b7d-dc53c8612a3a">
                <placeholder
                  name="Caption"
                  value="Tasks"
                  resid="1db8e2ba-d6b0-40db-b07f-855dd5543f32" />
                <placeholder
                  name="FontWeight"
                  value="48" />
                <placeholder
                  name="Margin"
                  value="mx-3" />
                <placeholder
                  name="Style"
                  value="font-family: Inter; font-size: 16px; font-weight: 600; line-height: 24px; text-align: left; text-underline-position: from-font; text-decoration-skip-ink: none;" />
                <placeholder
                  name="FormFlowConfiguration">
                  <xml>
                    <formFlows xmlns="">
                      <formFlow
                        newSession="True">ffc587308aca4ad69ad470bcdd04bd2b</formFlow>
                    </formFlows>
                  </xml>
                </placeholder>
              </control>
            </control>
            <control
              code="ICO"
              id="37c975a4-6862-41cf-b7bf-c2d4285b8cf2">
              <placeholder
                name="IconName"
                value="s-icon-caret-right" />
              <placeholder
                name="Style"
                value="margin-right: 12px;" />
              <placeholder
                name="Size"
                value="l" />
            </control>
          </control>
        </control>
      </control>
    </control>
  </form>
