#transformationVersion: 70.0
#
VZ_PK: edf99a2edde04e79bb870df2e0270295
VZ_ConfigurationKey: edf99a2e-dde0-4e79-bb87-0df2e0270295
VZ_FormID: CYP Edit Yard Unit
VZ_Caption:
  resKey: VZ_Caption|edf99a2edde04e79bb870df2e0270295
  text: Edit Yard Unit
VZ_FormFactor: DSK
VZ_EntityType: ICYDYardUnitState
VZ_FormType: FRM
VZ_RequiresLogin: true
VZ_MaterialDesign: true
VZ_Dependencies: <dependencies xmlns="http://cargowise.com/glow/2014/11/14/dependencies.xsd" />
VZ_StatusBindingPath: RejectStatus
VZ_EntityStatusDisplayMode: ReadOnly
VZ_FormData: >-
  <form
    id="bbc17aba-4506-4c70-926f-5592de19e4c1" xmlns="http://wisetechglobal.com/glow/2017/05/26/form.xsd">
    <placeholder
      name="Layout"
      value="flex" />
    <placeholder
      name="FlexWrap"
      value="flex-wrap" />
    <placeholder
      name="Style"
      value="align-content: flex-start; margin-bottom: -8px" />
    <placeholder
      name="FitToHeight"
      value="False" />
    <placeholder
      name="FlexDirection"
      value="flex-row" />
    <control
      code="TBS"
      id="da18b3ed-d47d-464b-b280-fe5952447e63">
      <control
        code="TAB"
        id="92ce7f2b-b373-4b53-b3e8-d17e17cff484">
        <placeholder
          name="Caption"
          value="Overview"
          resid="2d19a4da-885a-498d-bf64-df222c036545" />
      </control>
      <control
        code="TAI"
        id="99d5fab9-7f41-4c7b-b9e0-189cd32eac84">
        <control
          code="BOX"
          id="b5d8f87d-ba71-4ff4-a694-363e6b29875b">
          <placeholder
            name="Layout"
            value="flex" />
          <placeholder
            name="FlexWrap"
            value="flex-wrap" />
          <control
            code="PNL"
            id="eedb79ed-496c-4986-b4f6-b79be0b56ff6"
            binding="">
            <placeholder
              name="Margin"
              value="ma-2" />
            <placeholder
              name="Width"
              value="596" />
            <placeholder
              name="Layout"
              value="grid" />
            <control
              code="LBL"
              id="5522d615-f0e4-4adc-babf-0ff208c8090b"
              binding="">
              <placeholder
                name="Caption"
                value="Unit details"
                resid="8cabe491-e5d6-48ee-94a1-070b1912d4f4" />
              <placeholder
                name="Style"
                value="font-weight: 600" />
              <placeholder
                name="Margin"
                value="ma-2" />
            </control>
            <control
              code="TXT"
              id="897d0b67-9b21-45ff-8b21-dbb1f81ba60e"
              binding="YUS_UnitID">
              <placeholder
                name="Columns"
                value="col-6" />
              <placeholder
                name="Margin"
                value="ma-1" />
              <placeholder
                name="IsReadOnly"
                value="True" />
            </control>
            <control
              code="TXT"
              id="359434e9-1ec6-445b-8007-d7a54be64e8f"
              binding="UnitType">
              <placeholder
                name="Columns"
                value="col-6 col-sm-6 col-md-6 col-lg-6 col-xl-6" />
              <placeholder
                name="Margin"
                value="ma-1" />
            </control>
            <control
              code="TXT"
              id="a207c2f6-ba36-4a52-99b0-338d616240c9"
              binding="Client">
              <placeholder
                name="Columns"
                value="col-6" />
              <placeholder
                name="Margin"
                value="ma-1" />
            </control>
            <control
              code="TXT"
              id="0e12d0d8-7738-459d-8641-bcf61cfa6248"
              binding="Lessee">
              <placeholder
                name="Columns"
                value="col-6" />
              <placeholder
                name="Margin"
                value="ma-1" />
            </control>
            <control
              code="TXT"
              id="45e4aa6a-ff88-4ccb-b3da-dca32f55784f"
              binding="TypeSize">
              <placeholder
                name="Columns"
                value="col-6" />
              <placeholder
                name="CaptionOverride"
                value="Type size"
                resid="69a05464-8b8c-486a-a40f-7cd22a3372fb" />
              <placeholder
                name="Margin"
                value="ma-1" />
            </control>
            <control
              code="NUM"
              id="6c582bb7-5ac5-4f4c-a90f-9f707e3695fb"
              binding="Size">
              <placeholder
                name="Columns"
                value="col-6" />
              <placeholder
                name="Margin"
                value="ma-1" />
              <placeholder
                name="VisibilityCondition"
                value="ReceiveLine.UnitLineItem.YLI_Type==&quot;CNT&quot; || ReceiveLine.UnitLineItem.YLI_Type==&quot;CHS&quot;" />
            </control>
            <control
              code="DTE"
              id="ec362045-915d-4f3e-8584-d061c213df81"
              binding="ManufactureDate">
              <placeholder
                name="Columns"
                value="col-6" />
              <placeholder
                name="Margin"
                value="ma-1" />
              <placeholder
                name="VisibilityCondition"
                value="UnitType==&quot;CNT&quot;" />
            </control>
            <control
              code="OPT"
              id="226aebdb-9421-4d43-b93b-bf12ad82b3c0"
              binding="Empty">
              <placeholder
                name="CaptionOverride"
                value="Empty container"
                resid="7126a85e-9a9d-4d6d-b962-33da7d2e1fba" />
              <placeholder
                name="Columns"
                value="col-6" />
              <placeholder
                name="Margin"
                value="ma-1" />
              <placeholder
                name="VisibilityCondition"
                value="UnitType==&quot;CNT&quot;" />
              <placeholder
                name="Padding"
                value="pt-7" />
            </control>
            <control
              code="TXT"
              id="1a860307-1db4-44c8-926b-0433baff46ed"
              binding="UnitGrade">
              <placeholder
                name="Columns"
                value="col-6" />
              <placeholder
                name="CaptionOverride"
                value="Grade"
                resid="b4baafaa-b14e-4180-8123-627f18ceccf8" />
              <placeholder
                name="Margin"
                value="ma-1" />
            </control>
            <control
              code="SRC"
              id="dd067a43-0c9d-4828-86d2-cef9c3f59d50"
              binding="UnitLineItem.YLI_RX_NKDPPCurrency">
              <placeholder
                name="Columns"
                value="col-6" />
              <placeholder
                name="Margin"
                value="ma-1" />
              <placeholder
                name="IsReadOnly"
                value="True" />
            </control>
            <control
              code="NUM"
              id="7267ebd3-6546-4d3f-97a1-acfafc230bb2"
              binding="UnitLineItem.YLI_DPPAmount">
              <placeholder
                name="Columns"
                value="col-6" />
              <placeholder
                name="Margin"
                value="ma-1" />
            </control>
            <control
              code="OPT"
              id="ed6c10ac-fb2b-44c7-95a4-5d18c23db60b"
              binding="UnitLineItem.YLI_HasFullDPPCoverage">
              <placeholder
                name="CaptionOverride"
                value="Has Full DPP Coverage"
                resid="6cfaca04-8475-4894-a58a-c414ed6f810c" />
              <placeholder
                name="Columns"
                value="col-6" />
              <placeholder
                name="Margin"
                value="ma-1" />
              <placeholder
                name="Padding"
                value="pt-7" />
              <placeholder
                name="IsReadOnly"
                value="True" />
            </control>
            <control
              code="TXT"
              id="2eaa291a-a6bb-4845-8145-b49680df9ecd"
              binding="Status">
              <placeholder
                name="Columns"
                value="col-6" />
              <placeholder
                name="Margin"
                value="ma-1" />
              <placeholder
                name="CaptionOverride"
                value="Unit condition"
                resid="e930fcb4-2fdc-4c86-8db7-deb6a8a12ff7" />
            </control>
          </control>
          <control
            code="PNL"
            id="68718413-cd99-4e3c-889e-d7754e02fca4"
            binding="">
            <placeholder
              name="Margin"
              value="ma-2" />
            <placeholder
              name="Width"
              value="596" />
            <placeholder
              name="Layout"
              value="grid" />
            <control
              code="LBL"
              id="da498c05-5661-4681-b690-7f19fb89fe8c"
              binding="">
              <placeholder
                name="Caption"
                value="Yard in"
                resid="a7ffe3d7-d551-4ebf-810f-6f0d564f48ca" />
              <placeholder
                name="Style"
                value="font-weight: 600" />
              <placeholder
                name="Margin"
                value="ma-2" />
            </control>
            <control
              code="DTE"
              id="1d5eef58-f14c-4d67-90bc-b590eeade8b3"
              binding="YardInDate">
              <placeholder
                name="Columns"
                value="col-6" />
              <placeholder
                name="Margin"
                value="ma-1" />
            </control>
            <control
              code="TXT"
              id="72e63745-cd52-448e-877b-81bab8986fbb"
              binding="ReceiveLine.ReceiveAdvice.YRA_AcceptanceNumber">
              <placeholder
                name="Columns"
                value="col-6" />
              <placeholder
                name="Margin"
                value="ma-1" />
              <placeholder
                name="IsReadOnly"
                value="True" />
            </control>
            <control
              code="TXT"
              id="1bdb705c-8c4f-47d6-aab3-79309ef06f12"
              binding="Client">
              <placeholder
                name="Columns"
                value="col-6" />
              <placeholder
                name="Margin"
                value="ma-1" />
            </control>
            <control
              code="TXT"
              id="1312e140-0009-4c58-8315-6dc2800bc9eb"
              binding="Lessee">
              <placeholder
                name="Columns"
                value="col-6" />
              <placeholder
                name="Margin"
                value="ma-1" />
            </control>
            <control
              code="BOX"
              id="38fffcb2-54bd-48b4-be52-3e9557ac3500"
              binding="">
              <placeholder
                name="Columns"
                value="col-6" />
              <placeholder
                name="Margin"
                value="ma-1" />
              <placeholder
                name="FlexWrap"
                value="flex-nowrap" />
              <placeholder
                name="Layout"
                value="flex" />
              <placeholder
                name="FlexAlign"
                value="align-end" />
              <control
                code="TXT"
                id="7eb8b26c-68e7-4f45-8868-cabb625d3b62"
                binding="ReceiveLine.ReceiveAdvice.YRA_JobNumber" />
              <control
                code="IBT"
                id="4a54d782-f6be-4a23-b83a-95c24394f222"
                binding="">
                <placeholder
                  name="Icon"
                  value="s-icon-new-window" />
                <placeholder
                  name="Margin"
                  value="ml-1" />
                <placeholder
                  name="Style"
                  value="height:32px;width:32px;min-width:32px" />
                <placeholder
                  name="Transition"
                  value="True" />
                <placeholder
                  name="Variant"
                  value="default" />
                <placeholder
                  name="Disabled"
                  value="calc(ReceiveLine == null)" />
              </control>
            </control>
            <control
              code="BOX"
              id="94f096b2-f315-44cc-8736-1252f54794f3"
              binding="">
              <placeholder
                name="Columns"
                value="col-6" />
              <placeholder
                name="Layout"
                value="flex" />
              <placeholder
                name="FlexWrap"
                value="flex-nowrap" />
              <placeholder
                name="Margin"
                value="ma-1" />
              <placeholder
                name="FlexAlign"
                value="align-end" />
              <control
                code="TXT"
                id="b9c418bc-9c55-4d9c-81f6-255191aff098"
                binding="ReceiveTransportationUnit.YTU_TransportationUnitID">
                <placeholder
                  name="CaptionOverride"
                  value="Yard in transportation unit ID"
                  resid="0e7f6023-e568-4b54-936a-61fbc9527d1f" />
              </control>
              <control
                code="IBT"
                id="c9bd44e4-4760-4540-8841-fea4768e4942"
                binding="">
                <placeholder
                  name="Icon"
                  value="s-icon-new-window" />
                <placeholder
                  name="Variant"
                  value="default" />
                <placeholder
                  name="Style"
                  value="height:32px;width:32px;min-width:32px" />
                <placeholder
                  name="Transition"
                  value="True" />
                <placeholder
                  name="Margin"
                  value="ml-1" />
                <placeholder
                  name="Disabled"
                  value="calc(ReceiveTransportationUnit == null)" />
              </control>
            </control>
            <control
              code="TXT"
              id="6d86a0e9-4b16-40a4-81ee-46a44f75ed93"
              binding="ReceiveTransportationUnit.AddressTypeTRA.EffectiveCompanyName">
              <placeholder
                name="CaptionOverride"
                value="Transport Provider"
                resid="5d58e24e-73ce-4c9d-8433-ba11ee2ac5cd" />
              <placeholder
                name="Columns"
                value="col-6" />
              <placeholder
                name="Margin"
                value="ma-1" />
            </control>
            <control
              code="TXT"
              id="7698d85f-3f3a-4114-8552-1ef4e91c8667"
              binding="ReceiveTransportationUnit.YTU_TransportationReference">
              <placeholder
                name="Columns"
                value="col-6" />
              <placeholder
                name="Margin"
                value="ma-1" />
              <placeholder
                name="IsReadOnly"
                value="True" />
            </control>
            <control
              code="TXT"
              id="8eec551a-3d36-4edf-bc7a-32c59f019d6e"
              binding="YardInGrade">
              <placeholder
                name="Columns"
                value="col-6" />
              <placeholder
                name="CaptionOverride"
                value="Grade"
                resid="b01d1dc2-c0a3-43bc-b343-c4ecbca9836f" />
              <placeholder
                name="Margin"
                value="ma-1" />
            </control>
          </control>
          <control
            code="PNL"
            id="00fc435b-5d25-4cee-b681-7060a27a9989"
            binding="">
            <placeholder
              name="Layout"
              value="grid" />
            <placeholder
              name="Margin"
              value="ma-2" />
            <placeholder
              name="Width"
              value="596" />
            <control
              code="LBL"
              id="cec7c4ad-1280-45b0-9d73-b5404efa0b12"
              binding="">
              <placeholder
                name="Caption"
                value="Unload / load"
                resid="96ca9b8b-a5ed-44cc-8d34-2f4556f3225a" />
              <placeholder
                name="Style"
                value="font-weight: 600" />
              <placeholder
                name="Margin"
                value="ma-2" />
            </control>
            <control
              code="DTE"
              id="0f36c6d1-ae5b-4ab9-aa37-b0a9c8ea7b70"
              binding="YUS_UnloadTime">
              <placeholder
                name="Margin"
                value="ma-1" />
              <placeholder
                name="Columns"
                value="col-6" />
              <placeholder
                name="IsReadOnly"
                value="True" />
              <placeholder
                name="CaptionOverride"
                value="Unload date/time"
                resid="c52027ba-1da1-4d34-9be2-685793a13c17" />
            </control>
            <control
              code="TXT"
              id="861c594c-f95a-43ca-8b88-9724e3abf390"
              binding="YUS_GS_NKUnloadUser">
              <placeholder
                name="IsReadOnly"
                value="True" />
              <placeholder
                name="Margin"
                value="ma-1" />
              <placeholder
                name="Columns"
                value="col-6" />
              <placeholder
                name="CaptionOverride"
                value="Unload user"
                resid="86fb8344-58a0-4815-abb4-2724486927e1" />
            </control>
            <control
              code="SRC"
              id="4fc9f5f9-c098-493d-b3ee-c8bc0f643026"
              binding="YUS_WL_CurrentYardLocation">
              <placeholder
                name="Margin"
                value="ma-1" />
              <placeholder
                name="Columns"
                value="col-6" />
              <placeholder
                name="IsReadOnly"
                value="True" />
            </control>
            <control
              code="DTE"
              id="fc74e4ef-b026-4af1-b41c-ca9170c6bcbc"
              binding="YUS_LoadTime">
              <placeholder
                name="Columns"
                value="col-6" />
              <placeholder
                name="Margin"
                value="ma-1" />
              <placeholder
                name="IsReadOnly"
                value="True" />
              <placeholder
                name="CaptionOverride"
                value="Load date/time"
                resid="749006c6-645f-49ff-8bf3-b4be45d2d2b7" />
            </control>
            <control
              code="TXT"
              id="4e0c2af5-16e6-4779-9e46-54218470505b"
              binding="YUS_GS_NKLoadUser">
              <placeholder
                name="Margin"
                value="ma-1" />
              <placeholder
                name="Columns"
                value="col-6" />
              <placeholder
                name="IsReadOnly"
                value="True" />
              <placeholder
                name="CaptionOverride"
                value="Load user"
                resid="b84b8b82-10a4-41a7-82b7-4adc7fed7d09" />
            </control>
          </control>
          <control
            code="PNL"
            id="1d1400d5-a4ba-4b6c-a7c6-b785f8e8d876"
            binding="">
            <placeholder
              name="Margin"
              value="ma-2" />
            <placeholder
              name="Width"
              value="596" />
            <placeholder
              name="Layout"
              value="grid" />
            <control
              code="LBL"
              id="370e5bd7-7e17-426f-a353-53e5dd41c92b"
              binding="">
              <placeholder
                name="Caption"
                value="Yard out"
                resid="ae3ed80f-9bdc-4963-a7e3-eb8e5b0dffd0" />
              <placeholder
                name="Style"
                value="font-weight: 600" />
              <placeholder
                name="Margin"
                value="ma-2" />
            </control>
            <control
              code="DTE"
              id="d8c40b2b-a32c-4fbb-8d65-94ad818f413e"
              binding="ReceiveLine.GateOutTime">
              <placeholder
                name="CaptionOverride"
                value="Yard out date/time"
                resid="12e5b1af-3c91-4bec-ba33-46293a8d5aea" />
              <placeholder
                name="Columns"
                value="col-6" />
              <placeholder
                name="Margin"
                value="ma-1" />
            </control>
            <control
              code="TXT"
              id="79af249a-66e9-428a-b849-e48800492dac"
              binding="ReleaseLine.ReleaseAdvice.YRE_ReleaseNumber">
              <placeholder
                name="Columns"
                value="col-6" />
              <placeholder
                name="CaptionOverride"
                value="Release number"
                resid="279234d6-a065-4621-a6ff-5346417c510a" />
              <placeholder
                name="Margin"
                value="ma-1" />
              <placeholder
                name="IsReadOnly"
                value="True" />
            </control>
            <control
              code="TXT"
              id="70a32c65-cb4a-4ffb-8e12-4591398645c7"
              binding="ReceiveLine.Client">
              <placeholder
                name="Columns"
                value="col-6" />
              <placeholder
                name="Margin"
                value="ma-1" />
            </control>
            <control
              code="TXT"
              id="7cbf1d8a-6b4a-47fb-80cc-f4093319faf7"
              binding="ReceiveLine.Lessee">
              <placeholder
                name="Columns"
                value="col-6" />
              <placeholder
                name="Margin"
                value="ma-1" />
            </control>
            <control
              code="BOX"
              id="6c6a012a-ac9c-48d0-acb7-4ca9f2dd8b9e">
              <placeholder
                name="Columns"
                value="col-6" />
              <placeholder
                name="Margin"
                value="ma-1" />
              <placeholder
                name="FlexWrap"
                value="flex-nowrap" />
              <placeholder
                name="Layout"
                value="flex" />
              <placeholder
                name="FlexAlign"
                value="align-end" />
              <control
                code="TXT"
                id="ed279e88-7af6-4c1d-bf1f-15e955b935e0"
                binding="ReleaseLine.ReleaseAdvice.YRE_JobNumber">
                <placeholder
                  name="IsReadOnly"
                  value="True" />
              </control>
              <control
                code="IBT"
                id="9ef0d8ee-093a-4de5-badc-64c1e0e2ec16">
                <placeholder
                  name="Icon"
                  value="s-icon-new-window" />
                <placeholder
                  name="Margin"
                  value="ml-1" />
                <placeholder
                  name="Transition"
                  value="True" />
                <placeholder
                  name="Style"
                  value="height:32px;width:32px;min-width:32px" />
                <placeholder
                  name="Variant"
                  value="default" />
                <placeholder
                  name="Disabled"
                  value="calc(ReleaseLine == null)" />
              </control>
            </control>
            <control
              code="BOX"
              id="06ac32a4-68e6-40a3-99fd-699e7f2b285b">
              <placeholder
                name="Columns"
                value="col-6" />
              <placeholder
                name="Margin"
                value="ma-1" />
              <placeholder
                name="FlexWrap"
                value="flex-nowrap" />
              <placeholder
                name="Layout"
                value="flex" />
              <placeholder
                name="FlexAlign"
                value="align-end" />
              <control
                code="TXT"
                id="1eaa93b7-e856-47f9-b32f-fde7f35cea60"
                binding="DispatchTransportationUnit.YTU_TransportationUnitID">
                <placeholder
                  name="CaptionOverride"
                  value="Yard out transportation unit ID"
                  resid="420a66e7-7e6d-4b62-b0ae-94b34c922c2b" />
              </control>
              <control
                code="IBT"
                id="4217cd22-5a04-4842-afa7-66e7925a290c">
                <placeholder
                  name="Icon"
                  value="s-icon-new-window" />
                <placeholder
                  name="Variant"
                  value="default" />
                <placeholder
                  name="Margin"
                  value="ml-1" />
                <placeholder
                  name="Transition"
                  value="True" />
                <placeholder
                  name="Style"
                  value="height:32px;width:32px;min-width:32px" />
                <placeholder
                  name="Disabled"
                  value="calc(DispatchTransportationUnit == null)" />
              </control>
            </control>
            <control
              code="TXT"
              id="e98f9516-5330-4e13-a9ef-24fc6da042a6"
              binding="DispatchTransportationUnit.AddressTypeTRA.EffectiveCompanyName">
              <placeholder
                name="CaptionOverride"
                value="Transport provider"
                resid="75c8dbf5-7159-4efa-9635-ee57e4589105" />
              <placeholder
                name="Columns"
                value="col-6" />
              <placeholder
                name="Margin"
                value="ma-1" />
            </control>
            <control
              code="TXT"
              id="17dfc363-846f-42ef-b33a-aa84cd5bb3d6"
              binding="DispatchTransportationUnit.YTU_TransportationReference">
              <placeholder
                name="Columns"
                value="col-6" />
              <placeholder
                name="Margin"
                value="ma-1" />
              <placeholder
                name="IsReadOnly"
                value="True" />
            </control>
            <control
              code="TXT"
              id="fd874401-9036-4b8f-a816-e0af4c9b3989"
              binding="Pickup.SealNumber">
              <placeholder
                name="IsReadOnly"
                value="True" />
              <placeholder
                name="CaptionOverride"
                value="Seal number"
                resid="ad2e3062-cdc3-4ecf-8215-dff668873630" />
              <placeholder
                name="Margin"
                value="ma-1" />
              <placeholder
                name="Columns"
                value="col-6" />
            </control>
          </control>
        </control>
      </control>
      <control
        code="TAB"
        id="84bba531-51e9-4989-a18b-39b8e317ffba">
        <placeholder
          name="Caption"
          value="Maintenance &amp; repair"
          resid="26b2a66b-b70f-4208-a213-120d099565a8" />
      </control>
      <control
        code="TAI"
        id="614e06e8-8b68-49ac-a475-e6bb5bf6a805">
        <control
          code="PNL"
          id="4a3a80be-31cc-4ac3-9310-2fe602d68866">
          <placeholder
            name="Margin"
            value="ma-4" />
          <control
            code="RDT"
            id="d78d6b58-22e5-4656-97a8-368c2b6e7b21"
            binding="MNRWorkOrderHeaders">
            <placeholder
              name="FieldConfiguration">
              <xml>
                <fields xmlns="">
                  <field
                    path="Status"
                    width="180"
                    caption="M&amp;R status"
                    resid="7e262934-e2ec-4b76-be51-824778a06ed4"
                    mode="Mandatory" />
                  <field
                    path="MWO_JobNumber"
                    width="180"
                    caption="Estimate ID"
                    resid="21924ac9-460b-4212-896f-b9706a911868"
                    mode="Mandatory" />
                  <field
                    path="MWO_Type"
                    width="180"
                    caption="Estimate type"
                    resid="e0725d52-c31b-47dd-acfa-0f3c3d8a8f4c"
                    mode="Default" />
                  <field
                    path="YardUnitState.Client"
                    width="180"
                    mode="Default" />
                  <field
                    path="YardUnitState.Lessee"
                    width="180"
                    mode="Mandatory" />
                  <field
                    path="EstimateNote"
                    width="180"
                    caption="Estimate remarks"
                    resid="7d532877-0d3a-4c46-80e5-5260779f5ce6"
                    mode="Default" />
                  <field
                    path="MWO_SystemCreateTimeUtc"
                    width="180"
                    caption="Estimate date"
                    resid="7383be93-66f4-4b60-bcff-a64da681bb19"
                    mode="Default" />
                  <field
                    path="MWO_SystemCreateUser"
                    width="180"
                    caption="Surveyor"
                    resid="ec7dc10e-a268-4338-8f5b-0b57de2e20ac"
                    mode="Default" />
                  <field
                    path="YardUnitState.UnitGrade"
                    width="180"
                    caption="Grade"
                    resid="5a39dadf-582f-4c7a-b7b9-6f2769053e5d"
                    mode="Default" />
                  <field
                    path="YardUnitState.ManufactureDate"
                    width="180"
                    caption="Manufacture date"
                    resid="e475e186-d27e-4055-89fe-5ea2b033d5ea"
                    mode="Default" />
                </fields>
              </xml>
            </placeholder>
            <placeholder
              name="AllowAttach"
              value="False" />
            <placeholder
              name="AllowDetach"
              value="False" />
            <placeholder
              name="CaptionOverride"
              value="Current estimate of repairs"
              resid="14330866-9976-4159-bc07-4b9b5c399964" />
            <placeholder
              name="HideExport"
              value="True" />
            <placeholder
              name="HideImport"
              value="True" />
            <placeholder
              name="ShowCustomize"
              value="True" />
            <placeholder
              name="DisabledGridRowActions"
              value="Activation,Documents,eDocs,Logs,Messages,Notes,Tracking,Workflow" />
            <placeholder
              name="ActionMenuItems">
              <xml>
                <formFlows xmlns="">
                  <formFlow
                    inDrawer="True">c56f34004ba0413389557337b82be627</formFlow>
                </formFlows>
              </xml>
            </placeholder>
            <placeholder
              name="EditFormFlowConfiguration">
              <xml>
                <formFlows xmlns="">
                  <formFlow
                    inDrawer="True">c56f34004ba0413389557337b82be627</formFlow>
                </formFlows>
              </xml>
            </placeholder>
          </control>
        </control>
        <control
          code="PNL"
          id="36721395-b2e5-4800-9709-c67f480600a5">
          <placeholder
            name="Margin"
            value="ma-4" />
          <control
            code="RDT"
            id="fab069c6-36cc-432b-b1f9-59495a731059"
            binding="Workshop">
            <placeholder
              name="CaptionOverride"
              value="Workshop"
              resid="594776d6-87f5-4185-935a-cf22067e3367" />
            <placeholder
              name="AllowAttach"
              value="False" />
            <placeholder
              name="AllowDetach"
              value="False" />
            <placeholder
              name="FieldConfiguration">
              <xml>
                <fields xmlns="">
                  <field
                    path="Status"
                    width="180"
                    caption="Workshop status"
                    resid="2007efc1-e1a1-403e-8309-979e289dcc13"
                    mode="Mandatory" />
                  <field
                    path="MNRWorkOrderHeader.MWO_JobNumber"
                    width="180"
                    caption="Estimate ID"
                    resid="b903ad50-c7bd-4b87-afb8-fe97f18ea08d"
                    mode="Default" />
                  <field
                    path="MWL_Type"
                    width="180"
                    caption="Estimate type"
                    resid="ebe423f8-eb96-4318-82eb-f97a0c4a4a32"
                    mode="Default" />
                  <field
                    path="MWL_RUS_UnitSection"
                    width="180"
                    mode="Default" />
                  <field
                    path="MWL_RCC_ComponentCode"
                    width="180"
                    mode="Default" />
                  <field
                    path="MWL_RMC_Material"
                    width="180"
                    mode="Default" />
                  <field
                    path="MWL_RRC_RepairCode"
                    width="150"
                    mode="Mandatory" />
                  <field
                    path="MWL_RFM_Damage"
                    width="180"
                    mode="Default" />
                  <field
                    path="ServiceType"
                    width="180"
                    mode="Default" />
                  <field
                    path="MWL_LengthMeasure"
                    width="180"
                    caption="Length"
                    resid="b85cd79f-ede6-4ecb-b3f2-6312e8a600bc"
                    mode="Default" />
                  <field
                    path="MWL_WidthMeasure"
                    width="180"
                    caption="Width"
                    resid="4da1e603-2e77-415b-945f-fd1eca373613"
                    mode="Default" />
                  <field
                    path="MWL_TaskStartTime"
                    width="180"
                    mode="Default" />
                  <field
                    path="MWL_TaskEndTime"
                    width="180"
                    mode="Default" />
                </fields>
              </xml>
            </placeholder>
            <placeholder
              name="HideExport"
              value="True" />
            <placeholder
              name="HideImport"
              value="True" />
            <placeholder
              name="ShowCustomize"
              value="True" />
            <placeholder
              name="HideActions"
              value="True" />
          </control>
        </control>
      </control>
      <control
        code="TAB"
        id="f2a60f36-fd13-4ff4-b175-6f35381ed6fb">
        <placeholder
          name="Caption"
          value="Ad-hoc services"
          resid="a666bd5e-d898-4d34-8eee-0d90872447ed" />
      </control>
      <control
        code="TAI"
        id="21e8c70f-60a6-46b1-abf6-ce9b65530075">
        <control
          code="PNL"
          id="5264eea6-4f7b-4e4e-a0c4-99b3853690eb">
          <placeholder
            name="Margin"
            value="ma-4" />
          <control
            code="RDT"
            id="09f85d38-e304-4dc4-bcb3-4749ba547260"
            binding="AdHocServices">
            <placeholder
              name="CaptionOverride"
              value="Current ad-hoc services"
              resid="68bc470a-e8a7-4c27-8e57-9373738530a6" />
            <placeholder
              name="ActionMenuItems">
              <xml>
                <formFlows xmlns="">
                  <formFlow
                    inDrawer="True">5de33463c8754cfea248645f5b0e2bd5</formFlow>
                </formFlows>
              </xml>
            </placeholder>
            <placeholder
              name="FieldConfiguration">
              <xml>
                <fields xmlns="">
                  <field
                    path="JobService.ES_ServiceId"
                    width="300"
                    caption="Ad-hoc service ID"
                    resid="07c1ac92-fbeb-4da1-9811-4a4c2274645e"
                    mode="Mandatory" />
                  <field
                    path="JobNumber"
                    width="300"
                    caption="Service order ID"
                    resid="9a609210-f69f-4d82-b590-a50ce46eccee"
                    mode="Mandatory" />
                  <field
                    path="ServiceStatus"
                    width="300"
                    caption="Service status"
                    resid="2cd52dc1-f873-4619-9a37-e17b229a894e"
                    mode="Mandatory" />
                  <field
                    path="JobService.ES_ServiceCode"
                    width="300"
                    caption="Service type"
                    resid="d89a27e0-5142-4106-b369-85caaa716cad"
                    mode="Mandatory" />
                  <field
                    path="JobService.ES_BookedDateTimeOffset"
                    width="300"
                    caption="Start date"
                    resid="5fb77ba3-df99-4b6b-aa5f-614ae7bd13a3"
                    mode="Mandatory" />
                  <field
                    path="JobService.ES_CompletedDateTimeOffset"
                    width="300"
                    caption="Complete date"
                    resid="18153798-c2e0-4311-bb22-a4ea52348456"
                    mode="Mandatory" />
                  <field
                    path="JobService.Contractor.OH_FullName"
                    width="300"
                    caption="Contractor"
                    resid="9b0843f6-b754-4cfe-ba47-3ab15ba2683f"
                    mode="Mandatory" />
                </fields>
              </xml>
            </placeholder>
            <placeholder
              name="EditFormFlowConfiguration">
              <xml>
                <formFlows xmlns="">
                  <formFlow
                    inDrawer="True">5de33463c8754cfea248645f5b0e2bd5</formFlow>
                </formFlows>
              </xml>
            </placeholder>
          </control>
        </control>
      </control>
    </control>
  </form>
