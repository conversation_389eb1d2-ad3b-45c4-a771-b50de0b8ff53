#transformationVersion: 70.0
#
VZ_PK: 7793e2faac3d4364860f1ce446823dbe
VZ_ConfigurationKey: 7793e2fa-ac3d-4364-860f-1ce446823dbe
VZ_FormID: ETL - VDV3 - Destination Depot - Scan to Receive Items on Shipment with Button
VZ_Caption:
  resKey: VZ_Caption|7793e2fa-ac3d-4364-860f-1ce446823dbe
  text: Scan to Receive Items
VZ_FormFactor: DSK
VZ_EntityType: IJobShipment
VZ_FormType: FRM
VZ_RequiresLogin: true
VZ_Dependencies: >-
  <dependencies xmlns="http://cargowise.com/glow/2014/11/14/dependencies.xsd">
    <expandPath
      path="InternationalArrivalConsol.TransportFirstLeg" />
    <expandPath
      path="InternationalArrivalConsol" />
    <expandPath
      path="InternationalArrivalConsol.TransportLastLeg" />
    <expandPath
      path="HLRCargoReporting" />
    <expandPath
      path="InternationalArrivalConsol.ArrivalCTOAddress" />
    <expandPath
      path="InternationalArrivalConsol.UnpackDepotAddress" />
    <expandPath
      path="InternationalArrivalConsol.TransportLastLeg.JobSailing.JobVoyDestination" />
    <expandPath
      path="InternationalArrivalConsol.TransportLastLeg.DiscPort" />
    <expandPath
      path="DocsAndCartageDetail" />
    <expandPath
      path="DocsAndCartageDetail.DeliveryCartageCoAddr" />
    <expandPath
      path="FirstScanEvent" />
    <expandPath
      path="LastScanEvent" />
    <expandPath
      path="CurrentItem.Consignment" />
    <expandPath
      path="CurrentItem" />
    <calculatedProperty
      path="ConsignorName" />
    <calculatedProperty
      path="InternationalArrivalConsol" />
    <calculatedProperty
      path="InternationalArrivalConsol.TransportFirstLeg" />
    <calculatedProperty
      path="ItemCount" />
    <calculatedProperty
      path="InternationalArrivalConsol.TransportLastLeg" />
    <calculatedProperty
      path="HLRCargoReporting" />
    <calculatedProperty
      path="HeldCount" />
    <calculatedProperty
      path="InternationalArrivalConsol.ArrivalCTOAddress.CompanyName" />
    <calculatedProperty
      path="ClearCount" />
    <calculatedProperty
      path="InternationalArrivalConsol.UnpackDepotAddress.CompanyName" />
    <calculatedProperty
      path="DocsAndCartageDetail" />
    <calculatedProperty
      path="DocsAndCartageDetail.DeliveryCartageCoAddr.CompanyName" />
    <calculatedProperty
      path="SurplusCount" />
    <calculatedProperty
      path="FirstScanEvent" />
    <calculatedProperty
      path="LastOutturnDate" />
    <calculatedProperty
      path="LastScanEvent" />
    <calculatedProperty
      path="CurrentItem" />
    <calculatedProperty
      path="CurrentItem.Consignment.DetailedImportStatusWithBrackets" />
    <calculatedProperty
      path="CurrentItem.ReturnLastMileCarrierAndBarcode" />
    <calculatedProperty
      path="CurrentItem.ReturnReleaseStatus" />
    <calculatedProperty
      path="CurrentSearchTerm" />
  </dependencies>
VZ_FormData: >-
  <form
    id="c6466453-5ae1-4a8c-b1a2-6e357ff69154" xmlns="http://wisetechglobal.com/glow/2017/05/26/form.xsd">
    <placeholder
      name="BackgroundImagePk"
      value="" />
    <placeholder
      name="BackgroundSize"
      value="Auto" />
    <placeholder
      name="BackgroundImageOverride"
      value="" />
    <placeholder
      name="FormLayout"
      value="Splitview" />
    <placeholder
      name="ExpandPaths"
      value="" />
    <placeholder
      name="ScanInputHandling"
      value="NON" />
    <placeholder
      name="RemovePadding"
      value="false" />
    <placeholder
      name="TurnOnPortableOneToOneDesignMode"
      value="false" />
    <placeholder
      name="ContainsDefaultButton"
      value="False" />
    <control
      code="GRP"
      id="85658af3-aa5b-4787-8827-057d87d8d1d3"
      top="0"
      width="32"
      height="22.9">
      <placeholder
        name="CanBeHidden"
        value="True" />
      <placeholder
        name="VisibilityCondition"
        value="b0316001-e70d-494f-9f1f-4e0dd3055fe8" />
      <placeholder
        name="Header"
        value="New Section"
        resid="5acd7e58-fa76-4d9f-8518-c9f55578d01b" />
      <placeholder
        name="HeaderAlignment"
        value="Left" />
      <placeholder
        name="HeaderDisplayMode"
        value="Hidden" />
      <placeholder
        name="BackgroundImagePk"
        value="" />
      <placeholder
        name="BackgroundImageOverride"
        value="" />
      <control
        code="FLB"
        id="94ca5386-2292-4d41-8b7e-d2f5527fb15e"
        left="6"
        top="7"
        width="20"
        height="3">
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="VisibilityCondition"
          value="138f4f14-fb80-4f75-ae47-7cf5c9a62179" />
        <placeholder
          name="LabelText"
          value="Receipt at CFS before scanning can commence"
          resid="760f4707-fb64-455b-bdfb-76e44ead594c" />
        <placeholder
          name="LabelType"
          value="Notification" />
        <placeholder
          name="LabelNotificationType"
          value="Error" />
        <placeholder
          name="ForegroundThemeColor"
          value="GeneralText" />
        <placeholder
          name="TextSize"
          value="Large" />
        <placeholder
          name="FontWeight"
          value="Normal" />
        <placeholder
          name="LabelAlignment"
          value="Center" />
        <placeholder
          name="BackgroundColor"
          value="" />
        <placeholder
          name="Padding"
          value="None" />
      </control>
      <control
        code="FLB"
        id="60096d1d-736a-40a7-8798-4184229f7ba1"
        left="11.5"
        top="10"
        width="9.3"
        height="1">
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="VisibilityCondition"
          value="44ff7cc7-f820-4165-9a87-30a0f2855cd5" />
        <placeholder
          name="LabelText"
          value="Please Scan Next Item"
          resid="3fce35bd-9b0e-4b87-bc0b-cae1fdd2c7ca" />
        <placeholder
          name="LabelType"
          value="NormalLabel" />
        <placeholder
          name="LabelNotificationType"
          value="Information" />
        <placeholder
          name="ForegroundThemeColor"
          value="DefaultButton" />
        <placeholder
          name="TextSize"
          value="Medium" />
        <placeholder
          name="FontWeight"
          value="Bold" />
        <placeholder
          name="LabelAlignment"
          value="Center" />
        <placeholder
          name="BackgroundColor"
          value="" />
        <placeholder
          name="Padding"
          value="None" />
      </control>
      <control
        code="TBT"
        id="d7042e58-d1dc-450c-8b66-d3b7feaddc4d"
        left="11.5"
        top="11"
        width="9.3"
        height="1">
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="VisibilityCondition"
          value="35cf9df9-2d3a-4543-9d41-7fca57d2f9b9" />
        <placeholder
          name="Content"
          value="Edit Last Scanned Item"
          resid="dbce6229-8916-4496-abdc-6d27bbb30208" />
        <placeholder
          name="IsDefault"
          value="False" />
      </control>
      <control
        code="FLB"
        id="1aa44d90-688b-404d-8179-5b0ca55019e8"
        left="6"
        top="12.2"
        width="10"
        height="1">
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="LabelText"
          value="Arrival Details"
          resid="f0788cdd-d6bc-4e88-8a87-4eaafe6bce6a" />
        <placeholder
          name="LabelType"
          value="NormalLabel" />
        <placeholder
          name="LabelNotificationType"
          value="Information" />
        <placeholder
          name="ForegroundThemeColor"
          value="DefaultButton" />
        <placeholder
          name="TextSize"
          value="Medium" />
        <placeholder
          name="FontWeight"
          value="Bold" />
        <placeholder
          name="LabelAlignment"
          value="Center" />
        <placeholder
          name="BackgroundColor"
          value="" />
        <placeholder
          name="Padding"
          value="None" />
      </control>
      <control
        code="FLB"
        id="fc0e9a52-fd4e-44dc-8900-6060863e770d"
        left="17.1"
        top="12.2"
        width="8.8"
        height="1">
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="LabelText"
          value="Item Counts"
          resid="88f5f847-cecd-412d-835a-8f5cc04cd7fb" />
        <placeholder
          name="LabelType"
          value="NormalLabel" />
        <placeholder
          name="LabelNotificationType"
          value="Information" />
        <placeholder
          name="ForegroundThemeColor"
          value="DefaultButton" />
        <placeholder
          name="TextSize"
          value="Medium" />
        <placeholder
          name="FontWeight"
          value="Bold" />
        <placeholder
          name="LabelAlignment"
          value="Center" />
        <placeholder
          name="BackgroundColor"
          value="" />
        <placeholder
          name="Padding"
          value="None" />
      </control>
      <control
        code="TXT"
        id="a794d33c-4b6b-4f2d-8051-cf57417975df"
        left="6"
        top="13.2"
        width="6"
        height="1"
        binding="ConsignorName">
        <placeholder
          name="IsReadOnly"
          value="True" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="eTailer"
          resid="4d205a1c-3ab6-4023-b93b-37e306756165" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="OnSubmit"
          value="" />
        <placeholder
          name="MultiLine"
          value="False" />
        <placeholder
          name="EnableAutoComplete"
          value="False" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="DTE"
        id="1f72d4dd-624d-4ee1-a047-49c3e1642ee2"
        left="12"
        top="13.2"
        width="4"
        height="1"
        binding="InternationalArrivalConsol.TransportFirstLeg.JW_ETD">
        <placeholder
          name="IsReadOnly"
          value="True" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="DefaultValueExpression"
          value="" />
        <placeholder
          name="IncludeTime"
          value="True" />
        <placeholder
          name="ShowNowButton"
          value="True" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="FLB"
        id="885a5059-764c-4114-b4ff-4fcb1c41ea2a"
        left="17.1"
        top="13.2"
        width="4"
        height="2">
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="LabelText"
          value="Total  -"
          resid="abf97f26-1963-4dd7-8412-69dc247f977c" />
        <placeholder
          name="LabelType"
          value="Notification" />
        <placeholder
          name="LabelNotificationType"
          value="Information" />
        <placeholder
          name="ForegroundThemeColor"
          value="GeneralText" />
        <placeholder
          name="TextSize"
          value="ExtraLarge" />
        <placeholder
          name="FontWeight"
          value="Bold" />
        <placeholder
          name="LabelAlignment"
          value="Center" />
        <placeholder
          name="BackgroundColor"
          value="" />
        <placeholder
          name="Padding"
          value="None" />
      </control>
      <control
        code="LBL"
        id="e1d99a85-33c9-46a3-be46-5b0788d9576f"
        left="21.1"
        top="13.2"
        width="4.9"
        height="2"
        binding="ItemCount">
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="LabelType"
          value="Notification" />
        <placeholder
          name="LabelNotificationType"
          value="Information" />
        <placeholder
          name="ForegroundThemeColor"
          value="GeneralText" />
        <placeholder
          name="TextSize"
          value="ExtraLarge" />
        <placeholder
          name="FontWeight"
          value="Bold" />
        <placeholder
          name="LabelAlignment"
          value="CenterLeft" />
        <placeholder
          name="BackgroundColor"
          value="" />
        <placeholder
          name="Padding"
          value="None" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="TXT"
        id="f3c16f3a-a8f5-4f5c-b9ba-6c59eeeefe6a"
        left="6"
        top="14.2"
        width="6"
        height="1"
        binding="InternationalArrivalConsol.JK_MasterBillNum">
        <placeholder
          name="IsReadOnly"
          value="True" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="OnSubmit"
          value="" />
        <placeholder
          name="MultiLine"
          value="False" />
        <placeholder
          name="EnableAutoComplete"
          value="False" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="DTE"
        id="e38090bc-8ea8-4d89-8caa-7bbdc1baad88"
        left="12"
        top="14.2"
        width="4"
        height="1"
        binding="InternationalArrivalConsol.TransportLastLeg.JW_ETA">
        <placeholder
          name="IsReadOnly"
          value="True" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="DefaultValueExpression"
          value="" />
        <placeholder
          name="IncludeTime"
          value="True" />
        <placeholder
          name="ShowNowButton"
          value="True" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="TXT"
        id="7b561e10-6437-494e-83a6-5f39dd0d9d3c"
        left="6"
        top="15.2"
        width="6"
        height="1"
        binding="JS_HouseBill">
        <placeholder
          name="IsReadOnly"
          value="True" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="OnSubmit"
          value="" />
        <placeholder
          name="MultiLine"
          value="False" />
        <placeholder
          name="EnableAutoComplete"
          value="False" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="DTE"
        id="7407aca9-41bd-410c-9275-478fdaa652fc"
        left="12"
        top="15.2"
        width="4"
        height="1"
        binding="HLRCargoReporting.SL_PostedTimeUtc">
        <placeholder
          name="IsReadOnly"
          value="True" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="HVLV Data to Customs"
          resid="f50b6ea8-1b21-44e0-b84d-72e34cfb78fc" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="DefaultValueExpression"
          value="" />
        <placeholder
          name="IncludeTime"
          value="True" />
        <placeholder
          name="ShowNowButton"
          value="True" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="FLB"
        id="744dda17-5382-43ab-b154-604cbc4897b7"
        left="17.1"
        top="15.2"
        width="4"
        height="2.5">
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="LabelText"
          value="Held  -"
          resid="35115998-f1bb-44eb-b224-efe30743dd23" />
        <placeholder
          name="LabelType"
          value="Notification" />
        <placeholder
          name="LabelNotificationType"
          value="Error" />
        <placeholder
          name="ForegroundThemeColor"
          value="GeneralText" />
        <placeholder
          name="TextSize"
          value="ExtraLarge" />
        <placeholder
          name="FontWeight"
          value="Bold" />
        <placeholder
          name="LabelAlignment"
          value="Center" />
        <placeholder
          name="BackgroundColor"
          value="" />
        <placeholder
          name="Padding"
          value="None" />
      </control>
      <control
        code="LBL"
        id="7a1ed285-99ef-4b9f-a552-94a43c4d3c6a"
        left="21.1"
        top="15.2"
        width="4.9"
        height="2.5"
        binding="HeldCount">
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="LabelType"
          value="Notification" />
        <placeholder
          name="LabelNotificationType"
          value="Error" />
        <placeholder
          name="ForegroundThemeColor"
          value="GeneralText" />
        <placeholder
          name="TextSize"
          value="ExtraLarge" />
        <placeholder
          name="FontWeight"
          value="Bold" />
        <placeholder
          name="LabelAlignment"
          value="CenterLeft" />
        <placeholder
          name="BackgroundColor"
          value="" />
        <placeholder
          name="Padding"
          value="None" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="TXT"
        id="68dcec18-6b55-41a5-b6e9-d1583d46cfb0"
        left="6"
        top="16.2"
        width="6"
        height="1"
        binding="InternationalArrivalConsol.TransportLastLeg.JW_VoyageFlight">
        <placeholder
          name="IsReadOnly"
          value="True" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="OnSubmit"
          value="" />
        <placeholder
          name="MultiLine"
          value="False" />
        <placeholder
          name="EnableAutoComplete"
          value="False" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="DTE"
        id="887bf94b-da3a-4bb2-a68b-e750222f1dfb"
        left="12"
        top="16.2"
        width="4"
        height="1"
        binding="InternationalArrivalConsol.TransportLastLeg.JW_ATD">
        <placeholder
          name="IsReadOnly"
          value="True" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="DefaultValueExpression"
          value="" />
        <placeholder
          name="IncludeTime"
          value="True" />
        <placeholder
          name="ShowNowButton"
          value="True" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="TXT"
        id="41125dd0-22d7-49f7-9295-5f54ce272db9"
        left="6"
        top="17.2"
        width="6"
        height="1"
        binding="InternationalArrivalConsol.ArrivalCTOAddress.CompanyName">
        <placeholder
          name="IsReadOnly"
          value="True" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="Arrival CTO"
          resid="ad852598-cc30-43d0-95fd-cb32e701e0d0" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="OnSubmit"
          value="" />
        <placeholder
          name="MultiLine"
          value="False" />
        <placeholder
          name="EnableAutoComplete"
          value="False" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="DTE"
        id="ab169e49-d0dd-465d-bfe3-e9b236117087"
        left="12"
        top="17.2"
        width="4"
        height="1"
        binding="InternationalArrivalConsol.TransportLastLeg.JW_ATA">
        <placeholder
          name="IsReadOnly"
          value="True" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="DefaultValueExpression"
          value="" />
        <placeholder
          name="IncludeTime"
          value="True" />
        <placeholder
          name="ShowNowButton"
          value="True" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="FLB"
        id="6f85417c-4661-4a0e-9a6f-73e0b181930d"
        left="17.1"
        top="17.7"
        width="4"
        height="2.5">
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="LabelText"
          value="Clear  -"
          resid="dd30452f-7b63-48ca-8879-3f4ae96a1e3a" />
        <placeholder
          name="LabelType"
          value="Notification" />
        <placeholder
          name="LabelNotificationType"
          value="Success" />
        <placeholder
          name="ForegroundThemeColor"
          value="GeneralText" />
        <placeholder
          name="TextSize"
          value="ExtraLarge" />
        <placeholder
          name="FontWeight"
          value="Bold" />
        <placeholder
          name="LabelAlignment"
          value="Center" />
        <placeholder
          name="BackgroundColor"
          value="" />
        <placeholder
          name="Padding"
          value="None" />
      </control>
      <control
        code="LBL"
        id="3dd2d2ec-f080-469c-841e-45a33e08dec4"
        left="21.1"
        top="17.7"
        width="4.9"
        height="2.5"
        binding="ClearCount">
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="LabelType"
          value="Notification" />
        <placeholder
          name="LabelNotificationType"
          value="Success" />
        <placeholder
          name="ForegroundThemeColor"
          value="GeneralText" />
        <placeholder
          name="TextSize"
          value="ExtraLarge" />
        <placeholder
          name="FontWeight"
          value="Bold" />
        <placeholder
          name="LabelAlignment"
          value="CenterLeft" />
        <placeholder
          name="BackgroundColor"
          value="" />
        <placeholder
          name="Padding"
          value="None" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="TXT"
        id="811aa070-1f83-42c4-9d13-7720eee8053d"
        left="6"
        top="18.2"
        width="6"
        height="1"
        binding="InternationalArrivalConsol.UnpackDepotAddress.CompanyName">
        <placeholder
          name="IsReadOnly"
          value="True" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="Arrival CFS"
          resid="a7b3c794-e121-4f00-8352-65b27fc86e22" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="OnSubmit"
          value="" />
        <placeholder
          name="MultiLine"
          value="False" />
        <placeholder
          name="EnableAutoComplete"
          value="False" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="DTE"
        id="7f9313a4-32d5-40aa-a495-b56042197de3"
        left="12"
        top="18.2"
        width="4"
        height="1"
        binding="InternationalArrivalConsol.TransportLastLeg.JobSailing.JobVoyDestination.JB_AvailabilityDate">
        <placeholder
          name="IsReadOnly"
          value="True" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="DefaultValueExpression"
          value="" />
        <placeholder
          name="IncludeTime"
          value="True" />
        <placeholder
          name="ShowNowButton"
          value="True" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="SRC"
        id="869f6ad2-50e6-44f5-9231-c2d3fc9a295d"
        left="6"
        top="19.2"
        width="6"
        height="1"
        binding="InternationalArrivalConsol.TransportLastLeg.JW_RL_NKDiscPort">
        <placeholder
          name="IsReadOnly"
          value="True" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="DisplayMode"
          value="DescOnly" />
        <placeholder
          name="FieldConfiguration"
          value="" />
        <placeholder
          name="DefaultFilter"
          value="" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="DTE"
        id="7986442f-ec4d-4c52-bd86-00cf4f9d88ff"
        left="12"
        top="19.2"
        width="4"
        height="1"
        binding="DocsAndCartageDetail.JP_LCLAvailable">
        <placeholder
          name="IsReadOnly"
          value="True" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="Shipment CFS Available"
          resid="f81368ad-e631-455d-b77e-ea20bfdf7ad3" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="DefaultValueExpression"
          value="" />
        <placeholder
          name="IncludeTime"
          value="True" />
        <placeholder
          name="ShowNowButton"
          value="True" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="TXT"
        id="9bf2d24d-a8cd-44f2-98e9-6fdf7b2957ea"
        left="6"
        top="20.2"
        width="6"
        height="1"
        binding="DocsAndCartageDetail.DeliveryCartageCoAddr.CompanyName">
        <placeholder
          name="IsReadOnly"
          value="True" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="Delivery Cartage"
          resid="c5223a7a-2dec-4ff2-9165-36ba8ecb875a" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="OnSubmit"
          value="" />
        <placeholder
          name="MultiLine"
          value="False" />
        <placeholder
          name="EnableAutoComplete"
          value="False" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="DTE"
        id="db09828a-5d83-4658-a996-b2c0db21f39e"
        left="12"
        top="20.2"
        width="4"
        height="1"
        binding="DocsAndCartageDetail.JP_DeliveryCartageCompleted">
        <placeholder
          name="IsReadOnly"
          value="True" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="Dispatched"
          resid="4a690927-79d1-458d-95da-fb4067f95a73" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="DefaultValueExpression"
          value="" />
        <placeholder
          name="IncludeTime"
          value="True" />
        <placeholder
          name="ShowNowButton"
          value="True" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="FLB"
        id="4d58e55e-7479-4a4b-a87f-15d93258bb65"
        left="17.1"
        top="20.2"
        width="4"
        height="2">
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="LabelText"
          value="Surplus  -"
          resid="dce5b6c5-79ec-4e29-a903-acd19e48d7ad" />
        <placeholder
          name="LabelType"
          value="Notification" />
        <placeholder
          name="LabelNotificationType"
          value="MessageError" />
        <placeholder
          name="ForegroundThemeColor"
          value="GeneralText" />
        <placeholder
          name="TextSize"
          value="ExtraLarge" />
        <placeholder
          name="FontWeight"
          value="Bold" />
        <placeholder
          name="LabelAlignment"
          value="Center" />
        <placeholder
          name="BackgroundColor"
          value="" />
        <placeholder
          name="Padding"
          value="None" />
      </control>
      <control
        code="LBL"
        id="bcaf573e-dd27-4728-b91d-fd7004de6cdb"
        left="21.1"
        top="20.2"
        width="4.9"
        height="2"
        binding="SurplusCount">
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="LabelType"
          value="Notification" />
        <placeholder
          name="LabelNotificationType"
          value="MessageError" />
        <placeholder
          name="ForegroundThemeColor"
          value="GeneralText" />
        <placeholder
          name="TextSize"
          value="ExtraLarge" />
        <placeholder
          name="FontWeight"
          value="Bold" />
        <placeholder
          name="LabelAlignment"
          value="CenterLeft" />
        <placeholder
          name="BackgroundColor"
          value="" />
        <placeholder
          name="Padding"
          value="None" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="DTE"
        id="6b52af22-0a4d-4654-9a91-a0b2648ce3d8"
        left="6"
        top="21.2"
        width="6"
        height="1"
        binding="FirstScanEvent.SL_PostedTimeUtc">
        <placeholder
          name="IsReadOnly"
          value="True" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="First Scan Time"
          resid="0b09aabf-dbb2-4c7b-ace8-2eeeca75bfc3" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="DefaultValueExpression"
          value="" />
        <placeholder
          name="IncludeTime"
          value="True" />
        <placeholder
          name="ShowNowButton"
          value="True" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="DTE"
        id="be259e44-2d7c-47ab-b1d3-faada0b5c06f"
        left="12"
        top="21.2"
        width="4"
        height="1"
        binding="LastOutturnDate">
        <placeholder
          name="IsReadOnly"
          value="True" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="Outturn"
          resid="133730c3-e14e-40e3-8ff2-29bd3b8df192" />
        <placeholder
          name="VisibilityCondition"
          value="e86de108-19ab-4f3a-af3a-27865757dd85" />
        <placeholder
          name="DefaultValueExpression"
          value="" />
        <placeholder
          name="IncludeTime"
          value="True" />
        <placeholder
          name="ShowNowButton"
          value="True" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="DTE"
        id="8d998873-485a-48c0-be48-8b25091cc963"
        left="12"
        top="21.2"
        width="4"
        height="1"
        binding="LastScanEvent.SL_EventTime">
        <placeholder
          name="IsReadOnly"
          value="True" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="Scanning Complete"
          resid="49450467-d10d-4a7b-a6ab-99b0c54ff6a7" />
        <placeholder
          name="VisibilityCondition"
          value="e0c99c62-25d5-451c-b623-2cebf37c392c" />
        <placeholder
          name="DefaultValueExpression"
          value="" />
        <placeholder
          name="IncludeTime"
          value="True" />
        <placeholder
          name="ShowNowButton"
          value="True" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
    </control>
    <control
      code="GRP"
      id="511b35a4-b9f2-4857-9e7d-e5435d2311e1"
      top="1"
      width="30"
      height="8">
      <placeholder
        name="CanBeHidden"
        value="True" />
      <placeholder
        name="VisibilityCondition"
        value="e3633a27-818c-469a-a23a-bab2918a7fce" />
      <placeholder
        name="Header"
        value="New Section"
        resid="1539e53a-27f8-4d60-b056-3e858e8c04e6" />
      <placeholder
        name="HeaderAlignment"
        value="Left" />
      <placeholder
        name="HeaderDisplayMode"
        value="Hidden" />
      <placeholder
        name="BackgroundImagePk"
        value="" />
      <placeholder
        name="BackgroundImageOverride"
        value="" />
      <control
        code="FLB"
        id="87ce4dd9-5b89-42d5-b1fa-ed48ced64eb6"
        left="0"
        top="0"
        width="29.5"
        height="2">
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="LabelText"
          value="CLEAR - TRANSHIPMENT"
          resid="9cdf5f3c-d686-450a-8a96-495f8501f89f" />
        <placeholder
          name="LabelType"
          value="Notification" />
        <placeholder
          name="LabelNotificationType"
          value="Information" />
        <placeholder
          name="ForegroundThemeColor"
          value="GeneralText" />
        <placeholder
          name="TextSize"
          value="ExtraExtraLarge" />
        <placeholder
          name="FontWeight"
          value="Normal" />
        <placeholder
          name="LabelAlignment"
          value="Center" />
        <placeholder
          name="BackgroundColor"
          value="" />
        <placeholder
          name="Padding"
          value="None" />
      </control>
      <control
        code="LBL"
        id="4098b383-b286-4ba9-8011-9d092a089d78"
        left="0"
        top="2"
        width="29.5"
        height="1.6"
        binding="CurrentItem.Consignment.DetailedImportStatusWithBrackets">
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="LabelType"
          value="Notification" />
        <placeholder
          name="LabelNotificationType"
          value="Information" />
        <placeholder
          name="ForegroundThemeColor"
          value="GeneralText" />
        <placeholder
          name="TextSize"
          value="Large" />
        <placeholder
          name="FontWeight"
          value="Normal" />
        <placeholder
          name="LabelAlignment"
          value="Center" />
        <placeholder
          name="BackgroundColor"
          value="" />
        <placeholder
          name="Padding"
          value="None" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="LBL"
        id="19aa75b7-776a-43a2-960c-8539d948c9d9"
        left="0"
        top="4"
        width="29.5"
        height="3"
        binding="CurrentItem.ReturnLastMileCarrierAndBarcode">
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="LabelType"
          value="Notification" />
        <placeholder
          name="LabelNotificationType"
          value="Information" />
        <placeholder
          name="ForegroundThemeColor"
          value="GeneralText" />
        <placeholder
          name="TextSize"
          value="ExtraLarge" />
        <placeholder
          name="FontWeight"
          value="Normal" />
        <placeholder
          name="LabelAlignment"
          value="Center" />
        <placeholder
          name="BackgroundColor"
          value="" />
        <placeholder
          name="Padding"
          value="None" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
    </control>
    <control
      code="GRP"
      id="97551f1c-403c-4159-8302-0bd2594ad519"
      top="1"
      width="30"
      height="8">
      <placeholder
        name="CanBeHidden"
        value="True" />
      <placeholder
        name="VisibilityCondition"
        value="90e26c01-3ceb-4646-a15a-01bf79131e92" />
      <placeholder
        name="Header"
        value="New Section"
        resid="54e20342-cb72-4458-be18-6babd3bc61be" />
      <placeholder
        name="HeaderAlignment"
        value="Left" />
      <placeholder
        name="HeaderDisplayMode"
        value="Hidden" />
      <placeholder
        name="BackgroundImagePk"
        value="" />
      <placeholder
        name="BackgroundImageOverride"
        value="" />
      <control
        code="LBL"
        id="470d0bf3-ab1b-4639-851d-b7eb196830fc"
        left="0"
        top="0"
        width="29.9"
        height="2"
        binding="CurrentItem.ReturnReleaseStatus">
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="LabelType"
          value="Notification" />
        <placeholder
          name="LabelNotificationType"
          value="Success" />
        <placeholder
          name="ForegroundThemeColor"
          value="GeneralText" />
        <placeholder
          name="TextSize"
          value="ExtraExtraLarge" />
        <placeholder
          name="FontWeight"
          value="Normal" />
        <placeholder
          name="LabelAlignment"
          value="Center" />
        <placeholder
          name="BackgroundColor"
          value="" />
        <placeholder
          name="Padding"
          value="None" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="LBL"
        id="917c9967-805a-4ebf-b106-f56d477d2560"
        left="0"
        top="2"
        width="29.9"
        height="1.6"
        binding="CurrentItem.Consignment.DetailedImportStatusWithBrackets">
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="LabelType"
          value="Notification" />
        <placeholder
          name="LabelNotificationType"
          value="Success" />
        <placeholder
          name="ForegroundThemeColor"
          value="GeneralText" />
        <placeholder
          name="TextSize"
          value="Large" />
        <placeholder
          name="FontWeight"
          value="Normal" />
        <placeholder
          name="LabelAlignment"
          value="Center" />
        <placeholder
          name="BackgroundColor"
          value="" />
        <placeholder
          name="Padding"
          value="None" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="LBL"
        id="6de25615-78f1-42b9-b4a6-94f0133dacb1"
        left="0"
        top="4"
        width="29.9"
        height="2.9"
        binding="CurrentItem.ReturnLastMileCarrierAndBarcode">
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="LabelType"
          value="Notification" />
        <placeholder
          name="LabelNotificationType"
          value="Success" />
        <placeholder
          name="ForegroundThemeColor"
          value="GeneralText" />
        <placeholder
          name="TextSize"
          value="ExtraLarge" />
        <placeholder
          name="FontWeight"
          value="Normal" />
        <placeholder
          name="LabelAlignment"
          value="Center" />
        <placeholder
          name="BackgroundColor"
          value="" />
        <placeholder
          name="Padding"
          value="None" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
    </control>
    <control
      code="GRP"
      id="76cac658-9227-4aa4-b6aa-d3493212e8ef"
      top="1"
      width="29"
      height="8">
      <placeholder
        name="CanBeHidden"
        value="True" />
      <placeholder
        name="VisibilityCondition"
        value="02d17d71-e70a-4507-82ff-eb6570c12862" />
      <placeholder
        name="Header"
        value="New Section"
        resid="973b1b1d-40bd-4c19-9a74-e461b0f81ebb" />
      <placeholder
        name="HeaderAlignment"
        value="Left" />
      <placeholder
        name="HeaderDisplayMode"
        value="Hidden" />
      <placeholder
        name="BackgroundImagePk"
        value="" />
      <placeholder
        name="BackgroundImageOverride"
        value="" />
      <control
        code="FLB"
        id="c858f86c-e4af-49e2-b7d9-30d219a7ad9e"
        left="0"
        top="0"
        width="29"
        height="2">
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="LabelText"
          value="INTERCEPTED"
          resid="32212a5b-fc31-46c4-922b-b7ef0f926e9e" />
        <placeholder
          name="LabelType"
          value="Notification" />
        <placeholder
          name="LabelNotificationType"
          value="Error" />
        <placeholder
          name="ForegroundThemeColor"
          value="GeneralText" />
        <placeholder
          name="TextSize"
          value="ExtraExtraLarge" />
        <placeholder
          name="FontWeight"
          value="Normal" />
        <placeholder
          name="LabelAlignment"
          value="Center" />
        <placeholder
          name="BackgroundColor"
          value="" />
        <placeholder
          name="Padding"
          value="None" />
      </control>
      <control
        code="LBL"
        id="5b9482fb-e7de-4094-8f60-3758c0e56740"
        left="0"
        top="2"
        width="29"
        height="1.6"
        binding="CurrentItem.Consignment.DetailedImportStatusWithBrackets">
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="LabelType"
          value="Notification" />
        <placeholder
          name="LabelNotificationType"
          value="Error" />
        <placeholder
          name="ForegroundThemeColor"
          value="GeneralText" />
        <placeholder
          name="TextSize"
          value="Large" />
        <placeholder
          name="FontWeight"
          value="Normal" />
        <placeholder
          name="LabelAlignment"
          value="Center" />
        <placeholder
          name="BackgroundColor"
          value="" />
        <placeholder
          name="Padding"
          value="None" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="LBL"
        id="10db484c-e4a1-4217-b505-74a700393351"
        left="0"
        top="4"
        width="29"
        height="2.9"
        binding="CurrentItem.HVI_ItemId">
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="LabelType"
          value="Notification" />
        <placeholder
          name="LabelNotificationType"
          value="Error" />
        <placeholder
          name="ForegroundThemeColor"
          value="GeneralText" />
        <placeholder
          name="TextSize"
          value="ExtraExtraLarge" />
        <placeholder
          name="FontWeight"
          value="Normal" />
        <placeholder
          name="LabelAlignment"
          value="Center" />
        <placeholder
          name="BackgroundColor"
          value="" />
        <placeholder
          name="Padding"
          value="None" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
    </control>
    <control
      code="GRP"
      id="1081ccc9-aa6a-48ba-8558-4a7c30fdb4a9"
      top="1"
      width="30"
      height="8">
      <placeholder
        name="CanBeHidden"
        value="True" />
      <placeholder
        name="VisibilityCondition"
        value="59d0928e-122f-43c6-8f74-d4738049103e" />
      <placeholder
        name="Header"
        value="New Section"
        resid="e91cb084-bce1-44eb-a28a-63285c680d4e" />
      <placeholder
        name="HeaderAlignment"
        value="Left" />
      <placeholder
        name="HeaderDisplayMode"
        value="Hidden" />
      <placeholder
        name="BackgroundImagePk"
        value="" />
      <placeholder
        name="BackgroundImageOverride"
        value="" />
      <control
        code="FLB"
        id="1e4c311b-ce7d-48f3-9e35-4a9188dc7573"
        left="0"
        top="0"
        width="30"
        height="3">
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="LabelText"
          value="HELD - NO STATUS AVAILABLE"
          resid="e9c40319-3f07-414f-a1a4-fb90a94691f9" />
        <placeholder
          name="LabelType"
          value="Notification" />
        <placeholder
          name="LabelNotificationType"
          value="Error" />
        <placeholder
          name="ForegroundThemeColor"
          value="GeneralText" />
        <placeholder
          name="TextSize"
          value="ExtraExtraLarge" />
        <placeholder
          name="FontWeight"
          value="Normal" />
        <placeholder
          name="LabelAlignment"
          value="Center" />
        <placeholder
          name="BackgroundColor"
          value="" />
        <placeholder
          name="Padding"
          value="None" />
      </control>
      <control
        code="LBL"
        id="f3958642-b857-4a74-9ca2-be58c187dbbc"
        left="0"
        top="4"
        width="30"
        height="3"
        binding="CurrentItem.HVI_ItemId">
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="LabelType"
          value="Notification" />
        <placeholder
          name="LabelNotificationType"
          value="Error" />
        <placeholder
          name="ForegroundThemeColor"
          value="GeneralText" />
        <placeholder
          name="TextSize"
          value="ExtraExtraLarge" />
        <placeholder
          name="FontWeight"
          value="Normal" />
        <placeholder
          name="LabelAlignment"
          value="Center" />
        <placeholder
          name="BackgroundColor"
          value="" />
        <placeholder
          name="Padding"
          value="None" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
    </control>
    <control
      code="GRP"
      id="1fd12222-4b51-45db-ba19-19297ea80b59"
      top="1"
      width="30"
      height="8">
      <placeholder
        name="CanBeHidden"
        value="True" />
      <placeholder
        name="VisibilityCondition"
        value="238a3018-1d23-48eb-b771-618f7ba1f2e7" />
      <placeholder
        name="Header"
        value="New Section"
        resid="b6a1bc51-968c-4b3f-baff-5e63854d9296" />
      <placeholder
        name="HeaderAlignment"
        value="Left" />
      <placeholder
        name="HeaderDisplayMode"
        value="Hidden" />
      <placeholder
        name="BackgroundImagePk"
        value="" />
      <placeholder
        name="BackgroundImageOverride"
        value="" />
      <control
        code="FLB"
        id="793bfa0b-75d4-4404-84e7-63157deb33c0"
        left="0"
        top="0"
        width="29.9"
        height="2">
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="LabelText"
          value="HELD - SURPLUS"
          resid="644a41f2-060a-44fa-a5f5-c3fb183396dd" />
        <placeholder
          name="LabelType"
          value="Notification" />
        <placeholder
          name="LabelNotificationType"
          value="Warning" />
        <placeholder
          name="ForegroundThemeColor"
          value="GeneralText" />
        <placeholder
          name="TextSize"
          value="ExtraExtraLarge" />
        <placeholder
          name="FontWeight"
          value="Normal" />
        <placeholder
          name="LabelAlignment"
          value="Center" />
        <placeholder
          name="BackgroundColor"
          value="" />
        <placeholder
          name="Padding"
          value="None" />
      </control>
      <control
        code="LBL"
        id="857bb779-77b3-4bea-8a74-7e0b9e5262ca"
        left="0"
        top="2"
        width="29.9"
        height="1.7"
        binding="CurrentItem.Consignment.DetailedImportStatusWithBrackets">
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="LabelType"
          value="Notification" />
        <placeholder
          name="LabelNotificationType"
          value="Warning" />
        <placeholder
          name="ForegroundThemeColor"
          value="GeneralText" />
        <placeholder
          name="TextSize"
          value="Large" />
        <placeholder
          name="FontWeight"
          value="Normal" />
        <placeholder
          name="LabelAlignment"
          value="Center" />
        <placeholder
          name="BackgroundColor"
          value="" />
        <placeholder
          name="Padding"
          value="None" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="LBL"
        id="9569a265-4ec7-4ba7-86fa-f69eed2b15a2"
        left="0"
        top="4"
        width="29.9"
        height="2.8"
        binding="CurrentSearchTerm">
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="LabelType"
          value="Notification" />
        <placeholder
          name="LabelNotificationType"
          value="Warning" />
        <placeholder
          name="ForegroundThemeColor"
          value="GeneralText" />
        <placeholder
          name="TextSize"
          value="ExtraLarge" />
        <placeholder
          name="FontWeight"
          value="Normal" />
        <placeholder
          name="LabelAlignment"
          value="Center" />
        <placeholder
          name="BackgroundColor"
          value="" />
        <placeholder
          name="Padding"
          value="None" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
    </control>
    <control
      code="GRP"
      id="ae336027-e42c-407d-a53a-ec6dcd980eed"
      top="1"
      width="30"
      height="8">
      <placeholder
        name="CanBeHidden"
        value="True" />
      <placeholder
        name="VisibilityCondition"
        value="e0e70e79-5005-48f3-9079-8a31dece438c" />
      <placeholder
        name="Header"
        value="New Section"
        resid="0f61546d-9cc5-4f94-a1d3-201edbc66093" />
      <placeholder
        name="HeaderAlignment"
        value="Left" />
      <placeholder
        name="HeaderDisplayMode"
        value="Hidden" />
      <placeholder
        name="BackgroundImagePk"
        value="" />
      <placeholder
        name="BackgroundImageOverride"
        value="" />
      <control
        code="LBL"
        id="20260deb-b1c3-464a-8c75-8a566037a068"
        left="0"
        top="0"
        width="30"
        height="2"
        binding="CurrentItem.ReturnReleaseStatus">
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="LabelType"
          value="Notification" />
        <placeholder
          name="LabelNotificationType"
          value="Error" />
        <placeholder
          name="ForegroundThemeColor"
          value="GeneralText" />
        <placeholder
          name="TextSize"
          value="ExtraExtraLarge" />
        <placeholder
          name="FontWeight"
          value="Normal" />
        <placeholder
          name="LabelAlignment"
          value="Center" />
        <placeholder
          name="BackgroundColor"
          value="" />
        <placeholder
          name="Padding"
          value="None" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="LBL"
        id="6a1e434f-9a60-4123-b8f0-baa3856d30fd"
        left="0"
        top="2"
        width="30"
        height="1.7"
        binding="CurrentItem.Consignment.DetailedImportStatusWithBrackets">
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="LabelType"
          value="Notification" />
        <placeholder
          name="LabelNotificationType"
          value="Error" />
        <placeholder
          name="ForegroundThemeColor"
          value="GeneralText" />
        <placeholder
          name="TextSize"
          value="Large" />
        <placeholder
          name="FontWeight"
          value="Normal" />
        <placeholder
          name="LabelAlignment"
          value="Center" />
        <placeholder
          name="BackgroundColor"
          value="" />
        <placeholder
          name="Padding"
          value="None" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="LBL"
        id="d85907ac-2ea3-404b-a17e-5b23f0d37f61"
        left="0"
        top="4"
        width="30"
        height="3"
        binding="CurrentItem.HVI_ItemId">
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="LabelType"
          value="Notification" />
        <placeholder
          name="LabelNotificationType"
          value="Error" />
        <placeholder
          name="ForegroundThemeColor"
          value="GeneralText" />
        <placeholder
          name="TextSize"
          value="ExtraExtraLarge" />
        <placeholder
          name="FontWeight"
          value="Normal" />
        <placeholder
          name="LabelAlignment"
          value="Center" />
        <placeholder
          name="BackgroundColor"
          value="" />
        <placeholder
          name="Padding"
          value="None" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
    </control>
    <control
      code="GRP"
      id="f61f8880-a7c6-4b1c-b36b-af103a3f52db"
      left="12.6"
      top="9"
      height="1.5"
      right="12.4">
      <placeholder
        name="CanBeHidden"
        value="True" />
      <placeholder
        name="VisibilityCondition"
        value="44ff7cc7-f820-4165-9a87-30a0f2855cd5" />
      <placeholder
        name="Header"
        value="New Section"
        resid="9d222c97-14c2-44b0-a3e3-640f5c9386c2" />
      <placeholder
        name="HeaderAlignment"
        value="Left" />
      <placeholder
        name="HeaderDisplayMode"
        value="Hidden" />
      <placeholder
        name="BackgroundImagePk"
        value="" />
      <placeholder
        name="BackgroundImageOverride"
        value="" />
      <control
        code="TTX"
        id="215667da-b859-4b9b-9e2e-d6a5e950326a"
        left="0"
        top="0"
        height="1"
        right="0">
        <placeholder
          name="IsInitialFocus"
          value="True" />
        <placeholder
          name="CaptionOverride"
          value="Barcode"
          resid="f6fef38c-62f5-4a6a-8bb4-012141458064" />
      </control>
    </control>
  </form>
