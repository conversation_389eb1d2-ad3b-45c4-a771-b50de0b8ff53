#transformationVersion: 70.0
#
VZ_PK: cf821c8f3b114f41944f6083a8a97453
VZ_ConfigurationKey: cf821c8f-3b11-4f41-944f-6083a8a97453
VZ_FormID: CYP Edit Delivery Instruction
VZ_Caption:
  resKey: Caption|b6e78843-22ef-42e3-8b13-e24502975414
  text: Edit Delivery Instruction
VZ_FormFactor: DSK
VZ_EntityType: ICYDDeliveryHeader
VZ_FormType: FRM
VZ_RequiresLogin: true
VZ_MaterialDesign: true
VZ_Dependencies: <dependencies xmlns="http://cargowise.com/glow/2014/11/14/dependencies.xsd" />
VZ_FormData: >-
  <form
    id="cfb81db5-08b9-4808-bc9b-24b478c921e4" xmlns="http://wisetechglobal.com/glow/2017/05/26/form.xsd">
    <placeholder
      name="MinHeight"
      value="100%" />
    <control
      code="BOX"
      id="2bc8d54c-21bf-46af-bff6-319ab7271a5f"
      binding="">
      <placeholder
        name="Layout"
        value="grid" />
      <placeholder
        name="MinHeight"
        value="100%" />
      <control
        code="PNL"
        id="fcc14252-8af0-44da-864d-5e06833239cc"
        binding="">
        <placeholder
          name="Columns"
          value="col-md-2 col-lg-2 col-xl-2" />
        <placeholder
          name="MinHeight"
          value="100%" />
        <placeholder
          name="Layout"
          value="grid" />
        <control
          code="BOX"
          id="61c01b86-7e6a-4632-9977-c30da3ef6f12"
          binding="">
          <placeholder
            name="Layout"
            value="grid" />
          <placeholder
            name="MinHeight"
            value="100%" />
          <control
            code="LBL"
            id="8ef69d6d-0d0b-4be0-92a7-bdbf4bafb0de">
            <placeholder
              name="Caption"
              value="Basic Information"
              resid="3464fd24-4d03-4333-9897-2ac584791604" />
            <placeholder
              name="Typography"
              value="h6" />
            <placeholder
              name="Color"
              value="deep-purple darken-4" />
          </control>
          <control
            code="ADD"
            id="2336fad8-0df7-49b4-887e-93ddbc0cb7ff"
            binding="AddressTypeTRA.E2_OA_Address">
            <placeholder
              name="CaptionOverride"
              value="Transport Provider"
              resid="d032852c-bec9-4f75-b88a-e9b4b119c54a" />
          </control>
          <control
            code="SRC"
            id="2186ab17-4d27-4a71-9e9b-55ee38276bc7"
            binding="YDH_WW_Yard">
            <placeholder
              name="IsReadOnly"
              value="True" />
          </control>
        </control>
      </control>
      <control
        code="PNL"
        id="a162e390-90b2-4eea-b1a9-f12cbe92f2c2"
        binding="">
        <placeholder
          name="Columns"
          value="col-md-10 col-lg-10 col-xl-10" />
        <placeholder
          name="MinHeight"
          value="100%" />
        <control
          code="LBL"
          id="38d20209-cb4e-4360-acee-24ba21039d05"
          binding="">
          <placeholder
            name="Caption"
            value="Delivery Instruction Details"
            resid="9e09bda5-f6a3-45a5-85d6-882ea451280b" />
          <placeholder
            name="Typography"
            value="h5" />
          <placeholder
            name="Margin"
            value="mb-5" />
        </control>
        <control
          code="RDT"
          id="48c94262-633e-49bd-b690-7f2ec807ea6c"
          binding="CYDDeliveries">
          <placeholder
            name="AllowAdd"
            value="True" />
          <placeholder
            name="CaptionType"
            value="none" />
          <placeholder
            name="InlineEdit"
            value="cell" />
          <placeholder
            name="FullPage"
            value="True" />
          <placeholder
            name="CaptionOverride"
            value="Delivery Instruction Details"
            resid="03484590-d98e-4384-ae15-3c9e0184d206" />
          <placeholder
            name="FieldConfiguration">
            <xml>
              <fields xmlns="">
                <field
                  path="YDL_TransportReference"
                  width="300"
                  mode="Mandatory" />
                <field
                  path="YDL_Quantity"
                  width="300"
                  mode="Mandatory" />
                <field
                  path="YDL_RC_ContainerType"
                  width="300"
                  mode="Mandatory" />
                <field
                  path="YDL_Type"
                  width="300"
                  mode="Mandatory" />
                <field
                  path="IISOType"
                  width="300"
                  mode="Mandatory" />
                <field
                  path="YDL_YRL_ReceiveAdviceLine"
                  width="300"
                  mode="Mandatory" />
                <field
                  path="Size"
                  width="300"
                  mode="Mandatory" />
                <field
                  path="EditableUnitNumber"
                  width="300"
                  mode="Default" />
                <field
                  path="Client"
                  width="300"
                  mode="Mandatory" />
                <field
                  path="Lessee"
                  width="300"
                  mode="Mandatory" />
                <field
                  path="YDL_IsEmpty"
                  width="300"
                  mode="Default" />
                <field
                  path="YDL_ManufactureDate"
                  width="300"
                  mode="Default" />
                <field
                  path="TransportationUnitId"
                  width="300"
                  mode="Optional"
                  isVisible="true" />
                <field
                  path="ReceiveAdviceLine.ReceiveAdvice.YRA_AcceptanceNumber"
                  width="250"
                  mode="Mandatory"
                  readOnly="true" />
                <field
                  path="ReceiveAdviceLine.ReceiveAdvice.YRA_JobNumber"
                  width="250"
                  mode="Mandatory" />
                <field
                  path="VehicleRegistrationNo"
                  width="250"
                  mode="Optional" />
                <field
                  path="LinkedReceiveAdviceFromDate"
                  width="250"
                  mode="Optional" />
                <field
                  path="LinkedReceiveAdviceToDate"
                  width="250"
                  mode="Optional" />
                <field
                  path="ReceiveAdviceLine.ReceiveAdvice.AddressBKD.Address.OrgHeader.OH_FullName"
                  width="250"
                  mode="Mandatory"
                  isVisible="false" />
                <field
                  path="ReceiveAdviceLine.ReceiveAdvice.AddressSCP.Address.OrgHeader.OH_FullName"
                  width="250"
                  mode="Mandatory"
                  isVisible="false" />
                <field
                  path="UnloadDate"
                  width="250"
                  mode="Default" />
                <field
                  path="GrossWeight"
                  width="90"
                  mode="Optional" />
                <field
                  path="TareWeight"
                  width="90"
                  mode="Optional" />
                <field
                  path="UnitOfWeight"
                  width="90"
                  mode="Optional" />
              </fields>
            </xml>
          </placeholder>
          <placeholder
            name="ShowFilters"
            value="True" />
          <placeholder
            name="ShowCustomize"
            value="True" />
          <placeholder
            name="ShowGrouping"
            value="True" />
        </control>
      </control>
    </control>
  </form>
