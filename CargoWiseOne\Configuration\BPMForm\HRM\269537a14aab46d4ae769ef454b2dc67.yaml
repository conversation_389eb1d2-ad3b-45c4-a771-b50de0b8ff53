#transformationVersion: 70.0
#
VZ_PK: 269537a14aab46d4ae769ef454b2dc67
VZ_ConfigurationKey: 269537a1-4aab-46d4-ae76-9ef454b2dc67
VZ_FormID: HRM - Onboarding - Workflow Tasks
VZ_Caption:
  resKey: VZ_Caption|269537a1-4aab-46d4-ae76-9ef454b2dc67
  text: Workflow Tasks
VZ_FormFactor: DSK
VZ_EntityType: IHROnBoarding
VZ_FormType: FRM
VZ_RequiresLogin: true
VZ_MaterialDesign: true
VZ_Dependencies: <dependencies xmlns="http://cargowise.com/glow/2014/11/14/dependencies.xsd" />
VZ_FormData: >-
  <form
    id="a3780422-e997-4bfd-9946-d954e35cb1cf" xmlns="http://wisetechglobal.com/glow/2017/05/26/form.xsd">
    <control
      code="SDT"
      id="e3b60576-d66b-4cba-949a-26a8d09ea1ba"
      binding="">
      <placeholder
        name="EntityType"
        value="IWorkflowTask[[IHROnBoarding]]" />
      <placeholder
        name="DefaultFilter">
        <xml>
          <ArrayOfFilterGroup xmlns:a="http://schemas.microsoft.com/2003/10/Serialization/Arrays" xmlns:i="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://cargowise.com/glow/2014/07/16/filters.xsd">
            <FilterGroup>
              <Filters>
                <Filter>
                  <FilterType>SimpleLookupFilter</FilterType>
                  <Operation>Is</Operation>
                  <PropertyPath>P9_ParentID</PropertyPath>
                  <Values>
                    <a:string>&lt;HOB_PK&gt;</a:string>
                  </Values>
                </Filter>
              </Filters>
              <IsImplicit>true</IsImplicit>
            </FilterGroup>
          </ArrayOfFilterGroup>
        </xml>
      </placeholder>
      <placeholder
        name="FieldConfiguration">
        <xml>
          <fields xmlns="">
            <field
              path="P9_Sequence"
              width="80"
              mode="Default" />
            <field
              path="P9_FH_ProcessHeader"
              width="250"
              mode="Default" />
            <field
              path="P9_Description"
              width="250"
              mode="Default" />
            <field
              path="P9_EstDuration"
              width="120"
              mode="Default" />
            <field
              path="P9_ActualDate"
              width="180"
              mode="Default" />
            <field
              path="P9_ActualDuration"
              width="150"
              mode="Default" />
            <field
              path="P9_Type"
              width="40"
              mode="Default" />
            <field
              path="P9_GS_NKAssignedStaffMember"
              width="210"
              mode="Default" />
            <field
              path="P9_CompletedTimeUtc"
              width="180"
              mode="Default" />
            <field
              path="P9_Status"
              width="60"
              mode="Default" />
          </fields>
        </xml>
      </placeholder>
      <placeholder
        name="ShowSelect"
        value="False" />
      <placeholder
        name="HideFilters"
        value="true" />
      <placeholder
        name="ShowItemActions"
        value="True" />
      <placeholder
        name="ShowAutoRefresh"
        value="True" />
      <placeholder
        name="ShowCustomize"
        value="True" />
      <placeholder
        name="ShowAddActions"
        value="True" />
    </control>
  </form>
