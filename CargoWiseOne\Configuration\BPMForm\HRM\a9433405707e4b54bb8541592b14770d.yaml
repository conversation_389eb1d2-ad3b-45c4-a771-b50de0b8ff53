#transformationVersion: 70.0
#
VZ_PK: a9433405707e4b54bb8541592b14770d
VZ_ConfigurationKey: a9433405-707e-4b54-bb85-41592b14770d
VZ_FormID: HRM - Remuneration - Exchange Rates
VZ_Caption:
  resKey: VZ_Caption|a9433405707e4b54bb8541592b14770d
  text: Currency Exchange Rates
VZ_FormFactor: DSK
VZ_EntityType: IReviewProcessNode
VZ_FormType: FRM
VZ_RequiresLogin: true
VZ_MaterialDesign: true
VZ_Dependencies: <dependencies xmlns="http://cargowise.com/glow/2014/11/14/dependencies.xsd" />
VZ_FormData: >-
  <form
    id="1a11ed95-6edd-47be-ac30-c6090ba86b4b" xmlns="http://wisetechglobal.com/glow/2017/05/26/form.xsd">
    <control
      code="BOX"
      id="fc508fa4-f051-49f9-bf00-715d4dfd7607">
      <control
        code="PNL"
        id="232a3821-f9ca-4262-9db5-8e8ff7ca4f52"
        binding="">
        <placeholder
          name="Caption"
          value="Exchange Rates"
          resid="c3e0dc72-4857-42e4-b561-d113ef9aed5d" />
        <control
          code="DAE"
          id="5fda851c-83cf-47fe-89a0-091e78c1ad32"
          binding="ReviewProcess.RPR_ExchangeRateEffectiveDate">
          <placeholder
            name="CaptionOverride"
            value="Date of exchange rates used in this review"
            resid="cf929a17-24e7-4aca-904e-73cf2f873168" />
          <placeholder
            name="CaptionType"
            value="description" />
        </control>
      </control>
      <control
        code="SDT"
        id="70f610d9-66c7-4abe-a626-6b308c05ebc0"
        binding="">
        <placeholder
          name="EntityType"
          value="IRefExchangeRate" />
        <placeholder
          name="CaptionOverride"
          value="List of Exchange Rates"
          resid="b74ff687-32e2-481b-b8c9-05075df2e39f" />
        <placeholder
          name="Padding"
          value="pa-2" />
        <placeholder
          name="FieldConfiguration">
          <xml>
            <fields xmlns="">
              <field
                path="ExCurrency.RX_Code"
                width="300"
                mode="Default" />
              <field
                path="ExCurrency.RX_Desc"
                width="300"
                mode="Default" />
              <field
                path="RE_ExRateType"
                width="300"
                mode="Default" />
              <field
                path="RE_StartDate"
                width="300"
                mode="Default" />
              <field
                path="RE_ExpiryDate"
                width="300"
                mode="Default" />
              <field
                path="RE_SellRate"
                width="300"
                mode="Default" />
              <field
                path="GlbCompanyInfo.GC_Name"
                width="250"
                mode="Default" />
            </fields>
          </xml>
        </placeholder>
        <placeholder
          name="ShowToolbar"
          value="large" />
        <placeholder
          name="DefaultFilter">
          <xml>
            <ArrayOfFilterGroup xmlns:a="http://schemas.microsoft.com/2003/10/Serialization/Arrays" xmlns:i="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://cargowise.com/glow/2014/07/16/filters.xsd">
              <FilterGroup>
                <Filters>
                  <Filter>
                    <FilterType>DateTimeFilter</FilterType>
                    <Operation>GreaterThanOrEqualTo</Operation>
                    <PropertyPath>RE_ExpiryDate</PropertyPath>
                    <Values>
                      <a:string>&lt;ReviewProcess.RPR_ExchangeRateEffectiveDate&gt;</a:string>
                    </Values>
                  </Filter>
                  <Filter>
                    <FilterType>DateTimeFilter</FilterType>
                    <Operation>LessThanOrEqualTo</Operation>
                    <PropertyPath>RE_StartDate</PropertyPath>
                    <Values>
                      <a:string>&lt;ReviewProcess.RPR_ExchangeRateEffectiveDate&gt;</a:string>
                    </Values>
                  </Filter>
                  <Filter>
                    <FilterType>StringFilter</FilterType>
                    <Operation>Is</Operation>
                    <PropertyPath>GlbCompanyInfo.GC_Name</PropertyPath>
                    <Values>
                      <a:string>&lt;ReviewProcess.Company.GC_Name&gt;</a:string>
                    </Values>
                  </Filter>
                </Filters>
                <IsImplicit>true</IsImplicit>
              </FilterGroup>
            </ArrayOfFilterGroup>
          </xml>
        </placeholder>
      </control>
    </control>
  </form>
