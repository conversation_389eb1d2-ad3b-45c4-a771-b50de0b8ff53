#transformationVersion: 70.0
#
VZ_PK: 4818c89bfdb14223931d23cb00ecd780
VZ_ConfigurationKey: 4818c89b-fdb1-4223-931d-23cb00ecd780
VZ_FormID: HRM - My Leave Entitlement Applications
VZ_Caption:
  resKey: VZ_Caption|4818c89bfdb14223931d23cb00ecd780
  text: My Leave Entitlement Applications
VZ_FormFactor: DSK
VZ_FormType: PAG
VZ_RequiresLogin: true
VZ_MaterialDesign: true
VZ_Dependencies: <dependencies xmlns="http://cargowise.com/glow/2014/11/14/dependencies.xsd" />
VZ_FormData: >-
  <form
    id="48b2a014-bbd8-4683-8ab9-ede5c290f81e" xmlns="http://wisetechglobal.com/glow/2017/05/26/form.xsd">
    <control
      code="SDT"
      id="18bb0bd0-bf1e-4f2b-8c0d-f7c8d82c354f"
      binding="">
      <placeholder
        name="EntityType"
        value="IHrlBenefitOnDemand" />
      <placeholder
        name="DefaultFilter">
        <xml>
          <ArrayOfFilterGroup xmlns:a="http://schemas.microsoft.com/2003/10/Serialization/Arrays" xmlns:i="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://cargowise.com/glow/2014/07/16/filters.xsd">
            <FilterGroup>
              <Filters>
                <Filter>
                  <FilterType>AdvancedGuidLookupFilter</FilterType>
                  <Operation>Is</Operation>
                  <PropertyPath>LBD_GS_Staff</PropertyPath>
                  <Values />
                </Filter>
              </Filters>
              <IsImplicit>true</IsImplicit>
            </FilterGroup>
          </ArrayOfFilterGroup>
        </xml>
      </placeholder>
      <placeholder
        name="CaptionOverride"
        value="My Leave Entitlement Applications"
        resid="f2c3e06f-ef12-4e4b-9502-afd415f2b4b9" />
      <placeholder
        name="FullPage"
        value="True" />
      <placeholder
        name="DefaultSortFields">
        <xml>
          <ArrayOfFieldSortDefinition xmlns:i="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://schemas.datacontract.org/2004/07/CargoWise.Glow.Infrastructure.Interfaces">
            <FieldSortDefinition>
              <FieldName>LBD_EffectiveDate</FieldName>
              <IsAscending>false</IsAscending>
            </FieldSortDefinition>
          </ArrayOfFieldSortDefinition>
        </xml>
      </placeholder>
      <placeholder
        name="DisabledGridRowActions"
        value="Remove" />
      <placeholder
        name="HideDefaultFooter"
        value="False" />
    </control>
  </form>
