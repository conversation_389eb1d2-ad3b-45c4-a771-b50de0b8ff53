#transformationVersion: 70.0
#
VZ_PK: 8730567f39a84313a1a940301a756cbe
VZ_ConfigurationKey: 8730567f-39a8-4313-a1a9-40301a756cbe
VZ_FormID: GDM - All Movements
VZ_Caption:
  resKey: VZ_Caption|8730567f39a84313a1a940301a756cbe
  text: All movements
VZ_FormFactor: DSK
VZ_FormType: PAG
VZ_RequiresLogin: true
VZ_MaterialDesign: true
VZ_Dependencies: <dependencies xmlns="http://cargowise.com/glow/2014/11/14/dependencies.xsd" />
VZ_FormData: >-
  <form
    id="1f261d7e-99c3-44ba-bb02-68065dce9b69" xmlns="http://wisetechglobal.com/glow/2017/05/26/form.xsd">
    <placeholder
      name="Layout"
      value="fill" />
    <control
      code="SDT"
      id="d20854e3-d125-40f0-9d5d-ea05ab3639bf"
      binding="">
      <placeholder
        name="CaptionOverride"
        value="All movements"
        resid="ac00185e-8fe3-4b7d-aedf-9c8dca112080" />
      <placeholder
        name="FullPage"
        value="True" />
      <placeholder
        name="DisabledGridRowActions"
        value="Edit GateIn,Remove,Notes,Documents," />
      <placeholder
        name="EntityType"
        value="IGteVehicleEntry" />
      <placeholder
        name="AutoRefresh"
        value="fiveMinutes" />
      <placeholder
        name="ShowAutoRefresh"
        value="True" />
      <placeholder
        name="DefaultFilter">
        <xml>
          <ArrayOfFilterGroup xmlns:a="http://schemas.microsoft.com/2003/10/Serialization/Arrays" xmlns:i="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://cargowise.com/glow/2014/07/16/filters.xsd">
            <FilterGroup>
              <Filters>
                <Filter>
                  <FilterType>DateTimeOffsetFilter</FilterType>
                  <Operation>HasDate</Operation>
                  <PropertyPath>GVE_EntryTime</PropertyPath>
                  <Values />
                </Filter>
              </Filters>
              <IsImplicit>true</IsImplicit>
            </FilterGroup>
          </ArrayOfFilterGroup>
        </xml>
      </placeholder>
    </control>
  </form>
