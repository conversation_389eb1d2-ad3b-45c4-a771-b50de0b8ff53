#transformationVersion: 70.0
#
VZ_PK: 133a4bc2dd184e9fb2f58ca2cf187320
VZ_ConfigurationKey: 133a4bc2-dd18-4e9f-b2f5-8ca2cf187320
VZ_FormID: HRM - Bulk Import - Performance Reviews
VZ_Caption:
  resKey: VZ_Caption|133a4bc2-dd18-4e9f-b2f5-8ca2cf187320
  text: Bulk Import/Export - Performance Reviews
VZ_FormFactor: DSK
VZ_FormType: PAG
VZ_RequiresLogin: true
VZ_MaterialDesign: true
VZ_Dependencies: <dependencies xmlns="http://cargowise.com/glow/2014/11/14/dependencies.xsd" />
VZ_FormData: >-
  <form
    id="52ea24de-8b0d-4f2c-942b-74145d54eb9a" xmlns="http://wisetechglobal.com/glow/2017/05/26/form.xsd">
    <placeholder
      name="Layout"
      value="fill" />
    <control
      code="SDT"
      id="aa2e2f35-8c2d-4fdb-87bf-fe762bbf0036"
      binding="">
      <placeholder
        name="FullPage"
        value="True" />
      <placeholder
        name="CaptionOverride"
        value="Performance Reviews"
        resid="5adebf38-d2e6-4bcf-8314-dcb3d0358bee" />
      <placeholder
        name="EntityType"
        value="IGlbStaffReview" />
      <placeholder
        name="SearchMode"
        value="Entity" />
      <placeholder
        name="DefaultFilter">
        <xml>
          <ArrayOfFilterGroup xmlns:a="http://schemas.microsoft.com/2003/10/Serialization/Arrays" xmlns:i="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://cargowise.com/glow/2014/07/16/filters.xsd">
            <FilterGroup>
              <Filters>
                <Filter>
                  <FilterType>IsActiveFilter</FilterType>
                  <Operation>Is</Operation>
                  <PropertyPath>Staff.GS_IsActive</PropertyPath>
                  <Values>
                    <a:string>true</a:string>
                  </Values>
                </Filter>
                <Filter>
                  <FilterType>AdvancedGuidLookupFilter</FilterType>
                  <Operation>Is</Operation>
                  <PropertyPath>GSV_GS_Staff</PropertyPath>
                  <Values />
                </Filter>
                <Filter>
                  <FilterType>StringFilter</FilterType>
                  <Operation>Contains</Operation>
                  <PropertyPath>Staff.StaffName</PropertyPath>
                  <Values />
                </Filter>
              </Filters>
              <IsImplicit>false</IsImplicit>
            </FilterGroup>
          </ArrayOfFilterGroup>
        </xml>
      </placeholder>
      <placeholder
        name="DefaultSortFields">
        <xml>
          <ArrayOfFieldSortDefinition xmlns:i="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://schemas.datacontract.org/2004/07/CargoWise.Glow.Infrastructure.Interfaces">
            <FieldSortDefinition>
              <FieldName>Staff.GS_FullName</FieldName>
              <IsAscending>true</IsAscending>
            </FieldSortDefinition>
          </ArrayOfFieldSortDefinition>
        </xml>
      </placeholder>
      <placeholder
        name="FieldConfiguration">
        <xml>
          <fields xmlns="">
            <field
              path="EffectiveDate"
              width="140"
              mode="Default" />
            <field
              path="GSV_EffectiveDate"
              width="140"
              mode="FilterOnly" />
            <field
              path="GSV_GS_NKReviewer"
              width="220"
              mode="Optional" />
            <field
              path="Reviewer.GS_FullName"
              width="250"
              mode="Default" />
            <field
              path="PerformanceRating"
              width="300"
              mode="Default" />
            <field
              path="GSV_Comments"
              width="250"
              mode="Default" />
            <field
              path="Staff.GS_FullName"
              width="250"
              mode="Default" />
            <field
              path="Staff.GS_IsActive"
              width="100"
              mode="Default" />
            <field
              path="Staff.GS_Code"
              width="120"
              mode="Optional" />
            <field
              path="GSV_Score"
              width="80"
              mode="Optional" />
            <field
              path="Reviewer.GS_Code"
              width="160"
              mode="Optional" />
            <field
              path="Staff.GS_NextReviewDate"
              width="110"
              mode="Optional" />
            <field
              path="Staff.GS_FriendlyName"
              width="250"
              mode="Optional" />
            <field
              path="GSV_SystemCreateTimeUtc"
              width="250"
              mode="Optional" />
            <field
              path="GSV_SystemLastEditTimeUtc"
              width="250"
              mode="Optional" />
            <field
              path="GSV_SystemCreateUser"
              width="200"
              mode="FilterOnly" />
            <field
              path="GSV_SystemLastEditUser"
              width="200"
              mode="FilterOnly" />
            <field
              path="Staff.CurrentJobTitle"
              width="250"
              mode="Optional" />
            <field
              path="Staff.CurrentRole.GEH_JobFamily"
              width="250"
              mode="Optional" />
            <field
              path="Staff.GS_RN_NKCountryCode"
              width="150"
              mode="FilterOnly" />
            <field
              path="Staff.GS_EmploymentDate"
              width="130"
              mode="Optional" />
            <field
              path="Staff.GS_EmailAddress"
              width="250"
              mode="Optional" />
            <field
              path="Staff.GS_GB_HomeBranch"
              width="250"
              mode="FilterOnly" />
            <field
              path="Staff.GS_GE_HomeDepartment"
              width="250"
              mode="FilterOnly" />
            <field
              path="GSV_AutoEffectiveEndDate"
              width="140"
              mode="Optional" />
            <field
              path="CreatedByStaff.GS_Code"
              width="120"
              mode="Optional" />
            <field
              path="CreatedByStaff.GS_FullName"
              width="250"
              mode="Optional" />
            <field
              path="LastEditedByStaff.GS_Code"
              width="250"
              mode="Optional" />
            <field
              path="LastEditedByStaff.GS_FullName"
              width="250"
              mode="Optional" />
            <field
              path="Staff.Country.RN_Desc"
              width="250"
              mode="Optional" />
            <field
              path="Staff.HomeBranch.GB_BranchName"
              width="250"
              mode="Optional" />
            <field
              path="Staff.HomeBranch.GB_Code"
              width="120"
              mode="Optional" />
            <field
              path="Staff.HomeBranch.GlbCompany.GC_Code"
              width="120"
              mode="Optional" />
            <field
              path="Staff.HomeBranch.GlbCompany.GC_Name"
              width="250"
              mode="Optional" />
            <field
              path="Staff.HomeDepartment.GE_Desc"
              width="250"
              mode="Optional" />
            <field
              path="Staff.HomeDepartment.GE_Code"
              width="120"
              mode="Optional" />
            <field
              path="Staff.CurrentEmployingEntity.Branch.GB_BranchName"
              width="250"
              mode="Optional" />
            <field
              path="Staff.CurrentEmployingEntity.Branch.GB_Code"
              width="120"
              mode="Optional" />
            <field
              path="Staff.CurrentEmployingEntity.Branch.GlbCompany.GC_Code"
              width="120"
              mode="Optional" />
            <field
              path="Staff.CurrentEmployingEntity.Branch.GlbCompany.GC_Name"
              width="250"
              mode="Optional" />
            <field
              path="Staff.CurrentBeneficiaryEntity.GBB_GB_Branch"
              width="250"
              mode="Optional" />
            <field
              path="Staff.CurrentBeneficiaryEntity.Branch.GB_BranchName"
              width="250"
              mode="Optional" />
            <field
              path="Staff.CurrentBeneficiaryEntity.Branch.GB_Code"
              width="120"
              mode="Optional" />
            <field
              path="Staff.CurrentBeneficiaryEntity.Branch.GlbCompany.GC_Code"
              width="120"
              mode="Optional" />
            <field
              path="Staff.CurrentBeneficiaryEntity.Branch.GlbCompany.GC_Name"
              width="250"
              mode="Optional" />
            <field
              path="GSV_GS_Staff"
              width="250"
              mode="FilterOnly" />
            <field
              path="Staff.CurrentRemuneration.Country.RN_Desc"
              width="250"
              mode="Optional" />
            <field
              path="Staff.CurrentRemuneration.Country.RN_Code"
              width="250"
              mode="Optional" />
            <field
              path="Staff.CurrentRemuneration.Currency.RX_Code"
              width="250"
              mode="Optional" />
            <field
              path="Staff.CurrentRemuneration.Currency.RX_Desc"
              width="250"
              mode="Optional" />
            <field
              path="Staff.CurrentTeam.TeamNameDescription"
              width="250"
              mode="Optional" />
            <field
              path="Staff.GS_EmploymentBasis"
              width="250"
              mode="Optional" />
            <field
              path="Staff.StaffName"
              width="250"
              mode="FilterOnly" />
            <field
              path="IGlbStaffReview_Filter_CurrentOn"
              width="100"
              mode="FilterOnly" />
            <field
              path="IGlbStaffReview_Filter_CurrentOn_IgnoreTimezone"
              width="250"
              mode="FilterOnly" />
            <field
              path="Staff.CurrentWorkingBasis.WorkingBasisCode"
              width="250"
              mode="Optional" />
          </fields>
        </xml>
      </placeholder>
    </control>
  </form>
