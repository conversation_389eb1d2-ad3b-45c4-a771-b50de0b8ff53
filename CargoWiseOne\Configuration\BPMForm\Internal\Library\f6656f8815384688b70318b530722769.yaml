#transformationVersion: 70.0
#
VZ_PK: f6656f8815384688b70318b530722769
VZ_ConfigurationKey: f6656f88-1538-4688-b703-18b530722769
VZ_FormID: Field Configuration Page
VZ_Caption:
  resKey: VZ_Caption|f6656f8815384688b70318b530722769
  text: Field Configuration Page
VZ_FormFactor: DSK
VZ_FormType: PAG
VZ_RequiresLogin: true
VZ_MaterialDesign: true
VZ_Dependencies: <dependencies xmlns="http://cargowise.com/glow/2014/11/14/dependencies.xsd" />
VZ_FormData: >-
  <form
    id="bf55b78b-6ad4-4db1-ac32-61a68327699a" xmlns="http://wisetechglobal.com/glow/2017/05/26/form.xsd">
    <placeholder
      name="Height"
      value="100%" />
    <placeholder
      name="Layout"
      value="fill" />
    <placeholder
      name="FlexDirection"
      value="flex-column" />
    <control
      code="PNL"
      id="aa13b003-652b-486c-aa0a-9aacc8ca1fc7"
      binding="">
      <placeholder
        name="Margin"
        value="mb-3" />
      <placeholder
        name="Class"
        value="blue lighten-4 bg-blue-lighten-4" />
      <control
        code="BOX"
        id="7c25f33c-fbfa-42a7-9bd8-7b0faa0e52c5">
        <placeholder
          name="Layout"
          value="flex" />
        <control
          code="IBT"
          id="a272ad03-11ae-4a60-912f-31c734d5e1bf"
          binding="">
          <placeholder
            name="Icon"
            value="s-icon-documentation" />
          <placeholder
            name="Tooltip"
            value="Documentation"
            resid="4862af45-4bbb-46ad-b0f6-d38b04e6dafc" />
          <placeholder
            name="Hyperlink"
            value="https://wisetechglobal.sharepoint.com/sites/Content-as-Code/Shared%20Documents/GLOW-Content/_permalinks/PB-BR-FieldConfiguration.aspx" />
          <placeholder
            name="Target"
            value="_blank" />
        </control>
        <control
          code="IBT"
          id="8f4e0d80-10f3-4f3c-8a37-f3b8cf4aee32"
          binding="">
          <placeholder
            name="Icon"
            value="s-icon-pt-devtools" />
          <placeholder
            name="Margin"
            value="ml-2" />
          <placeholder
            name="Tooltip"
            value="YAML"
            resid="07f5972e-105c-4614-9181-0f0e73f5da71" />
          <placeholder
            name="Target"
            value="_blank" />
          <placeholder
            name="Hyperlink"
            value="https://github.com/WiseTechGlobal/Glow/blob/master/CargoWiseOne/Configuration/BPMForm/Internal/Library/f6656f8815384688b70318b530722769.yaml" />
        </control>
        <control
          code="IBT"
          id="e7435f30-d089-4b0d-bbb9-3dff90d8190c"
          binding="">
          <placeholder
            name="Icon"
            value="s-icon-settings" />
          <placeholder
            name="Tooltip"
            value="Platform Builder"
            resid="58ad00d8-0b78-4a8b-b252-e0084119d2b8" />
          <placeholder
            name="Margin"
            value="ml-2" />
          <placeholder
            name="Target"
            value="_blank" />
          <placeholder
            name="Hyperlink"
            value="platformbuilder:?target=BPMForm&amp;identifier=f6656f8815384688b70318b530722769" />
        </control>
        <control
          code="BOX"
          id="ab662a10-26ad-4bf7-b242-41e5fb1e50b1">
          <placeholder
            name="Margin"
            value="ml-3" />
          <placeholder
            name="Layout"
            value="flex" />
          <placeholder
            name="FlexDirection"
            value="flex-column" />
          <placeholder
            name="FlexJustify"
            value="justify-center" />
          <placeholder
            name="FlexAlign"
            value="align-center" />
          <control
            code="LBL"
            id="20fa8069-65fa-4690-b763-b92e9d9c5718"
            binding="">
            <placeholder
              name="Display"
              value="block" />
            <placeholder
              name="Caption"
              value="To have a hands on experience of the example within the Documentation, check out the Columns from the two Search Data Tables."
              resid="03831546-3911-434b-9e25-235e99ff8178" />
            <placeholder
              name="Typography"
              value="body-strong" />
          </control>
        </control>
      </control>
    </control>
    <control
      code="PNL"
      id="bf89367e-0e3e-4b63-b49a-1869ffe575bd"
      binding="">
      <placeholder
        name="Height"
        value="100%" />
      <placeholder
        name="Layout"
        value="fill" />
      <placeholder
        name="FillAvailable"
        value="True" />
      <control
        code="SDT"
        id="db531709-667d-47e2-aa34-65a12615a073"
        binding="">
        <placeholder
          name="EntityType"
          value="IDtbLandTransportConsignment" />
        <placeholder
          name="CaptionOverride"
          value="Data Table using Entity level Field Configuration"
          resid="f6a9dfe0-0630-4192-b6a3-66edfb2f6acd" />
        <placeholder
          name="HideFilters"
          value="True" />
        <placeholder
          name="ItemsPerPage"
          value="5" />
        <placeholder
          name="HideItemActions"
          value="True" />
        <placeholder
          name="ShowAddActions"
          value="True" />
      </control>
      <control
        code="DVR"
        id="38600784-2995-4317-870e-92599efb51fc">
        <placeholder
          name="Margin"
          value="mt-2" />
      </control>
      <control
        code="SDT"
        id="50675962-6374-4844-81dd-031e6d53abad">
        <placeholder
          name="Margin"
          value="mt-2" />
        <placeholder
          name="EntityType"
          value="IDtbLandTransportConsignment" />
        <placeholder
          name="HideFilters"
          value="True" />
        <placeholder
          name="ItemsPerPage"
          value="5" />
        <placeholder
          name="HideItemActions"
          value="True" />
        <placeholder
          name="CaptionOverride"
          value="Data Table using Control level Field Configuration"
          resid="5451a189-b98a-448c-b2ad-7b495c1956ca" />
        <placeholder
          name="FieldConfiguration">
          <xml>
            <fields xmlns="">
              <field
                path="LTC_JobID"
                width="200"
                mode="Mandatory" />
              <field
                path="LTC_Status"
                width="200"
                mode="Mandatory" />
              <field
                path="LTC_RS_NKServiceLevel"
                width="100"
                mode="Mandatory" />
              <field
                path="Branch.GB_Code"
                width="80"
                mode="Default" />
              <field
                path="LTC_ConnoteNumber"
                width="150"
                mode="Mandatory" />
              <field
                path="LTC_JobType"
                width="250"
                mode="Default" />
              <field
                path="PickupInstruction.SingleAction.LTA_RequiredFrom"
                width="120"
                mode="Default" />
              <field
                path="DeliveryInstruction.SingleAction.LTA_RequiredFrom"
                width="120"
                mode="Default" />
              <field
                path="PackageJob.PkgPackagesSumPackageQty"
                width="50"
                mode="Optional" />
              <field
                path="PackageJob.PkgPackagesSumWeight"
                width="50"
                mode="Optional" />
              <field
                path="PickupInstruction.SingleAction.LTA_ReferenceNumber"
                width="250"
                mode="Optional" />
              <field
                path="PickupInstruction.SingleAction.LTA_RequiredTo"
                width="120"
                mode="Optional" />
              <field
                path="PickupInstruction.SingleAction.LTA_ActualTime"
                width="120"
                mode="Optional" />
              <field
                path="DeliveryInstruction.SingleAction.LTA_EstimatedTime"
                width="120"
                mode="Optional" />
              <field
                path="DeliveryInstruction.SingleAction.LTA_ReferenceNumber"
                width="250"
                mode="Optional" />
              <field
                path="DeliveryInstruction.SingleAction.LTA_RequiredTo"
                width="120"
                mode="Optional" />
              <field
                path="DeliveryInstruction.SingleAction.LTA_ActualTime"
                width="120"
                mode="Optional" />
              <field
                path="PickupInstruction.SingleAction.LTA_EstimatedTime"
                width="120"
                mode="Optional" />
              <field
                path="LTC_IsActive"
                width="90"
                mode="Optional" />
              <field
                path="LTC_IsDirect"
                width="90"
                mode="Optional" />
              <field
                path="LTC_IsHeld"
                width="70"
                mode="Optional" />
              <field
                path="LTC_IsConfirmed"
                width="120"
                mode="Optional" />
              <field
                path="IsHazardous"
                width="70"
                mode="Optional" />
              <field
                path="RequiresRefrigeration"
                width="80"
                mode="Optional" />
              <field
                path="LTC_GoodsValue"
                width="200"
                mode="Optional" />
              <field
                path="LTC_InsuranceValue"
                width="200"
                mode="Optional" />
            </fields>
          </xml>
        </placeholder>
        <placeholder
          name="FitToHeight"
          value="True" />
      </control>
    </control>
  </form>
