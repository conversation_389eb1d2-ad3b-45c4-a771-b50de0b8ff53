#transformationVersion: 70.0
#
VZ_PK: 484c3d30f3f4495b8f02fd84e0df060d
VZ_ConfigurationKey: 484c3d30-f3f4-495b-8f02-fd84e0df060d
VZ_FormID: HRM - Leave - Manual Adjustment - View
VZ_Caption:
  resKey: VZ_Caption|484c3d30-f3f4-495b-8f02-fd84e0df060d
  text: View Manual Adjustment
VZ_FormFactor: DSK
VZ_EntityType: IHrlBalanceTransaction
VZ_FormType: FRM
VZ_RequiresLogin: true
VZ_MaterialDesign: true
VZ_Dependencies: <dependencies xmlns="http://cargowise.com/glow/2014/11/14/dependencies.xsd" />
VZ_FormData: >-
  <form
    id="a98d580b-4642-4b42-b50f-d5b16705a7df" xmlns="http://wisetechglobal.com/glow/2017/05/26/form.xsd">
    <placeholder
      name="Layout"
      value="flex" />
    <placeholder
      name="FlexDirection"
      value="flex-column" />
    <placeholder
      name="FlexAlign"
      value="align-center" />
    <placeholder
      name="FlexWrap"
      value="flex-wrap" />
    <placeholder
      name="MaxWidth"
      value="720" />
    <control
      code="BOX"
      id="dcedee58-8ac6-4b75-81fd-2410da64d324"
      binding="">
      <placeholder
        name="Margin"
        value="mb-1" />
      <placeholder
        name="Width"
        value="400" />
      <control
        code="CMB"
        id="7424ac51-1af2-47a2-baa5-8f062a7051af"
        binding="LLT_LeaveType">
        <placeholder
          name="Required"
          value="True" />
        <placeholder
          name="Margin"
          value="my-2" />
        <placeholder
          name="IsReadOnly"
          value="True" />
      </control>
    </control>
    <control
      code="BOX"
      id="b4b7990e-08d9-4b42-b72d-34291aa2dcc1"
      binding="">
      <placeholder
        name="Margin"
        value="mb-1" />
      <placeholder
        name="Width"
        value="400" />
      <control
        code="CMB"
        id="def95697-afec-45f5-bfef-a7733d393967"
        binding="LeaveComponent">
        <placeholder
          name="Columns"
          value="col-5" />
        <placeholder
          name="Margin"
          value="mb-2" />
        <placeholder
          name="IsReadOnly"
          value="True" />
      </control>
    </control>
    <control
      code="BOX"
      id="a415a390-66c4-46b4-8340-bc01e5d4a07f"
      binding="">
      <placeholder
        name="Margin"
        value="mb-1" />
      <placeholder
        name="Width"
        value="400" />
      <control
        code="NUM"
        id="7f30b10e-7fc9-488a-b127-3ef6a7a2bab0"
        binding="LastProcessedBalance">
        <placeholder
          name="Margin"
          value="mb-2" />
        <placeholder
          name="Columns"
          value="col-5" />
        <placeholder
          name="IsReadOnly"
          value="True" />
      </control>
    </control>
    <control
      code="BOX"
      id="5edd87bb-48c4-4dc1-8129-ab4a6d78e208"
      binding="">
      <placeholder
        name="Margin"
        value="mb-1" />
      <placeholder
        name="Width"
        value="400" />
      <control
        code="NUM"
        id="3d9b9219-643b-40f2-99c3-7fdd19978b94"
        binding="LLT_DeltaValueHours">
        <placeholder
          name="Margin"
          value="mb-2" />
        <placeholder
          name="Required"
          value="True" />
        <placeholder
          name="IsReadOnly"
          value="True" />
      </control>
    </control>
    <control
      code="BOX"
      id="14c14da1-cec7-43e5-84eb-2e314f9db47d"
      binding="">
      <placeholder
        name="Margin"
        value="mb-1" />
      <placeholder
        name="Width"
        value="400" />
      <control
        code="DTE"
        id="d05c7b32-74ec-4e4f-b32e-3390b8003dce"
        binding="LLT_Accrual">
        <placeholder
          name="CaptionOverride"
          value="Adjustment Effective Date"
          resid="3f09e0df-8ff1-4e25-936c-0e73392be4a1" />
        <placeholder
          name="IsReadOnly"
          value="True" />
        <placeholder
          name="Margin"
          value="mb-2" />
      </control>
    </control>
    <control
      code="BOX"
      id="162d779c-6491-4b57-a385-22b21f43a1fc"
      binding="">
      <placeholder
        name="Margin"
        value="mb-1" />
      <placeholder
        name="Width"
        value="400" />
      <placeholder
        name="Columns"
        value="col-5" />
      <control
        code="NUM"
        id="2cc0a7df-b0e3-4569-b52d-35a21dadb671"
        binding="BalanceAfterAdjustment">
        <placeholder
          name="Margin"
          value="mb-2" />
        <placeholder
          name="IsReadOnly"
          value="True" />
      </control>
    </control>
    <control
      code="BOX"
      id="a7a68433-4a6d-4a53-b126-bc74211e7499"
      binding="">
      <placeholder
        name="Margin"
        value="mb-4" />
      <placeholder
        name="Width"
        value="400" />
      <control
        code="TXT"
        id="9cb869ff-f678-4c4f-a715-67d4015dd373"
        binding="LLT_Comment">
        <placeholder
          name="Margin"
          value="mb-2" />
        <placeholder
          name="IsReadOnly"
          value="True" />
      </control>
    </control>
  </form>
