#transformationVersion: 70.0
#
VZ_PK: e94a0de9c4b743a280f89883cb4e8948
VZ_ConfigurationKey: e94a0de9-c4b7-43a2-80f8-9883cb4e8948
VZ_FormID: OPP - Base Search Screen
VZ_Caption:
  resKey: VZ_Caption|e94a0de9-c4b7-43a2-80f8-9883cb4e8948
  text: Opportunities
VZ_FormFactor: DSK
VZ_FormType: PAG
VZ_RequiresLogin: true
VZ_MaterialDesign: true
VZ_Dependencies: <dependencies xmlns="http://cargowise.com/glow/2014/11/14/dependencies.xsd" />
VZ_FormData: >-
  <form
    id="e85affea-ec7c-4fff-8bf7-d6d41e5e7ef5" xmlns="http://wisetechglobal.com/glow/2017/05/26/form.xsd">
    <placeholder
      name="Layout"
      value="fill" />
    <control
      code="SDT"
      id="747cba95-0ec0-4ede-948f-7acebb03ad9e"
      binding="">
      <placeholder
        name="FullPage"
        value="True" />
      <placeholder
        name="EntityType"
        value="ICrmOpportunity" />
      <placeholder
        name="FieldConfiguration">
        <xml>
          <fields xmlns="">
            <field
              path="COP_OpportunityID"
              width="160"
              mode="Mandatory" />
            <field
              path="COP_OpportunityName"
              width="300"
              mode="Mandatory" />
            <field
              path="COP_OH_Organization"
              width="300"
              mode="Default" />
            <field
              path="OverallStatus"
              width="300"
              mode="Default" />
            <field
              path="COP_CloseDate"
              width="300"
              mode="Default" />
            <field
              path="StatusWithInactive"
              width="300"
              mode="Default" />
            <field
              path="StageWithInactive"
              width="300"
              mode="Default" />
            <field
              path="COP_NextFollowUp"
              width="220"
              mode="Default" />
            <field
              path="COP_GS_NKSalesPerson"
              width="200"
              mode="Default" />
            <field
              path="SalesTypeWithInactive"
              width="300"
              mode="Default" />
            <field
              path="ProductTypeWithInactive"
              width="300"
              mode="Default" />
            <field
              path="COP_EstimatedProfit"
              width="220"
              mode="Optional" />
            <field
              path="COP_RX_NKOverallCurrency"
              width="220"
              mode="Optional" />
            <field
              path="COP_WinProbability"
              width="160"
              mode="Optional" />
            <field
              path="COP_EstimatedCloseDate"
              width="220"
              mode="Optional" />
            <field
              path="SourceTypeWithInactive"
              width="300"
              mode="Optional" />
            <field
              path="COP_SourceDetails"
              width="220"
              mode="Optional" />
            <field
              path="COP_OH_ReferringOrganization"
              width="300"
              mode="Optional" />
            <field
              path="COP_OC_ReferringContact"
              width="200"
              mode="Optional" />
            <field
              path="COP_SystemCreateTimeUtc"
              width="200"
              mode="Optional" />
            <field
              path="COP_SystemCreateUser"
              width="220"
              mode="Optional" />
            <field
              path="COP_SystemLastEditTimeUtc"
              width="220"
              mode="Default" />
            <field
              path="COP_SystemLastEditUser"
              width="220"
              mode="Default" />
            <field
              path="Contacts"
              width="250"
              mode="Optional" />
            <field
              path="PrimaryContact"
              width="250"
              mode="Optional" />
            <field
              path="CrmOpportunityContacts"
              width="250"
              mode="FilterOnly" />
            <field
              path="COP_IsRestricted"
              width="0"
              mode="FilterOnly"
              isFilterable="%.IsRestrictedOpportunitiesEnabled" />
            <field
              path="CrmOpportunityScopes"
              width="300"
              mode="FilterOnly" />
            <field
              path="COP_EffectiveStartDate"
              width="300"
              mode="Default" />
            <field
              path="COP_EffectiveEndDate"
              width="300"
              mode="Default" />
          </fields>
        </xml>
      </placeholder>
      <placeholder
        name="ShowAutoRefresh"
        value="True" />
      <placeholder
        name="ShowItemActions"
        value="True" />
      <placeholder
        name="ShowSelect"
        value="True" />
      <placeholder
        name="CaptionOverride"
        value="Opportunities"
        resid="2406657f-de2d-47e8-8221-a6a71a49a8e2" />
      <placeholder
        name="EditFormFlowConfiguration">
        <xml>
          <formFlows xmlns="">
            <formFlow>04b5641a43804baf99d7312d31dba3e2</formFlow>
          </formFlows>
        </xml>
      </placeholder>
      <placeholder
        name="DefaultSortFields">
        <xml>
          <ArrayOfFieldSortDefinition xmlns:i="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://schemas.datacontract.org/2004/07/CargoWise.Glow.Infrastructure.Interfaces">
            <FieldSortDefinition>
              <FieldName>COP_SystemLastEditTimeUtc</FieldName>
              <IsAscending>false</IsAscending>
            </FieldSortDefinition>
          </ArrayOfFieldSortDefinition>
        </xml>
      </placeholder>
      <placeholder
        name="ShowGrouping"
        value="True" />
      <placeholder
        name="DisabledGridRowActions"
        value="Documents,Workflow" />
      <placeholder
        name="ActionMenuItems">
        <xml>
          <formFlows xmlns="">
            <formFlow
              newSession="True">04b5641a43804baf99d7312d31dba3e2</formFlow>
            <formFlow
              newSession="True"
              inDialog="True">ada289c85c7b4f0c89795c30ca537345</formFlow>
            <formFlow
              newSession="True"
              inDialog="True">e43ee2c0407a48d8a30116ad5a511213</formFlow>
            <formFlow
              newSession="True"
              inDialog="True">880d4fe79c764ba7abadcfa2ef94a9b5</formFlow>
            <formFlow
              newSession="True"
              inDialog="True">da837afd379f48158be7812be2955227</formFlow>
          </formFlows>
        </xml>
      </placeholder>
    </control>
  </form>
