#transformationVersion: 70.0
#
VZ_PK: ada902dd66ec407993670ed0d8bc3013
VZ_ConfigurationKey: ada902dd-66ec-4079-9367-0ed0d8bc3013
VZ_FormID: HRM - Bulk Import - Employing Entities
VZ_Caption:
  resKey: VZ_Caption|ada902dd-66ec-4079-9367-0ed0d8bc3013
  text: Bulk Import/Export - Employing Entities
VZ_FormFactor: DSK
VZ_FormType: PAG
VZ_RequiresLogin: true
VZ_MaterialDesign: true
VZ_Dependencies: <dependencies xmlns="http://cargowise.com/glow/2014/11/14/dependencies.xsd" />
VZ_FormData: >-
  <form
    id="52ea24de-8b0d-4f2c-942b-74145d54eb9a" xmlns="http://wisetechglobal.com/glow/2017/05/26/form.xsd">
    <placeholder
      name="Layout"
      value="fill" />
    <control
      code="SDT"
      id="bcfa691c-e179-41e0-a1ce-6986d0929e01"
      binding="">
      <placeholder
        name="CaptionOverride"
        value="Employing Entities"
        resid="e54e286d-8c7b-4dd4-ba7c-453365cef846" />
      <placeholder
        name="EntityType"
        value="IGlbEmployingBranchDepartment" />
      <placeholder
        name="FullPage"
        value="True" />
      <placeholder
        name="SearchMode"
        value="Entity" />
      <placeholder
        name="DefaultFilter">
        <xml>
          <ArrayOfFilterGroup xmlns:a="http://schemas.microsoft.com/2003/10/Serialization/Arrays" xmlns:i="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://cargowise.com/glow/2014/07/16/filters.xsd">
            <FilterGroup>
              <Filters>
                <Filter>
                  <FilterType>IsActiveFilter</FilterType>
                  <Operation>Is</Operation>
                  <PropertyPath>Staff.GS_IsActive</PropertyPath>
                  <Values>
                    <a:string>true</a:string>
                  </Values>
                </Filter>
                <Filter>
                  <FilterType>AdvancedGuidLookupFilter</FilterType>
                  <Operation>Is</Operation>
                  <PropertyPath>GHB_GS_Staff</PropertyPath>
                  <Values />
                </Filter>
                <Filter>
                  <FilterType>StringFilter</FilterType>
                  <Operation>Contains</Operation>
                  <PropertyPath>Staff.StaffName</PropertyPath>
                  <Values />
                </Filter>
              </Filters>
              <IsImplicit>false</IsImplicit>
            </FilterGroup>
          </ArrayOfFilterGroup>
        </xml>
      </placeholder>
      <placeholder
        name="DefaultSortFields">
        <xml>
          <ArrayOfFieldSortDefinition xmlns:i="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://schemas.datacontract.org/2004/07/CargoWise.Glow.Infrastructure.Interfaces">
            <FieldSortDefinition>
              <FieldName>Staff.GS_FullName</FieldName>
              <IsAscending>true</IsAscending>
            </FieldSortDefinition>
          </ArrayOfFieldSortDefinition>
        </xml>
      </placeholder>
      <placeholder
        name="FieldConfiguration">
        <xml>
          <fields xmlns="">
            <field
              path="Staff.GS_Code"
              width="120"
              mode="Default" />
            <field
              path="Staff.GS_FullName"
              width="250"
              mode="Default" />
            <field
              path="EffectiveDate"
              width="120"
              mode="Default" />
            <field
              path="GHB_GB_Branch"
              width="300"
              mode="Optional" />
            <field
              path="Branch.GB_GC"
              width="300"
              mode="Optional" />
            <field
              path="GHB_GS_Staff"
              width="100"
              mode="FilterOnly" />
            <field
              path="GHB_SystemCreateTimeUtc"
              width="100"
              mode="Optional" />
            <field
              path="GHB_SystemCreateUser"
              width="100"
              mode="FilterOnly" />
            <field
              path="GHB_SystemLastEditTimeUtc"
              width="100"
              mode="Optional" />
            <field
              path="GHB_SystemLastEditUser"
              width="100"
              mode="FilterOnly" />
            <field
              path="GHB_EffectiveDate"
              width="120"
              mode="Optional" />
            <field
              path="GHB_AutoEffectiveEndDate"
              width="120"
              mode="Optional" />
            <field
              path="Branch.GB_BranchName"
              width="250"
              mode="Default" />
            <field
              path="Branch.GB_Code"
              width="120"
              mode="Optional" />
            <field
              path="Branch.GlbCompany.GC_Code"
              width="120"
              mode="Optional" />
            <field
              path="Branch.GlbCompany.GC_Name"
              width="250"
              mode="Default" />
            <field
              path="CreatedByStaff.GS_Code"
              width="120"
              mode="Optional" />
            <field
              path="CreatedByStaff.GS_FullName"
              width="250"
              mode="Optional" />
            <field
              path="LastEditedByStaff.GS_Code"
              width="120"
              mode="Optional" />
            <field
              path="LastEditedByStaff.GS_FullName"
              width="250"
              mode="Optional" />
            <field
              path="Staff.GS_EmploymentBasis"
              width="250"
              mode="Optional" />
            <field
              path="Staff.StaffName"
              width="250"
              mode="FilterOnly" />
            <field
              path="Staff.CurrentJobTitle"
              width="250"
              mode="Optional" />
            <field
              path="Staff.CurrentRole.GEH_JobFamily"
              width="250"
              mode="Optional" />
            <field
              path="Staff.CurrentTeam.TeamNameDescription"
              width="250"
              mode="Optional" />
            <field
              path="Staff.CurrentRemuneration.Country.RN_Desc"
              width="250"
              mode="Optional" />
            <field
              path="Staff.CurrentRemuneration.Currency.RX_Code"
              width="200"
              mode="Optional" />
            <field
              path="Staff.CurrentRemuneration.Currency.RX_Desc"
              width="250"
              mode="Optional" />
            <field
              path="Staff.CurrentRemuneration.Country.RN_Code"
              width="200"
              mode="Optional" />
            <field
              path="Staff.GS_IsActive"
              width="70"
              mode="FilterOnly" />
            <field
              path="Staff.CurrentBeneficiaryEntity.Branch.GB_BranchName"
              width="250"
              mode="Optional" />
            <field
              path="Staff.CurrentBeneficiaryEntity.Branch.GB_Code"
              width="250"
              mode="Optional" />
            <field
              path="Staff.CurrentBeneficiaryEntity.Branch.GlbCompany.GC_Code"
              width="250"
              mode="Optional" />
            <field
              path="Staff.CurrentBeneficiaryEntity.Branch.GlbCompany.GC_Name"
              width="250"
              mode="Optional" />
            <field
              path="IGlbEmployingBranchDepartment_Filter_CurrentOn"
              width="100"
              mode="FilterOnly" />
            <field
              path="IGlbEmployingBranchDepartment_Filter_CurrentOn_IgnoreTimezone"
              width="250"
              mode="FilterOnly" />
            <field
              path="Staff.CurrentWorkingBasis.WorkingBasisCode"
              width="250"
              mode="Optional" />
          </fields>
        </xml>
      </placeholder>
    </control>
  </form>
