#transformationVersion: 70.0
#
VZ_PK: b2bacdc99c7d4f27b486f45e626c1cd5
VZ_ConfigurationKey: b2bacdc9-9c7d-4f27-b486-f45e626c1cd5
VZ_FormID: HRM - Staff Search
VZ_Caption:
  resKey: VZ_Caption|b2bacdc9-9c7d-4f27-b486-f45e626c1cd5
  text: Staff Search
VZ_FormFactor: DSK
VZ_FormType: PAG
VZ_RequiresLogin: true
VZ_MaterialDesign: true
VZ_Dependencies: <dependencies xmlns="http://cargowise.com/glow/2014/11/14/dependencies.xsd" />
VZ_FormData: >-
  <form
    id="8571f58c-5b70-4316-b6dc-3d90f80ce68d" xmlns="http://wisetechglobal.com/glow/2017/05/26/form.xsd">
    <placeholder
      name="Layout"
      value="fill" />
    <control
      code="SDT"
      id="08012a3f-1055-4fa7-a004-92c5559016b3"
      binding="">
      <placeholder
        name="CaptionOverride"
        value="Staff Members"
        resid="9a4ae14d-de9b-4a02-ada7-15039a2e063b" />
      <placeholder
        name="EntityType"
        value="IGlbStaff" />
      <placeholder
        name="DefaultFilter">
        <xml>
          <ArrayOfFilterGroup xmlns:a="http://schemas.microsoft.com/2003/10/Serialization/Arrays" xmlns:i="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://cargowise.com/glow/2014/07/16/filters.xsd">
            <FilterGroup>
              <Filters>
                <Filter>
                  <FilterType>IsActiveFilter</FilterType>
                  <Operation>Is</Operation>
                  <PropertyPath>GS_IsActive</PropertyPath>
                  <Values>
                    <a:string>true</a:string>
                  </Values>
                </Filter>
                <Filter>
                  <FilterType>StringFilter</FilterType>
                  <Operation>Contains</Operation>
                  <PropertyPath>StaffNameOrCodeGeneral</PropertyPath>
                  <Values />
                </Filter>
                <Filter>
                  <FilterType>StringFilter</FilterType>
                  <Operation>Contains</Operation>
                  <PropertyPath>GS_FullName</PropertyPath>
                  <Values />
                </Filter>
                <Filter>
                  <FilterType>StringFilter</FilterType>
                  <Operation>StartsWith</Operation>
                  <PropertyPath>GS_Code</PropertyPath>
                  <Values />
                </Filter>
              </Filters>
              <IsImplicit>false</IsImplicit>
            </FilterGroup>
            <FilterGroup>
              <Filters>
                <Filter>
                  <FilterType>BoolFilter</FilterType>
                  <Operation>Is</Operation>
                  <PropertyPath>GS_IsSystemAccount</PropertyPath>
                  <Values>
                    <a:string>false</a:string>
                  </Values>
                </Filter>
                <Filter>
                  <FilterType>BoolFilter</FilterType>
                  <Operation>Is</Operation>
                  <PropertyPath>GS_IsResource</PropertyPath>
                  <Values>
                    <a:string>false</a:string>
                  </Values>
                </Filter>
              </Filters>
              <IsImplicit>true</IsImplicit>
            </FilterGroup>
          </ArrayOfFilterGroup>
        </xml>
      </placeholder>
      <placeholder
        name="FullPage"
        value="True" />
      <placeholder
        name="EditFormFlowConfiguration">
        <xml>
          <formFlows xmlns="">
            <formFlow
              newSession="True">ad909fa441b941bb86a0d0191cfcaa42</formFlow>
          </formFlows>
        </xml>
      </placeholder>
      <placeholder
        name="NewFormFlowConfiguration">
        <xml>
          <formFlows xmlns="">
            <formFlow>7472da9ea9d3411aa1499d3f93478c96</formFlow>
          </formFlows>
        </xml>
      </placeholder>
      <placeholder
        name="FitToHeight"
        value="False" />
      <placeholder
        name="FieldConfiguration"
        value="" />
    </control>
  </form>
