#transformationVersion: 70.0
#
VZ_PK: c90d78b15995478ca6370b0ba8a3bfbc
VZ_ConfigurationKey: c90d78b1-5995-478c-a637-0b0ba8a3bfbc
VZ_FormID: CYP Edit Ad Hoc Service Order
VZ_Caption:
  resKey: VZ_Caption|c90d78b15995478ca6370b0ba8a3bfbc
  text: Edit Ad Hoc Service Order
VZ_FormFactor: DSK
VZ_EntityType: ICYDAdHocServiceOrder
VZ_FormType: FRM
VZ_RequiresLogin: true
VZ_MaterialDesign: true
VZ_Dependencies: <dependencies xmlns="http://cargowise.com/glow/2014/11/14/dependencies.xsd" />
VZ_StatusBindingPath: OrderStatus
VZ_EntityStatusDisplayMode: ReadOnly
VZ_FormData: >-
  <form
    id="64eec4bc-be4b-42ed-8862-aa176917b32b" xmlns="http://wisetechglobal.com/glow/2017/05/26/form.xsd">
    <placeholder
      name="Layout"
      value="grid grid-fill-md" />
    <control
      code="PNL"
      id="09c77f37-1e1a-4cf8-bbe7-70f404b8a59d"
      binding="">
      <placeholder
        name="Columns"
        value="col-12 col-md-3" />
      <placeholder
        name="Layout"
        value="grid" />
      <placeholder
        name="Overflow"
        value="overflow-auto" />
      <placeholder
        name="FlexGrow"
        value="flex-md-grow-1" />
      <control
        code="BOX"
        id="f83cfc17-4d19-4a5b-8e70-fcf677142a60">
        <placeholder
          name="FlexDirection"
          value="flex-row-reverse" />
        <placeholder
          name="Columns"
          value="col-9" />
        <placeholder
          name="Padding"
          value="pb-7" />
        <control
          code="LBL"
          id="1342a1a1-1be9-44ae-999b-e92d007ffe79">
          <placeholder
            name="Caption"
            value="Service order details"
            resid="28873759-696a-4b74-804b-1b3f81829033" />
          <placeholder
            name="Typography"
            value="title-sm-default" />
          <placeholder
            name="Align"
            value="left" />
        </control>
      </control>
      <control
        code="BOX"
        id="b035e2ed-ceb2-4e22-a68f-abaa253c5ab4">
        <placeholder
          name="Layout"
          value="flex" />
        <placeholder
          name="FlexDirection"
          value="flex-row-reverse" />
        <placeholder
          name="Columns"
          value="col-3" />
        <control
          code="DIB"
          id="68ae71b9-1d23-4fc0-b7ed-a56e7bb946d7">
          <placeholder
            name="Icon"
            value="s-icon-menu-meatballs" />
            <placeholder
                name="ActionMenuItems">
                <xml>
                  <formFlows xmlns="">
                    <formFlow
                  newSession="False">container-yard-cypopen-ajsin-cw</formFlow>
                  </formFlows>
                </xml>
              </placeholder>
        </control>
      </control>
      <control
        code="ADD"
        id="4ad689a1-1093-4a0a-ba3f-fc4a2cad791a"
        binding="AddressBKD.E2_OA_Address">
        <placeholder
          name="CaptionOverride"
          value="Client"
          resid="69a1dbe6-208f-441e-b7a4-577111a3e609" />
        <placeholder
          name="Required"
          value="True" />
        <placeholder
          name="Columns"
          value="col-6" />
      </control>
      <control
        code="TXT"
        id="2e596159-a67d-4ceb-ad0a-f52b878a74b2"
        binding="YAO_ClientReference">
        <placeholder
          name="Columns"
          value="col-6" />
      </control>
      <control
        code="DAE"
        id="99c3c063-1a2b-43d5-b166-e62f29e79aaa"
        binding="YAO_BillingDate">
        <placeholder
          name="Required"
          value="True" />
        <placeholder
          name="CaptionOverride"
          value="Billing date"
          resid="658eef4f-097d-4bcd-8fcf-6d437451783c" />
        <placeholder
          name="Columns"
          value="col-6" />
      </control>
      <control
        code="DTE"
        id="c3e86696-dc43-40e0-8402-f1aa29f193e1"
        binding="YAO_SystemCreateTimeUtc">
        <placeholder
          name="CaptionOverride"
          value="Created date/time"
          resid="43cded0e-5151-4324-9fb7-49c8f8b34999" />
        <placeholder
          name="Columns"
          value="col-6" />
        <placeholder
          name="IsReadOnly"
          value="True" />
      </control>
      <control
        code="DTE"
        id="29f6d6f8-6e27-4e24-a3ed-487d8a8bad4b"
        binding="YAO_FinalizedTime">
        <placeholder
          name="Columns"
          value="col-6" />
        <placeholder
          name="IsReadOnly"
          value="True" />
      </control>
      <control
        code="TXT"
        id="cce1b457-9e09-4018-8498-df92d78fc228"
        binding="CreatedByStaff.GS_Code">
        <placeholder
          name="CaptionOverride"
          value="Create user code"
          resid="0c805770-969e-41be-875f-fc0f8df23e9e" />
        <placeholder
          name="Columns"
          value="col-6" />
        <placeholder
          name="IsReadOnly"
          value="True" />
      </control>
    </control>
    <control
      code="RDT"
      id="8f2a44c3-7a2f-4d33-9bae-10fc1c044251"
      binding="CYDAdHocServices">
      <placeholder
        name="Columns"
        value="col-12 col-md-9" />
      <placeholder
        name="FullPage"
        value="True" />
      <placeholder
        name="FieldConfiguration">
        <xml>
          <fields xmlns="">
            <field
              path="JobNumber"
              width="150"
              caption="Service order ID"
              resid="36c1b1e8-0812-4164-96b3-74ee60628610"
              mode="Default" />
            <field
              path="AutoCreatedJobService.ES_ServiceId"
              width="150"
              caption="Ad-hoc service ID"
              resid="6c7e9c44-6ead-4239-a8b5-0256b7bb7baf"
              mode="Default" />
            <field
              path="ServiceStatus"
              width="100"
              caption="Service status"
              resid="e6de2baa-9dab-4077-bbfb-f68c15c0584d"
              mode="Default" />
            <field
              path="ServiceType"
              width="150"
              caption="Service type"
              resid="e80b42cb-be42-45bd-8b4e-14a497970c49"
              mode="Default" />
            <field
              path="AutoCreatedJobService.ES_ServiceCount"
              width="150"
              mode="Default" />
            <field
              path="YAS_YUS_YardUnitState"
              width="150"
              caption="Unit number"
              resid="10cce277-c8db-4a1d-a6ab-f559c6557aaf"
              mode="Default" />
            <field
              path="YardUnitState.ReceiveTransportationUnitId"
              width="150"
              caption="Yard in TPU ID"
              resid="397a79d2-aac1-4fda-a648-d722ed94f3fd"
              mode="Default" />
            <field
              path="YardUnitState.TypeSize"
              width="150"
              caption="Type size"
              resid="27545435-9e1f-4d17-a454-a6ccb324dff5"
              mode="Default" />
            <field
              path="AutoCreatedJobService.ES_BookedDateTimeOffset"
              width="180"
              caption="Start date"
              resid="c02024a3-24f8-4a31-a18f-21ab709437a7"
              mode="Default" />
            <field
              path="AutoCreatedJobService.ES_CompletedDateTimeOffset"
              width="180"
              caption="Complete date"
              resid="21c9fe01-8ab0-4d72-96b6-1ca67d46ec07"
              mode="Default" />
            <field
              path="AutoCreatedJobService.ES_OH_Contractor"
              width="150"
              caption="Contractor"
              resid="3fea1f28-0ff0-456c-a20a-fa202834a3ac"
              mode="Default" />
            <field
              path="CompleteDate"
              width="300"
              caption="Complete date"
              resid="6d1ad0ad-4b81-4a4d-91be-53e1b70a43e5"
              mode="FilterOnly" />
            <field
              path="StartDate"
              width="300"
              caption="Start date"
              resid="a36ab22e-b6c3-4747-92a1-2e2ce8d787ef"
              mode="FilterOnly" />
            <field
              path="ServiceCount"
              width="300"
              caption="Service count"
              resid="a3351475-1bd7-4ef9-ba8c-4e65ff93f0c3"
              mode="FilterOnly" />
            <field
              path="Contractor"
              width="300"
              mode="FilterOnly" />
          </fields>
        </xml>
      </placeholder>
      <placeholder
        name="CaptionOverride"
        value="Service lines"
        resid="154434ad-b610-452d-9f0c-6e116aa3d67d" />
      <placeholder
        name="InlineEdit"
        value="cell" />
      <placeholder
        name="ShowGrouping"
        value="True" />
      <placeholder
        name="ShowFilters"
        value="True" />
      <placeholder
        name="AllowAdd"
        value="True" />
      <placeholder
        name="NewCaption"
        value="New" />
    </control>
  </form>
