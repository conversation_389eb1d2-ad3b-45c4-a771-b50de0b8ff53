#transformationVersion: 70.0
#
VZ_PK: 85ae88c1e3424d029b7dcee7a6b9474b
VZ_ConfigurationKey: 85ae88c1-e342-4d02-9b7d-cee7a6b9474b
VZ_FormID: HRM - Configuration - Processing Report
VZ_Caption:
  resKey: VZ_Caption|85ae88c1-e342-4d02-9b7d-cee7a6b9474b
  text: Processing Report
VZ_FormFactor: DSK
VZ_EntityType: IHrlProcessingRun
VZ_FormType: PAG
VZ_RequiresLogin: true
VZ_Dependencies: >-
  <dependencies xmlns="http://cargowise.com/glow/2014/11/14/dependencies.xsd">
    <expandPath
      path="Country" />
    <expandPath
      path="CreatedByStaff" />
    <expandPath
      path="HrlBalanceTransactions" />
    <datagrid
      path="HrlBalanceTransactions">
      <expandPath
        path="Staff" />
    </datagrid>
  </dependencies>
VZ_FormData: >-
  <form
    id="d48b97df-dc5a-4a35-a94e-593617e55d7e" xmlns="http://wisetechglobal.com/glow/2017/05/26/form.xsd">
    <placeholder
      name="BackgroundImagePk"
      value="" />
    <placeholder
      name="BackgroundSize"
      value="Auto" />
    <placeholder
      name="BackgroundImageOverride"
      value="" />
    <placeholder
      name="FormLayout"
      value="Splitview" />
    <placeholder
      name="ExpandPaths"
      value="" />
    <placeholder
      name="ScanInputHandling"
      value="NON" />
    <placeholder
      name="RemovePadding"
      value="false" />
    <placeholder
      name="TurnOnPortableOneToOneDesignMode"
      value="false" />
    <placeholder
      name="ContainsDefaultButton"
      value="False" />
    <control
      code="SRC"
      id="a82c8929-45d8-4d68-95d6-d86fd70b8731"
      left="1"
      top="1"
      width="4"
      height="1"
      binding="LLR_RN_NKCountry">
      <placeholder
        name="IsReadOnly"
        value="True" />
      <placeholder
        name="CanBeHidden"
        value="True" />
      <placeholder
        name="IsInitialFocus"
        value="False" />
      <placeholder
        name="CaptionOverride"
        value="Country/Region Processed"
        resid="0506368b-7575-4b00-b845-12828731fe3d" />
      <placeholder
        name="VisibilityCondition"
        value="" />
      <placeholder
        name="DisplayMode"
        value="Unspecified" />
      <placeholder
        name="FieldConfiguration"
        value="" />
      <placeholder
        name="DefaultFilter"
        value="" />
      <placeholder
        name="SuppressDependencyContributions"
        value="False" />
    </control>
    <control
      code="DTE"
      id="2e72e212-5217-4f3d-aebb-44131095789f"
      left="1"
      top="2"
      width="4"
      height="1"
      binding="LLR_ProcessTo">
      <placeholder
        name="IsReadOnly"
        value="True" />
      <placeholder
        name="CanBeHidden"
        value="True" />
      <placeholder
        name="IsInitialFocus"
        value="False" />
      <placeholder
        name="CaptionOverride"
        value="Accrual Date"
        resid="dc2c2e4e-7478-44f2-90e9-0d99c4133521" />
      <placeholder
        name="VisibilityCondition"
        value="" />
      <placeholder
        name="DefaultValueExpression"
        value="" />
      <placeholder
        name="IncludeTime"
        value="True" />
      <placeholder
        name="ShowNowButton"
        value="True" />
      <placeholder
        name="SuppressDependencyContributions"
        value="False" />
    </control>
    <control
      code="DTE"
      id="8aa54aef-fc0a-4a6b-ab32-45835cd8b1de"
      left="1"
      top="3"
      width="4"
      height="1"
      binding="LLR_SystemCreateTimeUtc">
      <placeholder
        name="IsReadOnly"
        value="True" />
      <placeholder
        name="CanBeHidden"
        value="True" />
      <placeholder
        name="IsInitialFocus"
        value="False" />
      <placeholder
        name="CaptionOverride"
        value="Date Processed On"
        resid="b8816d73-db9b-4843-a7db-c6b74139e6d3" />
      <placeholder
        name="VisibilityCondition"
        value="" />
      <placeholder
        name="DefaultValueExpression"
        value="" />
      <placeholder
        name="IncludeTime"
        value="True" />
      <placeholder
        name="ShowNowButton"
        value="True" />
      <placeholder
        name="SuppressDependencyContributions"
        value="False" />
    </control>
    <control
      code="SRC"
      id="e6ed2009-ffe8-46e8-8c22-58a03be353f6"
      left="1"
      top="4"
      width="4"
      height="1"
      binding="LLR_SystemCreateUser">
      <placeholder
        name="IsReadOnly"
        value="True" />
      <placeholder
        name="CanBeHidden"
        value="True" />
      <placeholder
        name="IsInitialFocus"
        value="False" />
      <placeholder
        name="CaptionOverride"
        value="Processed By"
        resid="d323952d-be67-4005-8eda-3b6d63699ce1" />
      <placeholder
        name="VisibilityCondition"
        value="" />
      <placeholder
        name="DisplayMode"
        value="Unspecified" />
      <placeholder
        name="FieldConfiguration"
        value="" />
      <placeholder
        name="DefaultFilter"
        value="" />
      <placeholder
        name="SuppressDependencyContributions"
        value="False" />
    </control>
    <control
      code="TBT"
      id="75a49fac-66ea-406d-8db4-165de3be5044"
      left="1"
      top="6"
      width="3"
      height="1">
      <placeholder
        name="CanBeHidden"
        value="True" />
      <placeholder
        name="IsInitialFocus"
        value="True" />
      <placeholder
        name="VisibilityCondition"
        value="" />
      <placeholder
        name="Content"
        value="Balances"
        resid="28fa46df-66ed-480b-bb01-ab52634b1c99" />
      <placeholder
        name="IsDefault"
        value="False" />
    </control>
    <control
      code="TBT"
      id="539eb40e-fb85-41dd-960a-dbde30e35cdd"
      left="4"
      top="6"
      width="3"
      height="1">
      <placeholder
        name="CanBeHidden"
        value="True" />
      <placeholder
        name="IsInitialFocus"
        value="False" />
      <placeholder
        name="VisibilityCondition"
        value="" />
      <placeholder
        name="Content"
        value="Applications"
        resid="33f5383c-d5c3-48a6-aba9-81a710928467" />
      <placeholder
        name="IsDefault"
        value="False" />
    </control>
    <control
      code="NTE"
      id="d89f6b1a-6120-4059-9630-d1024bc9f211"
      left="20"
      top="6"
      height="1"
      right="1">
      <placeholder
        name="CanBeHidden"
        value="True" />
      <placeholder
        name="VisibilityCondition"
        value="" />
      <placeholder
        name="NoteContents"
        value="&lt;p&gt;&lt;strong&gt;Number Of Staff&lt;/strong&gt;&lt;span class=&quot;g-default&quot;&gt;: &lt;/span&gt;&lt;span class=&quot;g-default&quot;&gt;{&amp;lt;NumberOfStaff&amp;gt;}&lt;/span&gt;&lt;/p&gt;" />
    </control>
    <control
      code="GRD"
      id="0c15ddbf-9202-446b-8787-09e47bf3ba30"
      left="1"
      top="7"
      right="1"
      bottom="1"
      binding="HrlBalanceTransactions">
      <placeholder
        name="IsReadOnly"
        value="True" />
      <placeholder
        name="CanBeHidden"
        value="True" />
      <placeholder
        name="CaptionOverride"
        value="Search By Staff Balances"
        resid="2bf1b882-5bc5-4884-9957-6ba820f7904c" />
      <placeholder
        name="VisibilityCondition"
        value="" />
      <placeholder
        name="HeaderAlignment"
        value="Left" />
      <placeholder
        name="HeaderDisplayMode"
        value="Visible" />
      <placeholder
        name="AllowAdd"
        value="False" />
      <placeholder
        name="AllowAttach"
        value="True" />
      <placeholder
        name="AllowDetach"
        value="True" />
      <placeholder
        name="AllowInlineEdit"
        value="False" />
      <placeholder
        name="FieldConfiguration"
        value="" />
      <placeholder
        name="DefaultSortFields"
        value="" />
      <placeholder
        name="ShowDocumentsIcon"
        value="True" />
      <placeholder
        name="RowActionsMode"
        value="RowAndMenu" />
      <placeholder
        name="HideGridActions"
        value="False" />
      <placeholder
        name="HideFilters"
        value="False" />
      <placeholder
        name="ActionMenuItems"
        value="" />
      <placeholder
        name="EditFormFlow"
        value="" />
      <placeholder
        name="SuppressDependencyContributions"
        value="False" />
    </control>
  </form>
