#transformationVersion: 70.0
#
VZ_PK: 33e7b2b8d5d74cb983649970f71fb5ca
VZ_ConfigurationKey: 33e7b2b8-d5d7-4cb9-8364-9970f71fb5ca
VZ_FormID: HRM - Leave Approval
VZ_Caption:
  resKey: VZ_Caption|33e7b2b8-d5d7-4cb9-8364-9970f71fb5ca
  text: Leave Approval
VZ_FormFactor: DSK
VZ_EntityType: IGlbStaffHoliday
VZ_FormType: FRM
VZ_RequiresLogin: true
VZ_MaterialDesign: true
VZ_Dependencies: <dependencies xmlns="http://cargowise.com/glow/2014/11/14/dependencies.xsd" />
VZ_FormData: >-
  <form
    id="944314aa-5b26-43a2-92b1-dcea59c3b7e3" xmlns="http://wisetechglobal.com/glow/2017/05/26/form.xsd">
    <placeholder
      name="Width"
      value="500" />
    <control
      code="LBL"
      id="a5d3f2ab-3caa-4698-8194-6d8d0419a7ba"
      binding="">
      <placeholder
        name="Caption"
        value="This Leave Request has been approved"
        resid="3bb81913-d13a-48cc-9bd9-357c42ea6212" />
      <placeholder
        name="Typography"
        value="h5" />
      <placeholder
        name="VisibilityCondition"
        value="GA_ApprovalStatus == &quot;APP&quot;" />
      <placeholder
        name="Color"
        value="success" />
    </control>
    <control
      code="LBL"
      id="a5d3f2ab-3caa-4698-8194-6d8d0419a7b9"
      binding="">
      <placeholder
        name="Caption"
        value="This Leave Request has been approved and requesting cancellation"
        resid="7ae64945-575b-4ca8-ad1e-c84587fb93ef" />
      <placeholder
        name="Typography"
        value="h5" />
      <placeholder
        name="VisibilityCondition"
        value="GA_ApprovalStatus == &quot;CAR&quot;" />
      <placeholder
        name="Color"
        value="warning" />
    </control>
    <control
      code="LBL"
      id="86c19981-ebb2-45bc-aa2a-e374f371a7fb"
      binding="">
      <placeholder
        name="Caption"
        value="This Leave Request has been declined"
        resid="7a285a10-7d39-4aa9-bbdb-a26ee4ffa079" />
      <placeholder
        name="Typography"
        value="h5" />
      <placeholder
        name="VisibilityCondition"
        value="GA_ApprovalStatus == &quot;DEC&quot;" />
      <placeholder
        name="Color"
        value="error" />
    </control>
    <control
      code="LBL"
      id="34573e5b-edaa-4eab-9066-e41a52f2662c"
      binding="">
      <placeholder
        name="Caption"
        value="This Leave Request has been canceled"
        resid="9c42e34c-0950-4198-94f2-27dd2a8ae7fa" />
      <placeholder
        name="Typography"
        value="h5" />
      <placeholder
        name="VisibilityCondition"
        value="GA_ApprovalStatus == &quot;CAN&quot;" />
      <placeholder
        name="Color"
        value="warning" />
    </control>
    <control
      code="LBL"
      id="34f1cab1-303c-432f-9135-99088c81a179"
      binding="">
      <placeholder
        name="Caption"
        value="Staff"
        resid="b97f26fe-541e-4c94-b702-d38d07e9720e" />
      <placeholder
        name="Typography"
        value="" />
      <placeholder
        name="Class"
        value="wtg-label wtg-field-label text-truncate" />
    </control>
    <control
      code="DSF"
      id="85ac5e70-6f35-4db0-8a7c-cd011dbf52c7"
      binding="GlbStaff.GS_FullName">
      <placeholder
        name="CaptionType"
        value="none" />
      <placeholder
        name="Typography"
        value="h5" />
      <placeholder
        name="Color"
        value="primary" />
    </control>
    <control
      code="SRC"
      id="9af741a5-af67-40bf-9870-b26a486f7bb9"
      binding="GA_WorkHolidayType">
      <placeholder
        name="CaptionOverride"
        value="Leave Type"
        resid="d7a830fc-1982-4235-a4b3-6415f3d9e70b" />
      <placeholder
        name="Padding"
        value="pt-3" />
      <placeholder
        name="IsReadOnly"
        value="True" />
    </control>
    <control
      code="BOX"
      id="2f6b3047-f290-44c9-8f51-eafbd2f7ed7b">
      <placeholder
        name="Layout"
        value="grid" />
      <placeholder
        name="VisibilityCondition"
        value="LeaveInstructions != null" />
      <placeholder
        name="Style"
        value="background-color: rgb(250, 250, 250); border-color: blue; border-radius: 20px; border-style: groove;" />
      <placeholder
        name="Columns"
        value="col-12" />
      <control
        code="IMG"
        id="cbb57fbc-37c5-4afd-967c-f5e84a40b2a3">
        <placeholder
          name="FallbackIcon"
          value="s-icon-info-circle-filled" />
        <placeholder
          name="Style"
          value="color: blue;" />
        <placeholder
          name="Margin"
          value="mt-10" />
        <placeholder
          name="Columns"
          value="col-1" />
      </control>
      <control
        code="BOX"
        id="34ebc4f6-61c3-4209-a66c-56054c312571">
        <placeholder
          name="Columns"
          value="col-11" />
        <control
          code="RTF"
          id="d004c458-fe30-47e1-b46a-02242b3fd665"
          binding="LeaveInstructions">
          <placeholder
            name="Toolbar"
            value="false" />
          <placeholder
            name="IsReadOnly"
            value="True" />
          <placeholder
            name="CaptionType"
            value="none" />
          <placeholder
            name="Height"
            value="250" />
        </control>
      </control>
    </control>
    <control
      code="OPT"
      id="221ab331-5359-4a02-a97f-f4ef5f9c9ca0"
      binding="CashOutLeaves">
      <placeholder
        name="Padding"
        value="" />
      <placeholder
        name="CaptionOverride"
        value="Cash out leaves"
        resid="6acd100d-57b2-417d-a791-bfce93f83023" />
      <placeholder
        name="VisibilityCondition"
        value="GlbStaff.Country.RN_Code == &quot;BR&quot;" />
      <placeholder
        name="IsReadOnly"
        value="True" />
    </control>
    <control
      code="BOX"
      id="9071c26b-8b15-4576-b319-88edaadc776f">
      <placeholder
        name="Layout"
        value="flex" />
      <placeholder
        name="Padding"
        value="" />
      <control
        code="OPT"
        id="42ecb39b-eb62-49a6-9910-77c396554971"
        binding="IsPartDay">
        <placeholder
          name="CaptionOverride"
          value="Part Day Leave"
          resid="658681ed-2550-4de0-b50f-353eb09bd0f4" />
        <placeholder
          name="Margin"
          value="" />
        <placeholder
          name="IsReadOnly"
          value="True" />
      </control>
    </control>
    <control
      code="DAE"
      id="3e196ce4-8511-4551-a3e7-fedbc590a9df"
      binding="GA_StartTime">
      <placeholder
        name="CaptionOverride"
        value="Start Date"
        resid="b551f383-97dc-4acb-ad39-f848eab0fbb3" />
      <placeholder
        name="Padding"
        value="pt-3" />
      <placeholder
        name="IsReadOnly"
        value="True" />
    </control>
    <control
      code="BOX"
      id="8537e624-9ee0-4507-8bc9-a7e74c71ce82">
      <placeholder
        name="Layout"
        value="" />
      <placeholder
        name="Padding"
        value="py-3" />
      <control
        code="DAE"
        id="8c819de8-8318-4d5c-965c-682d273c41ad"
        binding="GA_EndTime">
        <placeholder
          name="CaptionOverride"
          value="End Date"
          resid="0ee87f66-a959-4ab7-9c4c-fe02b4b4faaf" />
        <placeholder
          name="VisibilityCondition"
          value="!IsPartDay" />
        <placeholder
          name="Padding"
          value="" />
        <placeholder
          name="IsReadOnly"
          value="True" />
      </control>
      <control
        code="BOX"
        id="e0c026c6-8341-4dd7-a975-f3a0edbe3a31">
        <placeholder
          name="Layout"
          value="flex" />
        <placeholder
          name="Padding"
          value="" />
        <control
          code="TIM"
          id="a751fd73-0fb8-4291-ae6f-d23961e3a44a"
          binding="StartTime">
          <placeholder
            name="CaptionOverride"
            value="Start Time"
            resid="29f9affc-3294-4e08-90df-2fd40df665f6" />
          <placeholder
            name="VisibilityCondition"
            value="IsPartDay" />
          <placeholder
            name="IsReadOnly"
            value="True" />
        </control>
        <control
          code="TIM"
          id="08effcaf-9c69-4e64-bb63-4b6c93727c7f"
          binding="EndTime">
          <placeholder
            name="CaptionOverride"
            value="End Time"
            resid="95ebf3d0-02ab-4caf-b463-0f18e59b7579" />
          <placeholder
            name="VisibilityCondition"
            value="IsPartDay" />
          <placeholder
            name="IsReadOnly"
            value="True" />
        </control>
      </control>
    </control>
    <control
      code="NUM"
      id="f099fa65-a116-426f-ad2d-060e0918b91d"
      binding="LeaveHours">
      <placeholder
        name="CaptionOverride"
        value="Leave Requested (hours)"
        resid="4688f470-da1f-4137-9393-8bc84d6445d7" />
      <placeholder
        name="IsReadOnly"
        value="True" />
    </control>
    <control
      code="LBL"
      id="4c4314bc-8452-433d-89fe-f5c982caf8bc"
      binding="">
      <placeholder
        name="Align"
        value="left" />
      <placeholder
        name="Caption"
        value="Leave balance is insufficient!"
        resid="8e8978ce-4197-47f2-b2c0-68d7d7fcdb52" />
      <placeholder
        name="Display"
        value="box" />
      <placeholder
        name="Typography"
        value="title-small" />
      <placeholder
        name="Color"
        value="warning" />
      <placeholder
        name="VisibilityCondition"
        value="LeaveHours &gt; LeaveBalance &amp;&amp; !IsForNonAccruingType" />
    </control>
    <control
      code="BOX"
      id="cb9ead9a-65e6-47d8-894d-c7022fce184a"
      binding="">
      <placeholder
        name="VisibilityCondition"
        value="LeaveFallbackTypeName != null &amp;&amp; LeaveFallbackTypeName != &quot;&quot; &amp;&amp; LeaveTypeName != null &amp;&amp; LeaveTypeName != &quot;&quot;" />
      <control
        code="LBL"
        id="5bea85ab-367f-4b60-ad45-087011f66666"
        binding="">
        <placeholder
          name="Caption"
          value="This leave request for"
          resid="f8080832-8b56-4c51-b342-f9b8a7ba255c" />
        <placeholder
          name="Color"
          value="warning" />
        <placeholder
          name="Typography"
          value="title-small" />
      </control>
      <control
        code="DSF"
        id="89b37470-e0d7-47ff-a3e2-c673ffae361a"
        binding="LeaveTypeName">
        <placeholder
          name="CaptionType"
          value="none" />
        <placeholder
          name="Color"
          value="warning" />
        <placeholder
          name="Typography"
          value="title-small" />
      </control>
      <control
        code="LBL"
        id="f656e353-e546-46e4-b961-46235644cee7"
        binding="">
        <placeholder
          name="Caption"
          value="may fall back to"
          resid="d5863445-34f3-4dec-91e0-ced77c592e06" />
        <placeholder
          name="Color"
          value="warning" />
        <placeholder
          name="Typography"
          value="title-small" />
      </control>
      <control
        code="DSF"
        id="ac45be6b-367a-4ba5-84c1-5f1820c1a9f8"
        binding="LeaveFallbackTypeName">
        <placeholder
          name="CaptionType"
          value="none" />
        <placeholder
          name="Color"
          value="warning" />
        <placeholder
          name="Typography"
          value="title-small" />
      </control>
      <control
        code="LBL"
        id="8b20197d-9bae-4b1c-a601-e20d2236f475"
        binding="">
        <placeholder
          name="Caption"
          value="!"
          resid="b31efe53-a41c-4322-b385-e74b0f824687" />
        <placeholder
          name="Color"
          value="warning" />
        <placeholder
          name="Typography"
          value="title-small" />
      </control>
    </control>
    <control
      code="BOX"
      id="84b46f33-ede8-4ed1-bb92-a0ee48d47d5e"
      binding="">
      <placeholder
        name="VisibilityCondition"
        value="!IsForNonAccruingType" />
      <control
        code="LBL"
        id="cc855b1d-c0a9-4e04-9ccf-72eb96e22ac5"
        binding="">
        <placeholder
          name="Caption"
          value="Estimated leave balance (hours):"
          resid="023ea31f-257c-46cf-93b4-0924bee19e90" />
        <placeholder
          name="Typography"
          value="body" />
      </control>
      <control
        code="DSF"
        id="b60265ff-f5ba-45d7-9a54-064cf6cd46bb"
        binding="LeaveBalance">
        <placeholder
          name="CaptionType"
          value="none" />
        <placeholder
          name="Typography"
          value="body" />
      </control>
      <control
        code="PNL"
        id="225741b6-068c-4f99-87a4-285af6d33a14">
        <placeholder
          name="Caption"
          value="Public Holidays Within Selected Date Range"
          resid="f42e95a7-c6d6-4226-ae30-4b33ca2095be" />
        <control
          code="CMP"
          id="656a1ceb-9f19-4028-a540-7851aaefe8b2">
          <placeholder
            name="Component"
            value="cargoWiseOne.productHrm.components.LeaveRequestPublicHolidayList" />
        </control>
      </control>
    </control>
    <control
      code="FIL"
      id="59f5e4c7-0fc5-4573-9f27-3aabf294d999"
      binding="">
      <placeholder
        name="Caption"
        value="Attachments"
        resid="ce5bd206-9518-4992-824b-6f628ad576ff" />
      <placeholder
        name="DocumentType"
        value="MSC" />
      <placeholder
        name="Padding"
        value="pt-3" />
      <placeholder
        name="CanDeleteExisting"
        value="True" />
      <placeholder
        name="IsReadOnly"
        value="True" />
    </control>
    <control
      code="TXA"
      id="c2482924-27cf-4c1d-b3f8-7450dbf7648a"
      binding="GA_LeaveComment">
      <placeholder
        name="Padding"
        value="pt-3" />
      <placeholder
        name="IsReadOnly"
        value="True" />
    </control>
  </form>
