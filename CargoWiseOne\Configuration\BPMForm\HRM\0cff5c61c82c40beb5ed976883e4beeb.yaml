#transformationVersion: 70.0
#
VZ_PK: 0cff5c61c82c40beb5ed976883e4beeb
VZ_ConfigurationKey: 0cff5c61-c82c-40be-b5ed-976883e4beeb
VZ_FormID: HRM - Review Process - Invalid proposals
VZ_Caption:
  resKey: VZ_Caption|0cff5c61c82c40beb5ed976883e4beeb
  text: Review proposals with invalid data
VZ_FormFactor: DSK
VZ_EntityType: IReviewProcessNode
VZ_FormType: FRM
VZ_RequiresLogin: true
VZ_MaterialDesign: true
VZ_Dependencies: <dependencies xmlns="http://cargowise.com/glow/2014/11/14/dependencies.xsd" />
VZ_FormData: >-
  <form
    id="75e147f5-9a93-4e4e-90a9-ff26fd2f6c52" xmlns="http://wisetechglobal.com/glow/2017/05/26/form.xsd">
    <control
      code="PNL"
      id="5c39bc49-490c-4c1f-9522-b7ab40e88bf5"
      binding="">
      <placeholder
        name="Padding"
        value="pa-2" />
      <placeholder
        name="Layout"
        value="flex" />
      <placeholder
        name="FlexDirection"
        value="flex-column" />
      <placeholder
        name="VisibilityCondition"
        value="ReviewProcess.IncludesClassification &amp;&amp; UnclassifiedProposals.Any()" />
      <placeholder
        name="Margin"
        value="mb-2" />
      <control
        code="LBL"
        id="9d1e90cd-9042-487e-b634-69ad2b987740"
        binding="">
        <placeholder
          name="Margin"
          value="mb-2" />
        <placeholder
          name="Typography"
          value="body" />
        <placeholder
          name="Caption"
          value="These proposals are missing a classification for the staff member."
          resid="f6e14579-5b9f-4a47-97e8-91c274813053" />
      </control>
      <control
        code="RDT"
        id="40f3a020-ce20-40f5-9e14-192e9198850f"
        binding="UnclassifiedProposals">
        <placeholder
          name="InlineEdit"
          value="cell" />
        <placeholder
          name="FieldConfiguration">
          <xml>
            <fields xmlns="">
              <field
                path="RRP_GS_Staff"
                width="300"
                mode="Default"
                readOnly="true" />
              <field
                path="RRP_Classification"
                width="300"
                mode="Default" />
              <field
                path="RRP_PerformanceScore"
                width="170"
                mode="Optional" />
              <field
                path="RRP_Comments"
                width="250"
                mode="Default" />
              <field
                path="RRP_SystemCreateTimeUtc"
                width="220"
                mode="Optional" />
              <field
                path="RRP_SystemLastEditTimeUtc"
                width="250"
                mode="Optional" />
              <field
                path="RRP_SystemCreateUser"
                width="160"
                mode="Optional" />
              <field
                path="RRP_SystemLastEditUser"
                width="200"
                mode="Optional" />
              <field
                path="Staff.CurrentClassification.GSL_Classification"
                width="250"
                mode="Optional"
                readOnly="true" />
              <field
                path="Staff.CurrentPerformanceReview.GSV_Score"
                width="170"
                mode="Optional"
                readOnly="true" />
              <field
                path="Staff.CurrentRemuneration.GSR_RX_NKCurrency"
                width="300"
                mode="Optional"
                readOnly="true" />
              <field
                path="Staff.GS_RN_NKCountryCode"
                width="220"
                mode="Optional"
                readOnly="true" />
              <field
                path="Staff.CurrentJobTitle"
                width="250"
                mode="Optional" />
              <field
                path="IsStartable"
                width="120"
                mode="Optional" />
              <field
                path="ReviewManager"
                width="250"
                mode="Optional" />
              <field
                path="RRP_IsExcluded"
                width="110"
                mode="Optional" />
            </fields>
          </xml>
        </placeholder>
        <placeholder
          name="CaptionOverride"
          value="Incomplete proposals"
          resid="34c55a83-6110-4233-8e10-9c52afbe7578" />
      </control>
    </control>
    <control
      code="PNL"
      id="194b60d0-9b70-4e61-8d80-262bf0b35c8b"
      binding="">
      <placeholder
        name="Margin"
        value="mb-2" />
      <placeholder
        name="VisibilityCondition"
        value="ReviewProcess.IncludesPerformance &amp;&amp; ProposalsWithoutScore.Any()" />
      <placeholder
        name="Padding"
        value="pa-2" />
      <placeholder
        name="Layout"
        value="flex" />
      <placeholder
        name="FlexDirection"
        value="flex-column" />
      <control
        code="LBL"
        id="f7cfd943-b799-4521-8db4-2213aaf0beae">
        <placeholder
          name="Typography"
          value="body" />
        <placeholder
          name="Caption"
          value="These proposals are missing a performance score or comments for the staff member."
          resid="8526b193-7943-4306-8d48-b6d6ea15aa1b" />
        <placeholder
          name="Margin"
          value="mb-2" />
      </control>
      <control
        code="RDT"
        id="bf5b7d42-56cd-4a30-b6ab-5b31d9457847"
        binding="ProposalsWithoutScore">
        <placeholder
          name="FieldConfiguration">
          <xml>
            <fields xmlns="">
              <field
                path="RRP_GS_Staff"
                width="300"
                mode="Default"
                readOnly="true" />
              <field
                path="RRP_PerformanceScore"
                width="300"
                mode="Default" />
              <field
                path="RRP_Comments"
                width="300"
                mode="Default" />
              <field
                path="RRP_Classification"
                width="140"
                mode="Optional" />
              <field
                path="RRP_SystemCreateTimeUtc"
                width="220"
                mode="Optional" />
              <field
                path="RRP_SystemLastEditTimeUtc"
                width="250"
                mode="Optional" />
              <field
                path="RRP_SystemCreateUser"
                width="160"
                mode="Optional" />
              <field
                path="RRP_SystemLastEditUser"
                width="200"
                mode="Optional" />
              <field
                path="Staff.CurrentClassification.GSL_Classification"
                width="250"
                mode="Optional"
                readOnly="true" />
              <field
                path="Staff.CurrentPerformanceReview.GSV_Score"
                width="170"
                mode="Optional"
                readOnly="true" />
              <field
                path="Staff.CurrentRemuneration.GSR_RX_NKCurrency"
                width="300"
                mode="Optional"
                readOnly="true" />
              <field
                path="Staff.GS_RN_NKCountryCode"
                width="220"
                mode="Optional"
                readOnly="true" />
              <field
                path="Staff.CurrentJobTitle"
                width="250"
                mode="Optional" />
              <field
                path="IsStartable"
                width="120"
                mode="Optional" />
              <field
                path="ReviewManager"
                width="250"
                mode="Optional" />
              <field
                path="RRP_IsExcluded"
                width="110"
                mode="Optional" />
            </fields>
          </xml>
        </placeholder>
        <placeholder
          name="InlineEdit"
          value="cell" />
        <placeholder
          name="CaptionOverride"
          value="Incomplete proposals"
          resid="9a4fee67-3495-4277-abe4-bd0583e45b7e" />
      </control>
    </control>
    <control
      code="PNL"
      id="54eba9ee-760d-40ac-b721-0a7b5189ac6d"
      binding="">
      <placeholder
        name="Layout"
        value="flex" />
      <placeholder
        name="FlexDirection"
        value="flex-column" />
      <placeholder
        name="Padding"
        value="pa-2" />
      <placeholder
        name="VisibilityCondition"
        value="ReviewProcess.RPR_Type != &quot;REM&quot; &amp;&amp; ConflictingProposals.Any()" />
      <control
        code="BOX"
        id="a2dca2a2-38d3-4b84-8f99-40a4559fb054">
        <placeholder
          name="Margin"
          value="mb-2" />
        <control
          code="LBL"
          id="f316ea8f-f4ec-4a5e-93f5-0ec3a28325c4"
          binding="">
          <placeholder
            name="Caption"
            value="These proposals have been made for staff members who already have a"
            resid="764e64c2-7941-497f-8cbf-44c77ef606a8" />
          <placeholder
            name="Typography"
            value="body" />
          <placeholder
            name="Margin"
            value="mr-1" />
        </control>
        <control
          code="LBL"
          id="d47a4de3-44db-4467-ab99-8b4a3943f099"
          binding="">
          <placeholder
            name="Typography"
            value="body" />
          <placeholder
            name="Caption"
            value="performance score"
            resid="aa8410f5-3c99-4831-8a6f-f4a89cfba5c6" />
          <placeholder
            name="Margin"
            value="mr-1" />
          <placeholder
            name="VisibilityCondition"
            value="ReviewProcess.RPR_Type == &quot;PER&quot; || ReviewProcess.RPR_Type == &quot;C&amp;P&quot;" />
        </control>
        <control
          code="LBL"
          id="53d71e33-748f-4695-bdf6-7ba517f765d6"
          binding="">
          <placeholder
            name="Typography"
            value="body" />
          <placeholder
            name="Caption"
            value="or"
            resid="2768358e-d7cc-4bc4-baa8-bed7e23b4871" />
          <placeholder
            name="Margin"
            value="mr-1" />
          <placeholder
            name="VisibilityCondition"
            value="ReviewProcess.RPR_Type == &quot;C&amp;P&quot;" />
        </control>
        <control
          code="LBL"
          id="cf9b3cc5-10b9-45c9-bbf4-8009cf0d904f"
          binding="">
          <placeholder
            name="Typography"
            value="body" />
          <placeholder
            name="Caption"
            value="classification"
            resid="dafca1e2-4a9f-4059-8db3-906b6e822f74" />
          <placeholder
            name="Margin"
            value="mr-1" />
          <placeholder
            name="VisibilityCondition"
            value="ReviewProcess.RPR_Type == &quot;CLA&quot; || ReviewProcess.RPR_Type == &quot;C&amp;P&quot;" />
        </control>
        <control
          code="LBL"
          id="d9dc7fa5-765b-4abe-9346-c532181bf631">
          <placeholder
            name="Typography"
            value="body" />
          <placeholder
            name="Caption"
            value="that comes into effect on the same date as this review."
            resid="7508c275-b1f5-4853-87da-21236d298201" />
        </control>
      </control>
      <control
        code="RDT"
        id="3b234e95-181c-4c5a-a03a-56dd73c38e18"
        binding="ConflictingProposals">
        <placeholder
          name="FieldConfiguration">
          <xml>
            <fields xmlns="">
              <field
                path="RRP_GS_Staff"
                width="300"
                mode="Default"
                readOnly="true" />
              <field
                path="RRP_Classification"
                width="300"
                mode="Default" />
              <field
                path="RRP_PerformanceScore"
                width="170"
                mode="Optional" />
              <field
                path="RRP_Comments"
                width="250"
                mode="Default" />
              <field
                path="RRP_SystemCreateTimeUtc"
                width="220"
                mode="Optional" />
              <field
                path="RRP_SystemLastEditTimeUtc"
                width="250"
                mode="Optional" />
              <field
                path="RRP_SystemCreateUser"
                width="160"
                mode="Optional" />
              <field
                path="RRP_SystemLastEditUser"
                width="200"
                mode="Optional" />
              <field
                path="Staff.CurrentClassification.GSL_Classification"
                width="250"
                mode="Optional"
                readOnly="true" />
              <field
                path="Staff.CurrentPerformanceReview.GSV_Score"
                width="170"
                mode="Optional"
                readOnly="true" />
              <field
                path="Staff.CurrentRemuneration.GSR_RX_NKCurrency"
                width="300"
                mode="Optional"
                readOnly="true" />
              <field
                path="Staff.GS_RN_NKCountryCode"
                width="220"
                mode="Optional"
                readOnly="true" />
              <field
                path="Staff.CurrentJobTitle"
                width="250"
                mode="Optional" />
              <field
                path="IsStartable"
                width="120"
                mode="Optional" />
              <field
                path="ReviewManager"
                width="250"
                mode="Optional" />
              <field
                path="RRP_IsExcluded"
                width="110"
                mode="Optional" />
            </fields>
          </xml>
        </placeholder>
        <placeholder
          name="VisibilityCondition"
          value="ReviewProcess.IncludesClassification &amp;&amp; !ReviewProcess.IncludesPerformance" />
        <placeholder
          name="InlineEdit"
          value="cell" />
        <placeholder
          name="CaptionOverride"
          value="Conflicting proposals"
          resid="e9374d14-cc82-427e-9062-e470bcc53a85" />
      </control>
      <control
        code="RDT"
        id="e67a2a05-5e35-44c9-90d7-2a678828627d"
        binding="ConflictingProposals">
        <placeholder
          name="InlineEdit"
          value="cell" />
        <placeholder
          name="FieldConfiguration">
          <xml>
            <fields xmlns="">
              <field
                path="RRP_GS_Staff"
                width="300"
                mode="Default"
                readOnly="true" />
              <field
                path="RRP_PerformanceScore"
                width="300"
                mode="Default" />
              <field
                path="RRP_Comments"
                width="300"
                mode="Default" />
              <field
                path="RRP_Classification"
                width="140"
                mode="Optional" />
              <field
                path="RRP_SystemCreateTimeUtc"
                width="220"
                mode="Optional" />
              <field
                path="RRP_SystemLastEditTimeUtc"
                width="250"
                mode="Optional" />
              <field
                path="RRP_SystemCreateUser"
                width="160"
                mode="Optional" />
              <field
                path="RRP_SystemLastEditUser"
                width="200"
                mode="Optional" />
              <field
                path="Staff.CurrentClassification.GSL_Classification"
                width="250"
                mode="Optional"
                readOnly="true" />
              <field
                path="Staff.CurrentPerformanceReview.GSV_Score"
                width="170"
                mode="Optional"
                readOnly="true" />
              <field
                path="Staff.CurrentRemuneration.GSR_RX_NKCurrency"
                width="300"
                mode="Optional"
                readOnly="true" />
              <field
                path="Staff.GS_RN_NKCountryCode"
                width="220"
                mode="Optional"
                readOnly="true" />
              <field
                path="Staff.CurrentJobTitle"
                width="250"
                mode="Optional" />
              <field
                path="IsStartable"
                width="120"
                mode="Optional" />
              <field
                path="ReviewManager"
                width="250"
                mode="Optional" />
              <field
                path="RRP_IsExcluded"
                width="110"
                mode="Optional" />
            </fields>
          </xml>
        </placeholder>
        <placeholder
          name="VisibilityCondition"
          value="ReviewProcess.IncludesPerformance &amp;&amp; !ReviewProcess.IncludesClassification" />
        <placeholder
          name="CaptionOverride"
          value="Conflicting proposals"
          resid="d385843f-c612-4bc8-aa24-6fb9a392a1d7" />
      </control>
      <control
        code="RDT"
        id="6014cb97-3de6-4120-808d-dcf88f0e1361"
        binding="ConflictingProposals">
        <placeholder
          name="VisibilityCondition"
          value="ReviewProcess.IncludesClassification &amp;&amp; ReviewProcess.IncludesPerformance" />
        <placeholder
          name="CaptionOverride"
          value="Conflicting proposals"
          resid="e3371c89-57a1-4fe3-b1d8-84d6dca1dce8" />
        <placeholder
          name="FieldConfiguration">
          <xml>
            <fields xmlns="">
              <field
                path="RRP_GS_Staff"
                width="300"
                mode="Default"
                readOnly="true" />
              <field
                path="RRP_Classification"
                width="300"
                mode="Default" />
              <field
                path="RRP_PerformanceScore"
                width="300"
                mode="Default" />
              <field
                path="RRP_Comments"
                width="300"
                mode="Default" />
              <field
                path="RRP_SystemCreateTimeUtc"
                width="220"
                mode="Optional" />
              <field
                path="RRP_SystemLastEditTimeUtc"
                width="250"
                mode="Optional" />
              <field
                path="RRP_SystemCreateUser"
                width="160"
                mode="Optional" />
              <field
                path="RRP_SystemLastEditUser"
                width="200"
                mode="Optional" />
              <field
                path="Staff.CurrentClassification.GSL_Classification"
                width="250"
                mode="Optional"
                readOnly="true" />
              <field
                path="Staff.CurrentPerformanceReview.GSV_Score"
                width="170"
                mode="Optional"
                readOnly="true" />
              <field
                path="Staff.CurrentRemuneration.GSR_RX_NKCurrency"
                width="300"
                mode="Optional"
                readOnly="true" />
              <field
                path="Staff.GS_RN_NKCountryCode"
                width="220"
                mode="Optional"
                readOnly="true" />
              <field
                path="Staff.CurrentJobTitle"
                width="250"
                mode="Optional" />
              <field
                path="IsStartable"
                width="120"
                mode="Optional" />
              <field
                path="ReviewManager"
                width="250"
                mode="Optional" />
              <field
                path="RRP_IsExcluded"
                width="110"
                mode="Optional" />
            </fields>
          </xml>
        </placeholder>
      </control>
    </control>
  </form>
