#transformationVersion: 70.0
#
VZ_PK: d33262a2515f49a19bc0221984decbc9
VZ_ConfigurationKey: d33262a2-515f-49a1-9bc0-221984decbc9
VZ_FormID: Show Form - Disable Expression for Footer Transition
VZ_Caption:
  resKey: VZ_Caption|d33262a2515f49a19bc0221984decbc9
  text: Show Form - Disable Expression for Footer Transition
VZ_FormFactor: DSK
VZ_FormType: PAG
VZ_RequiresLogin: true
VZ_MaterialDesign: true
VZ_Dependencies: <dependencies xmlns="http://cargowise.com/glow/2014/11/14/dependencies.xsd" />
VZ_FormData: >-
  <form
    id="bf55b78b-6ad4-4db1-ac32-61a68327699a" xmlns="http://wisetechglobal.com/glow/2017/05/26/form.xsd">
    <placeholder
      name="FitToHeight"
      value="True" />
    <control
      code="BOX"
      id="2e1044ab-0905-4a7f-abcb-a5c44163bd30"
      binding="">
      <placeholder
        name="Height"
        value="100%" />
      <placeholder
        name="Layout"
        value="flex" />
      <placeholder
        name="FlexDirection"
        value="flex-column" />
      <control
        code="PNL"
        id="aa13b003-652b-486c-aa0a-9aacc8ca1fc7"
        binding="">
        <placeholder
          name="Margin"
          value="mb-3" />
        <placeholder
          name="Class"
          value="blue lighten-4 bg-blue-lighten-4" />
        <control
          code="BOX"
          id="9e40d24f-1fa9-4b62-847a-72ed2dba91ff">
          <placeholder
            name="Layout"
            value="flex" />
          <control
            code="IBT"
            id="08a5899c-671b-49f4-9d46-34585892d779"
            binding="">
            <placeholder
              name="Icon"
              value="s-icon-documentation" />
            <placeholder
              name="Tooltip"
              value="Documentation"
              resid="e54d0d0c-8916-49b0-a34a-cb6bc2435a09" />
            <placeholder
              name="Target"
              value="_blank" />
            <placeholder
              name="Hyperlink"
              value="https://github.com/WiseTechGlobal/Glow.Content/blob/main/Home/Guides/Platform-Builder/Documentation/Form-Flows/Activities/Show-Form-Activity-Disable-Expression-for-Footer-Transition.md" />
          </control>
          <control
            code="IBT"
            id="57da1501-89d8-48a1-b3b4-c89731899daf"
            binding="">
            <placeholder
              name="Icon"
              value="s-icon-pt-devtools" />
            <placeholder
              name="Margin"
              value="ml-2" />
            <placeholder
              name="Target"
              value="_blank" />
            <placeholder
              name="Tooltip"
              value="YAML"
              resid="4eeb3104-06a8-4d41-9048-fdbafd1c832a" />
            <placeholder
              name="Hyperlink"
              value="https://github.com/WiseTechGlobal/Glow/blob/master/CargoWiseOne/Configuration/BPMFormFlow/Internal/Library/dee05842493547ccb28198319ff75a85.yaml" />
          </control>
          <control
            code="IBT"
            id="57a89d79-e13e-4567-9303-9c1855db8546"
            binding="">
            <placeholder
              name="Icon"
              value="s-icon-settings" />
            <placeholder
              name="Margin"
              value="ml-2" />
            <placeholder
              name="Target"
              value="_blank" />
            <placeholder
              name="Tooltip"
              value="Platform Builder"
              resid="bd9188e0-be21-4bf1-b8a5-6f845c10a780" />
            <placeholder
              name="Hyperlink"
              value="platformbuilder:?target=BPMFormFlow&amp;identifier=dee05842493547ccb28198319ff75a85" />
          </control>
          <control
            code="BOX"
            id="d5006272-98d2-4ed6-b8ae-542b2acaa7c4">
            <placeholder
              name="Margin"
              value="ml-3" />
            <placeholder
              name="Layout"
              value="flex" />
            <placeholder
              name="FlexDirection"
              value="flex-column" />
            <placeholder
              name="FlexJustify"
              value="justify-center" />
            <placeholder
              name="FlexAlign"
              value="align-center" />
            <control
              code="LBL"
              id="cef969b0-bbfa-4a47-ba5f-61c7b5b3aae7"
              binding="">
              <placeholder
                name="Margin"
                value="ml-2" />
              <placeholder
                name="Display"
                value="block" />
              <placeholder
                name="Typography"
                value="body-strong" />
              <placeholder
                name="Caption"
                value="For hands-on experience of the example described in the Documentation, click the Action Menu item &quot;Show Form - Disable Expression for Footer Transition&quot;."
                resid="2863ad6d-9af1-49be-82d4-cf6a64a7456e" />
            </control>
          </control>
        </control>
      </control>
      <control
        code="PNL"
        id="bf89367e-0e3e-4b63-b49a-1869ffe575bd"
        binding="">
        <control
          code="SDT"
          id="db531709-667d-47e2-aa34-65a12615a073"
          binding="">
          <placeholder
            name="EntityType"
            value="IDtbLandTransportConsignment" />
          <placeholder
            name="CaptionOverride"
            value="List to explain the usage of the &quot;Disable Expression” feature on the Show Form activity detail in Footer Transition section."
            resid="68bfdc2e-4869-420d-9f37-6974064daf1e" />
          <placeholder
            name="HideActions"
            value="True" />
          <placeholder
            name="HideFilters"
            value="True" />
          <placeholder
            name="DisabledGridRowActions"
            value="eDocs,Notes,Logs,Remove,Documents,Workflow" />
          <placeholder
            name="ActionMenuItems">
            <xml>
              <formFlows xmlns="">
                <formFlow>dee05842493547ccb28198319ff75a85</formFlow>
              </formFlows>
            </xml>
          </placeholder>
        </control>
      </control>
    </control>
  </form>
