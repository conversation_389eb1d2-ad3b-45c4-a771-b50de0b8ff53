#transformationVersion: 70.0
#
VZ_PK: 16a9181015054e708e8fd1bbbbdd1b97
VZ_ConfigurationKey: 16a91810-1505-4e70-8e8f-d1bbbbdd1b97
VZ_FormID: HRM - Change Request - My Approvals
VZ_Caption:
  resKey: VZ_Caption|16a91810-1505-4e70-8e8f-d1bbbbdd1b97
  text: My Change Request Approvals
VZ_FormFactor: DSK
VZ_FormType: PAG
VZ_RequiresLogin: true
VZ_MaterialDesign: true
VZ_Dependencies: <dependencies xmlns="http://cargowise.com/glow/2014/11/14/dependencies.xsd" />
VZ_FormData: >-
  <form
    id="e9bcdbe3-7abf-4a6f-80da-92c22baec78f" xmlns="http://wisetechglobal.com/glow/2017/05/26/form.xsd">
    <placeholder
      name="Layout"
      value="fill" />
    <control
      code="SDT"
      id="77746bd8-11fa-4f11-8f10-310c47274161"
      binding="">
      <placeholder
        name="FullPage"
        value="True" />
      <placeholder
        name="CaptionOverride"
        value="My Change Request Approvals"
        resid="c91b0f61-380b-4cb3-bd5f-82a7d26ac3f5" />
      <placeholder
        name="Columns"
        value="col-6" />
      <placeholder
        name="EntityType"
        value="IWorkflowTask[[IGlbStaffChangeRequest]]" />
      <placeholder
        name="DefaultFilter">
        <xml>
          <ArrayOfFilterGroup xmlns:a="http://schemas.microsoft.com/2003/10/Serialization/Arrays" xmlns:i="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://cargowise.com/glow/2014/07/16/filters.xsd">
            <FilterGroup>
              <Filters>
                <Filter>
                  <FilterType>SimpleLookupFilter</FilterType>
                  <Operation>Is</Operation>
                  <PropertyPath>P9_Status</PropertyPath>
                  <Values>
                    <a:string>ASN</a:string>
                  </Values>
                </Filter>
              </Filters>
              <IsImplicit>false</IsImplicit>
            </FilterGroup>
            <FilterGroup>
              <Filters>
                <Filter>
                  <FilterType>SimpleLookupFilter</FilterType>
                  <Operation>IsNot</Operation>
                  <PropertyPath>P9_Status</PropertyPath>
                  <Values>
                    <a:string>CAN</a:string>
                  </Values>
                </Filter>
                <Filter>
                  <FilterType>BoolFilter</FilterType>
                  <Operation>Is</Operation>
                  <PropertyPath>IsApprovalTask</PropertyPath>
                  <Values>
                    <a:string>true</a:string>
                  </Values>
                </Filter>
                <Filter>
                  <FilterType>BoolFilter</FilterType>
                  <Operation>Is</Operation>
                  <PropertyPath>IsSelf</PropertyPath>
                  <Values>
                    <a:string>true</a:string>
                  </Values>
                </Filter>
              </Filters>
              <IsImplicit>true</IsImplicit>
            </FilterGroup>
          </ArrayOfFilterGroup>
        </xml>
      </placeholder>
      <placeholder
        name="SearchMode"
        value="Entity" />
      <placeholder
        name="EditFormFlowConfiguration">
        <xml>
          <formFlows xmlns="">
            <formFlow>c582b21cd45246d484d308d318db3329</formFlow>
          </formFlows>
        </xml>
      </placeholder>
      <placeholder
        name="FieldConfiguration">
        <xml>
          <fields xmlns="">
            <field
              path="P9_GS_NKAssignedStaffMember"
              width="210"
              mode="Default" />
            <field
              path="P9_CompletedTimeUtc"
              width="180"
              mode="Optional" />
            <field
              path="P9_Status"
              width="60"
              mode="Default" />
            <field
              path="IsApprovalTask"
              width="150"
              mode="Optional" />
            <field
              path="ApprovalType"
              width="250"
              mode="Optional" />
            <field
              path="ApprovalStatus"
              width="250"
              mode="Optional" />
            <field
              path="P9_Outcome"
              width="70"
              mode="Optional" />
            <field
              path="Parent.CreatedByStaff.GS_FullName"
              width="250"
              mode="Default" />
            <field
              path="Parent.Staff.GS_FullName"
              width="250"
              mode="Default" />
            <field
              path="Parent.Template.GSG_TemplateName"
              width="250"
              mode="Default" />
            <field
              path="Parent.GCR_SystemCreateTimeUtc"
              width="250"
              mode="Default" />
            <field
              path="EarliestEffectiveDate"
              width="230"
              caption="Change Effective Date"
              resid="87de79ba-9365-4ef9-9902-81790de7fcad"
              mode="Default" />
          </fields>
        </xml>
      </placeholder>
      <placeholder
        name="DisabledGridRowActions"
        value="Remove" />
    </control>
  </form>
