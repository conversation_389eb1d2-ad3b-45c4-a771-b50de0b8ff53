#transformationVersion: 70.0
#
VZ_PK: 8ba9924d057149d3a2dead801a43354f
VZ_ConfigurationKey: 8ba9924d-0571-49d3-a2de-ad801a43354f
VZ_FormID: CYS Edit Yard Unit Details
VZ_Caption:
  resKey: VZ_Caption|8ba9924d057149d3a2dead801a43354f
  text: Edit Yard Unit Details
VZ_FormFactor: MOB
VZ_EntityType: IMNRSurvey
VZ_FormType: FRM
VZ_RequiresLogin: true
VZ_MaterialDesign: true
VZ_Dependencies: <dependencies xmlns="http://cargowise.com/glow/2014/11/14/dependencies.xsd" />
VZ_FormData: >-
  <form
    id="39d1df72-a6a4-472a-805f-d591cf5ee199" xmlns="http://wisetechglobal.com/glow/2017/05/26/form.xsd">
    <placeholder
      name="MaxWidth"
      value="600" />
    <placeholder
      name="Padding"
      value="pa-0" />
    <control
      code="BOX"
      id="8d608ecd-8681-401e-98c7-e84538ff6bb1">
      <placeholder
        name="Padding"
        value="pb-3 pl-4" />
      <control
        code="LBL"
        id="f9a09a9e-977b-4635-942a-2e9b8ef9e195">
        <placeholder
          name="Caption"
          value="Structural Unit Check"
          resid="ec498c35-f5cc-44fb-ab9d-4f0b74ea20d6" />
        <placeholder
          name="Typography"
          value="title-large" />
      </control>
    </control>
    <control
      code="BOX"
      id="6b2fb2a8-b678-4372-be5d-b958a9b5a03c">
      <placeholder
        name="Style"
        value="background-color:#fff; border-top: 1px solid var(--s-neutral-border-weak-default); border-bottom: 1px solid var(--s-neutral-border-weak-default);" />
      <placeholder
        name="Padding"
        value="pa-4" />
      <placeholder
        name="Margin"
        value="mb-4" />
      <control
        code="CLO"
        id="72873caf-748c-4112-860a-1cf1bce9d6a4">
        <placeholder
          name="Sentiment"
          value="info" />
        <placeholder
          name="Title"
          value="Survey completed"
          resid="4130755b-e63e-40dc-8f3c-5accdff0d1e7" />
        <placeholder
          name="Description"
          value="This survey has been completed and is awaiting estimation."
          resid="72029088-d80e-46af-8999-6857bced3cba" />
        <placeholder
          name="VisibilityCondition"
          value="MRS_IsCompleted == true &amp;&amp; MRS_RequireEstimate == true &amp;&amp; (MNRWorkOrderHeader != null &amp;&amp; MNRWorkOrderHeader.MWO_IsEstimateCompleted == false)" />
        <placeholder
          name="Margin"
          value="mb-2" />
      </control>
      <control
        code="CLO"
        id="75d3d83c-2a8d-4dc3-958d-232834772949">
        <placeholder
          name="Sentiment"
          value="info" />
        <placeholder
          name="Title"
          value="Estimate submitted"
          resid="8dd4c00e-31b9-4834-9884-63db9a8fcde7" />
        <placeholder
          name="Description"
          value="If required, please make edits in the Container Yard portal."
          resid="484b70f3-9be9-45ca-8039-707176a5c0f6" />
        <placeholder
          name="VisibilityCondition"
          value="MNRWorkOrderHeader != null &amp;&amp; MNRWorkOrderHeader.MWO_IsEstimateCompleted == true" />
        <placeholder
          name="Margin"
          value="mb-2" />
      </control>
      <control
        code="LBL"
        id="dfbbcf9b-08f7-49e0-8eda-ad28c00932e2">
        <placeholder
          name="Caption"
          value="Container details"
          resid="bb013f23-47a6-43cd-b4e2-5bf0268de020" />
        <placeholder
          name="FontWeight"
          value="bold" />
      </control>
      <control
        code="TXT"
        id="fbaf38f6-fdb5-45b4-9120-4eafac2041e2"
        binding="YardUnitState.YUS_UnitID">
        <placeholder
          name="Columns"
          value="2" />
        <placeholder
          name="IsReadOnly"
          value="True" />
        <placeholder
          name="Margin"
          value="mt-3 mb-4" />
      </control>
      <control
        code="BOX"
        id="22bbdbc3-36cc-4f85-aa40-63798cf7c5fb">
        <placeholder
          name="Layout"
          value="grid" />
        <placeholder
          name="Margin"
          value="mb-4" />
        <placeholder
          name="FlexJustify"
          value="justify-space-between" />
        <control
          code="CMB"
          id="8deecaa4-e1c3-4787-bd2b-0b6c2d4798a4"
          binding="UnitType">
          <placeholder
            name="IsReadOnly"
            value="False" />
          <placeholder
            name="Required"
            value="True" />
          <placeholder
            name="CaptionOverride"
            value="Unit type"
            resid="fd46b017-1a26-4bb2-b7fa-0091a261340f" />
          <placeholder
            name="Columns"
            value="col-6" />
        </control>
        <control
          code="SRC"
          id="b82d9de4-74de-4c33-b0a3-109ac5aaacf0"
          binding="UnitLineItem.YLI_REG_Grade">
          <placeholder
            name="Required"
            value="True" />
          <placeholder
            name="Margin"
            value="ml-2" />
          <placeholder
            name="Columns"
            value="col-6" />
        </control>
      </control>
      <control
        code="SRC"
        id="aaeaa4f1-4424-415c-b5f0-40469b94ba18"
        binding="ContainerType">
        <placeholder
          name="Required"
          value="True" />
        <placeholder
          name="Columns"
          value="col-2" />
        <placeholder
          name="CaptionOverride"
          value="Type Size"
          resid="6e28b87f-5963-44c3-8745-53b87da924fa" />
        <placeholder
          name="Margin"
          value="mb-4" />
      </control>
      <control
        code="TXT"
        id="c16ffbf0-0f0c-41b2-bd97-7e2124c87321"
        binding="YardUnitState.Client">
        <placeholder
          name="CaptionOverride"
          value="Client"
          resid="9823bd2e-4085-4913-b213-f916b470c846" />
        <placeholder
          name="Required"
          value="True" />
        <placeholder
          name="Margin"
          value="mb-4" />
      </control>
      <control
        code="TXT"
        id="b0a1d2aa-4147-45a3-b537-3df9c112c441"
        binding="YardUnitState.Lessee">
        <placeholder
          name="Columns"
          value="col-2" />
        <placeholder
          name="Margin"
          value="mb-4" />
      </control>
      <control
        code="MHS"
        id="1a50a528-df7b-422f-88f2-a5cd4ba70a3d"
        binding="TareWeight">
        <placeholder
          name="Margin"
          value="mb-4" />
      </control>
      <control
        code="MHS"
        id="a769719f-ed4c-4275-81fa-acff25fcbf9c"
        binding="MaxGrossWeight">
        <placeholder
          name="Margin"
          value="mb-4" />
      </control>
      <control
        code="MHS"
        id="a4a6fb8c-527b-4c1c-a999-63208b54807d"
        binding="GrossWeightMeasure">
        <placeholder
          name="Columns"
          value="2" />
        <placeholder
          name="Required"
          value="True" />
        <placeholder
          name="CaptionOverride"
          value="Gross Weight"
          resid="0c85aaf2-6454-487a-92e8-5f9df404ec11" />
        <placeholder
          name="Margin"
          value="mb-4" />
        <placeholder
          name="VisibilityCondition"
          value="!UnitLineItem.YLI_IsEmpty" />
      </control>
      <control
        code="DAE"
        id="ead92cf3-558f-4f29-ab2d-65c6db9d490f"
        binding="UnitLineItem.YLI_ManufactureDate">
        <placeholder
          name="Columns"
          value="col-2" />
        <placeholder
          name="Margin"
          value="mb-4" />
      </control>
      <control
        code="OPT"
        id="acc59f30-96dd-476a-9009-482a8656df96"
        binding="IsEmpty">
        <placeholder
          name="CaptionOverride"
          value="Empty container?"
          resid="35566531-898b-43b1-8532-93d84ab5f6d7" />
        <placeholder
          name="Margin"
          value="mb-4" />
      </control>
      <control
        code="OPT"
        id="0b00d213-80c9-4e65-9c70-1807fd784bd1"
        binding="IsDamaged">
        <placeholder
          name="CaptionOverride"
          value="Is damaged?"
          resid="fe77acdc-455c-46e1-8101-585972e59aa4" />
        <placeholder
          name="Margin"
          value="mb-4" />
      </control>
      <control
        code="OPT"
        id="aee6f54-7f5e-426a-90c9-81b8cfe19e77"
        binding="MRS_RequireEstimate">
        <placeholder
          name="CaptionOverride"
          value="Requires structural estimate?"
          resid="baf3d539-218f-4c02-b553-bf733c325575" />
        <placeholder
          name="Margin"
          value="mb-4" />
      </control>
      <control
        code="TXA"
        id="fa424c89-9f57-42b6-a28c-769fa6ad6335"
        binding="UnitNote">
        <placeholder
          name="CaptionOverride"
          value="Notes"
          resid="3a619957-1c05-45ec-8164-1d010e703cad" />
        <placeholder
          name="Columns"
          value="col-2" />
      </control>
    </control>
    <control
      code="BOX"
      id="c6581529-89cb-46b2-9875-89c5eec02a9a">
      <placeholder
        name="Margin"
        value="mb-4" />
      <placeholder
        name="Style"
        value="background-color:#fff; border-top: 1px solid var(--s-neutral-border-weak-default); border-bottom: 1px solid var(--s-neutral-border-weak-default);" />
      <placeholder
        name="Padding"
        value="pa-4" />
      <control
        code="BOX"
        id="83271fa4-a919-4656-92d8-b469a25c244b">
        <placeholder
          name="Margin"
          value="mb-4" />
        <control
          code="LBL"
          id="e9c42f3b-c980-45c2-811d-476d3f8b6e30">
          <placeholder
            name="Caption"
            value="Survey images"
            resid="f3f2c690-08d4-45c4-a408-e19ef300f156" />
          <placeholder
            name="FontWeight"
            value="bold" />
        </control>
      </control>
      <control
        code="BTN"
        id="ee01f4c2-d78b-4917-88d0-cf3fed5d0776">
        <placeholder
          name="Caption"
          value="Add photos"
          resid="7304459f-dfda-4746-addc-d5cacc196d96" />
        <placeholder
          name="LeadingIcon"
          value="s-icon-add" />
        <placeholder
          name="Width"
          value="100%" />
        <placeholder
          name="Variant"
          value="default" />
        <placeholder
          name="Transition"
          value="True" />
      </control>
    </control>
  </form>
