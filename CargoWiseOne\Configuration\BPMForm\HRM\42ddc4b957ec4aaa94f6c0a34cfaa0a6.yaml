#transformationVersion: 70.0
#
VZ_PK: 42ddc4b957ec4aaa94f6c0a34cfaa0a6
VZ_ConfigurationKey: 42ddc4b9-57ec-4aaa-94f6-c0a34cfaa0a6
VZ_FormID: HRM - Review Budget - Budget Demographic
VZ_Caption:
  resKey: VZ_Caption|42ddc4b9-57ec-4aaa-94f6-c0a34cfaa0a6
  text: Budget Demographic
VZ_FormFactor: DSK
VZ_EntityType: IReviewProcessDemographic
VZ_FormType: FRM
VZ_RequiresLogin: true
VZ_MaterialDesign: true
VZ_Dependencies: <dependencies xmlns="http://cargowise.com/glow/2014/11/14/dependencies.xsd" />
VZ_FormData: >-
  <form
    id="7ac3dfac-e5a6-4cd8-840a-de46420bda27" xmlns="http://wisetechglobal.com/glow/2017/05/26/form.xsd">
    <placeholder
      name="Layout"
      value="grid" />
    <control
      code="BOX"
      id="c9c59361-2dcb-4358-919d-7525c9eaf747">
      <placeholder
        name="Layout"
        value="grid" />
      <control
        code="BOX"
        id="a71b727a-72ba-4140-93b6-0d5fd726384d">
        <placeholder
          name="Columns"
          value="col-7" />
        <placeholder
          name="Layout"
          value="grid" />
        <control
          code="PNL"
          id="faaa89a5-0b3c-4cf1-bb93-e002ddfa8b42">
          <placeholder
            name="Layout"
            value="grid" />
          <control
            code="LBL"
            id="28e71cc1-c17d-4917-9a03-600a7e5755f0">
            <placeholder
              name="Caption"
              value="Use the filters below to filter the staff to be included in this budget demographic."
              resid="b75bcd54-01c3-44ff-baa7-792f004dbc3e" />
          </control>
          <control
            code="LBL"
            id="166e8c46-8f7c-4827-9c08-1a64f2be3bbc">
            <placeholder
              name="Caption"
              value="The budget demographic details can be viewed and edited on the right."
              resid="105889c0-4b18-4eaf-8220-236ac46cb79f" />
          </control>
        </control>
        <control
          code="PNL"
          id="1b413f57-3335-46a4-8fa1-8483cabd9eb5"
          binding="">
          <placeholder
            name="Layout"
            value="grid" />
          <control
            code="SDT"
            id="15d344dd-bef7-49b0-abac-01138ff16be1"
            binding="">
            <placeholder
              name="CaptionType"
              value="short" />
            <placeholder
              name="EntityType"
              value="IGlbStaff" />
            <placeholder
              name="DefaultFilter">
              <xml>
                <ArrayOfFilterGroup xmlns:a="http://schemas.microsoft.com/2003/10/Serialization/Arrays" xmlns:i="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://cargowise.com/glow/2014/07/16/filters.xsd">
                  <FilterGroup>
                    <Filters>
                      <Filter>
                        <FilterType>BoolFilter</FilterType>
                        <Operation>Is</Operation>
                        <PropertyPath>GS_IsSystemAccount</PropertyPath>
                        <Values>
                          <a:string>false</a:string>
                        </Values>
                      </Filter>
                      <Filter>
                        <FilterType>BoolFilter</FilterType>
                        <Operation>Is</Operation>
                        <PropertyPath>GS_IsResource</PropertyPath>
                        <Values>
                          <a:string>false</a:string>
                        </Values>
                      </Filter>
                      <Filter>
                        <FilterType>IsActiveFilter</FilterType>
                        <Operation>Is</Operation>
                        <PropertyPath>GS_IsActive</PropertyPath>
                        <Values>
                          <a:string>true</a:string>
                        </Values>
                      </Filter>
                    </Filters>
                    <IsImplicit>true</IsImplicit>
                  </FilterGroup>
                </ArrayOfFilterGroup>
              </xml>
            </placeholder>
            <placeholder
              name="IsReadOnly"
              value="True" />
            <placeholder
              name="BindingForFilter"
              value="Filter.FilterData" />
            <placeholder
              name="CaptionOverride"
              value="Staff in this budget demographic"
              resid="eca67bef-1790-4ea8-8253-065340254cf1" />
          </control>
        </control>
      </control>
      <control
        code="PNL"
        id="9993461d-78d8-4c8c-8504-64e6a8283997"
        binding="">
        <placeholder
          name="Layout"
          value="grid" />
        <placeholder
          name="Columns"
          value="col-5" />
        <control
          code="BOX"
          id="223488df-5c19-4c32-b1f2-e0a2402e37b5">
          <control
            code="DVR"
            id="5b95b0d6-82c6-4598-b52f-232b3cdae66b" />
          <control
            code="BOX"
            id="4dba9614-9f99-4d81-aa90-6e8f1d41591c"
            binding="">
            <placeholder
              name="Layout"
              value="grid" />
            <control
              code="TXT"
              id="271d10d4-95df-47ed-9740-8a950907879f"
              binding="RPD_Name">
              <placeholder
                name="CaptionOverride"
                value="Budget demographic name"
                resid="fb4bd385-db78-4c58-bf9d-e82f596cb39a" />
            </control>
            <control
              code="NUM"
              id="2b3c39fb-d54d-4c8e-9e3f-eb5b1ba6f05d"
              binding="RPD_StandardIncreasePercent">
              <placeholder
                name="CaptionOverride"
                value="Standard increase (%)"
                resid="5925f999-0fd0-44ae-a3f4-07cb2f26b649" />
            </control>
            <control
              code="NUM"
              id="85a47cdd-8ff6-426c-b88d-b6a6a4aa30d7"
              binding="RPD_Priority" />
          </control>
        </control>
        <control
          code="RDT"
          id="51c5c0ea-3eb0-4e4e-a0a0-c91fc582e370"
          binding="ReviewProcessBudgets">
          <placeholder
            name="AllowAdd"
            value="True" />
          <placeholder
            name="InlineEdit"
            value="row" />
          <placeholder
            name="ShowItemActions"
            value="True" />
          <placeholder
            name="CaptionOverride"
            value="Remuneration breakdown"
            resid="e66106ed-4dbc-48cb-a9b3-f78d293fb028" />
          <placeholder
            name="ShowCustomize"
            value="True" />
          <placeholder
            name="AllowAttach"
            value="False" />
          <placeholder
            name="ShowSelect"
            value="True" />
          <placeholder
            name="FieldConfiguration"
            value="" />
          <placeholder
            name="DefaultSortFields">
            <xml>
              <ArrayOfFieldSortDefinition xmlns:i="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://schemas.datacontract.org/2004/07/CargoWise.Glow.Infrastructure.Interfaces">
                <FieldSortDefinition>
                  <FieldName>RPB_Entitlement</FieldName>
                  <IsAscending>false</IsAscending>
                </FieldSortDefinition>
              </ArrayOfFieldSortDefinition>
            </xml>
          </placeholder>
          <placeholder
            name="Margin"
            value="my-2" />
        </control>
      </control>
    </control>
  </form>
