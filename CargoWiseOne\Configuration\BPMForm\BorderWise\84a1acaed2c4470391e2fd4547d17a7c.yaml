#transformationVersion: 70.0
#
VZ_PK: 84a1acaed2c4470391e2fd4547d17a7c
VZ_ConfigurationKey: 84a1acae-d2c4-4703-91e2-fd4547d17a7c
VZ_FormID: Job Voyage form
VZ_Caption:
  resKey: VZ_Caption|84a1acae-d2c4-4703-91e2-fd4547d17a7c
  text: Job Voyage form
VZ_FormFactor: DSK
VZ_EntityType: IJobVoyage
VZ_FormType: FRM
VZ_RequiresLogin: true
VZ_Dependencies: >-
  <dependencies xmlns="http://cargowise.com/glow/2014/11/14/dependencies.xsd">
    <expandPath
      path="JobVoyOrigins" />
    <expandPath
      path="JobVoyDestinations" />
    <expandPath
      path="JobVoyOrigins/JobSailings" />
    <expandPath
      path="Vessel" />
    <expandPath
      path="Line" />
    <datagrid
      path="JobVoyOrigins">
      <expandPath
        path="PortOfLoading" />
    </datagrid>
    <datagrid
      path="JobVoyDestinations">
      <expandPath
        path="PortOfDischarge" />
    </datagrid>
    <datagrid
      path="JobVoyOrigins/JobSailings">
      <expandPath
        path="JobVoyDestination.PortOfDischarge" />
      <expandPath
        path="JobVoyDestination" />
    </datagrid>
  </dependencies>
VZ_FormData: >-
  <form
    id="bb1ee053-670d-44b2-bb88-45ac9725c81a" xmlns="http://wisetechglobal.com/glow/2017/05/26/form.xsd">
    <placeholder
      name="BackgroundImagePk"
      value="" />
    <placeholder
      name="BackgroundSize"
      value="Auto" />
    <placeholder
      name="BackgroundImageOverride"
      value="" />
    <placeholder
      name="FormLayout"
      value="Splitview" />
    <placeholder
      name="ExpandPaths"
      value="" />
    <placeholder
      name="ScanInputHandling"
      value="NON" />
    <placeholder
      name="RemovePadding"
      value="false" />
    <placeholder
      name="TurnOnPortableOneToOneDesignMode"
      value="false" />
    <placeholder
      name="ContainsDefaultButton"
      value="False" />
    <control
      code="GRP"
      id="cc36f889-6587-45a9-bbe8-da5f51107771"
      left="0"
      top="0"
      width="11"
      height="3">
      <placeholder
        name="CanBeHidden"
        value="True" />
      <placeholder
        name="VisibilityCondition"
        value="e046f230-b9a2-4b86-9c9f-a4a341e1e24b" />
      <placeholder
        name="Header"
        value="Voyage Details"
        resid="ec3dfdbb-329f-4a18-9249-09d3b9d563c8" />
      <placeholder
        name="HeaderAlignment"
        value="Left" />
      <placeholder
        name="HeaderDisplayMode"
        value="Visible" />
      <placeholder
        name="BackgroundImagePk"
        value="" />
      <placeholder
        name="BackgroundImageOverride"
        value="" />
      <control
        code="SRC"
        id="5f39e79d-110b-4551-8f41-0e09f67b5590"
        left="0"
        top="0"
        width="6"
        height="1"
        binding="JV_RV_NKVessel">
        <placeholder
          name="IsReadOnly"
          value="True" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="DisplayMode"
          value="Unspecified" />
        <placeholder
          name="FieldConfiguration"
          value="" />
        <placeholder
          name="DefaultFilter"
          value="" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="TXT"
        id="74fc45de-23e2-4b44-9327-82eba3557475"
        left="7"
        top="0"
        width="4"
        height="1"
        binding="JV_VoyageFlight">
        <placeholder
          name="IsReadOnly"
          value="True" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="Voyage"
          resid="39716d55-118f-48ab-82db-8be32b0829fb" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="OnSubmit"
          value="" />
        <placeholder
          name="MultiLine"
          value="False" />
        <placeholder
          name="EnableAutoComplete"
          value="False" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="SRC"
        id="84ab640f-42a8-4b8b-85b2-4460c1ea4dfe"
        left="0"
        top="1"
        width="6"
        height="1"
        binding="JV_OH_Line">
        <placeholder
          name="IsReadOnly"
          value="True" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="Carrier"
          resid="d3a794c8-8a7f-461f-be4a-e6519a6a1805" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="DisplayMode"
          value="Unspecified" />
        <placeholder
          name="FieldConfiguration"
          value="" />
        <placeholder
          name="DefaultFilter"
          value="" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
    </control>
    <control
      code="GRP"
      id="69f2f474-976a-4180-bef8-4f98adf9b75a"
      left="0"
      top="0"
      width="6"
      height="3">
      <placeholder
        name="CanBeHidden"
        value="True" />
      <placeholder
        name="VisibilityCondition"
        value="9e7fe62d-8760-452c-89bf-c41d74b808d7" />
      <placeholder
        name="Header"
        value="Voyage Details"
        resid="a4853aa9-35c4-428d-8fa1-78f5c227734c" />
      <placeholder
        name="HeaderAlignment"
        value="Left" />
      <placeholder
        name="HeaderDisplayMode"
        value="Visible" />
      <placeholder
        name="BackgroundImagePk"
        value="" />
      <placeholder
        name="BackgroundImageOverride"
        value="" />
      <control
        code="TXT"
        id="58f2398e-d628-44ad-b54b-43ecd8131dd3"
        left="0"
        top="0"
        width="5"
        height="1"
        binding="JV_VoyageFlight">
        <placeholder
          name="IsReadOnly"
          value="True" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="Truck Reference"
          resid="e1371197-5450-4bea-a16b-c10a13b735bc" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="OnSubmit"
          value="" />
        <placeholder
          name="MultiLine"
          value="False" />
        <placeholder
          name="EnableAutoComplete"
          value="False" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="SRC"
        id="4d036e5b-a2e4-4300-b1a7-6393f2b2e52e"
        left="0"
        top="1"
        width="6"
        height="1"
        binding="JV_OH_Line">
        <placeholder
          name="IsReadOnly"
          value="True" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="Carrier"
          resid="3f08d564-80f2-4f99-9c9b-2395c3ff7282" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="DisplayMode"
          value="Unspecified" />
        <placeholder
          name="FieldConfiguration"
          value="" />
        <placeholder
          name="DefaultFilter"
          value="" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
    </control>
    <control
      code="GRP"
      id="4f7779e2-bbd1-4404-bb69-455b6f7e3131"
      left="0"
      top="0"
      width="8"
      height="3">
      <placeholder
        name="CanBeHidden"
        value="True" />
      <placeholder
        name="VisibilityCondition"
        value="4af153ce-99e0-4149-9759-9b625f180f0e" />
      <placeholder
        name="Header"
        value="Voyage Details"
        resid="7a732e1a-6fc5-4675-ac18-dfa7be8b59ca" />
      <placeholder
        name="HeaderAlignment"
        value="Left" />
      <placeholder
        name="HeaderDisplayMode"
        value="Visible" />
      <placeholder
        name="BackgroundImagePk"
        value="" />
      <placeholder
        name="BackgroundImageOverride"
        value="" />
      <control
        code="TXT"
        id="516c4463-2ae3-410b-8ca4-dde679a1d3b9"
        left="0"
        top="0"
        width="5"
        height="1"
        binding="JV_VoyageFlight">
        <placeholder
          name="IsReadOnly"
          value="True" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="Journey Number"
          resid="05290ac2-a5bc-4a26-8e90-81dbc7a1eccc" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="OnSubmit"
          value="" />
        <placeholder
          name="MultiLine"
          value="False" />
        <placeholder
          name="EnableAutoComplete"
          value="False" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="SRC"
        id="ccc35fd1-9b97-4be9-aca6-a01484eec124"
        left="0"
        top="1"
        width="6"
        height="1"
        binding="JV_OH_Line">
        <placeholder
          name="IsReadOnly"
          value="True" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="Carrier"
          resid="a222c1d5-a2d6-4a97-b4f6-d3d23fcfeb40" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="DisplayMode"
          value="Unspecified" />
        <placeholder
          name="FieldConfiguration"
          value="" />
        <placeholder
          name="DefaultFilter"
          value="" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
    </control>
    <control
      code="GRP"
      id="32d575b9-a44f-40fc-ac4d-1a564fea0903"
      left="0"
      top="0"
      width="14"
      height="3">
      <placeholder
        name="CanBeHidden"
        value="True" />
      <placeholder
        name="VisibilityCondition"
        value="dc2480d4-9891-4fcb-9c9e-9d23ff959caf" />
      <placeholder
        name="Header"
        value="Voyage Details"
        resid="a5808f77-f1dc-49d3-ae3a-cd57e9443cf6" />
      <placeholder
        name="HeaderAlignment"
        value="Left" />
      <placeholder
        name="HeaderDisplayMode"
        value="Visible" />
      <placeholder
        name="BackgroundImagePk"
        value="" />
      <placeholder
        name="BackgroundImageOverride"
        value="" />
      <control
        code="TXT"
        id="830a84a0-c50f-4fe5-83da-935c33081a75"
        left="0"
        top="0"
        width="5"
        height="1"
        binding="JV_VoyageFlight">
        <placeholder
          name="IsReadOnly"
          value="True" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="Flight Number"
          resid="00298233-d696-4582-bf84-24fa598c91b6" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="OnSubmit"
          value="" />
        <placeholder
          name="MultiLine"
          value="False" />
        <placeholder
          name="EnableAutoComplete"
          value="False" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="TXT"
        id="44de805a-e1b9-46cf-aab2-a071a4e436ad"
        left="7"
        top="0"
        width="6"
        height="1"
        binding="JV_AircraftType">
        <placeholder
          name="IsReadOnly"
          value="True" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="OnSubmit"
          value="" />
        <placeholder
          name="MultiLine"
          value="False" />
        <placeholder
          name="EnableAutoComplete"
          value="False" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="SRC"
        id="de9a19a7-d016-4215-8c94-f97fffa056b5"
        left="0"
        top="1"
        width="6"
        height="1"
        binding="JV_OH_Line">
        <placeholder
          name="IsReadOnly"
          value="True" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="Carrier"
          resid="832233a4-511c-4e66-aad2-cf2e48ad14ef" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="DisplayMode"
          value="Unspecified" />
        <placeholder
          name="FieldConfiguration"
          value="" />
        <placeholder
          name="DefaultFilter"
          value="" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="OPT"
        id="9a07e9da-67b6-4174-8dce-700bd59d107b"
        left="7"
        top="1"
        width="6"
        height="1"
        binding="JV_IsCargoOnly">
        <placeholder
          name="IsReadOnly"
          value="True" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
    </control>
    <control
      code="GRD"
      id="da3b6da3-323b-4702-bbc7-2e236614ab25"
      left="0"
      top="3"
      width="13"
      height="5"
      binding="JobVoyOrigins">
      <placeholder
        name="IsReadOnly"
        value="True" />
      <placeholder
        name="CanBeHidden"
        value="True" />
      <placeholder
        name="CaptionOverride"
        value="Load Ports"
        resid="6a614abd-3c6f-433f-9662-8211a38d1048" />
      <placeholder
        name="VisibilityCondition"
        value="" />
      <placeholder
        name="HeaderAlignment"
        value="Left" />
      <placeholder
        name="HeaderDisplayMode"
        value="Visible" />
      <placeholder
        name="AllowAdd"
        value="True" />
      <placeholder
        name="AllowAttach"
        value="True" />
      <placeholder
        name="AllowDetach"
        value="True" />
      <placeholder
        name="AllowInlineEdit"
        value="True" />
      <placeholder
        name="FieldConfiguration"
        value="" />
      <placeholder
        name="DefaultSortFields">
        <xml>
          <ArrayOfFieldSortDefinition xmlns:i="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://schemas.datacontract.org/2004/07/CargoWise.Glow.Infrastructure.Interfaces" />
        </xml>
      </placeholder>
      <placeholder
        name="ShowDocumentsIcon"
        value="True" />
      <placeholder
        name="RowActionsMode"
        value="RowAndMenu" />
      <placeholder
        name="HideGridActions"
        value="False" />
      <placeholder
        name="HideFilters"
        value="False" />
      <placeholder
        name="ActionMenuItems"
        value="" />
      <placeholder
        name="EditFormFlow"
        value="" />
      <placeholder
        name="SuppressDependencyContributions"
        value="False" />
    </control>
    <control
      code="GRD"
      id="a3665679-03ff-489d-be20-120bacbb10e8"
      left="13"
      top="3"
      height="5"
      right="0"
      binding="JobVoyDestinations">
      <placeholder
        name="IsReadOnly"
        value="True" />
      <placeholder
        name="CanBeHidden"
        value="True" />
      <placeholder
        name="CaptionOverride"
        value="Discharge Ports"
        resid="7782b624-f5b7-43f8-9a14-fa9a7cd5ae5d" />
      <placeholder
        name="VisibilityCondition"
        value="" />
      <placeholder
        name="HeaderAlignment"
        value="Left" />
      <placeholder
        name="HeaderDisplayMode"
        value="Visible" />
      <placeholder
        name="AllowAdd"
        value="True" />
      <placeholder
        name="AllowAttach"
        value="True" />
      <placeholder
        name="AllowDetach"
        value="True" />
      <placeholder
        name="AllowInlineEdit"
        value="True" />
      <placeholder
        name="FieldConfiguration"
        value="" />
      <placeholder
        name="DefaultSortFields">
        <xml>
          <ArrayOfFieldSortDefinition xmlns:i="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://schemas.datacontract.org/2004/07/CargoWise.Glow.Infrastructure.Interfaces" />
        </xml>
      </placeholder>
      <placeholder
        name="ShowDocumentsIcon"
        value="True" />
      <placeholder
        name="RowActionsMode"
        value="RowAndMenu" />
      <placeholder
        name="HideGridActions"
        value="False" />
      <placeholder
        name="HideFilters"
        value="False" />
      <placeholder
        name="ActionMenuItems"
        value="" />
      <placeholder
        name="EditFormFlow"
        value="" />
      <placeholder
        name="SuppressDependencyContributions"
        value="False" />
    </control>
    <control
      code="GRD"
      id="e46f094e-b558-4613-990f-8f1c3c2d433d"
      left="0"
      top="8"
      right="0"
      bottom="0"
      binding="JobVoyOrigins/JobSailings">
      <placeholder
        name="IsReadOnly"
        value="True" />
      <placeholder
        name="CanBeHidden"
        value="True" />
      <placeholder
        name="CaptionOverride"
        value="Sailing Schedule"
        resid="cd7712b7-ffdf-42cf-97ef-7a1c6177d06b" />
      <placeholder
        name="VisibilityCondition"
        value="" />
      <placeholder
        name="HeaderAlignment"
        value="Left" />
      <placeholder
        name="HeaderDisplayMode"
        value="Visible" />
      <placeholder
        name="AllowAdd"
        value="True" />
      <placeholder
        name="AllowAttach"
        value="True" />
      <placeholder
        name="AllowDetach"
        value="True" />
      <placeholder
        name="AllowInlineEdit"
        value="True" />
      <placeholder
        name="FieldConfiguration">
        <xml>
          <fields xmlns="">
            <field
              path="JX_UniqueReference"
              width="200"
              mode="Default" />
            <field
              path="JobVoyDestination.PortOfDischarge.RL_Code"
              width="150"
              mode="Default" />
            <field
              path="JX_IsPublished"
              width="120"
              mode="Default" />
            <field
              path="JX_DepotReceivalCommences"
              width="150"
              mode="Default" />
            <field
              path="JX_DepotCutOff"
              width="150"
              mode="Default" />
            <field
              path="JX_DepotAvailabilityDate"
              width="150"
              mode="Default" />
            <field
              path="JX_DepotStorageDate"
              width="150"
              mode="Default" />
            <field
              path="JX_ContainerReleaseNumber"
              width="240"
              mode="Default" />
            <field
              path="JX_ReservedMasterBill"
              width="200"
              mode="Default" />
            <field
              path="JX_Status"
              width="60"
              mode="Default" />
            <field
              path="JX_OnlineScheduleStatus"
              width="220"
              mode="Default" />
            <field
              path="PortOfDischarge"
              width="250"
              mode="Default" />
            <field
              path="PortOfOrigin"
              width="250"
              mode="Default" />
            <field
              path="JX_JB"
              width="250"
              mode="Default" />
          </fields>
        </xml>
      </placeholder>
      <placeholder
        name="DefaultSortFields">
        <xml>
          <ArrayOfFieldSortDefinition xmlns:i="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://schemas.datacontract.org/2004/07/CargoWise.Glow.Infrastructure.Interfaces" />
        </xml>
      </placeholder>
      <placeholder
        name="ShowDocumentsIcon"
        value="True" />
      <placeholder
        name="RowActionsMode"
        value="RowAndMenu" />
      <placeholder
        name="HideGridActions"
        value="False" />
      <placeholder
        name="HideFilters"
        value="False" />
      <placeholder
        name="ActionMenuItems"
        value="" />
      <placeholder
        name="EditFormFlow"
        value="" />
      <placeholder
        name="SuppressDependencyContributions"
        value="False" />
    </control>
  </form>
