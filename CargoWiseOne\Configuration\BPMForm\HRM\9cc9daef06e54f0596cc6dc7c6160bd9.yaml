#transformationVersion: 70.0
#
VZ_PK: 9cc9daef06e54f0596cc6dc7c6160bd9
VZ_ConfigurationKey: 9cc9daef-06e5-4f05-96cc-6dc7c6160bd9
VZ_FormID: HRM - Onboarding List
VZ_Caption:
  resKey: VZ_Caption|9cc9daef-06e5-4f05-96cc-6dc7c6160bd9
  text: Onboarding
VZ_FormFactor: DSK
VZ_FormType: PAG
VZ_RequiresLogin: true
VZ_MaterialDesign: true
VZ_Dependencies: <dependencies xmlns="http://cargowise.com/glow/2014/11/14/dependencies.xsd" />
VZ_FormData: >-
  <form
    id="3533be97-a144-4b9e-b6ed-bc9efd04f047" xmlns="http://wisetechglobal.com/glow/2017/05/26/form.xsd">
    <placeholder
      name="FitToHeight"
      value="true" />
    <control
      code="BOX"
      id="7a079152-c98e-4a5d-ba9b-33e6c5af726f"
      binding="">
      <placeholder
        name="Class"
        value="d-flex align-center" />
      <control
        code="SPC"
        id="2c696253-3f45-4c8c-ad00-6f84b162fafb"
        binding="" />
      <control
        code="BTN"
        id="16b14c0f-542a-4cf4-8773-d5e7166a285e"
        binding="">
        <placeholder
          name="Caption"
          value="New Onboarding"
          resid="68adb7e0-5ed6-4ab3-9c3d-c4a85b9e68a3" />
        <placeholder
          name="Margin"
          value="mb-2" />
        <placeholder
          name="Sentiment"
          value="primary" />
        <placeholder
          name="Variant"
          value="fill" />
        <placeholder
          name="FormFlowConfiguration">
          <xml>
            <formFlows xmlns="">
              <formFlow>9dfd20a60ffa4438be9c4257d93b02f9</formFlow>
            </formFlows>
          </xml>
        </placeholder>
        <placeholder
          name="VisibilityCondition"
          value="%.CurrentUserCheckpoints.Contains(&quot;CanViewAllOnBoardings&quot;)" />
      </control>
    </control>
    <control
      code="SDT"
      id="86ce6426-9f28-420d-904a-b597df643149"
      binding="">
      <placeholder
        name="CaptionOverride"
        value="Onboarding"
        resid="c70e677c-4eb2-47e3-bb7a-0ee17b29f6d4" />
      <placeholder
        name="EntityType"
        value="IHROnBoarding" />
      <placeholder
        name="FullPage"
        value="True" />
      <placeholder
        name="EditFormFlowConfiguration">
        <xml>
          <formFlows xmlns="">
            <formFlow>af775077a1f1402e9acfc02ab7caaba8</formFlow>
          </formFlows>
        </xml>
      </placeholder>
      <placeholder
        name="DefaultFilter">
        <xml>
          <ArrayOfFilterGroup xmlns:a="http://schemas.microsoft.com/2003/10/Serialization/Arrays" xmlns:i="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://cargowise.com/glow/2014/07/16/filters.xsd">
            <FilterGroup>
              <Filters>
                <Filter>
                  <FilterType>BoolFilter</FilterType>
                  <Operation>Is</Operation>
                  <PropertyPath>IsClosed</PropertyPath>
                  <Values>
                    <a:string>false</a:string>
                  </Values>
                </Filter>
              </Filters>
              <IsImplicit>false</IsImplicit>
            </FilterGroup>
          </ArrayOfFilterGroup>
        </xml>
      </placeholder>
    </control>
  </form>
