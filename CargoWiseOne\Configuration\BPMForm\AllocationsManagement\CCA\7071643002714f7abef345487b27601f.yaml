#transformationVersion: 70.0
#
VZ_PK: 7071643002714f7abef345487b27601f
VZ_ConfigurationKey: 70716430-0271-4f7a-bef3-45487b27601f
VZ_FormID: CCA - Contracts Landing Page
VZ_Caption:
  resKey: VZ_Caption|70716430-0271-4f7a-bef3-45487b27601f
  text: Carrier Contract & Allocations
VZ_FormFactor: DSK
VZ_FormType: PAG
VZ_RequiresLogin: true
VZ_MaterialDesign: true
VZ_Dependencies: <dependencies xmlns="http://cargowise.com/glow/2014/11/14/dependencies.xsd" />
VZ_FormData: >-
  <form
    id="d53617bc-8283-40b1-a7f1-19fee22fadb8" xmlns="http://wisetechglobal.com/glow/2017/05/26/form.xsd">
    <placeholder
      name="Layout"
      value="fill" />
    <control
      code="SDT"
      id="990f8064-5484-4401-a57a-e21d4a1bab7a"
      binding="">
      <placeholder
        name="EntityType"
        value="IRatingContract" />
      <placeholder
        name="FullPage"
        value="True" />
      <placeholder
        name="NewFormFlowConfiguration">
        <xml>
          <formFlows xmlns="">
            <formFlow>e4a3448dfec64779a854a60ee5f92986</formFlow>
          </formFlows>
        </xml>
      </placeholder>
      <placeholder
        name="ShowItemActions"
        value="True" />
      <placeholder
        name="ShowAddActions"
        value="True" />
      <placeholder
        name="ActionMenuItems">
        <xml>
          <formFlows xmlns="">
            <formFlow>99f97adf055b4cb9b9e90886bbefa9b7</formFlow>
            <formFlow>a6640ec9944349e7bd5fd92e24b00adb</formFlow>
            <formFlow>163360b2dd1940dca1db1fba855e8541</formFlow>
            <formFlow
              newSession="True">d01e5307627c41a1bca244727bcc4013</formFlow>
          </formFlows>
        </xml>
      </placeholder>
      <placeholder
        name="CaptionOverride"
        value="Carrier Contracts"
        resid="bb47fb48-9bf8-42cb-b9f7-bb4e6e594d5e" />
      <placeholder
        name="SearchMode"
        value="Entity" />
      <placeholder
        name="EditFormFlowConfiguration">
        <xml>
          <formFlows xmlns="">
            <formFlow
              newSession="True">d01e5307627c41a1bca244727bcc4013</formFlow>
          </formFlows>
        </xml>
      </placeholder>
      <placeholder
        name="DefaultFilter">
        <xml>
          <ArrayOfFilterGroup xmlns:a="http://schemas.microsoft.com/2003/10/Serialization/Arrays" xmlns:i="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://cargowise.com/glow/2014/07/16/filters.xsd">
            <FilterGroup>
              <Filters>
                <Filter>
                  <FilterType>SimpleLookupFilter</FilterType>
                  <Operation>Is</Operation>
                  <PropertyPath>RCT_ContractType</PropertyPath>
                  <Values>
                    <a:string>PRO</a:string>
                  </Values>
                </Filter>
              </Filters>
              <IsImplicit>true</IsImplicit>
            </FilterGroup>
          </ArrayOfFilterGroup>
        </xml>
      </placeholder>
    </control>
  </form>
