#transformationVersion: 70.0
#
VZ_PK: f56dff660f464eacb12d1c348fab617c
VZ_ConfigurationKey: f56dff66-0f46-4eac-b12d-1c348fab617c
VZ_FormID: AST - Asset edit form
VZ_Caption:
  resKey: VZ_Caption|f56dff660f464eacb12d1c348fab617c
  text: Edit Asset
VZ_FormFactor: DSK
VZ_EntityType: IAccAssetHeader
VZ_FormType: FRM
VZ_RequiresLogin: true
VZ_MaterialDesign: true
VZ_Dependencies: <dependencies xmlns="http://cargowise.com/glow/2014/11/14/dependencies.xsd" />
VZ_FormData: >-
  <form
    id="f3e08253-0eab-40cb-8a51-faf79008e231" xmlns="http://wisetechglobal.com/glow/2017/05/26/form.xsd">
    <placeholder
      name="Layout"
      value="flex" />
    <placeholder
      name="MaxWidth"
      value="100%" />
    <control
      code="BOX"
      id="f71c10e1-7aa4-4fd5-960c-b9a39fc14ba6"
      binding="">
      <placeholder
        name="Layout"
        value="flex" />
      <placeholder
        name="FillAvailable"
        value="True" />
      <control
        code="PNL"
        id="378403fd-1e1a-4c96-9bef-68155b30e488"
        binding="">
        <placeholder
          name="Caption"
          value="Basic information"
          resid="ad545f45-d0eb-4632-84ec-932afbb60f2e" />
        <placeholder
          name="FitToHeight"
          value="False" />
        <placeholder
          name="Margin"
          value="me-3" />
        <placeholder
          name="Width"
          value="300" />
        <placeholder
          name="MinWidth"
          value="300" />
        <placeholder
          name="MaxWidth"
          value="300" />
        <placeholder
          name="Columns"
          value="col-md-6" />
        <control
          code="OPT"
          id="62c5cee1-8149-459f-9916-1b5b175a5875"
          binding="AAH_IsActive">
          <placeholder
            name="CaptionOverride"
            value="Active"
            resid="000d626d-5027-4f07-9f97-d72cb54ac6fe" />
          <placeholder
            name="Required"
            value="True" />
        </control>
        <control
          code="TXT"
          id="99ec89eb-2328-4f1f-8fc8-8d26055ee9bb"
          binding="AAH_Code">
          <placeholder
            name="Required"
            value="True" />
        </control>
        <control
          code="TXT"
          id="4c627b7a-2c94-4786-a177-ad401c2b12f7"
          binding="AAH_Description">
          <placeholder
            name="Required"
            value="True" />
        </control>
        <control
          code="SRC"
          id="7e39e4f4-d39a-4ab4-9bac-09d63e38467e"
          binding="AAH_GC_Company">
          <placeholder
            name="IsReadOnly"
            value="False" />
          <placeholder
            name="Required"
            value="True" />
        </control>
        <control
          code="SRC"
          id="4f6b9e54-0cad-4e74-9141-f006bb6201ed"
          binding="AAH_GB_Branch">
          <placeholder
            name="Required"
            value="True" />
        </control>
        <control
          code="SRC"
          id="ab497b3f-c631-419c-a024-21c72ae250e2"
          binding="AAH_GE_Department">
          <placeholder
            name="Required"
            value="True" />
        </control>
        <control
          code="TXT"
          id="89603f3a-9db3-41b3-9be6-7ee922294e9b"
          binding="AAH_Note" />
      </control>
      <control
        code="BOX"
        id="4bd0cf7e-df01-4e43-8164-07e6d815a786">
        <placeholder
          name="FlexJustify"
          value="`" />
        <control
          code="TBS"
          id="dfb7da95-8cfe-4c99-b339-0baf4f5789d5">
          <control
            code="TAB"
            id="5a127c86-47a2-426f-b183-7b140d3be5e2">
            <placeholder
              name="Caption"
              value="Advanced information"
              resid="70003aab-9c8e-4c7d-bcab-b7826fab151a" />
          </control>
          <control
            code="TAI"
            id="554128f1-ba76-4da5-b2cd-91c21684d178">
            <control
              code="BOX"
              id="d70c970f-bb5e-46d6-95d0-1c9bd55c2561">
              <placeholder
                name="Layout"
                value="flex" />
              <control
                code="PNL"
                id="9a671d78-2c7f-431a-9b9d-670117a23d3a"
                binding="">
                <placeholder
                  name="Caption"
                  value="Asset detail"
                  resid="379d63c6-8e0c-4041-b265-8c56eec1ab25" />
                <placeholder
                  name="Columns"
                  value="col-md-6" />
                <placeholder
                  name="MinWidth"
                  value="300" />
                <placeholder
                  name="MaxWidth"
                  value="300" />
                <control
                  code="DAE"
                  id="89ccf3df-4cc4-4031-a8fc-5ba194e1ba0f"
                  binding="AAH_PurchaseDate" />
                <control
                  code="DAE"
                  id="2d93632e-72a5-436c-94b9-da689cc1a9c8"
                  binding="AAH_ReceivedDate" />
                <control
                  code="DAE"
                  id="a073972c-a52a-4b65-bd36-ff5789d956d0"
                  binding="AAH_SaleDisposalDate" />
                <control
                  code="TXT"
                  id="3421243d-185c-420e-9c68-49505563f2ed"
                  binding="AAH_SerialNumber" />
              </control>
              <control
                code="PNL"
                id="74bab05d-35c7-4605-b908-cdcceeb1fc4e"
                binding="">
                <placeholder
                  name="Columns"
                  value="col-md-6" />
                <placeholder
                  name="MinWidth"
                  value="400" />
                <control
                  code="RDT"
                  id="f135f87c-bc67-48b4-8b26-416bcafd0120"
                  binding="AccAssetGLAccounts">
                  <placeholder
                    name="CaptionOverride"
                    value="Accounts"
                    resid="67bc4a27-c85f-4cb5-a3bd-bcbdaa1d8e38" />
                  <placeholder
                    name="AllowAdd"
                    value="True" />
                  <placeholder
                    name="AllowAttach"
                    value="False" />
                  <placeholder
                    name="AllowDetach"
                    value="False" />
                  <placeholder
                    name="AllowUpdateOnReadonlyParent"
                    value="False" />
                  <placeholder
                    name="FieldConfiguration">
                    <xml>
                      <fields xmlns="">
                        <field
                          path="AAG_GLAccountType"
                          width="100"
                          mode="Mandatory" />
                        <field
                          path="AAG_AG_GLHeader"
                          width="300"
                          mode="Mandatory" />
                      </fields>
                    </xml>
                  </placeholder>
                  <placeholder
                    name="InlineEdit"
                    value="cell" />
                  <placeholder
                    name="ItemsPerPage"
                    value="4" />
                  <placeholder
                    name="HideDefaultFooter"
                    value="False" />
                </control>
                <control
                  code="RDT"
                  id="2f278d93-63d2-421d-864f-1a83807d4b93"
                  binding="AccAssetDepreciationRules">
                  <placeholder
                    name="CaptionOverride"
                    value="Depreciation info"
                    resid="9c432ee8-c3e5-4c26-b2b6-86db605ca638" />
                  <placeholder
                    name="AllowAdd"
                    value="True" />
                  <placeholder
                    name="AllowAttach"
                    value="False" />
                  <placeholder
                    name="AllowDetach"
                    value="False" />
                  <placeholder
                    name="AllowUpdateOnReadonlyParent"
                    value="False" />
                  <placeholder
                    name="FieldConfiguration">
                    <xml>
                      <fields xmlns="">
                        <field
                          path="ADR_DepreciationMethod"
                          width="100"
                          mode="Mandatory" />
                        <field
                          path="ADR_DepreciationPercentage"
                          width="100"
                          mode="Mandatory" />
                        <field
                          path="ADR_DepreciationStartDate"
                          width="200"
                          mode="Mandatory" />
                      </fields>
                    </xml>
                  </placeholder>
                  <placeholder
                    name="InlineEdit"
                    value="cell" />
                  <placeholder
                    name="ItemsPerPage"
                    value="2" />
                  <placeholder
                    name="HideDefaultFooter"
                    value="False" />
                </control>
              </control>
            </control>
          </control>
          <control
            code="TAB"
            id="707c415c-34d8-4c0c-aede-3339a5f5b08c">
            <placeholder
              name="Caption"
              value="Transactions information"
              resid="1109f38c-f360-4d4f-8bd7-4fcee0e69d38" />
          </control>
          <control
            code="TAI"
            id="51cc5068-167a-49a2-b47f-f93e06f72dc3">
            <control
              code="XPS"
              id="5ea4ca74-6965-457a-95e7-10fb592179f7">
              <control
                code="XPA"
                id="0249ca98-03b0-44b4-9356-0fdc815c2e48">
                <control
                  code="XPH"
                  id="3ae761cc-84c2-4fd4-a70a-a9eedcf10f78">
                  <control
                    code="LBL"
                    id="f6b75b0f-99c9-4954-83b1-5a73d4dcb019">
                    <placeholder
                      name="Caption"
                      value="Financial summary in local currency"
                      resid="1e981f31-1293-4ce2-b207-a586bf7fce2b" />
                    <placeholder
                      name="Italic"
                      value="False" />
                    <placeholder
                      name="Typography"
                      value="title-small" />
                  </control>
                </control>
                <control
                  code="XPC"
                  id="681b9450-7214-48fe-b5e9-522f74bb7700">
                  <control
                    code="BOX"
                    id="9b917c64-9c20-498b-80e9-e3f4d687bf4a">
                    <placeholder
                      name="Layout"
                      value="flex" />
                    <control
                      code="DAE"
                      id="1c77497b-1d9f-41bf-ac51-bbad248e32b0"
                      binding="AccumulationEndDate">
                      <placeholder
                        name="CaptionOverride"
                        value="Values up to"
                        resid="d0672582-e041-47e1-be62-ba8c86a62c07" />
                      <placeholder
                        name="Margin"
                        value="mr-8" />
                    </control>
                    <control
                      code="NUM"
                      id="31c55243-f516-4fa4-a017-1730a76c375f"
                      binding="AccumulatedPurchaseAmount">
                      <placeholder
                        name="CaptionOverride"
                        value="Value of asset"
                        resid="501f6b0e-47d9-4796-8afa-1e18fbb1c812" />
                      <placeholder
                        name="IsReadOnly"
                        value="True" />
                      <placeholder
                        name="Margin"
                        value="mr-8" />
                    </control>
                    <control
                      code="NUM"
                      id="974946db-f2d4-4f9c-9d7c-a7929ce17789"
                      binding="AccumulatedDepreciationAmountInAYear">
                      <placeholder
                        name="CaptionOverride"
                        value="Depreciation amount for selected year"
                        resid="0fdedf78-e375-4729-9fd0-76404e6bb596" />
                      <placeholder
                        name="IsReadOnly"
                        value="True" />
                      <placeholder
                        name="Margin"
                        value="mr-8" />
                    </control>
                    <control
                      code="NUM"
                      id="40c875b4-0398-4a64-a6a2-abacab87bdbf"
                      binding="AccumulatedDepreciationAmount">
                      <placeholder
                        name="IsReadOnly"
                        value="True" />
                      <placeholder
                        name="CaptionOverride"
                        value="Accumulated depreciation amount"
                        resid="4c7e17ff-988c-4a2b-99bc-aa060392773f" />
                      <placeholder
                        name="Margin"
                        value="mr-8" />
                    </control>
                    <control
                      code="NUM"
                      id="8ba7ed67-09b4-4367-8b83-df227ed34f78"
                      binding="AccumulatedResidualValue">
                      <placeholder
                        name="CaptionOverride"
                        value="Residual value"
                        resid="6e10fae8-2a49-48ba-9215-e086dab44e92" />
                      <placeholder
                        name="IsReadOnly"
                        value="True" />
                    </control>
                  </control>
                </control>
              </control>
              <control
                code="XPA"
                id="b93f2066-eedc-4623-82b0-21b0bcc9f9ce">
                <control
                  code="XPH"
                  id="0efba8aa-ff7b-478e-8ee2-e6527da4dc59">
                  <control
                    code="LBL"
                    id="141d9d3e-723d-4eb4-bde2-7209a48ea2ac">
                    <placeholder
                      name="Caption"
                      value="Depreciation Schedule"
                      resid="d862e684-d2d9-4e96-9084-0200956e044a" />
                    <placeholder
                      name="Typography"
                      value="title-small" />
                  </control>
                </control>
                <control
                  code="XPC"
                  id="c4677d05-0fae-478e-aac5-39cbb7f94a16">
                  <control
                    code="BOX"
                    id="aabf0793-8a6b-4b79-8123-df29932243a9">
                    <placeholder
                      name="Layout"
                      value="flex" />
                    <control
                      code="LBL"
                      id="5e275cf0-f11a-4639-986c-5266bac8494d">
                      <placeholder
                        name="Caption"
                        value="TODO"
                        resid="84e313db-1813-48a7-920b-7a98e890713f" />
                    </control>
                  </control>
                </control>
              </control>
            </control>
            <control
              code="BOX"
              id="10813978-5899-4696-b11d-b0740973a020">
              <placeholder
                name="Layout"
                value="flex" />
              <placeholder
                name="Columns"
                value="col-2 col-sm-2 col-md-2 col-lg-2 col-xl-2" />
              <placeholder
                name="Padding"
                value="py-3" />
              <control
                code="DVR"
                id="c055e02c-2e34-4728-b9d2-99488011cb2b" />
            </control>
            <control
              code="BOX"
              id="b140ee66-64f6-4484-9555-709cc78a03e0">
              <control
                code="PNL"
                id="ed5e7e12-eacc-46fa-bc24-315cf2b28941">
                <placeholder
                  name="FillAvailable"
                  value="False" />
                <control
                  code="RDT"
                  id="6974de2c-1d5b-4c65-894b-b3af3fae98a9"
                  binding="AccAssetTransactionHeaders">
                  <placeholder
                    name="FieldConfiguration">
                    <xml>
                      <fields xmlns="">
                        <field
                          path="ASH_TransactionNumber"
                          width="250"
                          mode="Mandatory" />
                        <field
                          path="ASH_TransactionType"
                          width="160"
                          mode="Mandatory" />
                        <field
                          path="ASH_Description"
                          width="300"
                          mode="Optional" />
                        <field
                          path="ASH_PostDate"
                          width="300"
                          mode="Optional" />
                        <field
                          path="ASH_LocalAmount"
                          width="200"
                          mode="Default" />
                        <field
                          path="ASH_SupplierReferenceDate"
                          width="300"
                          mode="Optional" />
                        <field
                          path="ASH_SupplierReferenceNumber"
                          width="300"
                          mode="Optional" />
                      </fields>
                    </xml>
                  </placeholder>
                  <placeholder
                    name="ShowFilters"
                    value="True" />
                  <placeholder
                    name="DisabledGridActions"
                    value="Export,Import" />
                  <placeholder
                    name="InlineEdit"
                    value="none" />
                  <placeholder
                    name="ShowCustomize"
                    value="True" />
                  <placeholder
                    name="CaptionOverride"
                    value="Transactions"
                    resid="8918ec4f-8a9a-496d-9e2c-59f28e74c483" />
                  <placeholder
                    name="NewFormFlowConfiguration">
                    <xml>
                      <formFlows xmlns="">
                        <formFlow
                          newSession="True"
                          inDialog="True">683fbcbc658a47e1a71817b318fad541</formFlow>
                        <formFlow
                          newSession="True"
                          inDialog="True">f6728ac09d034cefad116b9b7ef47f1c</formFlow>
                      </formFlows>
                    </xml>
                  </placeholder>
                  <placeholder
                    name="ActionMenuItems">
                    <xml>
                      <formFlows xmlns="">
                        <formFlow
                          newSession="True"
                          inDialog="True">59a96e52434c47cbb05e12df2e028d0a</formFlow>
                      </formFlows>
                    </xml>
                  </placeholder>
                  <placeholder
                    name="EditFormFlowConfiguration">
                    <xml>
                      <formFlows xmlns="">
                        <formFlow
                          newSession="True"
                          inDialog="True">59a96e52434c47cbb05e12df2e028d0a</formFlow>
                      </formFlows>
                    </xml>
                  </placeholder>
                  <placeholder
                    name="DisabledGridRowActions"
                    value="Activation,Remove" />
                </control>
              </control>
            </control>
          </control>
          <control
            code="TAB"
            id="ebc196aa-5640-4e5c-88fe-18ba45302844">
            <placeholder
              name="Caption"
              value="Event Log"
              resid="765e6703-49bc-427b-8065-c822c8eeec25" />
          </control>
          <control
            code="TAI"
            id="a48a1c0d-8068-4b04-87cf-3bc9afd1486e">
            <control
              code="RDT"
              id="092cf0d0-7e94-4295-9ed7-d572b6e32659"
              binding="Logs">
              <placeholder
                name="CaptionOverride"
                value="Header events"
                resid="7f07abbb-73a3-4b86-8b0a-30a1f7fb3700" />
              <placeholder
                name="ShowFilters"
                value="True" />
              <placeholder
                name="AllowAttach"
                value="False" />
              <placeholder
                name="AllowDetach"
                value="False" />
              <placeholder
                name="IsReadOnly"
                value="True" />
              <placeholder
                name="HideItemActions"
                value="True" />
              <placeholder
                name="HideImport"
                value="True" />
              <placeholder
                name="ShowCustomize"
                value="True" />
              <placeholder
                name="InlineEdit"
                value="none" />
              <placeholder
                name="FieldConfiguration">
                <xml>
                  <fields xmlns="">
                    <field
                      path="SL_SE_NKEvent"
                      width="50"
                      mode="Mandatory" />
                    <field
                      path="Event.SE_Desc"
                      width="100"
                      mode="Default" />
                    <field
                      path="SL_EventTime"
                      width="180"
                      mode="Mandatory" />
                    <field
                      path="SL_GS_NKUser"
                      width="50"
                      mode="Mandatory" />
                    <field
                      path="User.GS_FullName"
                      width="200"
                      mode="Default" />
                    <field
                      path="EventDetails"
                      width="250"
                      mode="Optional" />
                    <field
                      path="SL_EventTimeUtc"
                      width="180"
                      mode="Optional" />
                    <field
                      path="SL_PostedTimeUtc"
                      width="180"
                      mode="Optional" />
                    <field
                      path="SL_GB_NKBranch"
                      width="60"
                      mode="Optional" />
                    <field
                      path="SL_GE_NKDepartment"
                      width="100"
                      mode="Optional" />
                    <field
                      path="SL_DataSource"
                      width="110"
                      mode="Optional" />
                  </fields>
                </xml>
              </placeholder>
            </control>
            <control
              code="RDT"
              id="483739a4-dd48-4ac6-871e-************"
              binding="AccAssetGLAccounts/Logs">
              <placeholder
                name="CaptionOverride"
                value="Accounts events"
                resid="1792c563-84bc-479c-9891-575e7eeffdd4" />
              <placeholder
                name="ShowFilters"
                value="True" />
              <placeholder
                name="AllowAttach"
                value="False" />
              <placeholder
                name="AllowDetach"
                value="False" />
              <placeholder
                name="IsReadOnly"
                value="True" />
              <placeholder
                name="HideItemActions"
                value="True" />
              <placeholder
                name="HideImport"
                value="True" />
              <placeholder
                name="ShowCustomize"
                value="True" />
              <placeholder
                name="InlineEdit"
                value="none" />
              <placeholder
                name="FieldConfiguration">
                <xml>
                  <fields xmlns="">
                    <field
                      path="SL_SE_NKEvent"
                      width="50"
                      mode="Mandatory" />
                    <field
                      path="Event.SE_Desc"
                      width="100"
                      mode="Default" />
                    <field
                      path="SL_EventTime"
                      width="180"
                      mode="Mandatory" />
                    <field
                      path="SL_GS_NKUser"
                      width="50"
                      mode="Mandatory" />
                    <field
                      path="User.GS_FullName"
                      width="200"
                      mode="Default" />
                    <field
                      path="EventDetails"
                      width="250"
                      mode="Optional" />
                    <field
                      path="SL_EventTimeUtc"
                      width="180"
                      mode="Optional" />
                    <field
                      path="SL_PostedTimeUtc"
                      width="180"
                      mode="Optional" />
                    <field
                      path="SL_GB_NKBranch"
                      width="60"
                      mode="Optional" />
                    <field
                      path="SL_GE_NKDepartment"
                      width="100"
                      mode="Optional" />
                    <field
                      path="SL_DataSource"
                      width="110"
                      mode="Optional" />
                  </fields>
                </xml>
              </placeholder>
            </control>
            <control
              code="RDT"
              id="dda37a2b-c98b-4d26-a4f9-7001453a9743"
              binding="AccAssetDepreciationRules/Logs">
              <placeholder
                name="CaptionOverride"
                value="Depreciation events"
                resid="dd7863ef-0f15-4649-939e-e3041f7b0f25" />
              <placeholder
                name="ShowFilters"
                value="True" />
              <placeholder
                name="AllowAttach"
                value="False" />
              <placeholder
                name="AllowDetach"
                value="False" />
              <placeholder
                name="IsReadOnly"
                value="True" />
              <placeholder
                name="HideItemActions"
                value="True" />
              <placeholder
                name="HideImport"
                value="True" />
              <placeholder
                name="ShowCustomize"
                value="True" />
              <placeholder
                name="InlineEdit"
                value="none" />
              <placeholder
                name="FieldConfiguration">
                <xml>
                  <fields xmlns="">
                    <field
                      path="SL_SE_NKEvent"
                      width="50"
                      mode="Mandatory" />
                    <field
                      path="Event.SE_Desc"
                      width="100"
                      mode="Default" />
                    <field
                      path="SL_EventTime"
                      width="180"
                      mode="Mandatory" />
                    <field
                      path="SL_GS_NKUser"
                      width="50"
                      mode="Mandatory" />
                    <field
                      path="User.GS_FullName"
                      width="200"
                      mode="Default" />
                    <field
                      path="EventDetails"
                      width="250"
                      mode="Optional" />
                    <field
                      path="SL_EventTimeUtc"
                      width="180"
                      mode="Optional" />
                    <field
                      path="SL_PostedTimeUtc"
                      width="180"
                      mode="Optional" />
                    <field
                      path="SL_GB_NKBranch"
                      width="60"
                      mode="Optional" />
                    <field
                      path="SL_GE_NKDepartment"
                      width="100"
                      mode="Optional" />
                    <field
                      path="SL_DataSource"
                      width="110"
                      mode="Optional" />
                  </fields>
                </xml>
              </placeholder>
            </control>
          </control>
        </control>
      </control>
    </control>
  </form>
