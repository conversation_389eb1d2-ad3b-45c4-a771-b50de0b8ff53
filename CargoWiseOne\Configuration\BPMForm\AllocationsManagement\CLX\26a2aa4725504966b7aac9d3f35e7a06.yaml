#transformationVersion: 70.0
#
#G1 Don't delete this file, used instead of 3cf841c21c064281bda33ccd6e33b945
VZ_PK: 26a2aa4725504966b7aac9d3f35e7a06
VZ_ConfigurationKey: 26a2aa47-2550-4966-b7aa-c9d3f35e7a06
VZ_FormID: CLX - Client Contracts Web Search and Import
VZ_Caption:
  resKey: VZ_Caption|26a2aa47-2550-4966-b7aa-c9d3f35e7a06
  text: Client Contracts
VZ_FormFactor: DSK
VZ_EntityType: IClientContract
VZ_FormType: PAG
VZ_RequiresLogin: true
VZ_ExtenderFunc: CargoWise.Glow.Business.Forwarding.Contracts.CWPopupWindow.ClientContractLookupFormExtender.extendClientContractLookup
VZ_Dependencies: >-
  <dependencies
    xmlns="http://cargowise.com/glow/2014/11/14/dependencies.xsd" />
VZ_FormData: >-
  <form
    id="a87d4723-4a04-4bde-beef-bd0fbb25eeb8"
    xmlns="http://wisetechglobal.com/glow/2017/05/26/form.xsd">
    <placeholder
      name="BackgroundImagePk"
      value="" />
    <placeholder
      name="BackgroundSize"
      value="Auto" />
    <placeholder
      name="BackgroundImageOverride"
      value="" />
    <placeholder
      name="FormLayout"
      value="Splitview" />
    <placeholder
      name="ExpandPaths"
      value="" />
    <placeholder
      name="ScanInputHandling"
      value="NON" />
    <placeholder
      name="RemovePadding"
      value="false" />
    <placeholder
      name="TurnOnPortableOneToOneDesignMode"
      value="false" />
    <placeholder
      name="ContainsDefaultButton"
      value="False" />
    <control
      code="SRL"
      id="3aa296bd-3bda-44a9-9fc4-527b4a9b7876"
      left="1"
      top="1"
      right="1"
      bottom="1">
      <placeholder
        name="CanBeHidden"
        value="True" />
      <placeholder
        name="IsInitialFocus"
        value="False" />
      <placeholder
        name="CaptionOverride"
        value="Client Contracts"
        resid="778d8a23-92d0-42ef-bb27-91b3cca41ae7" />
      <placeholder
        name="VisibilityCondition"
        value="" />
      <placeholder
        name="HeaderAlignment"
        value="Left" />
      <placeholder
        name="HeaderDisplayMode"
        value="Visible" />
      <placeholder
        name="FilterType"
        value="Entity" />
      <placeholder
        name="EntityType"
        value="IClientContract" />
      <placeholder
        name="ItemDisplayPath"
        value="" />
      <placeholder
        name="DisabledGridRowActions"
        value="eDocs,Notes,Documents,Workflow,Tracking,Activation,Remove,Messages" />
      <placeholder
        name="DefaultFilter"
        value="" />
      <placeholder
        name="DefaultFilterPropertyPath"
        value="" />
      <placeholder
        name="FieldConfiguration"
        value="" />
      <placeholder
        name="DefaultSortFields"
        value="" />
      <placeholder
        name="DefaultGroupFields"
        value="" />
      <placeholder
        name="EnableGrouping"
        value="False" />
      <placeholder
        name="AutoRefreshInterval"
        value="0" />
      <placeholder
        name="AllowMultiSelect"
        value="False" />
      <placeholder
        name="ShowWorkflowLabel"
        value="False" />
      <placeholder
        name="RowActionsMode"
        value="None" />
      <placeholder
        name="HideGridActions"
        value="True" />
      <placeholder
        name="MinimizeFilters"
        value="False" />
      <placeholder
        name="WorkflowOpenInNewWindow"
        value="False" />
      <placeholder
        name="SearchOnInit"
        value="False" />
      <placeholder
        name="ShowQuickSearch"
        value="False" />
      <placeholder
        name="QuickSearchCaption"
        value="" />
      <placeholder
        name="ValueField"
        value="" />
      <placeholder
        name="Category"
        value="" />
      <placeholder
        name="EditFormFlow"
        value="" />
      <placeholder
        name="ResetFilterOnInit"
        value="False" />
      <placeholder
        name="FilterDisplayMode"
        value="InlineOnly" />
    </control>
  </form>
