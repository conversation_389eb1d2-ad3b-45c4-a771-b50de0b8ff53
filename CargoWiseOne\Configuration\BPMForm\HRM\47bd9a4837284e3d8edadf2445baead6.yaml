#transformationVersion: 70.0
#
VZ_PK: 47bd9a4837284e3d8edadf2445baead6
VZ_ConfigurationKey: 47bd9a48-3728-4e3d-8eda-df2445baead6
VZ_FormID: HRM - Bulk Import - Roles
VZ_Caption:
  resKey: VZ_Caption|47bd9a48-3728-4e3d-8eda-df2445baead6
  text: Bulk Import/Export - Roles
VZ_FormFactor: DSK
VZ_FormType: PAG
VZ_RequiresLogin: true
VZ_MaterialDesign: true
VZ_Dependencies: <dependencies xmlns="http://cargowise.com/glow/2014/11/14/dependencies.xsd" />
VZ_FormData: >-
  <form
    id="52ea24de-8b0d-4f2c-942b-74145d54eb9a" xmlns="http://wisetechglobal.com/glow/2017/05/26/form.xsd">
    <placeholder
      name="Layout"
      value="fill" />
    <control
      code="SDT"
      id="2a06eee9-3750-4e61-9429-36d4bfe64564"
      binding="">
      <placeholder
        name="CaptionOverride"
        value="Roles"
        resid="0cc0def4-122d-43aa-a738-b028fa1a4e79" />
      <placeholder
        name="EntityType"
        value="IGlbEmploymentHistory" />
      <placeholder
        name="FullPage"
        value="True" />
      <placeholder
        name="SearchMode"
        value="Entity" />
      <placeholder
        name="DefaultFilter">
        <xml>
          <ArrayOfFilterGroup xmlns:a="http://schemas.microsoft.com/2003/10/Serialization/Arrays" xmlns:i="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://cargowise.com/glow/2014/07/16/filters.xsd">
            <FilterGroup>
              <Filters>
                <Filter>
                  <FilterType>IsActiveFilter</FilterType>
                  <Operation>Is</Operation>
                  <PropertyPath>Staff.GS_IsActive</PropertyPath>
                  <Values>
                    <a:string>true</a:string>
                  </Values>
                </Filter>
                <Filter>
                  <FilterType>StringFilter</FilterType>
                  <Operation>StartsWith</Operation>
                  <PropertyPath>Staff.GS_Code</PropertyPath>
                  <Values />
                </Filter>
                <Filter>
                  <FilterType>StringFilter</FilterType>
                  <Operation>Contains</Operation>
                  <PropertyPath>Staff.StaffName</PropertyPath>
                  <Values />
                </Filter>
              </Filters>
              <IsImplicit>false</IsImplicit>
            </FilterGroup>
            <FilterGroup>
              <Filters>
                <Filter>
                  <FilterType>BoolFilter</FilterType>
                  <Operation>Is</Operation>
                  <PropertyPath>GEH_IsApproved</PropertyPath>
                  <Values>
                    <a:string>true</a:string>
                  </Values>
                </Filter>
              </Filters>
              <IsImplicit>true</IsImplicit>
            </FilterGroup>
          </ArrayOfFilterGroup>
        </xml>
      </placeholder>
      <placeholder
        name="DefaultSortFields">
        <xml>
          <ArrayOfFieldSortDefinition xmlns:i="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://schemas.datacontract.org/2004/07/CargoWise.Glow.Infrastructure.Interfaces">
            <FieldSortDefinition>
              <FieldName>Staff.GS_FullName</FieldName>
              <IsAscending>true</IsAscending>
            </FieldSortDefinition>
          </ArrayOfFieldSortDefinition>
        </xml>
      </placeholder>
      <placeholder
        name="FieldConfiguration">
        <xml>
          <fields xmlns="">
            <field
              path="Staff.GS_Code"
              width="160"
              mode="Default" />
            <field
              path="Staff.GS_FullName"
              width="200"
              mode="Default" />
            <field
              path="EffectiveDate"
              width="120"
              mode="Default" />
            <field
              path="GEH_JobTitle"
              width="200"
              mode="Default" />
            <field
              path="GEH_JobFamily"
              width="200"
              mode="Default" />
            <field
              path="GEH_DepartureReason"
              width="200"
              mode="Default" />
            <field
              path="GEH_DepartureComments"
              width="300"
              mode="Default" />
            <field
              path="GEH_CompanyName"
              width="100"
              mode="Optional" />
            <field
              path="GEH_EmploymentType"
              width="100"
              mode="Optional" />
            <field
              path="GEH_GS_Staff"
              width="100"
              mode="FilterOnly" />
            <field
              path="GEH_JobDescription"
              width="160"
              mode="Optional" />
            <field
              path="GEH_SystemCreateTimeUtc"
              width="120"
              mode="Optional" />
            <field
              path="GEH_SystemCreateUser"
              width="100"
              mode="FilterOnly" />
            <field
              path="GEH_SystemLastEditTimeUtc"
              width="120"
              mode="Optional" />
            <field
              path="GEH_SystemLastEditUser"
              width="100"
              mode="FilterOnly" />
            <field
              path="GEH_AutoEffectiveEndDate"
              width="120"
              mode="Optional" />
            <field
              path="GEH_EffectiveDate"
              width="120"
              mode="Optional" />
            <field
              path="CreatedByStaff.GS_Code"
              width="200"
              mode="Optional" />
            <field
              path="CreatedByStaff.GS_FullName"
              width="200"
              mode="Optional" />
            <field
              path="LastEditedByStaff.GS_Code"
              width="200"
              mode="Optional" />
            <field
              path="LastEditedByStaff.GS_FullName"
              width="200"
              mode="Optional" />
            <field
              path="Staff.GS_IsActive"
              width="70"
              mode="FilterOnly" />
            <field
              path="Staff.StaffName"
              width="250"
              mode="FilterOnly" />
            <field
              path="Staff.CurrentTeam.TeamNameDescription"
              width="250"
              mode="Optional" />
            <field
              path="Staff.CurrentTeam.Team.GST_Code"
              width="250"
              mode="Optional" />
            <field
              path="Staff.GS_EmploymentBasis"
              width="250"
              mode="Optional" />
            <field
              path="Staff.CurrentRemuneration.Country.RN_Desc"
              width="250"
              mode="Optional" />
            <field
              path="Staff.CurrentRemuneration.Country.RN_Code"
              width="120"
              mode="Optional" />
            <field
              path="Staff.CurrentRemuneration.Currency.RX_Code"
              width="120"
              mode="Optional" />
            <field
              path="Staff.CurrentRemuneration.Currency.RX_Desc"
              width="250"
              mode="Optional" />
            <field
              path="Staff.CurrentBeneficiaryEntity.Branch.GB_Code"
              width="120"
              mode="Optional" />
            <field
              path="Staff.CurrentBeneficiaryEntity.Branch.GB_BranchName"
              width="250"
              mode="Optional" />
            <field
              path="Staff.CurrentBeneficiaryEntity.Branch.GlbCompany.GC_Code"
              width="120"
              mode="Optional" />
            <field
              path="Staff.CurrentBeneficiaryEntity.Branch.GlbCompany.GC_Name"
              width="250"
              mode="Optional" />
            <field
              path="Staff.CurrentEmployingEntity.Branch.GB_BranchName"
              width="250"
              mode="Optional" />
            <field
              path="Staff.CurrentEmployingEntity.Branch.GB_Code"
              width="120"
              mode="Optional" />
            <field
              path="Staff.CurrentEmployingEntity.Branch.GlbCompany.GC_Code"
              width="120"
              mode="Optional" />
            <field
              path="Staff.CurrentEmployingEntity.Branch.GlbCompany.GC_Name"
              width="250"
              mode="Optional" />
            <field
              path="Staff.HomeBranch.GB_BranchName"
              width="250"
              mode="Optional" />
            <field
              path="Staff.HomeBranch.GB_Code"
              width="250"
              mode="Optional" />
            <field
              path="Staff.GS_GB_HomeBranch"
              width="250"
              mode="FilterOnly" />
            <field
              path="Staff.GS_GE_HomeDepartment"
              width="250"
              mode="FilterOnly" />
            <field
              path="Staff.HomeDepartment.GE_Code"
              width="120"
              mode="Optional" />
            <field
              path="Staff.HomeDepartment.GE_Desc"
              width="250"
              mode="Optional" />
            <field
              path="GEH_IsApproved"
              width="110"
              mode="FilterOnly" />
            <field
              path="IGlbEmploymentHistory_Filter_CurrentOn"
              width="100"
              mode="FilterOnly" />
            <field
              path="IGlbEmploymentHistory_Filter_CurrentOn_IgnoreTimezone"
              width="250"
              mode="FilterOnly" />
            <field
              path="Staff.CurrentWorkingBasis.WorkingBasisCode"
              width="250"
              mode="Optional" />
          </fields>
        </xml>
      </placeholder>
    </control>
  </form>
