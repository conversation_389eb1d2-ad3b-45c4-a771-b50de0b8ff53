#transformationVersion: 70.0
#
VZ_PK: 07cd26808d254aa8b9553d7b116d6f14
VZ_ConfigurationKey: 07cd2680-8d25-4aa8-b955-3d7b116d6f14
VZ_FormID: ETL - VDV3 - Scan Outer Package Page
VZ_Caption:
  resKey: VZ_Caption|07cd2680-8d25-4aa8-b955-3d7b116d6f14
  text: Scan Outer Package
VZ_FormFactor: DSK
VZ_FormType: PAG
VZ_RequiresLogin: true
VZ_Dependencies: <dependencies xmlns="http://cargowise.com/glow/2014/11/14/dependencies.xsd" />
VZ_FormData: >-
  <form
    id="78b01ced-df93-4581-b206-19ed66e8b616" xmlns="http://wisetechglobal.com/glow/2017/05/26/form.xsd">
    <placeholder
      name="BackgroundImagePk"
      value="" />
    <placeholder
      name="BackgroundSize"
      value="Auto" />
    <placeholder
      name="BackgroundImageOverride"
      value="" />
    <placeholder
      name="FormLayout"
      value="Splitview" />
    <placeholder
      name="ExpandPaths"
      value="" />
    <placeholder
      name="ScanInputHandling"
      value="NON" />
    <placeholder
      name="RemovePadding"
      value="false" />
    <placeholder
      name="TurnOnPortableOneToOneDesignMode"
      value="false" />
    <placeholder
      name="ContainsDefaultButton"
      value="False" />
    <control
      code="GRP"
      id="75e6b73d-475e-440f-94a6-a63dc6ab5727"
      width="26"
      height="14">
      <placeholder
        name="CanBeHidden"
        value="True" />
      <placeholder
        name="VisibilityCondition"
        value="" />
      <placeholder
        name="Header"
        value="New Section"
        resid="f3cc797e-9e2e-4c15-bdd7-9d71085ff4fb" />
      <placeholder
        name="HeaderAlignment"
        value="Left" />
      <placeholder
        name="HeaderDisplayMode"
        value="Hidden" />
      <placeholder
        name="BackgroundImagePk"
        value="" />
      <placeholder
        name="BackgroundImageOverride"
        value="" />
      <control
        code="FLB"
        id="f7e30c07-9eb7-4948-81bc-078158e82b9f"
        left="9"
        top="11"
        width="8"
        height="1">
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="LabelText"
          value="Please Scan Next Outer Package"
          resid="fd6bb8bc-e56e-4fb3-9a22-b7ea1a6b153a" />
        <placeholder
          name="LabelType"
          value="NormalLabel" />
        <placeholder
          name="LabelNotificationType"
          value="Information" />
        <placeholder
          name="ForegroundThemeColor"
          value="DefaultButton" />
        <placeholder
          name="TextSize"
          value="Medium" />
        <placeholder
          name="FontWeight"
          value="Bold" />
        <placeholder
          name="LabelAlignment"
          value="Center" />
        <placeholder
          name="BackgroundColor"
          value="" />
      </control>
      <control
        code="TBT"
        id="1205cd8c-5082-49ec-a816-5d4bd798393c"
        left="9"
        top="12"
        width="8"
        height="1">
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="Content"
          value="Create New Outer Package"
          resid="76093cb3-c86f-4001-a986-71947376c6f3" />
        <placeholder
          name="IsDefault"
          value="False" />
      </control>
      <control
        code="FFI"
        id="d4090e5d-8eaa-4557-89f5-2046588194b7"
        width="9"
        height="1"
        bottom="3">
        <placeholder
          name="CanBeHidden"
          value="False" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="Outer Package Barcode"
          resid="fce834e0-4039-4743-b23e-70bad09a7759" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="EntityType"
          value="IHVLVOuterPackage" />
        <placeholder
          name="DefaultFilter">
          <xml>
            <ArrayOfFilterGroup xmlns:a="http://schemas.microsoft.com/2003/10/Serialization/Arrays" xmlns:i="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://cargowise.com/glow/2014/07/16/filters.xsd">
              <FilterGroup>
                <Filters>
                  <Filter>
                    <FilterType>SimpleLookupFilter</FilterType>
                    <Operation>Is</Operation>
                    <PropertyPath>HVO_Status</PropertyPath>
                    <Values>
                      <a:string>OPN</a:string>
                    </Values>
                  </Filter>
                </Filters>
                <IsImplicit>true</IsImplicit>
              </FilterGroup>
            </ArrayOfFilterGroup>
          </xml>
        </placeholder>
        <placeholder
          name="ItemTemplateID"
          value="" />
        <placeholder
          name="FormFlowPK"
          value="" />
        <placeholder
          name="ItemPosition"
          value="Top" />
      </control>
    </control>
  </form>
