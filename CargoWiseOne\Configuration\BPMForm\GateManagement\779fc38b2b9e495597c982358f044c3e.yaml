#transformationVersion: 70.0
#
VZ_PK: 779fc38b2b9e495597c982358f044c3e
VZ_ConfigurationKey: 779fc38b-2b9e-4955-97c9-82358f044c3e
VZ_FormID: GDM - Completed Movement
VZ_Caption:
  resKey: VZ_Caption|779fc38b2b9e495597c982358f044c3e
  text: Completed movement
VZ_FormFactor: DSK
VZ_EntityType: IGteVehicleMovement
VZ_FormType: FRM
VZ_RequiresLogin: true
VZ_MaterialDesign: true
VZ_Dependencies: <dependencies xmlns="http://cargowise.com/glow/2014/11/14/dependencies.xsd" />
VZ_StatusBindingPath: Status
VZ_EntityStatusDisplayMode: ReadOnly
VZ_FormData: >-
  <form
    id="b422810d-07c9-4789-977b-03ceae0ac055" xmlns="http://wisetechglobal.com/glow/2017/05/26/form.xsd">
    <placeholder
      name="Align"
      value="left" />
    <placeholder
      name="Layout"
      value="flex" />
    <placeholder
      name="FlexDirection"
      value="flex-column" />
    <control
      code="BOX"
      id="fd3bfbe1-bf9a-4d2f-836f-032125e8d802"
      binding="">
      <placeholder
        name="Layout"
        value="grid" />
      <placeholder
        name="Margin"
        value="mt-2" />
      <placeholder
        name="Height"
        value="100%" />
      <control
        code="BOX"
        id="4e26d3e3-ee99-4d43-a04d-4349bf6b093f"
        binding="">
        <placeholder
          name="Height"
          value="100%" />
        <placeholder
          name="Margin"
          value="mr-4" />
        <placeholder
          name="Columns"
          value="col-3" />
        <control
          code="PNL"
          id="7ab8d565-9577-4eb6-839a-4934d025d393"
          binding="">
          <placeholder
            name="Caption"
            value="Transport details"
            resid="4f46f0b2-d742-4492-9ed0-b9f2a2682d8b" />
          <control
            code="BOX"
            id="dae81f7c-cfe8-42a4-882d-9837025b7776">
            <placeholder
              name="Layout"
              value="grid" />
            <placeholder
              name="Margin"
              value="mb-2" />
            <control
              code="TXT"
              id="ae5b5485-bd8b-4b23-9a2c-60c7f07fbcbf"
              binding="VehicleRegistrationForBinding">
              <placeholder
                name="Columns"
                value="col-6" />
              <placeholder
                name="IsReadOnly"
                value="True" />
              <placeholder
                name="Margin"
                value="mb-2" />
              <placeholder
                name="CaptionOverride"
                value="Vehicle registration"
                resid="497bf0ad-0497-43bc-91f7-60018d0763e0" />
            </control>
            <control
              code="SRC"
              id="5fd047ff-285e-49dc-8742-5341279c6e0e"
              binding="GVM_RC_VehicleType">
              <placeholder
                name="Columns"
                value="col-6" />
              <placeholder
                name="IsReadOnly"
                value="True" />
              <placeholder
                name="Margin"
                value="mb-2" />
              <placeholder
                name="CaptionOverride"
                value="Vehicle type"
                resid="9be2bf4a-a94c-4dd3-94cf-f3dcda6e5e9c" />
            </control>
            <control
              code="TXT"
              id="8c0d5fec-054d-4aed-8f52-db7037b1a812"
              binding="TransportCompany.OH_Code">
              <placeholder
                name="CaptionOverride"
                value="Transport provider"
                resid="9ee65cca-60eb-421b-abb2-f723b2ed29f1" />
              <placeholder
                name="IsReadOnly"
                value="True" />
              <placeholder
                name="Margin"
                value="mb-2" />
            </control>
            <control
              code="TXT"
              id="0c987200-1db4-4340-8815-ef31907a4d06"
              binding="MainBooking.GBK_ReferenceNumber">
              <placeholder
                name="Columns"
                value="col-6" />
              <placeholder
                name="IsReadOnly"
                value="True" />
              <placeholder
                name="Margin"
                value="mb-2" />
              <placeholder
                name="CaptionOverride"
                value="Gate booking"
                resid="8dd54500-f2fb-4387-b2be-2122c41ba2b3" />
            </control>
            <control
              code="CMB"
              id="feda3763-f009-4cc5-92e2-32281b5e2c3f"
              binding="MainBooking.GBK_BookingType">
              <placeholder
                name="Columns"
                value="col-6" />
              <placeholder
                name="IsReadOnly"
                value="True" />
              <placeholder
                name="Margin"
                value="mb-2" />
              <placeholder
                name="CaptionOverride"
                value="Booking type"
                resid="a226f4de-76c3-4e65-ae33-3f510834a81d" />
            </control>
            <control
              code="TXA"
              id="e5d49a46-693b-452d-842e-23702a867216"
              binding="GateReferenceCheckGateOut">
              <placeholder
                name="CaptionOverride"
                value="Gate reference check"
                resid="94150ba7-3fbd-41f7-8623-4abab7885a3c" />
              <placeholder
                name="Rows"
                value="3" />
              <placeholder
                name="Margin"
                value="mb-2" />
              <placeholder
                name="IsReadOnly"
                value="True" />
            </control>
            <control
              code="SRC"
              id="b97bdc1b-9109-4979-a826-09aacb4d2416"
              binding="MainBooking.GBK_WW_Facility">
              <placeholder
                name="IsReadOnly"
                value="True" />
              <placeholder
                name="CaptionOverride"
                value="Facility"
                resid="1a5c55ef-ca26-4d2b-8855-461d7ded4d2f" />
              <placeholder
                name="Columns"
                value="col-6" />
              <placeholder
                name="Margin"
                value="mb-2" />
            </control>
            <control
              code="SRC"
              id="edc805be-0efc-4c59-a260-f0edf6179230"
              binding="GVM_WL_Location">
              <placeholder
                name="IsReadOnly"
                value="True" />
              <placeholder
                name="Columns"
                value="col-6" />
              <placeholder
                name="Margin"
                value="mb-2" />
            </control>
            <control
              code="DVR"
              id="df8cb8c8-1ff3-44fc-b3d9-9c4fd00367d1">
              <placeholder
                name="Variant"
                value="solid" />
              <placeholder
                name="Margin"
                value="mb-2" />
            </control>
            <control
              code="BOX"
              id="140b088c-ad29-492c-b636-c7bf13556f7d">
              <placeholder
                name="Columns"
                value="col-6" />
              <control
                code="TXT"
                id="8411e29f-fa65-4e4c-bf21-086c007adfc4"
                binding="GateInVehicleEntry.GVE_GateActionNumber">
                <placeholder
                  name="Columns"
                  value="col-6" />
                <placeholder
                  name="IsReadOnly"
                  value="True" />
                <placeholder
                  name="Margin"
                  value="mb-2" />
                <placeholder
                  name="CaptionOverride"
                  value="Gate in number"
                  resid="3b62b692-77ae-43d3-a697-a15b319698e1" />
              </control>
              <control
                code="SRC"
                id="c12d8ca3-462b-4a81-b959-69072b6c0c3a"
                binding="GateInVehicleEntry.GVE_SystemCreateUser">
                <placeholder
                  name="CaptionOverride"
                  value="Entry gate operator"
                  resid="98686b04-ed97-4fbe-8cd7-fec074d32451" />
                <placeholder
                  name="Columns"
                  value="col-6" />
                <placeholder
                  name="Margin"
                  value="mb-2" />
                <placeholder
                  name="IsReadOnly"
                  value="True" />
              </control>
              <control
                code="TXT"
                id="cf36b8b2-3152-4e4b-802d-cb0e97f862ac"
                binding="GateInVehicleEntry.GVE_DriverName">
                <placeholder
                  name="Columns"
                  value="col-6" />
                <placeholder
                  name="CaptionOverride"
                  value="Entry driver name"
                  resid="3cc902d7-e169-444d-bbd9-ed5785aee362" />
                <placeholder
                  name="Margin"
                  value="mb-2" />
                <placeholder
                  name="IsReadOnly"
                  value="True" />
              </control>
              <control
                code="TXT"
                id="ce5ecdb1-f2e3-45e4-9177-a03401d6d7a6"
                binding="GateInVehicleEntry.GVE_DriverLicenseNumber">
                <placeholder
                  name="Columns"
                  value="col-6" />
                <placeholder
                  name="CaptionOverride"
                  value="Entry driver license"
                  resid="df72ae68-f6c1-4ce7-9228-bcf5afcb5e7d" />
                <placeholder
                  name="Margin"
                  value="mb-2" />
                <placeholder
                  name="IsReadOnly"
                  value="True" />
              </control>
              <control
                code="DTE"
                id="bf7c9c5d-4ca8-4936-91a8-c8cb64c1260e"
                binding="GateInVehicleEntry.GVE_EntryTime">
                <placeholder
                  name="Columns"
                  value="col-6" />
                <placeholder
                  name="CaptionOverride"
                  value="Entry date and time"
                  resid="b2882538-30fb-49d4-b164-afaabcf24051" />
                <placeholder
                  name="Margin"
                  value="mb-2" />
                <placeholder
                  name="IsReadOnly"
                  value="True" />
              </control>
              <control
                code="MHS"
                id="7d2f02bb-76d1-4d87-b65f-a86b9a63ffa7"
                binding="GateInVehicleEntry.GVE_WeightMeasure">
                <placeholder
                  name="Columns"
                  value="col-6" />
                <placeholder
                  name="CaptionOverride"
                  value="Entry weight"
                  resid="b8ece72a-34b9-4b36-beaa-383750e8aa2e" />
                <placeholder
                  name="Margin"
                  value="mb-2" />
                <placeholder
                  name="IsReadOnly"
                  value="True" />
              </control>
              <control
                code="SRC"
                id="75a4a36e-598e-4c44-8d47-3cd1353fdb00"
                binding="GateInVehicleEntry.Lane.GLN_GTE_Gate">
                <placeholder
                  name="CaptionOverride"
                  value="Entry gate"
                  resid="34ce9de4-1971-4d5e-ad80-08c3ff629529" />
                <placeholder
                  name="Columns"
                  value="col-6" />
                <placeholder
                  name="IsReadOnly"
                  value="True" />
                <placeholder
                  name="Margin"
                  value="mb-2" />
              </control>
              <control
                code="SRC"
                id="9fc1b0de-4149-4f72-b1c5-a1c26af6b941"
                binding="GateInVehicleEntry.GVE_GLN_Lane">
                <placeholder
                  name="Columns"
                  value="col-6" />
                <placeholder
                  name="CaptionOverride"
                  value="Entry lane"
                  resid="4b0f1271-0f22-4f1f-94ff-3bffef39931a" />
                <placeholder
                  name="Margin"
                  value="mb-2" />
                <placeholder
                  name="IsReadOnly"
                  value="True" />
              </control>
            </control>
            <control
              code="BOX"
              id="b3f2f11c-ccf4-4d2c-bcba-0567a4156e5a">
              <placeholder
                name="Columns"
                value="col-6" />
              <control
                code="TXT"
                id="54548f45-273b-42ab-883b-e4db568462b2"
                binding="GateOutVehicleEntry.GVE_GateActionNumber">
                <placeholder
                  name="Columns"
                  value="col-6" />
                <placeholder
                  name="IsReadOnly"
                  value="True" />
                <placeholder
                  name="Margin"
                  value="mb-2" />
                <placeholder
                  name="CaptionOverride"
                  value="Gate out number"
                  resid="c299cae1-129d-4f8e-bad9-13f423016bc0" />
              </control>
              <control
                code="SRC"
                id="a3c1f9e1-35d6-4d22-87b6-2fdc4e03cb0e"
                binding="GateOutVehicleEntry.GVE_SystemCreateUser">
                <placeholder
                  name="IsReadOnly"
                  value="True" />
                <placeholder
                  name="CaptionOverride"
                  value="Exit gate operator"
                  resid="e072b58d-a2dd-48d5-af54-534b6e22498b" />
                <placeholder
                  name="Columns"
                  value="col-6" />
                <placeholder
                  name="Margin"
                  value="mb-2" />
              </control>
              <control
                code="TXT"
                id="e187eaac-37d1-49e3-9f39-b061dd3a3533"
                binding="GateOutVehicleEntry.GVE_DriverName">
                <placeholder
                  name="Columns"
                  value="col-6" />
                <placeholder
                  name="CaptionOverride"
                  value="Exit driver name"
                  resid="b16a0e41-b26e-46f2-85e7-b18e2dc391e1" />
                <placeholder
                  name="Margin"
                  value="mb-2" />
                <placeholder
                  name="IsReadOnly"
                  value="True" />
              </control>
              <control
                code="TXT"
                id="90e00281-a64e-49f0-8c22-0c5f3db91595"
                binding="GateOutVehicleEntry.GVE_DriverLicenseNumber">
                <placeholder
                  name="CaptionOverride"
                  value="Exit driver license"
                  resid="582141ff-4f36-46cf-a783-19e803ce4ba5" />
                <placeholder
                  name="Columns"
                  value="col-6" />
                <placeholder
                  name="Margin"
                  value="mb-2" />
                <placeholder
                  name="IsReadOnly"
                  value="True" />
              </control>
              <control
                code="DTE"
                id="d0517782-a1ed-4fb0-89b5-175bbe27ba5b"
                binding="GateOutVehicleEntry.GVE_EntryTime">
                <placeholder
                  name="Columns"
                  value="col-6" />
                <placeholder
                  name="CaptionOverride"
                  value="Exit date and time"
                  resid="4b42b3db-cb99-425b-9c62-be6b010b0960" />
                <placeholder
                  name="Margin"
                  value="mb-2" />
                <placeholder
                  name="IsReadOnly"
                  value="True" />
              </control>
              <control
                code="MHS"
                id="22e10c30-6e1c-4c24-8ba0-fcfbf5ba14dc"
                binding="GateOutVehicleEntry.GVE_WeightMeasure">
                <placeholder
                  name="CaptionOverride"
                  value="Exit weight"
                  resid="51516ea6-5994-495a-80a3-b566b8ef9f70" />
                <placeholder
                  name="Columns"
                  value="col-6" />
                <placeholder
                  name="Margin"
                  value="mb-2" />
                <placeholder
                  name="IsReadOnly"
                  value="True" />
              </control>
              <control
                code="SRC"
                id="efa4006d-b24e-474a-b023-9b34a348d61d"
                binding="GateOutVehicleEntry.Lane.GLN_GTE_Gate">
                <placeholder
                  name="Columns"
                  value="col-6" />
                <placeholder
                  name="CaptionOverride"
                  value="Exit gate"
                  resid="60976db6-2977-4bbe-a74c-d0e364271179" />
                <placeholder
                  name="IsReadOnly"
                  value="True" />
                <placeholder
                  name="Margin"
                  value="mb-2" />
              </control>
              <control
                code="SRC"
                id="11f67958-4456-4134-adc1-851f496439ff"
                binding="GateOutVehicleEntry.GVE_GLN_Lane">
                <placeholder
                  name="Columns"
                  value="col-6" />
                <placeholder
                  name="CaptionOverride"
                  value="Exit lane"
                  resid="6a418d55-75ed-40f3-b2ed-3855a11fc611" />
                <placeholder
                  name="Margin"
                  value="mb-2" />
                <placeholder
                  name="IsReadOnly"
                  value="True" />
              </control>
            </control>
          </control>
        </control>
      </control>
      <control
        code="PNL"
        id="c01d271f-9f8c-4572-9c04-81f2f0c7d2fe"
        binding="">
        <placeholder
          name="FitToHeight"
          value="True" />
        <placeholder
          name="Layout"
          value="flex" />
        <placeholder
          name="Columns"
          value="col-9" />
        <control
          code="RDT"
          id="335f86f4-4c4f-4cdc-a70a-85947c56a559"
          binding="GteGateMovements">
          <placeholder
            name="AllowAdd"
            value="False" />
          <placeholder
            name="InlineEdit"
            value="cell" />
          <placeholder
            name="CaptionOverride"
            value="Associated bookings"
            resid="1cefc055-ab9e-412a-92fc-d3b907b8989f" />
          <placeholder
            name="IsReadOnly"
            value="True" />
          <placeholder
            name="FieldConfiguration">
            <xml>
              <fields xmlns="">
                <field
                  path="MovementType"
                  width="300"
                  mode="Default"
                  readOnly="true" />
                <field
                  path="MovementBooking.GBM_SlotStartTime"
                  width="300"
                  mode="Default"
                  readOnly="true" />
                <field
                  path="MovementBooking.GBM_SlotEndTime"
                  width="300"
                  mode="Default"
                  readOnly="true" />
                <field
                  path="MovementBooking.GBM_SourceReferenceNumber"
                  width="300"
                  mode="Default"
                  readOnly="true" />
                <field
                  path="BookingReferenceNumber"
                  width="300"
                  mode="Default"
                  readOnly="true" />
                <field
                  path="GGM_TransportReference"
                  width="300"
                  mode="Default"
                  readOnly="true" />
                <field
                  path="GGM_UnitNumber"
                  width="300"
                  mode="Default"
                  readOnly="true" />
                <field
                  path="GGM_RC_UnitType"
                  width="300"
                  mode="Default"
                  readOnly="true" />
                <field
                  path="GGM_RH_NKCargoType"
                  width="300"
                  mode="Default"
                  readOnly="true" />
                <field
                  path="GGM_F3_NKPackageType"
                  width="300"
                  mode="Default"
                  readOnly="true" />
              </fields>
            </xml>
          </placeholder>
          <placeholder
            name="AllowAttach"
            value="False" />
          <placeholder
            name="AllowDetach"
            value="False" />
          <placeholder
            name="ActionMenuItems">
            <xml>
              <formFlows xmlns="">
                <formFlow
                  inDrawer="True">af0773685c154e8f91653ebb54b67ad5</formFlow>
              </formFlows>
            </xml>
          </placeholder>
        </control>
      </control>
    </control>
  </form>
