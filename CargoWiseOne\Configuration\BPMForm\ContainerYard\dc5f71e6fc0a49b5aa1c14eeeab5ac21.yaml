#transformationVersion: 70.0
#
VZ_PK: dc5f71e6fc0a49b5aa1c14eeeab5ac21
VZ_ConfigurationKey: dc5f71e6-fc0a-49b5-aa1c-14eeeab5ac21
VZ_FormID: CYP Edit Release Advice
VZ_Caption:
  resKey: VZ_Caption|dc5f71e6-fc0a-49b5-aa1c-14eeeab5ac21
  text: Edit Release Order
VZ_FormFactor: DSK
VZ_EntityType: ICYDReleaseAdvice
VZ_FormType: FRM
VZ_RequiresLogin: true
VZ_MaterialDesign: true
VZ_Dependencies: <dependencies xmlns="http://cargowise.com/glow/2014/11/14/dependencies.xsd" />
VZ_FormData: >-
  <form
    id="8a977bfd-fd16-4c30-a10b-db9fb537b059" xmlns="http://wisetechglobal.com/glow/2017/05/26/form.xsd">
    <placeholder
      name="MinHeight"
      value="100%" />
    <control
      code="BOX"
      id="bdc63fb6-720f-4df2-a888-1366eba383c8"
      binding="">
      <placeholder
        name="Layout"
        value="grid" />
      <placeholder
        name="MinHeight"
        value="100%" />
      <control
        code="PNL"
        id="e5483ec4-0298-4eeb-9619-2ab16ee2c650"
        binding="">
        <placeholder
          name="Columns"
          value="col-md-3 col-lg-3 col-xl-3" />
        <placeholder
          name="MinHeight"
          value="100%" />
        <control
          code="BOX"
          id="81dafd32-a3e8-478b-bcbf-5c8b146c0f3c">
          <placeholder
            name="Layout"
            value="grid" />
          <control
            code="LBL"
            id="4140a4a1-b0fd-414b-a078-3315a7c23845"
            binding="">
            <placeholder
              name="Caption"
              value="Release order details"
              resid="1fc0f6a6-39ab-4f87-8f92-1656a990a65f" />
            <placeholder
              name="Typography"
              value="h6" />
            <placeholder
              name="Color"
              value="deep-purple darken-4" />
          </control>
          <control
            code="ADD"
            id="353d35ec-5579-4f9d-9a8f-01d0bad68bb5"
            binding="AddressTypeBKD.E2_OA_Address">
            <placeholder
              name="CaptionOverride"
              value="Client"
              resid="95fc8222-411d-4f83-825e-4c5bbc0f9559" />
            <placeholder
              name="Required"
              value="True" />
          </control>
          <control
            code="ADD"
            id="d76fa079-62c5-423e-8ff4-9c04ee9ab21e"
            binding="AddressTypeSCP.E2_OA_Address">
            <placeholder
              name="CaptionOverride"
              value="Lessee"
              resid="f30da073-de17-42a9-885a-e97b7d0af2a1" />
          </control>
          <control
            code="TXT"
            id="fd87c5d1-a58e-4f2f-897d-837902c51e54"
            binding="YRE_ReleaseNumber" />
          <control
            code="DAE"
            id="49acbe32-a86a-4c35-b3d9-a30e69b09f9d"
            binding="YRE_FromDate" />
          <control
            code="DAE"
            id="157518d6-f643-471b-b506-d86975a2af18"
            binding="YRE_ToDate">
            <placeholder
              name="Columns"
              value="col-md-12 col-lg-12 col-xl-12" />
          </control>
        </control>
      </control>
      <control
        code="PNL"
        id="dbf82f37-64d3-4ac0-9538-74959305108e"
        binding="">
        <placeholder
          name="Columns"
          value="col-md-9 col-lg-9 col-xl-9" />
        <placeholder
          name="MinHeight"
          value="100%" />
        <control
          code="RDT"
          id="ae34e46b-ec5c-4311-b660-9d9ee0e5438c"
          binding="CYDReleaseAdviceLines">
          <placeholder
            name="AllowAdd"
            value="calc(DaysTillExpiry&gt;=0)" />
          <placeholder
            name="InlineEdit"
            value="cell" />
          <placeholder
            name="CaptionType"
            value="description" />
          <placeholder
            name="ShowFilters"
            value="True" />
          <placeholder
            name="ShowGrouping"
            value="True" />
          <placeholder
            name="ShowCustomize"
            value="True" />
          <placeholder
            name="CaptionOverride"
            value="Units"
            resid="af4ca5cb-f458-446c-b0c2-9c2702bae697" />
          <placeholder
            name="ActionMenuItems">
            <xml>
              <formFlows xmlns="">
                <formFlow>bb051436c112441cb4b96db0abfe859f</formFlow>
                <formFlow
                  inDialog="True">c15733df64494ce99afdb2cd9250389e</formFlow>
                <formFlow>44921f453f744505ae21a24bc9b7ec2f</formFlow>
              </formFlows>
            </xml>
          </placeholder>
          <placeholder
            name="FieldConfiguration">
            <xml>
              <fields xmlns="">
                <field
                  path="YEL_Type"
                  width="140"
                  mode="Mandatory" />
                <field
                  path="YEL_RC_ContainerType"
                  width="90"
                  mode="Mandatory" />
                <field
                  path="Size"
                  width="90"
                  mode="Mandatory" />
                <field
                  path="YEL_PickupDate"
                  width="170"
                  mode="Mandatory" />
                <field
                  path="YEL_ReadyDate"
                  width="170"
                  mode="Mandatory" />
                <field
                  path="UnitID"
                  width="120"
                  mode="Mandatory" />
                <field
                  path="YEL_Quantity"
                  width="90"
                  mode="Mandatory" />
                <field
                  path="AllocatedToPickupButNotGatedOutQuantity"
                  width="90"
                  mode="Mandatory" />
                <field
                  path="AvailableToPickupQuantity"
                  width="90"
                  mode="Mandatory" />
                <field
                  path="QuantityOut"
                  width="90"
                  mode="Mandatory" />
                <field
                  path="BalanceQuantity"
                  width="90"
                  mode="Mandatory" />
                <field
                  path="Empty"
                  width="56"
                  mode="Mandatory" />
                <field
                  path="ManufactureDate"
                  width="130"
                  mode="Mandatory" />
                <field
                  path="DaysInYard"
                  width="90"
                  mode="Optional" />
                <field
                  path="YardInTPUId"
                  width="124"
                  mode="Optional" />
                <field
                  path="YardInDateTime"
                  width="170"
                  mode="Optional" />
                <field
                  path="YardOutTPUId"
                  width="124"
                  mode="Optional" />
                <field
                  path="YardOutDateTime"
                  width="170"
                  mode="Optional" />
                <field
                  path="UnitGrade"
                  width="300"
                  mode="Optional" />
                <field
                  path="GrossWeight"
                  width="90"
                  mode="Optional" />
                <field
                  path="TareWeight"
                  width="90"
                  mode="Optional" />
                <field
                  path="MaxGrossWeight"
                  width="90"
                  mode="Optional" />
                <field
                  path="UnitOfWeight"
                  width="90"
                  mode="Optional" />
              </fields>
            </xml>
          </placeholder>
        </control>
      </control>
    </control>
  </form>
