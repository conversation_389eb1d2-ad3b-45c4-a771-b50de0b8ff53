#transformationVersion: 70.0
#
VZ_PK: 0908071210f24d1f83aa2ebc047d76cc
VZ_ConfigurationKey: 09080712-10f2-4d1f-83aa-2ebc047d76cc
VZ_FormID: HRM - Position - Roles
VZ_Caption:
  resKey: VZ_Caption|09080712-10f2-4d1f-83aa-2ebc047d76cc
  text: Roles
VZ_FormFactor: DSK
VZ_EntityType: IGlbStaff
VZ_FormType: FRM
VZ_RequiresLogin: true
VZ_MaterialDesign: true
VZ_Dependencies: <dependencies xmlns="http://cargowise.com/glow/2014/11/14/dependencies.xsd" />
VZ_FormData: >-
  <form
    id="10098868-95b1-4628-9453-c7bf0fe3a0a1" xmlns="http://wisetechglobal.com/glow/2017/05/26/form.xsd">
    <placeholder
      name="Layout"
      value="grid" />
    <placeholder
      name="Fluid"
      value="True" />
    <placeholder
      name="MinWidth"
      value="1500" />
    <control
      code="RDT"
      id="d35395d8-3ab2-4a86-ad3a-cf883cc8676a"
      binding="GlbEmploymentHistories">
      <placeholder
        name="AllowAdd"
        value="True" />
      <placeholder
        name="InlineEdit"
        value="table" />
      <placeholder
        name="IsReadOnly"
        value="False" />
      <placeholder
        name="CaptionOverride"
        value="Roles"
        resid="5dd586a1-8a3a-4210-8e68-28824e7ad22f" />
      <placeholder
        name="FieldConfiguration">
        <xml>
          <fields xmlns="">
            <field
              path="EffectiveDate"
              width="200"
              mode="Mandatory" />
            <field
              path="GEH_JobTitle"
              width="300"
              mode="Mandatory" />
            <field
              path="GEH_JobFamily"
              width="300"
              mode="Mandatory" />
            <field
              path="GEH_IsPromotion"
              width="300"
              caption="Promotion"
              resid="a0cb64a3-a4ce-4b76-85eb-5e471d21f74b"
              mode="Mandatory" />
            <field
              path="GEH_DepartureReason"
              width="100"
              mode="Optional" />
            <field
              path="GEH_DepartureComments"
              width="100"
              mode="Optional" />
            <field
              path="GEH_EffectiveDate"
              width="120"
              mode="Optional" />
            <field
              path="GEH_CompanyName"
              width="100"
              mode="Optional" />
            <field
              path="GEH_EmploymentType"
              width="100"
              mode="Optional" />
            <field
              path="GEH_JobDescription"
              width="300"
              mode="Optional" />
            <field
              path="GEH_SystemCreateTimeUtc"
              width="100"
              mode="Optional" />
            <field
              path="GEH_SystemLastEditTimeUtc"
              width="100"
              mode="Optional" />
            <field
              path="GEH_AutoEffectiveEndDate"
              width="120"
              mode="Optional" />
            <field
              path="CreatedByStaff.GS_Code"
              width="200"
              mode="Optional" />
            <field
              path="CreatedByStaff.GS_FullName"
              width="200"
              mode="Optional" />
            <field
              path="LastEditedByStaff.GS_Code"
              width="200"
              mode="Optional" />
            <field
              path="LastEditedByStaff.GS_FullName"
              width="200"
              mode="Optional" />
            <field
              path="GEH_SystemCreateUser"
              width="100"
              mode="FilterOnly" />
            <field
              path="GEH_SystemLastEditUser"
              width="100"
              mode="FilterOnly" />
            <field
              path="GEH_IsApproved"
              width="110"
              mode="FilterOnly" />
            <field
              path="IGlbEmploymentHistory_Filter_CurrentOn"
              width="100"
              mode="FilterOnly" />
            <field
              path="IGlbEmploymentHistory_Filter_CurrentOn_IgnoreTimezone"
              width="250"
              mode="FilterOnly" />
          </fields>
        </xml>
      </placeholder>
      <placeholder
        name="DefaultSortFields">
        <xml>
          <ArrayOfFieldSortDefinition xmlns:i="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://schemas.datacontract.org/2004/07/CargoWise.Glow.Infrastructure.Interfaces">
            <FieldSortDefinition>
              <FieldName>EffectiveDate</FieldName>
              <IsAscending>false</IsAscending>
            </FieldSortDefinition>
          </ArrayOfFieldSortDefinition>
        </xml>
      </placeholder>
      <placeholder
        name="ShowCustomize"
        value="True" />
      <placeholder
        name="ShowItemActions"
        value="True" />
      <placeholder
        name="ShowSelect"
        value="True" />
      <placeholder
        name="VisibilityCondition"
        value="%.CurrentUserCheckpoints.Contains(&quot;CanAddNewEmploymentHistory&quot;)" />
      <placeholder
        name="AllowAttach"
        value="false" />
      <placeholder
        name="DefaultFilter">
        <xml>
          <ArrayOfFilterGroup xmlns:a="http://schemas.microsoft.com/2003/10/Serialization/Arrays" xmlns:i="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://cargowise.com/glow/2014/07/16/filters.xsd">
            <FilterGroup>
              <Filters>
                <Filter>
                  <FilterType>BoolFilter</FilterType>
                  <Operation>Is</Operation>
                  <PropertyPath>GEH_IsApproved</PropertyPath>
                  <Values>
                    <a:string>true</a:string>
                  </Values>
                </Filter>
              </Filters>
              <IsImplicit>true</IsImplicit>
            </FilterGroup>
          </ArrayOfFilterGroup>
        </xml>
      </placeholder>
    </control>
    <control
      code="RDT"
      id="ea564f47-2edf-49d9-b1b3-40cbeb4b243c"
      binding="GlbEmploymentHistories">
      <placeholder
        name="CaptionOverride"
        value="Roles"
        resid="cc1182fd-9b7e-4efb-b048-aa5d68ad8d1c" />
      <placeholder
        name="InlineEdit"
        value="table" />
      <placeholder
        name="IsReadOnly"
        value="False" />
      <placeholder
        name="FieldConfiguration">
        <xml>
          <fields xmlns="">
            <field
              path="EffectiveDate"
              width="200"
              mode="Mandatory" />
            <field
              path="GEH_JobTitle"
              width="300"
              mode="Mandatory" />
            <field
              path="GEH_JobFamily"
              width="300"
              mode="Mandatory" />
            <field
              path="GEH_IsPromotion"
              width="300"
              caption="Promotion"
              resid="30d45347-ac8c-4603-9fee-b5bb8f41342d"
              mode="Mandatory" />
            <field
              path="GEH_DepartureReason"
              width="100"
              mode="Optional" />
            <field
              path="GEH_DepartureComments"
              width="100"
              mode="Optional" />
            <field
              path="GEH_EffectiveDate"
              width="120"
              mode="Optional" />
            <field
              path="GEH_CompanyName"
              width="100"
              mode="Optional" />
            <field
              path="GEH_EmploymentType"
              width="100"
              mode="Optional" />
            <field
              path="GEH_JobDescription"
              width="300"
              mode="Optional" />
            <field
              path="GEH_SystemCreateTimeUtc"
              width="100"
              mode="Optional" />
            <field
              path="GEH_SystemLastEditTimeUtc"
              width="100"
              mode="Optional" />
            <field
              path="GEH_AutoEffectiveEndDate"
              width="120"
              mode="Optional" />
            <field
              path="CreatedByStaff.GS_Code"
              width="200"
              mode="Optional" />
            <field
              path="CreatedByStaff.GS_FullName"
              width="200"
              mode="Optional" />
            <field
              path="LastEditedByStaff.GS_Code"
              width="200"
              mode="Optional" />
            <field
              path="LastEditedByStaff.GS_FullName"
              width="200"
              mode="Optional" />
            <field
              path="GEH_SystemCreateUser"
              width="100"
              mode="FilterOnly" />
            <field
              path="GEH_SystemLastEditUser"
              width="100"
              mode="FilterOnly" />
            <field
              path="GEH_IsApproved"
              width="110"
              mode="FilterOnly" />
            <field
              path="IGlbEmploymentHistory_Filter_CurrentOn"
              width="100"
              mode="FilterOnly" />
            <field
              path="IGlbEmploymentHistory_Filter_CurrentOn_IgnoreTimezone"
              width="250"
              mode="FilterOnly" />
          </fields>
        </xml>
      </placeholder>
      <placeholder
        name="DefaultSortFields">
        <xml>
          <ArrayOfFieldSortDefinition xmlns:i="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://schemas.datacontract.org/2004/07/CargoWise.Glow.Infrastructure.Interfaces">
            <FieldSortDefinition>
              <FieldName>EffectiveDate</FieldName>
              <IsAscending>false</IsAscending>
            </FieldSortDefinition>
          </ArrayOfFieldSortDefinition>
        </xml>
      </placeholder>
      <placeholder
        name="ShowCustomize"
        value="True" />
      <placeholder
        name="ShowFilters"
        value="True" />
      <placeholder
        name="ShowItemActions"
        value="True" />
      <placeholder
        name="ShowSelect"
        value="True" />
      <placeholder
        name="VisibilityCondition"
        value="!%.CurrentUserCheckpoints.Contains(&quot;CanAddNewEmploymentHistory&quot;)" />
      <placeholder
        name="AllowAttach"
        value="false" />
      <placeholder
        name="DefaultFilter">
        <xml>
          <ArrayOfFilterGroup xmlns:a="http://schemas.microsoft.com/2003/10/Serialization/Arrays" xmlns:i="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://cargowise.com/glow/2014/07/16/filters.xsd">
            <FilterGroup>
              <Filters>
                <Filter>
                  <FilterType>BoolFilter</FilterType>
                  <Operation>Is</Operation>
                  <PropertyPath>GEH_IsApproved</PropertyPath>
                  <Values>
                    <a:string>true</a:string>
                  </Values>
                </Filter>
              </Filters>
              <IsImplicit>true</IsImplicit>
            </FilterGroup>
          </ArrayOfFilterGroup>
        </xml>
      </placeholder>
    </control>
  </form>
