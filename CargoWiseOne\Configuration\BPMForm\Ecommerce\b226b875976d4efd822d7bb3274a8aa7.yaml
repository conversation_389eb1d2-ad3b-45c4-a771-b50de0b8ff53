#transformationVersion: 70.0
#
VZ_PK: b226b875976d4efd822d7bb3274a8aa7
VZ_ConfigurationKey: b226b875-976d-4efd-822d-7bb3274a8aa7
VZ_FormID: ETL - VDV3 - Origin Depot - Receive in Specific Booking Header with Button
VZ_Caption:
  resKey: VZ_Caption|b226b875-976d-4efd-822d-7bb3274a8aa7
  text: Receive in Specific Booking Header
VZ_FormFactor: DSK
VZ_EntityType: IHVLVBookingHeader
VZ_FormType: FRM
VZ_RequiresLogin: true
VZ_Dependencies: <dependencies xmlns="http://cargowise.com/glow/2014/11/14/dependencies.xsd" />
VZ_FormData: >-
  <form
    id="e715c3a5-2afc-4512-ab46-76670413d272" xmlns="http://wisetechglobal.com/glow/2017/05/26/form.xsd">
    <placeholder
      name="BackgroundImagePk"
      value="" />
    <placeholder
      name="BackgroundSize"
      value="Auto" />
    <placeholder
      name="BackgroundImageOverride"
      value="" />
    <placeholder
      name="FormLayout"
      value="Splitview" />
    <placeholder
      name="ExpandPaths"
      value="" />
    <placeholder
      name="ScanInputHandling"
      value="NON" />
    <placeholder
      name="RemovePadding"
      value="false" />
    <placeholder
      name="TurnOnPortableOneToOneDesignMode"
      value="false" />
    <placeholder
      name="ContainsDefaultButton"
      value="False" />
    <control
      code="GRP"
      id="f3b191bc-92bb-4546-a424-ddf00797c935"
      width="26"
      height="13.2">
      <placeholder
        name="CanBeHidden"
        value="True" />
      <placeholder
        name="VisibilityCondition"
        value="" />
      <placeholder
        name="Header"
        value="New Section"
        resid="1099b1c8-6e60-481c-b559-06a02f2c935f" />
      <placeholder
        name="HeaderAlignment"
        value="Left" />
      <placeholder
        name="HeaderDisplayMode"
        value="Hidden" />
      <placeholder
        name="BackgroundImagePk"
        value="" />
      <placeholder
        name="BackgroundImageOverride"
        value="" />
      <control
        code="FLB"
        id="907d42f6-6059-42e3-9614-1afdf34ed607"
        left="9"
        top="10.6"
        width="8"
        height="1">
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="LabelText"
          value="Please Scan Next Item"
          resid="2b20773c-9b74-4d6b-a2d9-3a7e5b77428b" />
        <placeholder
          name="LabelType"
          value="NormalLabel" />
        <placeholder
          name="LabelNotificationType"
          value="Information" />
        <placeholder
          name="ForegroundThemeColor"
          value="DefaultButton" />
        <placeholder
          name="TextSize"
          value="Medium" />
        <placeholder
          name="FontWeight"
          value="Bold" />
        <placeholder
          name="LabelAlignment"
          value="Center" />
        <placeholder
          name="BackgroundColor"
          value="" />
      </control>
      <control
        code="TBT"
        id="9947ec87-8a52-40fd-8c58-2a403260cf17"
        left="8.9"
        top="11.6"
        width="8.3"
        height="1">
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="Content"
          value="Edit Last Scanned Item"
          resid="d15629d3-9be4-4406-b6d2-c68df72c4bfe" />
        <placeholder
          name="IsDefault"
          value="False" />
      </control>
      <control
        code="FFI"
        id="66f08969-55e6-44b8-a6f8-f47ef8e29d29"
        width="9"
        height="1"
        bottom="2.6">
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="Barcode"
          resid="dd73ca3a-c1e2-4f85-a5d2-032efb915e62" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="EntityType"
          value="IHVLVItem" />
        <placeholder
          name="DefaultFilter">
          <xml>
            <ArrayOfFilterGroup xmlns:a="http://schemas.microsoft.com/2003/10/Serialization/Arrays" xmlns:i="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://cargowise.com/glow/2014/07/16/filters.xsd">
              <FilterGroup>
                <Filters>
                  <Filter>
                    <FilterType>AdvancedGuidLookupFilter</FilterType>
                    <Operation>Is</Operation>
                    <PropertyPath>Consignment.HVC_HVH_BookingHeader</PropertyPath>
                    <Values>
                      <a:string>&lt;HVH_PK&gt;</a:string>
                    </Values>
                    <Visibility>Hidden</Visibility>
                  </Filter>
                </Filters>
                <IsImplicit>true</IsImplicit>
              </FilterGroup>
            </ArrayOfFilterGroup>
          </xml>
        </placeholder>
        <placeholder
          name="ItemTemplateID"
          value="" />
        <placeholder
          name="FormFlowPK"
          value="" />
        <placeholder
          name="ItemPosition"
          value="Bottom" />
      </control>
    </control>
  </form>
