#transformationVersion: 70.0
#
VZ_PK: e086b3aea5e84b88bef8913818b5a2e7
VZ_ConfigurationKey: e086b3ae-a5e8-4b88-bef8-913818b5a2e7
VZ_FormID: GDM - Gate In - Canceled
VZ_Caption:
  resKey: VZ_Caption|e086b3aea5e84b88bef8913818b5a2e7
  text: Gate in - Canceled
VZ_FormFactor: DSK
VZ_EntityType: IGteVehicleMovement
VZ_FormType: FRM
VZ_RequiresLogin: true
VZ_MaterialDesign: true
VZ_Dependencies: <dependencies xmlns="http://cargowise.com/glow/2014/11/14/dependencies.xsd" />
VZ_StatusBindingPath: Status
VZ_EntityStatusDisplayMode: ReadOnly
VZ_FormData: >-
  <form
    id="b422810d-07c9-4789-977b-03ceae0ac055" xmlns="http://wisetechglobal.com/glow/2017/05/26/form.xsd">
    <placeholder
      name="Padding"
      value="pa-4" />
    <placeholder
      name="NoGutters"
      value="True" />
    <placeholder
      name="Layout"
      value="grid" />
    <placeholder
      name="Align"
      value="left" />
    <placeholder
      name="FitToHeight"
      value="True" />
    <control
      code="BOX"
      id="8ff51775-901b-43a0-9448-f7ebbe413cfa"
      binding="">
      <placeholder
        name="Height"
        value="100%" />
      <placeholder
        name="Layout"
        value="grid" />
      <control
        code="PNL"
        id="7ab8d565-9577-4eb6-839a-4934d025d393"
        binding="">
        <placeholder
          name="Caption"
          value="Transport details"
          resid="f72097f5-16ac-4eee-a096-eb93d08c91e2" />
        <placeholder
          name="FitToHeight"
          value="True" />
        <placeholder
          name="Margin"
          value="mr-4" />
        <placeholder
          name="Columns"
          value="col-3" />
        <control
          code="BOX"
          id="dae81f7c-cfe8-42a4-882d-9837025b7776">
          <placeholder
            name="Layout"
            value="grid" />
          <placeholder
            name="Margin"
            value="mb-2" />
          <control
            code="SRC"
            id="ae5b5485-bd8b-4b23-9a2c-60c7f07fbcbf"
            binding="VehicleRegistrationForBinding">
            <placeholder
              name="Columns"
              value="col-6" />
            <placeholder
              name="IsReadOnly"
              value="True" />
            <placeholder
              name="Margin"
              value="mb-2" />
          </control>
          <control
            code="SRC"
            id="5fd047ff-285e-49dc-8742-5341279c6e0e"
            binding="GVM_RC_VehicleType">
            <placeholder
              name="Columns"
              value="col-6" />
            <placeholder
              name="IsReadOnly"
              value="True" />
            <placeholder
              name="Margin"
              value="mb-2" />
          </control>
          <control
            code="TXT"
            id="3c7858ea-a3d6-48a5-940c-8667375e7286"
            binding="TransportProvider">
            <placeholder
              name="Columns"
              value="col-6" />
            <placeholder
              name="IsReadOnly"
              value="True" />
            <placeholder
              name="Margin"
              value="mb-2" />
          </control>
          <control
            code="TXT"
            id="ba2941cd-b7b6-4835-bc49-e9d511b8bde8"
            binding="FirstGateMovementBySlotStartTime.GGM_TransportReference">
            <placeholder
              name="Columns"
              value="col-6" />
            <placeholder
              name="IsReadOnly"
              value="True" />
            <placeholder
              name="Margin"
              value="mb-2" />
          </control>
          <control
            code="TXT"
            id="b295092f-39dc-475f-9b39-be800185a329"
            binding="MainBooking.GBK_ReferenceNumber">
            <placeholder
              name="Columns"
              value="col-6" />
            <placeholder
              name="IsReadOnly"
              value="True" />
            <placeholder
              name="CaptionOverride"
              value="Gate booking"
              resid="c724a4bb-2e53-4eb3-9c60-99d6170f5521" />
            <placeholder
              name="Margin"
              value="mb-2" />
          </control>
          <control
            code="CMB"
            id="1b30ff20-27a7-40d9-ab99-9a0a371ee8b4"
            binding="MainBooking.GBK_BookingType">
            <placeholder
              name="Columns"
              value="col-6" />
            <placeholder
              name="IsReadOnly"
              value="True" />
            <placeholder
              name="Margin"
              value="mb-2" />
            <placeholder
              name="CaptionOverride"
              value="Booking type"
              resid="b5ca7645-b71b-468e-beca-08d21c6fcaa6" />
          </control>
          <control
            code="TXA"
            id="11c6db90-ecbb-463f-ab1f-1c4b7610365c"
            binding="GateReferenceCheckGateIn">
            <placeholder
              name="CaptionOverride"
              value="Gate reference check"
              resid="c53d703f-dbbe-422c-9e8e-2c4dc45bed41" />
            <placeholder
              name="IsReadOnly"
              value="True" />
            <placeholder
              name="Margin"
              value="mb-2" />
            <placeholder
              name="Rows"
              value="3" />
            <placeholder
              name="VisibilityCondition"
              value="MainBooking.Exists" />
          </control>
          <control
            code="SRC"
            id="257c8f39-966b-446e-80e2-54baa582a1ab"
            binding="MainBooking.GBK_WW_Facility">
            <placeholder
              name="VisibilityCondition"
              value="MainBooking != null" />
            <placeholder
              name="IsReadOnly"
              value="True" />
            <placeholder
              name="CaptionOverride"
              value="Facility"
              resid="a8d7f169-306c-422f-82dd-ada891954201" />
            <placeholder
              name="Columns"
              value="col-6" />
            <placeholder
              name="Margin"
              value="mb-2" />
          </control>
          <control
            code="SRC"
            id="0fb446d1-97b2-4b64-a7eb-cbfab325fdde"
            binding="FacilityForGateInWithoutBooking">
            <placeholder
              name="VisibilityCondition"
              value="MainBooking == null" />
            <placeholder
              name="IsReadOnly"
              value="True" />
            <placeholder
              name="CaptionOverride"
              value="Facility"
              resid="5bc9c176-9456-47b1-91db-906eaca2ee9c" />
            <placeholder
              name="Columns"
              value="col-6" />
            <placeholder
              name="Margin"
              value="mb-2" />
          </control>
          <control
            code="SRC"
            id="35e5f01c-de2c-487a-b43b-bf25a2e9142d"
            binding="GVM_WL_Location">
            <placeholder
              name="Columns"
              value="col-6" />
            <placeholder
              name="Margin"
              value="mb-2" />
            <placeholder
              name="IsReadOnly"
              value="True" />
          </control>
          <control
            code="DVR"
            id="fcee4db9-9477-4b82-b805-814c481717de">
            <placeholder
              name="Variant"
              value="solid" />
            <placeholder
              name="Margin"
              value="mb-2" />
          </control>
          <control
            code="TXT"
            id="57503fb3-07b5-47fc-b4de-7b452ab311ed"
            binding="GateInActionNumber">
            <placeholder
              name="Columns"
              value="col-6" />
            <placeholder
              name="IsReadOnly"
              value="True" />
            <placeholder
              name="Margin"
              value="mb-2" />
            <placeholder
              name="CaptionOverride"
              value="Gate in number"
              resid="85a8cdba-a03f-414e-94f6-397916d00124" />
          </control>
          <control
            code="TXT"
            id="d3df9560-88e3-4a9e-a46b-453214a725d1"
            binding="GateInVehicleEntry.CreatedByStaff.GS_FullName">
            <placeholder
              name="CaptionOverride"
              value="Gate operator"
              resid="285e3940-e376-455d-afb6-d6561e8d8684" />
            <placeholder
              name="Columns"
              value="col-6" />
            <placeholder
              name="IsReadOnly"
              value="True" />
            <placeholder
              name="Margin"
              value="mb-2" />
          </control>
          <control
            code="TXT"
            id="cf36b8b2-3152-4e4b-802d-cb0e97f862ac"
            binding="GateInVehicleEntry.GVE_DriverName">
            <placeholder
              name="Columns"
              value="col-6" />
            <placeholder
              name="CaptionOverride"
              value="Entry driver name"
              resid="f4b6ab57-d067-4e0f-b3a2-9e057333add5" />
            <placeholder
              name="IsReadOnly"
              value="True" />
            <placeholder
              name="Margin"
              value="mb-2" />
          </control>
          <control
            code="TXT"
            id="ce5ecdb1-f2e3-45e4-9177-a03401d6d7a6"
            binding="GateInVehicleEntry.GVE_DriverLicenseNumber">
            <placeholder
              name="Columns"
              value="col-6" />
            <placeholder
              name="CaptionOverride"
              value="Entry driver license"
              resid="135c430a-aee5-40a6-90d7-c31a0c38b961" />
            <placeholder
              name="IsReadOnly"
              value="True" />
            <placeholder
              name="Margin"
              value="mb-2" />
          </control>
          <control
            code="DTE"
            id="bf7c9c5d-4ca8-4936-91a8-c8cb64c1260e"
            binding="GateInVehicleEntry.GVE_EntryTime">
            <placeholder
              name="Columns"
              value="col-6" />
            <placeholder
              name="CaptionOverride"
              value="Entry date and time"
              resid="1d389a9d-31cd-482f-97c7-7a4accb619ed" />
            <placeholder
              name="Required"
              value="False" />
            <placeholder
              name="IsReadOnly"
              value="True" />
            <placeholder
              name="Margin"
              value="mb-2" />
          </control>
          <control
            code="MHS"
            id="7d2f02bb-76d1-4d87-b65f-a86b9a63ffa7"
            binding="GateInVehicleEntry.GVE_WeightMeasure">
            <placeholder
              name="Columns"
              value="col-6" />
            <placeholder
              name="CaptionOverride"
              value="Entry weight"
              resid="9af06ae6-101a-41ef-8dce-c47bc0ab1cd5" />
            <placeholder
              name="IsReadOnly"
              value="True" />
            <placeholder
              name="Margin"
              value="mb-2" />
          </control>
          <control
            code="SRC"
            id="75a4a36e-598e-4c44-8d47-3cd1353fdb00"
            binding="GateInVehicleEntry.Lane.GLN_GTE_Gate">
            <placeholder
              name="IsReadOnly"
              value="True" />
            <placeholder
              name="CaptionOverride"
              value="Entry gate"
              resid="0a143b32-943b-49ef-8bf6-0e22d5df1c88" />
            <placeholder
              name="Columns"
              value="col-6" />
            <placeholder
              name="Margin"
              value="mb-2" />
          </control>
          <control
            code="SRC"
            id="9fc1b0de-4149-4f72-b1c5-a1c26af6b941"
            binding="GateInVehicleEntry.GVE_GLN_Lane">
            <placeholder
              name="Columns"
              value="col-6" />
            <placeholder
              name="CaptionOverride"
              value="Entry lane"
              resid="691322cd-2e72-40df-a604-b9df58794b19" />
            <placeholder
              name="IsReadOnly"
              value="True" />
            <placeholder
              name="Margin"
              value="mb-2" />
          </control>
        </control>
      </control>
      <control
        code="PNL"
        id="c01d271f-9f8c-4572-9c04-81f2f0c7d2fe"
        binding="">
        <placeholder
          name="FitToHeight"
          value="True" />
        <placeholder
          name="Columns"
          value="col-9" />
        <placeholder
          name="Height"
          value="100%" />
        <control
          code="RDT"
          id="335f86f4-4c4f-4cdc-a70a-85947c56a559"
          binding="GteGateMovements">
          <placeholder
            name="AllowAdd"
            value="False" />
          <placeholder
            name="InlineEdit"
            value="cell" />
          <placeholder
            name="FieldConfiguration">
            <xml>
              <fields xmlns="">
                <field
                  path="MovementType"
                  width="150"
                  mode="Default" />
                <field
                  path="MovementBooking.GBM_SourceReferenceNumber"
                  width="150"
                  mode="Default" />
                <field
                  path="GGM_RC_UnitType"
                  width="150"
                  mode="Default" />
                <field
                  path="GGM_UnitNumber"
                  width="150"
                  mode="Default" />
                <field
                  path="GGM_TransportReference"
                  width="150"
                  mode="Default" />
                <field
                  path="MovementBooking.GBM_SlotStartTime"
                  width="200"
                  mode="Default" />
                <field
                  path="MovementBooking.GBM_SlotEndTime"
                  width="200"
                  mode="Default" />
                <field
                  path="BookingReferenceNumber"
                  width="100"
                  mode="Default" />
                <field
                  path="GGM_RH_NKCargoType"
                  width="180"
                  mode="Default" />
                <field
                  path="GGM_F3_NKPackageType"
                  width="100"
                  mode="Default" />
              </fields>
            </xml>
          </placeholder>
          <placeholder
            name="IsReadOnly"
            value="True" />
          <placeholder
            name="FitToHeight"
            value="True" />
          <placeholder
            name="CaptionOverride"
            value="Associated bookings"
            resid="254259cd-8fc5-4846-88a8-15c963e410f2" />
          <placeholder
            name="AllowAttach"
            value="False" />
          <placeholder
            name="AllowDetach"
            value="False" />
          <placeholder
            name="DisabledGridRowActions"
            value="Remove" />
          <placeholder
            name="HideItemActions"
            value="True" />
          <placeholder
            name="ActionMenuItems">
            <xml>
              <formFlows xmlns="">
                <formFlow
                  inDrawer="True">af0773685c154e8f91653ebb54b67ad5</formFlow>
              </formFlows>
            </xml>
          </placeholder>
        </control>
      </control>
    </control>
  </form>
