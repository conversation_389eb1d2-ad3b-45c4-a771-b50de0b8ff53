#transformationVersion: 70.0
#
VZ_PK: abb7a4e875eb430ba04acf0a011690fd
VZ_ConfigurationKey: abb7a4e8-75eb-430b-a04a-cf0a011690fd
VZ_FormID: HRM - Hiring Request
VZ_Caption:
  resKey: VZ_Caption|abb7a4e8-75eb-430b-a04a-cf0a011690fd
  text: Hiring Request Details
VZ_FormFactor: DSK
VZ_EntityType: IHRHiringRequest
VZ_FormType: FRM
VZ_RequiresLogin: true
VZ_MaterialDesign: true
VZ_Dependencies: >-
  <dependencies xmlns="http://cargowise.com/glow/2014/11/14/dependencies.xsd">
    <expandPath
      path="JobApplicant" />
    <expandPath
      path="Team" />
    <expandPath
      path="ReportingManager" />
    <expandPath
      path="HiringRequestNotes" />
    <calculatedProperty
      path="HiringRequestNotes" />
    <calculatedProperty
      path="HiringRequestNotes.ST_NoteText" />
  </dependencies>
VZ_FormData: >-
  <form
    id="0eb0da0a-9884-4e7d-ba4e-2635cfcd891d" xmlns="http://wisetechglobal.com/glow/2017/05/26/form.xsd">
    <control
      code="TBS"
      id="dbd28d8a-d77f-43a7-a673-986b9a6ceed6">
      <placeholder
        name="Application"
        value="True" />
      <control
        code="TAB"
        id="92a13ad1-f2b7-4e09-b2a5-7562c3c8c94f">
        <placeholder
          name="Caption"
          value="Details"
          resid="fea6727f-dbfb-4b90-a99c-e2aa68ef1c9b" />
      </control>
      <control
        code="TAB"
        id="0c5d3426-361e-4248-931f-4139101fc199">
        <placeholder
          name="Caption"
          value="Approvals"
          resid="4a33635b-f9b9-4ac2-95da-de1d6ecf604e" />
      </control>
      <control
        code="TAI"
        id="14d487c0-d3dd-47e5-9540-f465337298e3">
        <control
          code="CRD"
          id="db5faeac-b103-43f3-b18b-24a97fb3396d"
          binding="">
          <placeholder
            name="Margin"
            value="mt-4" />
          <placeholder
            name="Padding"
            value="px-8 py-4" />
          <control
            code="BOX"
            id="524d5cd5-c879-4f87-a3b3-52c41ee331d9">
            <placeholder
              name="Layout"
              value="grid" />
            <control
              code="BOX"
              id="297bcf35-3853-443b-92cb-1f5996e905f8"
              binding="">
              <placeholder
                name="Class"
                value="my-0" />
              <placeholder
                name="Columns"
                value="" />
              <placeholder
                name="Layout"
                value="grid" />
              <control
                code="BOX"
                id="8aaf5733-0d9f-4ef1-83f7-d90c17d8a2ae"
                binding="">
                <placeholder
                  name="Columns"
                  value="col-sm-6 col-md-6 col-lg-6 col-xl-6" />
                <control
                  code="BOX"
                  id="cb8b5968-20a6-497b-a36d-7b2f2749da3f">
                  <placeholder
                    name="Layout"
                    value="grid" />
                  <control
                    code="SRC"
                    id="ed419372-c728-43d8-918c-9a2f4e494e58"
                    binding="HRR_HA_JobApplicant">
                    <placeholder
                      name="Class"
                      value="flex-grow-1" />
                    <placeholder
                      name="Columns"
                      value="col-sm-6" />
                  </control>
                  <control
                    code="TXT"
                    id="348caa8c-dafd-4bc9-9235-21abfe36465e"
                    binding="HRR_JobTitle">
                    <placeholder
                      name="Columns"
                      value="col-sm-6" />
                    <placeholder
                      name="CaptionOverride"
                      value="Role of Interest"
                      resid="c6647556-dc5a-49b2-bf8e-cae771fd1b4f" />
                    <placeholder
                      name="Required"
                      value="True" />
                  </control>
                  <control
                    code="TXT"
                    id="083d345d-9bb5-4c1f-aa83-938eaaba4641"
                    binding="HRR_PurposeOfRole">
                    <placeholder
                      name="Columns"
                      value="col-sm-6" />
                    <placeholder
                      name="Class"
                      value="flex-grow-1" />
                  </control>
                  <control
                    code="SRC"
                    id="841f4690-3cd7-45b2-b6fc-3f99b2802e67"
                    binding="HRR_JobFamily">
                    <placeholder
                      name="Columns"
                      value="col-sm-6" />
                    <placeholder
                      name="Class"
                      value="flex-grow-1" />
                    <placeholder
                      name="Required"
                      value="True" />
                  </control>
                  <control
                    code="SRC"
                    id="7127003a-b46a-4716-8e3a-5cb32f6c9a66"
                    binding="HRR_GST_NKTeam">
                    <placeholder
                      name="Class"
                      value="flex-grow-1" />
                    <placeholder
                      name="Columns"
                      value="col-sm-6" />
                  </control>
                  <control
                    code="SRC"
                    id="bc0e6cac-8ea2-4fa6-b472-0b7827d16d01"
                    binding="HRR_GS_NKReportingManager">
                    <placeholder
                      name="Class"
                      value="flex-grow-1" />
                    <placeholder
                      name="Columns"
                      value="col-sm-6" />
                  </control>
                  <control
                    code="SRC"
                    id="af89d72e-00f1-4282-8469-2a773c6b456e"
                    binding="HRR_WorkingBasis">
                    <placeholder
                      name="Class"
                      value="flex-grow-1" />
                    <placeholder
                      name="Columns"
                      value="col-sm-6" />
                    <placeholder
                      name="Required"
                      value="True" />
                  </control>
                  <control
                    code="SRC"
                    id="f633e9a3-9a4a-4609-b576-952bc8b95b0b"
                    binding="HRR_RN_NKWorkLocationCountry">
                    <placeholder
                      name="Class"
                      value="flex-grow-1" />
                    <placeholder
                      name="Columns"
                      value="col-sm-6" />
                    <placeholder
                      name="Required"
                      value="True" />
                  </control>
                  <control
                    code="TXT"
                    id="2cfd162c-58e9-491c-8ee4-04ebc025b96c"
                    binding="HRR_WorkLocationCity">
                    <placeholder
                      name="Columns"
                      value="col-sm-6" />
                    <placeholder
                      name="Required"
                      value="True" />
                  </control>
                  <control
                    code="OPT"
                    id="af81bcd8-63ab-4378-8893-dea447f1a97f"
                    binding="HRR_IsRemoteWorker">
                    <placeholder
                      name="Columns"
                      value="col-sm-6" />
                    <placeholder
                      name="Class"
                      value="flex-grow-1" />
                  </control>
                  <control
                    code="SRC"
                    id="9f46338f-e200-40fe-88eb-ae6e363967ff"
                    binding="HRR_GB_BeneficiaryBranch">
                    <placeholder
                      name="Class"
                      value="flex-grow-1" />
                    <placeholder
                      name="Columns"
                      value="col-sm-6" />
                  </control>
                  <control
                    code="SRC"
                    id="8b8e76fa-8498-4b0f-b6b4-8f8ec875b94a"
                    binding="HRR_Status">
                    <placeholder
                      name="Class"
                      value="flex-grow-1" />
                    <placeholder
                      name="Columns"
                      value="col-sm-6" />
                  </control>
                </control>
              </control>
              <control
                code="TXA"
                id="14bcaac6-3110-491e-97a6-04cb95458b0b"
                binding="HiringRequestNotes.ST_NoteText">
                <placeholder
                  name="CaptionOverride"
                  value="Notes"
                  resid="387b21a1-2c40-4ef5-b299-d713c0230038" />
                <placeholder
                  name="Columns"
                  value="col-sm-6 col-md-6 col-lg-6 col-xl-6" />
                <placeholder
                  name="Rows"
                  value="12" />
              </control>
              <control
                code="BOX"
                id="86337a2c-f102-45e5-b09d-5cd3f78e1827"
                binding="">
                <placeholder
                  name="Class"
                  value="mt-10" />
                <control
                  code="LBL"
                  id="efe42497-bf14-48ea-8324-52ac06d11b38"
                  binding="">
                  <placeholder
                    name="Caption"
                    value="Additional Details"
                    resid="1662c52e-aaaa-43e9-a8f5-542bd81c08f9" />
                  <placeholder
                    name="Typography"
                    value="label" />
                  <placeholder
                    name="Class"
                    value="font-weight-bold" />
                  <placeholder
                    name="NoWrap"
                    value="True" />
                </control>
                <control
                  code="CUS"
                  id="10b30421-fef5-4a9c-9caf-b7ad0f912777"
                  binding="">
                  <placeholder
                    name="ViewModel"
                    value="CustomFieldsViewModel" />
                  <placeholder
                    name="ItemColumns"
                    value="col-1 col-sm-2 col-md-3 col-lg-3 col-xl-3" />
                </control>
              </control>
              <control
                code="BOX"
                id="826a6595-afaf-484e-b66a-65e832c7d12f"
                binding="">
                <placeholder
                  name="Class"
                  value="mt-10" />
                <control
                  code="RLS"
                  id="fdb15b81-0420-45d6-a33b-022011f468af"
                  binding="HRCandidateEntitlements">
                  <placeholder
                    name="Caption"
                    value="Remuneration Package"
                    resid="a41371a5-cb99-42d8-8e75-68a332d8b4c5" />
                  <placeholder
                    name="HideDefaultFooter"
                    value="True" />
                  <placeholder
                    name="ContentTemplateID"
                    value="2face640fc7943839dcc65c6f026f3ca" />
                  <placeholder
                    name="NewFormFlow"
                    value="83e6e64eafd449829f6eaf572a6c211f" />
                  <placeholder
                    name="ShowSearch"
                    value="False" />
                </control>
              </control>
              <control
                code="NUM"
                id="744f3772-3abe-4814-ab7e-4feecc7d0411"
                binding="RemunerationEquityPercentage">
                <placeholder
                  name="IsReadOnly"
                  value="True" />
                <placeholder
                  name="CaptionOverride"
                  value="Remuneration Equity as a % of Base Salary"
                  resid="debad2be-a433-4a43-818c-47cb3828f32e" />
                <placeholder
                  name="Columns"
                  value="col-sm-3" />
              </control>
              <control
                code="NUM"
                id="26c9afeb-4f6d-4c7a-8cda-c234bb3b0830"
                binding="RemunerationBasePlusEquity">
                <placeholder
                  name="Columns"
                  value="col-sm-3" />
                <placeholder
                  name="CaptionOverride"
                  value="Base Salary + Remuneration Equity"
                  resid="9e94b6c8-353a-4f70-a7a8-855020cb97d9" />
              </control>
              <control
                code="NUM"
                id="75bc9119-2d53-4959-b746-16a3ce6c3062"
                binding="RemunerationEquityPercentageOfBaseSalaryPlusRem">
                <placeholder
                  name="Columns"
                  value="col-sm-3" />
                <placeholder
                  name="IsReadOnly"
                  value="True" />
                <placeholder
                  name="CaptionOverride"
                  value="Remuneration Equity as a % of Base + Equity"
                  resid="800b5d38-1c45-4477-a8b1-a62c9425167f" />
              </control>
            </control>
          </control>
        </control>
      </control>
      <control
        code="TAI"
        id="d9c97371-6678-4d4c-a8f0-a23a0da5c69f">
        <control
          code="CRD"
          id="e53e4133-f086-40c8-a44e-098973853c79"
          binding="">
          <placeholder
            name="Padding"
            value="px-8 py-4" />
          <placeholder
            name="Margin"
            value="mt-4" />
          <control
            code="RDT"
            id="3284566e-a14f-4e47-ad1a-b6be3a222e9a"
            binding="ApprovalTasks">
            <placeholder
              name="IsReadOnly"
              value="True" />
            <placeholder
              name="HideItemActions"
              value="True" />
            <placeholder
              name="FieldConfiguration">
              <xml>
                <fields xmlns="">
                  <field
                    path="P9_ActualDate"
                    width="180"
                    mode="Optional" />
                  <field
                    path="P9_ActualDateUtc"
                    width="180"
                    mode="Optional" />
                  <field
                    path="P9_ActualDuration"
                    width="150"
                    mode="Optional" />
                  <field
                    path="P9_Sequence"
                    width="120"
                    mode="Default" />
                  <field
                    path="P9_GS_NKAssignedStaffMember"
                    width="210"
                    mode="Default" />
                  <field
                    path="P9_CompletedTimeUtc"
                    width="180"
                    mode="Optional" />
                  <field
                    path="P9_Description"
                    width="250"
                    mode="Default" />
                  <field
                    path="P9_EstDuration"
                    width="120"
                    mode="Optional" />
                  <field
                    path="P9_EstimateVariationFactor"
                    width="250"
                    mode="Optional" />
                  <field
                    path="P9_Status"
                    width="60"
                    mode="Optional" />
                  <field
                    path="P9_Type"
                    width="100"
                    mode="Default" />
                  <field
                    path="ApprovalStatus"
                    width="150"
                    mode="Default" />
                  <field
                    path="IsApprovalTask"
                    width="150"
                    mode="Optional" />
                  <field
                    path="ApprovalType"
                    width="250"
                    mode="Optional" />
                </fields>
              </xml>
            </placeholder>
          </control>
          <control
            code="BTN"
            id="fbbc3cb2-df26-4c9c-b5d4-39515b123900"
            binding="">
            <placeholder
              name="Caption"
              value="Edit Approvals"
              resid="b4592fa8-96a4-4f19-93a8-6066c207cb95" />
            <placeholder
              name="Margin"
              value="mt-3" />
            <placeholder
              name="Transition"
              value="True" />
          </control>
          <control
            code="BTN"
            id="668e7906-8681-4721-b63f-279d23e1c10d"
            binding="">
            <placeholder
              name="Caption"
              value="Refresh"
              resid="272120fc-6cf0-4f33-9d37-cce78709c850" />
            <placeholder
              name="Margin"
              value="mt-3 ms-2" />
            <placeholder
              name="Transition"
              value="True" />
          </control>
        </control>
      </control>
    </control>
  </form>
