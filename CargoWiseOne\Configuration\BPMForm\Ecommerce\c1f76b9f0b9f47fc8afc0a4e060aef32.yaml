#transformationVersion: 70.0
#
VZ_PK: c1f76b9f0b9f47fc8afc0a4e060aef32
VZ_ConfigurationKey: c1f76b9f-0b9f-47fc-8afc-0a4e060aef32
VZ_FormID: CFS Shipment
VZ_Caption:
  resKey: VZ_Caption|c1f76b9f-0b9f-47fc-8afc-0a4e060aef32
  text: CFS Shipment
VZ_FormFactor: DSK
VZ_EntityType: IJobShipment
VZ_FormType: FRM
VZ_RequiresLogin: true
VZ_Dependencies: >-
  <dependencies xmlns="http://cargowise.com/glow/2014/11/14/dependencies.xsd">
    <expandPath
      path="ConsignorPickupAddress.Address" />
    <expandPath
      path="ConsignorPickupAddress.Country" />
    <expandPath
      path="ConsigneeDeliveryAddress.Address" />
    <expandPath
      path="ConsigneeDeliveryAddress.Country" />
    <expandPath
      path="FirstConsolShipmentLink.JobConsol.TransportRoutings" />
    <expandPath
      path="DestinationCFSDepartureConfirms" />
    <expandPath
      path="JobPackLines" />
    <expandPath
      path="ServiceLevel" />
    <expandPath
      path="PackType" />
    <expandPath
      path="GoodsValueCurr" />
    <expandPath
      path="InsuranceCurrency" />
    <expandPath
      path="Origin" />
    <expandPath
      path="DocsAndCartageDetail" />
    <expandPath
      path="Destination" />
    <calculatedProperty
      path="ConsignorPickupAddress" />
    <calculatedProperty
      path="ConsigneeDeliveryAddress" />
    <calculatedProperty
      path="FirstConsolShipmentLink" />
    <calculatedProperty
      path="JS_ActualWeightMeasure" />
    <calculatedProperty
      path="JS_ActualVolumeMeasure" />
    <calculatedProperty
      path="DocsAndCartageDetail" />
    <datagrid
      path="JobPackLines">
      <expandPath
        path="PackType" />
      <expandPath
        path="Commodity" />
      <expandPath
        path="Origin" />
      <expandPath
        path="CreatedByStaff" />
      <expandPath
        path="LastEditedByStaff" />
    </datagrid>
  </dependencies>
VZ_FormData: >-
  <form
    id="45dfd74f-a237-414b-9e7e-5cd5d08b0e16" xmlns="http://wisetechglobal.com/glow/2017/05/26/form.xsd">
    <placeholder
      name="BackgroundImagePk"
      value="" />
    <placeholder
      name="BackgroundSize"
      value="Auto" />
    <placeholder
      name="BackgroundImageOverride"
      value="" />
    <placeholder
      name="FormLayout"
      value="Splitview" />
    <placeholder
      name="ExpandPaths"
      value="" />
    <placeholder
      name="ScanInputHandling"
      value="NON" />
    <placeholder
      name="RemovePadding"
      value="false" />
    <placeholder
      name="TurnOnPortableOneToOneDesignMode"
      value="false" />
    <placeholder
      name="ContainsDefaultButton"
      value="False" />
    <control
      code="GRP"
      id="4aafa4a7-b0c8-4aa7-a77b-5108155d5c53"
      left="0"
      top="0"
      width="8"
      height="6">
      <placeholder
        name="CanBeHidden"
        value="True" />
      <placeholder
        name="VisibilityCondition"
        value="" />
      <placeholder
        name="Header"
        value="Shipment Details"
        resid="e9efa8cf-81e5-4612-a80b-cc04f5e41945" />
      <placeholder
        name="HeaderAlignment"
        value="Left" />
      <placeholder
        name="HeaderDisplayMode"
        value="Visible" />
      <placeholder
        name="BackgroundImagePk"
        value="" />
      <placeholder
        name="BackgroundImageOverride"
        value="" />
      <control
        code="TXT"
        id="caaf9d26-87d8-409a-a305-86901bfbc304"
        left="0"
        top="0"
        width="4"
        height="1"
        binding="JS_HouseBill">
        <placeholder
          name="IsReadOnly"
          value="True" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="OnSubmit"
          value="" />
        <placeholder
          name="MultiLine"
          value="False" />
        <placeholder
          name="EnableAutoComplete"
          value="False" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="TXT"
        id="314a0e4a-5522-48d1-b3c1-e8d7acdcc82c"
        left="4"
        top="0"
        width="4"
        height="1"
        binding="JS_BookingReference">
        <placeholder
          name="IsReadOnly"
          value="True" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="OnSubmit"
          value="" />
        <placeholder
          name="MultiLine"
          value="False" />
        <placeholder
          name="EnableAutoComplete"
          value="False" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="CMB"
        id="d45eaff1-033d-4687-9900-ad1226bb58b2"
        left="0"
        top="1"
        width="4"
        height="1"
        binding="JS_TransportMode">
        <placeholder
          name="IsReadOnly"
          value="True" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="DisplayMode"
          value="DescOnly" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="CMB"
        id="7b6b348b-d2f6-45ed-9bb9-f93cd013fdab"
        left="4"
        top="1"
        width="4"
        height="1"
        binding="JS_PackingMode">
        <placeholder
          name="IsReadOnly"
          value="True" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="DisplayMode"
          value="DescOnly" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="SRC"
        id="1a4b33ef-7939-49db-bded-1ac772be2b04"
        left="0"
        top="2"
        width="4"
        height="1"
        binding="JS_RS_NKServiceLevel">
        <placeholder
          name="IsReadOnly"
          value="True" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="DisplayMode"
          value="DescOnly" />
        <placeholder
          name="FieldConfiguration"
          value="" />
        <placeholder
          name="DefaultFilter"
          value="" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="TXT"
        id="69746241-7b5f-4231-a907-adffadfcd271"
        left="4"
        top="2"
        width="4"
        height="1"
        binding="JS_GoodsDescription">
        <placeholder
          name="IsReadOnly"
          value="True" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="OnSubmit"
          value="" />
        <placeholder
          name="MultiLine"
          value="False" />
        <placeholder
          name="EnableAutoComplete"
          value="False" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="NUM"
        id="f5d32701-ff7c-4cd4-839d-c0521e39f701"
        left="0"
        top="3"
        width="2"
        height="1"
        binding="JS_OuterPacks">
        <placeholder
          name="IsReadOnly"
          value="True" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="NumericFieldLeftAlignment"
          value="False" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="SRC"
        id="003c1392-1027-4e2a-b322-6705c42e604b"
        left="2"
        top="3"
        width="2"
        height="1"
        binding="JS_F3_NKPackType">
        <placeholder
          name="IsReadOnly"
          value="True" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="DisplayMode"
          value="DescOnly" />
        <placeholder
          name="FieldConfiguration"
          value="" />
        <placeholder
          name="DefaultFilter"
          value="" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="MHS"
        id="77ce0fea-14ae-4070-9b29-1f85dcfa7500"
        left="4"
        top="3"
        width="4"
        height="1"
        binding="JS_ActualWeightMeasure">
        <placeholder
          name="IsReadOnly"
          value="True" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="MHS"
        id="48b1af36-a69d-4308-b652-1e7e0c1e4ec8"
        left="0"
        top="4"
        width="4"
        height="1"
        binding="JS_ActualVolumeMeasure">
        <placeholder
          name="IsReadOnly"
          value="True" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="NUM"
        id="a8eb1505-cc68-408d-96b9-58dd0ecd40aa"
        left="4"
        top="4"
        width="4"
        height="1"
        binding="JS_ActualChargeable">
        <placeholder
          name="IsReadOnly"
          value="True" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="NumericFieldLeftAlignment"
          value="False" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <additional
        code="CMB"
        id="ed934146-f597-479b-a281-fcbca41baacc"
        binding="JS_ShipmentType">
        <placeholder
          name="IsReadOnly"
          value="True" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="DisplayMode"
          value="Unspecified" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </additional>
      <additional
        code="CMB"
        id="b54c6395-6639-4926-80b2-bdf719d85599"
        binding="JS_TransportMode">
        <placeholder
          name="IsReadOnly"
          value="True" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="DisplayMode"
          value="Unspecified" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </additional>
      <additional
        code="CMB"
        id="1fd51b3b-caf6-43c4-8bba-f69cf2f12845"
        binding="JS_PackingMode">
        <placeholder
          name="IsReadOnly"
          value="True" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="DisplayMode"
          value="Unspecified" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </additional>
    </control>
    <control
      code="JDA"
      id="e6e86f92-bf96-4c6d-a699-0e2a2cd3c0e3"
      left="9"
      top="0"
      width="8"
      height="7"
      binding="ConsignorPickupAddress">
      <placeholder
        name="IsReadOnly"
        value="True" />
      <placeholder
        name="CanBeHidden"
        value="True" />
      <placeholder
        name="IsInitialFocus"
        value="False" />
      <placeholder
        name="VisibilityCondition"
        value="" />
      <placeholder
        name="Header"
        value="" />
      <placeholder
        name="HeaderAlignment"
        value="Left" />
      <placeholder
        name="IsCountryVisible"
        value="False" />
      <placeholder
        name="IsFaxVisible"
        value="False" />
      <placeholder
        name="IsStreetDetailsVisible"
        value="True" />
      <placeholder
        name="SuppressDependencyContributions"
        value="False" />
    </control>
    <control
      code="JDA"
      id="0a87c573-0ad6-48e3-8488-93ef8e7b798a"
      left="18"
      top="0"
      width="8"
      height="7"
      binding="ConsigneeDeliveryAddress">
      <placeholder
        name="IsReadOnly"
        value="True" />
      <placeholder
        name="CanBeHidden"
        value="True" />
      <placeholder
        name="IsInitialFocus"
        value="False" />
      <placeholder
        name="VisibilityCondition"
        value="" />
      <placeholder
        name="Header"
        value="" />
      <placeholder
        name="HeaderAlignment"
        value="Left" />
      <placeholder
        name="IsCountryVisible"
        value="False" />
      <placeholder
        name="IsFaxVisible"
        value="False" />
      <placeholder
        name="IsStreetDetailsVisible"
        value="True" />
      <placeholder
        name="SuppressDependencyContributions"
        value="False" />
    </control>
    <control
      code="GRP"
      id="ba3ed4d5-a3f4-420e-a519-9918e6504016"
      left="0"
      top="7"
      width="8"
      height="5">
      <placeholder
        name="CanBeHidden"
        value="True" />
      <placeholder
        name="VisibilityCondition"
        value="" />
      <placeholder
        name="Header"
        value="Charges"
        resid="38df45cd-9cf3-4ad9-8b10-23c5b0880eb2" />
      <placeholder
        name="HeaderAlignment"
        value="Left" />
      <placeholder
        name="HeaderDisplayMode"
        value="Visible" />
      <placeholder
        name="BackgroundImagePk"
        value="" />
      <placeholder
        name="BackgroundImageOverride"
        value="" />
      <control
        code="CMB"
        id="532dfa65-62ee-4dfd-a910-5ab8bb0e259b"
        left="0"
        top="0"
        width="4"
        height="1"
        binding="JS_INCO">
        <placeholder
          name="IsReadOnly"
          value="True" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="DisplayMode"
          value="Unspecified" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="NUM"
        id="d829366c-0e69-476a-9285-2f9d579e86b2"
        left="4"
        top="0"
        width="2"
        height="1"
        binding="JS_GoodsValue">
        <placeholder
          name="IsReadOnly"
          value="True" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="NumericFieldLeftAlignment"
          value="False" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="SRC"
        id="4ed1aceb-b00a-46b9-9af6-69d4d35e1c06"
        left="6"
        top="0"
        width="2"
        height="1"
        binding="JS_RX_NKGoodsValueCurr">
        <placeholder
          name="IsReadOnly"
          value="True" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="DisplayMode"
          value="CodeOnly" />
        <placeholder
          name="FieldConfiguration"
          value="" />
        <placeholder
          name="DefaultFilter"
          value="" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="TXT"
        id="f1e9aced-3e5c-4250-902d-1da8404dc72e"
        left="0"
        top="1"
        width="4"
        height="1"
        binding="JS_AdditionalTerms">
        <placeholder
          name="IsReadOnly"
          value="True" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="OnSubmit"
          value="" />
        <placeholder
          name="MultiLine"
          value="False" />
        <placeholder
          name="EnableAutoComplete"
          value="False" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="NUM"
        id="e2008278-f3b8-4a25-afec-27eb70218701"
        left="4"
        top="1"
        width="2"
        height="1"
        binding="JS_InsuranceValue">
        <placeholder
          name="IsReadOnly"
          value="True" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="NumericFieldLeftAlignment"
          value="False" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="SRC"
        id="bf06ba52-e709-4841-998e-a34950b45467"
        left="6"
        top="1"
        width="2"
        height="1"
        binding="JS_RX_NKInsuranceCurrency">
        <placeholder
          name="IsReadOnly"
          value="True" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="DisplayMode"
          value="CodeOnly" />
        <placeholder
          name="FieldConfiguration"
          value="" />
        <placeholder
          name="DefaultFilter"
          value="" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="CMB"
        id="3fac0bf6-6aeb-4ce1-a197-040ec6a39c46"
        left="0"
        top="2"
        width="8"
        height="1"
        binding="JS_HBLAWBChargesDisplay">
        <placeholder
          name="IsReadOnly"
          value="True" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="DisplayMode"
          value="DescOnly" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="CMB"
        id="770c2454-8c9c-4023-a0f3-6523d4728fab"
        left="0"
        top="3"
        width="4"
        height="1"
        binding="JS_ShippedOnBoard">
        <placeholder
          name="IsReadOnly"
          value="True" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="DisplayMode"
          value="DescOnly" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="CMB"
        id="072ff919-e676-445b-aaef-33f86a2bd2a1"
        left="4"
        top="3"
        width="4"
        height="1"
        binding="JS_ReleaseType">
        <placeholder
          name="IsReadOnly"
          value="True" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="DisplayMode"
          value="DescOnly" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
    </control>
    <control
      code="GRP"
      id="b679ce77-7c42-4373-999d-9e17b7edf7a8"
      left="9"
      top="7"
      width="8"
      height="4">
      <placeholder
        name="CanBeHidden"
        value="True" />
      <placeholder
        name="VisibilityCondition"
        value="" />
      <placeholder
        name="Header"
        value="Origin"
        resid="680d85d4-dc8b-4bf9-bf66-a4095893d0dc" />
      <placeholder
        name="HeaderAlignment"
        value="Left" />
      <placeholder
        name="HeaderDisplayMode"
        value="Visible" />
      <placeholder
        name="BackgroundImagePk"
        value="" />
      <placeholder
        name="BackgroundImageOverride"
        value="" />
      <control
        code="TXT"
        id="f591db20-2647-449c-991d-1086dc7a0281"
        left="0"
        top="0"
        width="4"
        height="1"
        binding="Origin.RL_PortName">
        <placeholder
          name="IsReadOnly"
          value="True" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="OnSubmit"
          value="" />
        <placeholder
          name="MultiLine"
          value="False" />
        <placeholder
          name="EnableAutoComplete"
          value="False" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="DTE"
        id="4448d937-343e-44eb-8bd6-66bf960c578e"
        left="4"
        top="0"
        width="4"
        height="1"
        binding="JS_E_DEP">
        <placeholder
          name="IsReadOnly"
          value="True" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="" />
        <placeholder
          name="VisibilityCondition"
          value="010534f1-a4e5-4b48-a18d-4de901dc8c76" />
        <placeholder
          name="DefaultValueExpression"
          value="" />
        <placeholder
          name="IncludeTime"
          value="True" />
        <placeholder
          name="ShowNowButton"
          value="True" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="DTE"
        id="05c66496-df16-449d-8603-ede62f651fcd"
        left="4"
        top="0"
        width="4"
        height="1"
        binding="JS_E_DEP">
        <placeholder
          name="IsReadOnly"
          value="True" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="" />
        <placeholder
          name="VisibilityCondition"
          value="5d7e8ae3-5cec-4c27-8e8b-2513529a4a9a" />
        <placeholder
          name="DefaultValueExpression"
          value="" />
        <placeholder
          name="IncludeTime"
          value="False" />
        <placeholder
          name="ShowNowButton"
          value="True" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="DTE"
        id="fbe369fe-9ac3-42fb-a3b0-1444a3d272a8"
        left="0"
        top="1"
        width="4"
        height="1"
        binding="DocsAndCartageDetail.JP_EstimatedPickup">
        <placeholder
          name="IsReadOnly"
          value="True" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="DefaultValueExpression"
          value="" />
        <placeholder
          name="IncludeTime"
          value="True" />
        <placeholder
          name="ShowNowButton"
          value="True" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="DTE"
        id="0110a35c-74f4-4961-bb4f-779bfef0f565"
        left="4"
        top="1"
        width="4"
        height="1"
        binding="DocsAndCartageDetail.JP_PickupRequiredBy">
        <placeholder
          name="IsReadOnly"
          value="True" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="DefaultValueExpression"
          value="" />
        <placeholder
          name="IncludeTime"
          value="True" />
        <placeholder
          name="ShowNowButton"
          value="True" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="DTE"
        id="1c014d42-2188-40f6-bb86-cbedb02abaa1"
        left="0"
        top="2"
        width="4"
        height="1"
        binding="DocsAndCartageDetail.JP_PickupCartageAdvised">
        <placeholder
          name="IsReadOnly"
          value="True" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="DefaultValueExpression"
          value="" />
        <placeholder
          name="IncludeTime"
          value="True" />
        <placeholder
          name="ShowNowButton"
          value="True" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="DTE"
        id="4e82ef5e-8bbf-4c64-a7b8-f4525324f377"
        left="4"
        top="2"
        width="4"
        height="1"
        binding="DocsAndCartageDetail.JP_PickupCartageCompleted">
        <placeholder
          name="IsReadOnly"
          value="True" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="DefaultValueExpression"
          value="" />
        <placeholder
          name="IncludeTime"
          value="True" />
        <placeholder
          name="ShowNowButton"
          value="True" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <additional
        code="DTE"
        id="057243a3-f75d-43c6-80ab-50eb185611af"
        binding="DocsAndCartageDetail.JP_EstimatedPickup">
        <placeholder
          name="IsReadOnly"
          value="True" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="DefaultValueExpression"
          value="" />
        <placeholder
          name="IncludeTime"
          value="True" />
        <placeholder
          name="ShowNowButton"
          value="True" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </additional>
      <additional
        code="DTE"
        id="76597172-03d9-4ec3-8ee5-bd7abb620b18"
        binding="DocsAndCartageDetail.JP_PickupCartageAdvised">
        <placeholder
          name="IsReadOnly"
          value="True" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="DefaultValueExpression"
          value="" />
        <placeholder
          name="IncludeTime"
          value="True" />
        <placeholder
          name="ShowNowButton"
          value="True" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </additional>
      <additional
        code="DTE"
        id="43caaca8-2329-483e-887e-38e8355f6684"
        binding="JS_E_DEP">
        <placeholder
          name="IsReadOnly"
          value="True" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="DefaultValueExpression"
          value="" />
        <placeholder
          name="IncludeTime"
          value="True" />
        <placeholder
          name="ShowNowButton"
          value="True" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </additional>
      <additional
        code="CMB"
        id="1a5c7ab5-4455-4f4a-a125-7f0c166ddbec"
        binding="DocsAndCartageDetail.JP_FCLPickupEquipmentNeeded">
        <placeholder
          name="IsReadOnly"
          value="True" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="DisplayMode"
          value="Unspecified" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </additional>
    </control>
    <control
      code="GRP"
      id="276b1751-07ce-4d06-80ae-8ba57dbf8078"
      left="18"
      top="7"
      width="8"
      height="5">
      <placeholder
        name="CanBeHidden"
        value="True" />
      <placeholder
        name="VisibilityCondition"
        value="" />
      <placeholder
        name="Header"
        value="Destination"
        resid="fa8fcf92-ab44-41e1-95d8-5c13fee43cec" />
      <placeholder
        name="HeaderAlignment"
        value="Left" />
      <placeholder
        name="HeaderDisplayMode"
        value="Visible" />
      <placeholder
        name="BackgroundImagePk"
        value="" />
      <placeholder
        name="BackgroundImageOverride"
        value="" />
      <control
        code="TXT"
        id="e8017459-0959-4c7b-887c-51e6afc44237"
        left="0"
        top="0"
        width="4"
        height="1"
        binding="Destination.RL_PortName">
        <placeholder
          name="IsReadOnly"
          value="True" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="OnSubmit"
          value="" />
        <placeholder
          name="MultiLine"
          value="False" />
        <placeholder
          name="EnableAutoComplete"
          value="False" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="DTE"
        id="f52f63e6-38db-433b-a254-ce0e3ad45f26"
        left="4"
        top="0"
        width="4"
        height="1"
        binding="JS_E_ARV">
        <placeholder
          name="IsReadOnly"
          value="True" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="" />
        <placeholder
          name="VisibilityCondition"
          value="010534f1-a4e5-4b48-a18d-4de901dc8c76" />
        <placeholder
          name="DefaultValueExpression"
          value="" />
        <placeholder
          name="IncludeTime"
          value="True" />
        <placeholder
          name="ShowNowButton"
          value="True" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="DTE"
        id="5315cd55-13fe-4e12-8d78-3beb2280b829"
        left="4"
        top="0"
        width="4"
        height="1"
        binding="JS_E_ARV">
        <placeholder
          name="IsReadOnly"
          value="True" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="" />
        <placeholder
          name="VisibilityCondition"
          value="5d7e8ae3-5cec-4c27-8e8b-2513529a4a9a" />
        <placeholder
          name="DefaultValueExpression"
          value="" />
        <placeholder
          name="IncludeTime"
          value="False" />
        <placeholder
          name="ShowNowButton"
          value="True" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="DTE"
        id="9473b0f8-3bc1-431b-8e9c-f2749342a61e"
        left="0"
        top="1"
        width="4"
        height="1"
        binding="DocsAndCartageDetail.JP_EstimatedDelivery">
        <placeholder
          name="IsReadOnly"
          value="True" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="DefaultValueExpression"
          value="" />
        <placeholder
          name="IncludeTime"
          value="True" />
        <placeholder
          name="ShowNowButton"
          value="True" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="DTE"
        id="04057b6f-d178-4276-863f-a48db4c11ebd"
        left="4"
        top="1"
        width="4"
        height="1"
        binding="DocsAndCartageDetail.JP_DeliveryRequiredBy">
        <placeholder
          name="IsReadOnly"
          value="True" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="DefaultValueExpression"
          value="" />
        <placeholder
          name="IncludeTime"
          value="True" />
        <placeholder
          name="ShowNowButton"
          value="True" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="DTE"
        id="89a8b174-e801-4279-8f9e-482f6588c793"
        left="0"
        top="2"
        width="4"
        height="1"
        binding="DocsAndCartageDetail.JP_DeliveryCartageAdvised">
        <placeholder
          name="IsReadOnly"
          value="True" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="DefaultValueExpression"
          value="" />
        <placeholder
          name="IncludeTime"
          value="True" />
        <placeholder
          name="ShowNowButton"
          value="True" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="DTE"
        id="16c5fb15-7026-4369-bdd3-74068804237d"
        left="4"
        top="2"
        width="4"
        height="1"
        binding="DocsAndCartageDetail.JP_DeliveryCartageCompleted">
        <placeholder
          name="IsReadOnly"
          value="True" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="DefaultValueExpression"
          value="" />
        <placeholder
          name="IncludeTime"
          value="True" />
        <placeholder
          name="ShowNowButton"
          value="True" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="DTE"
        id="ba73ffa3-8956-4509-a741-dac98c757b6e"
        left="0"
        top="3"
        width="4"
        height="1"
        binding="DocsAndCartageDetail.JP_LCLAvailable">
        <placeholder
          name="IsReadOnly"
          value="True" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="DefaultValueExpression"
          value="" />
        <placeholder
          name="IncludeTime"
          value="True" />
        <placeholder
          name="ShowNowButton"
          value="True" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="DTE"
        id="eb7a9e62-80d8-4fa2-8da6-5e299a2bb2fe"
        left="4"
        top="3"
        width="4"
        height="1"
        binding="DocsAndCartageDetail.JP_LCLStorageCommences">
        <placeholder
          name="IsReadOnly"
          value="True" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="DefaultValueExpression"
          value="" />
        <placeholder
          name="IncludeTime"
          value="True" />
        <placeholder
          name="ShowNowButton"
          value="True" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <additional
        code="DTE"
        id="2711d0b2-99ca-4bd8-b9f8-65dc4a2c6f90"
        binding="DocsAndCartageDetail.JP_EstimatedDelivery">
        <placeholder
          name="IsReadOnly"
          value="True" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="DefaultValueExpression"
          value="" />
        <placeholder
          name="IncludeTime"
          value="True" />
        <placeholder
          name="ShowNowButton"
          value="True" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </additional>
      <additional
        code="DTE"
        id="c2a432bb-98c7-43a1-a135-dbd07e83762a"
        binding="DocsAndCartageDetail.JP_DeliveryCartageAdvised">
        <placeholder
          name="IsReadOnly"
          value="True" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="DefaultValueExpression"
          value="" />
        <placeholder
          name="IncludeTime"
          value="True" />
        <placeholder
          name="ShowNowButton"
          value="True" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </additional>
      <additional
        code="DTE"
        id="9ff5cb69-a51d-4b05-ae21-b1c5c94b5784"
        binding="JS_E_ARV">
        <placeholder
          name="IsReadOnly"
          value="True" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="DefaultValueExpression"
          value="" />
        <placeholder
          name="IncludeTime"
          value="True" />
        <placeholder
          name="ShowNowButton"
          value="True" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </additional>
      <additional
        code="CMB"
        id="2d676de0-7c13-4882-8a77-f1aed680559e"
        binding="DocsAndCartageDetail.JP_FCLDeliveryEquipmentNeeded">
        <placeholder
          name="IsReadOnly"
          value="True" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="DisplayMode"
          value="Unspecified" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </additional>
    </control>
    <control
      code="GRD"
      id="e1f86cdb-dd55-4ce9-82f7-5f903ed616a6"
      left="0"
      top="12"
      height="3"
      right="0"
      binding="FirstConsolShipmentLink.JobConsol.TransportRoutings">
      <placeholder
        name="IsReadOnly"
        value="False" />
      <placeholder
        name="CanBeHidden"
        value="True" />
      <placeholder
        name="CaptionOverride"
        value="" />
      <placeholder
        name="VisibilityCondition"
        value="" />
      <placeholder
        name="HeaderAlignment"
        value="Left" />
      <placeholder
        name="HeaderDisplayMode"
        value="Visible" />
      <placeholder
        name="AllowAdd"
        value="True" />
      <placeholder
        name="AllowAttach"
        value="True" />
      <placeholder
        name="AllowDetach"
        value="True" />
      <placeholder
        name="AllowInlineEdit"
        value="True" />
      <placeholder
        name="FieldConfiguration"
        value="" />
      <placeholder
        name="DefaultSortFields">
        <xml>
          <ArrayOfFieldSortDefinition xmlns:i="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://schemas.datacontract.org/2004/07/CargoWise.Glow.Infrastructure.Interfaces" />
        </xml>
      </placeholder>
      <placeholder
        name="ShowDocumentsIcon"
        value="True" />
      <placeholder
        name="RowActionsMode"
        value="RowAndMenu" />
      <placeholder
        name="HideGridActions"
        value="False" />
      <placeholder
        name="HideFilters"
        value="False" />
      <placeholder
        name="ActionMenuItems"
        value="" />
      <placeholder
        name="EditFormFlow"
        value="" />
      <placeholder
        name="SuppressDependencyContributions"
        value="False" />
    </control>
    <control
      code="GRD"
      id="6f1fcb13-0921-4b10-a023-3d73f7b65cf9"
      left="0"
      top="15"
      height="3"
      right="0"
      binding="DestinationCFSDepartureConfirms">
      <placeholder
        name="IsReadOnly"
        value="True" />
      <placeholder
        name="CanBeHidden"
        value="True" />
      <placeholder
        name="CaptionOverride"
        value="" />
      <placeholder
        name="VisibilityCondition"
        value="" />
      <placeholder
        name="HeaderAlignment"
        value="Left" />
      <placeholder
        name="HeaderDisplayMode"
        value="Visible" />
      <placeholder
        name="AllowAdd"
        value="True" />
      <placeholder
        name="AllowAttach"
        value="True" />
      <placeholder
        name="AllowDetach"
        value="True" />
      <placeholder
        name="AllowInlineEdit"
        value="True" />
      <placeholder
        name="FieldConfiguration"
        value="" />
      <placeholder
        name="DefaultSortFields">
        <xml>
          <ArrayOfFieldSortDefinition xmlns:i="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://schemas.datacontract.org/2004/07/CargoWise.Glow.Infrastructure.Interfaces" />
        </xml>
      </placeholder>
      <placeholder
        name="ShowDocumentsIcon"
        value="True" />
      <placeholder
        name="RowActionsMode"
        value="RowAndMenu" />
      <placeholder
        name="HideGridActions"
        value="False" />
      <placeholder
        name="HideFilters"
        value="False" />
      <placeholder
        name="ActionMenuItems"
        value="" />
      <placeholder
        name="EditFormFlow"
        value="" />
      <placeholder
        name="SuppressDependencyContributions"
        value="False" />
    </control>
    <control
      code="GRD"
      id="37b4b8f1-b359-418b-8ddc-964cc0826a35"
      left="0"
      top="18"
      right="0"
      bottom="0"
      binding="JobPackLines">
      <placeholder
        name="IsReadOnly"
        value="True" />
      <placeholder
        name="CanBeHidden"
        value="True" />
      <placeholder
        name="CaptionOverride"
        value="" />
      <placeholder
        name="VisibilityCondition"
        value="" />
      <placeholder
        name="HeaderAlignment"
        value="Left" />
      <placeholder
        name="HeaderDisplayMode"
        value="Visible" />
      <placeholder
        name="AllowAdd"
        value="False" />
      <placeholder
        name="AllowAttach"
        value="True" />
      <placeholder
        name="AllowDetach"
        value="True" />
      <placeholder
        name="AllowInlineEdit"
        value="False" />
      <placeholder
        name="FieldConfiguration"
        value="" />
      <placeholder
        name="DefaultSortFields">
        <xml>
          <ArrayOfFieldSortDefinition xmlns:i="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://schemas.datacontract.org/2004/07/CargoWise.Glow.Infrastructure.Interfaces" />
        </xml>
      </placeholder>
      <placeholder
        name="ShowDocumentsIcon"
        value="True" />
      <placeholder
        name="RowActionsMode"
        value="RowAndMenu" />
      <placeholder
        name="HideGridActions"
        value="False" />
      <placeholder
        name="HideFilters"
        value="False" />
      <placeholder
        name="ActionMenuItems"
        value="" />
      <placeholder
        name="EditFormFlow"
        value="" />
      <placeholder
        name="SuppressDependencyContributions"
        value="False" />
    </control>
  </form>
