#transformationVersion: 70.0
#
VZ_PK: 8929cf7490c24de790ce78b30e6fc9d4
VZ_ConfigurationKey: 8929cf74-90c2-4de7-90ce-78b30e6fc9d4
VZ_FormID: ETL - VDV3 - Origin Depot - Edit Item Line
VZ_Caption:
  resKey: VZ_Caption|8929cf74-90c2-4de7-90ce-78b30e6fc9d4
  text: Edit Item Line
VZ_FormFactor: DSK
VZ_EntityType: IHVLVItemLine
VZ_FormType: FRM
VZ_RequiresLogin: true
VZ_Dependencies: >-
  <dependencies xmlns="http://cargowise.com/glow/2014/11/14/dependencies.xsd">
    <expandPath
      path="OriginCountry" />
  </dependencies>
VZ_FormData: >-
  <form
    id="3b9d24c5-8b9c-4e1b-93cf-f3525e6de687" xmlns="http://wisetechglobal.com/glow/2017/05/26/form.xsd">
    <placeholder
      name="BackgroundImagePk"
      value="" />
    <placeholder
      name="BackgroundSize"
      value="Auto" />
    <placeholder
      name="BackgroundImageOverride"
      value="" />
    <placeholder
      name="FormLayout"
      value="Splitview" />
    <placeholder
      name="ExpandPaths"
      value="" />
    <placeholder
      name="ScanInputHandling"
      value="NON" />
    <placeholder
      name="RemovePadding"
      value="false" />
    <placeholder
      name="TurnOnPortableOneToOneDesignMode"
      value="false" />
    <placeholder
      name="ContainsDefaultButton"
      value="False" />
    <control
      code="GRP"
      id="bd1698f9-36c6-49e1-a8d8-2fed2d150162"
      top="0"
      width="14"
      height="7.6">
      <placeholder
        name="CanBeHidden"
        value="True" />
      <placeholder
        name="VisibilityCondition"
        value="20a8ecf5-678d-4e7b-98f8-6d957f4eb9ec" />
      <placeholder
        name="Header"
        value="Item Line Details"
        resid="f13a6f27-be02-4d50-be6e-b8e9d751eb0e" />
      <placeholder
        name="HeaderAlignment"
        value="Center" />
      <placeholder
        name="HeaderDisplayMode"
        value="Visible" />
      <placeholder
        name="BackgroundImagePk"
        value="" />
      <placeholder
        name="BackgroundImageOverride"
        value="" />
      <control
        code="TXT"
        id="822fa463-42a0-4d9d-8c7b-e7823fdcae7d"
        left="1"
        top="0"
        width="4"
        height="1"
        binding="HVS_DestinationTariff">
        <placeholder
          name="IsReadOnly"
          value="False" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="OnSubmit"
          value="" />
        <placeholder
          name="MultiLine"
          value="False" />
        <placeholder
          name="EnableAutoComplete"
          value="False" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="NUM"
        id="2fd8a05a-176d-410a-bcff-2421676818ff"
        left="5"
        top="0"
        width="4"
        height="1"
        binding="HVS_NetWeight">
        <placeholder
          name="IsReadOnly"
          value="False" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="NumericFieldLeftAlignment"
          value="False" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="NUM"
        id="21b07e06-49ed-4e1c-b7d0-4e29514def15"
        left="9"
        top="0"
        width="4"
        height="1"
        binding="HVS_Quantity">
        <placeholder
          name="IsReadOnly"
          value="False" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="NumericFieldLeftAlignment"
          value="False" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="TXT"
        id="de9db6f5-3779-4719-8257-5142137f3538"
        left="1"
        top="1"
        width="4"
        height="1"
        binding="HVS_OriginTariff">
        <placeholder
          name="IsReadOnly"
          value="False" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="OnSubmit"
          value="" />
        <placeholder
          name="MultiLine"
          value="False" />
        <placeholder
          name="EnableAutoComplete"
          value="False" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="NUM"
        id="e3e8492f-fd45-4bf7-be78-31eca79f5e08"
        left="5"
        top="1"
        width="4"
        height="1"
        binding="HVS_GrossWeight">
        <placeholder
          name="IsReadOnly"
          value="False" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="NumericFieldLeftAlignment"
          value="False" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="NUM"
        id="56562d89-4478-40a9-b1e5-ba5364f15826"
        left="9"
        top="1"
        width="4"
        height="1"
        binding="HVS_CustomsValue">
        <placeholder
          name="IsReadOnly"
          value="False" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="NumericFieldLeftAlignment"
          value="False" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="SRC"
        id="0750059c-6840-4e75-bb59-dc71d0437ed0"
        left="1"
        top="2"
        width="4"
        height="1"
        binding="HVS_RN_NKOriginCountryCode">
        <placeholder
          name="IsReadOnly"
          value="False" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="DisplayMode"
          value="Unspecified" />
        <placeholder
          name="FieldConfiguration"
          value="" />
        <placeholder
          name="DefaultFilter"
          value="" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="CMB"
        id="ce45aedd-5ad2-41cb-87d4-e8ce36d60887"
        left="5"
        top="2"
        width="4"
        height="1"
        binding="HVS_WeightUnit">
        <placeholder
          name="IsReadOnly"
          value="False" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="DisplayMode"
          value="Unspecified" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="NUM"
        id="7bf5b31c-a8ca-4e53-a809-bf35a24ddf1e"
        left="9"
        top="2"
        width="4"
        height="1"
        binding="HVS_IntrinsicValue">
        <placeholder
          name="IsReadOnly"
          value="False" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="NumericFieldLeftAlignment"
          value="False" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="SRC"
        id="3597c87f-6a0d-400a-a813-b08fad1913ea"
        left="1"
        top="3"
        width="12"
        height="1"
        binding="HVS_ProductCode">
        <placeholder
          name="IsReadOnly"
          value="False" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="DisplayMode"
          value="Unspecified" />
        <placeholder
          name="FieldConfiguration"
          value="" />
        <placeholder
          name="DefaultFilter"
          value="" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="TXT"
        id="68df589f-d686-4bb7-9b4f-c685d77ef294"
        left="1"
        top="4"
        width="6"
        height="1"
        binding="HVS_GoodsDescription">
        <placeholder
          name="IsReadOnly"
          value="False" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="OnSubmit"
          value="" />
        <placeholder
          name="MultiLine"
          value="False" />
        <placeholder
          name="EnableAutoComplete"
          value="False" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="TXT"
        id="ffda11c4-31d8-4067-945b-2f153732c6d5"
        left="7"
        top="4"
        width="6"
        height="1"
        binding="HVS_OriginGoodsDescription">
        <placeholder
          name="IsReadOnly"
          value="False" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="OnSubmit"
          value="" />
        <placeholder
          name="MultiLine"
          value="False" />
        <placeholder
          name="EnableAutoComplete"
          value="False" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="HPL"
        id="c900fd7c-f8d1-4bbc-a0b6-83eb339ed303"
        left="1"
        top="5.2"
        width="12"
        height="1"
        binding="HVS_ItemURL">
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="CaptionOverride"
          value="" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="FontWeight"
          value="Bold" />
        <placeholder
          name="OpenInNewWindow"
          value="True" />
      </control>
    </control>
    <control
      code="GRP"
      id="8392ea22-354e-4533-9dfb-95d084479341"
      top="0"
      width="14"
      height="7.6">
      <placeholder
        name="CanBeHidden"
        value="True" />
      <placeholder
        name="VisibilityCondition"
        value="a90690e2-327c-42a7-8733-f72ad507296f" />
      <placeholder
        name="Header"
        value="Item Line Details"
        resid="3c551841-3600-48c3-b2e4-2fadeb134a60" />
      <placeholder
        name="HeaderAlignment"
        value="Center" />
      <placeholder
        name="HeaderDisplayMode"
        value="Visible" />
      <placeholder
        name="BackgroundImagePk"
        value="" />
      <placeholder
        name="BackgroundImageOverride"
        value="" />
      <control
        code="TXT"
        id="ecf5a8ef-71f1-43cc-ac45-a8af4da52d6c"
        left="1"
        top="0"
        width="4"
        height="1"
        binding="HVS_DestinationTariff">
        <placeholder
          name="IsReadOnly"
          value="True" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="OnSubmit"
          value="" />
        <placeholder
          name="MultiLine"
          value="False" />
        <placeholder
          name="EnableAutoComplete"
          value="False" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="NUM"
        id="55cc2731-8b02-43dd-8a71-dc2655f33d98"
        left="5"
        top="0"
        width="4"
        height="1"
        binding="HVS_NetWeight">
        <placeholder
          name="IsReadOnly"
          value="True" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="NumericFieldLeftAlignment"
          value="False" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="NUM"
        id="7cff38fc-bfed-47da-a232-6026e25b4b03"
        left="9"
        top="0"
        width="4"
        height="1"
        binding="HVS_Quantity">
        <placeholder
          name="IsReadOnly"
          value="True" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="NumericFieldLeftAlignment"
          value="False" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="TXT"
        id="f41d503c-4cc0-48e4-baf4-ad7dd4663608"
        left="1"
        top="1"
        width="4"
        height="1"
        binding="HVS_OriginTariff">
        <placeholder
          name="IsReadOnly"
          value="True" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="OnSubmit"
          value="" />
        <placeholder
          name="MultiLine"
          value="False" />
        <placeholder
          name="EnableAutoComplete"
          value="False" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="NUM"
        id="eb84af62-c410-429f-93af-915eda03bfa4"
        left="5"
        top="1"
        width="4"
        height="1"
        binding="HVS_GrossWeight">
        <placeholder
          name="IsReadOnly"
          value="True" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="NumericFieldLeftAlignment"
          value="False" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="NUM"
        id="184b768c-a0d4-445f-944f-c60d34241527"
        left="9"
        top="1"
        width="4"
        height="1"
        binding="HVS_CustomsValue">
        <placeholder
          name="IsReadOnly"
          value="True" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="NumericFieldLeftAlignment"
          value="False" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="SRC"
        id="3875d3be-2117-409b-998f-cc74398e2da6"
        left="1"
        top="2"
        width="4"
        height="1"
        binding="HVS_RN_NKOriginCountryCode">
        <placeholder
          name="IsReadOnly"
          value="True" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="DisplayMode"
          value="Unspecified" />
        <placeholder
          name="FieldConfiguration"
          value="" />
        <placeholder
          name="DefaultFilter"
          value="" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="CMB"
        id="cf3b158f-fd6b-47cf-b527-2ce258821280"
        left="5"
        top="2"
        width="4"
        height="1"
        binding="HVS_WeightUnit">
        <placeholder
          name="IsReadOnly"
          value="True" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="DisplayMode"
          value="CodeDesc" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="NUM"
        id="6d8e85c8-8307-4054-8b98-b66510fd40bb"
        left="9"
        top="2"
        width="4"
        height="1"
        binding="HVS_IntrinsicValue">
        <placeholder
          name="IsReadOnly"
          value="True" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="NumericFieldLeftAlignment"
          value="False" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="SRC"
        id="0dda8655-87f3-4364-95d1-4143e3c6f116"
        left="1"
        top="3"
        width="12"
        height="1"
        binding="HVS_ProductCode">
        <placeholder
          name="IsReadOnly"
          value="True" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="DisplayMode"
          value="Unspecified" />
        <placeholder
          name="FieldConfiguration"
          value="" />
        <placeholder
          name="DefaultFilter"
          value="" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="TXT"
        id="1393b8f6-e8b7-4eb3-b87c-fe6fdc2e098e"
        left="1"
        top="4"
        width="6"
        height="1"
        binding="HVS_GoodsDescription">
        <placeholder
          name="IsReadOnly"
          value="True" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="OnSubmit"
          value="" />
        <placeholder
          name="MultiLine"
          value="False" />
        <placeholder
          name="EnableAutoComplete"
          value="False" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="TXT"
        id="15e88344-10a7-4adb-a2a8-8ee97b2e5508"
        left="7"
        top="4"
        width="6"
        height="1"
        binding="HVS_OriginGoodsDescription">
        <placeholder
          name="IsReadOnly"
          value="True" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="OnSubmit"
          value="" />
        <placeholder
          name="MultiLine"
          value="False" />
        <placeholder
          name="EnableAutoComplete"
          value="False" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="HPL"
        id="587ce20f-a9c8-4dce-8f6f-ce733d277ca2"
        left="1"
        top="5.2"
        width="12"
        height="1"
        binding="HVS_ItemURL">
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="CaptionOverride"
          value="" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="FontWeight"
          value="Bold" />
        <placeholder
          name="OpenInNewWindow"
          value="True" />
      </control>
    </control>
    <control
      code="SIM"
      id="29cdf07a-3998-441a-81cf-3981250dc4e3"
      top="7.6"
      width="10"
      height="4.4">
      <placeholder
        name="CanBeHidden"
        value="True" />
      <placeholder
        name="VisibilityCondition"
        value="" />
      <placeholder
        name="Behaviour"
        value="Image" />
      <placeholder
        name="Image"
        value="05efab2c7a124c39ac1fd6924e7eb0fd" />
      <placeholder
        name="SystemResource"
        value="" />
      <placeholder
        name="ImageOverride"
        value="" />
      <placeholder
        name="SuppressDependencyContributions"
        value="False" />
    </control>
  </form>
