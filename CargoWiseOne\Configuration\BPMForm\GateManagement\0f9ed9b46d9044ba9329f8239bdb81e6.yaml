#transformationVersion: 70.0
#
VZ_PK: 0f9ed9b46d9044ba9329f8239bdb81e6
VZ_ConfigurationKey: 0f9ed9b4-6d90-44ba-9329-f8239bdb81e6
VZ_FormID: GDM - Gate Out - Canceled (Vehicle Entry)
VZ_Caption:
  resKey: VZ_Caption|0f9ed9b46d9044ba9329f8239bdb81e6
  text: Gate Out - Canceled
VZ_FormFactor: DSK
VZ_EntityType: IGteVehicleEntry
VZ_FormType: FRM
VZ_RequiresLogin: true
VZ_MaterialDesign: true
VZ_Dependencies: <dependencies xmlns="http://cargowise.com/glow/2014/11/14/dependencies.xsd" />
VZ_StatusBindingPath: Status
VZ_EntityStatusDisplayMode: ReadOnly
VZ_FormData: >-
  <form
    id="b422810d-07c9-4789-977b-03ceae0ac055" xmlns="http://wisetechglobal.com/glow/2017/05/26/form.xsd">
    <placeholder
      name="Padding"
      value="pa-4" />
    <placeholder
      name="NoGutters"
      value="True" />
    <placeholder
      name="Layout"
      value="grid" />
    <placeholder
      name="Align"
      value="left" />
    <control
      code="BOX"
      id="8ff51775-901b-43a0-9448-f7ebbe413cfa"
      binding="">
      <placeholder
        name="Height"
        value="100%" />
      <placeholder
        name="Layout"
        value="grid" />
      <control
        code="PNL"
        id="7ab8d565-9577-4eb6-839a-4934d025d393"
        binding="">
        <placeholder
          name="Caption"
          value="Transport details"
          resid="042d157f-e937-41b7-af73-eaddcbb4be03" />
        <placeholder
          name="FitToHeight"
          value="True" />
        <placeholder
          name="Margin"
          value="mr-4" />
        <placeholder
          name="Columns"
          value="col-3" />
        <control
          code="BOX"
          id="dae81f7c-cfe8-42a4-882d-9837025b7776">
          <placeholder
            name="Layout"
            value="grid" />
          <placeholder
            name="Margin"
            value="mb-2" />
          <control
            code="TXT"
            id="ae5b5485-bd8b-4b23-9a2c-60c7f07fbcbf"
            binding="VehicleMovement.VehicleRegistrationForBinding">
            <placeholder
              name="Columns"
              value="col-6" />
            <placeholder
              name="CaptionOverride"
              value="Vehicle registration"
              resid="ee558f75-d866-46c0-8451-f0b9fa49c9df" />
            <placeholder
              name="IsReadOnly"
              value="True" />
            <placeholder
              name="Margin"
              value="mb-2" />
          </control>
          <control
            code="SRC"
            id="5fd047ff-285e-49dc-8742-5341279c6e0e"
            binding="VehicleMovement.GVM_RC_VehicleType">
            <placeholder
              name="Columns"
              value="col-6" />
            <placeholder
              name="IsReadOnly"
              value="True" />
            <placeholder
              name="Margin"
              value="mb-2" />
          </control>
          <control
            code="TXT"
            id="3c7858ea-a3d6-48a5-940c-8667375e7286"
            binding="VehicleMovement.TransportProvider">
            <placeholder
              name="IsReadOnly"
              value="True" />
            <placeholder
              name="Margin"
              value="mb-2" />
          </control>
          <control
            code="TXT"
            id="38f56da3-a1c3-419e-8aa2-20e3de3a934f"
            binding="VehicleMovement.MainBooking.GBK_ReferenceNumber">
            <placeholder
              name="Columns"
              value="col-6" />
            <placeholder
              name="Margin"
              value="mb-2" />
            <placeholder
              name="CaptionOverride"
              value="Gate booking"
              resid="58f8d9a8-ba84-4e43-a293-a9d5c1795dc8" />
            <placeholder
              name="IsReadOnly"
              value="True" />
          </control>
          <control
            code="CMB"
            id="9b32b962-f249-4e43-bbb8-9ba705439961"
            binding="VehicleMovement.MainBooking.GBK_BookingType">
            <placeholder
              name="IsReadOnly"
              value="True" />
            <placeholder
              name="Columns"
              value="col-6" />
            <placeholder
              name="Margin"
              value="mb-2" />
          </control>
          <control
            code="TXA"
            id="938db762-6b82-42bc-b647-e5d014bc08f7"
            binding="VehicleMovement.GateReferenceCheckGateOut">
            <placeholder
              name="CaptionOverride"
              value="Gate reference check"
              resid="94150ba7-3fbd-41f7-8623-4abab7885a3c" />
            <placeholder
              name="Rows"
              value="3" />
            <placeholder
              name="Margin"
              value="mb-2" />
            <placeholder
              name="IsReadOnly"
              value="True" />
          </control>
          <control
            code="SRC"
            id="ffa9a84e-407e-42f8-b206-70cb6ae7c964"
            binding="VehicleMovement.MainBooking.GBK_WW_Facility">
            <placeholder
              name="IsReadOnly"
              value="True" />
            <placeholder
              name="CaptionOverride"
              value="Facility"
              resid="40841b55-3286-46c8-adff-769c9c676b24" />
            <placeholder
              name="Columns"
              value="col-6" />
            <placeholder
              name="Margin"
              value="mb-2" />
          </control>
          <control
            code="SRC"
            id="f4e60d3f-9e21-443d-be1c-4f66091e4f6b"
            binding="VehicleMovement.GVM_WL_Location">
            <placeholder
              name="IsReadOnly"
              value="True" />
            <placeholder
              name="Columns"
              value="col-6" />
            <placeholder
              name="Margin"
              value="mb-2" />
          </control>
          <control
            code="DVR"
            id="250597f4-3b86-4b15-9bfd-260696080647">
            <placeholder
              name="Variant"
              value="solid" />
            <placeholder
              name="Margin"
              value="mb-2" />
          </control>
          <control
            code="BOX"
            id="9e1f7dac-a92f-4fe4-b86d-fe18d805d811">
            <placeholder
              name="Columns"
              value="col-6" />
            <control
              code="TXT"
              id="3ca3f4bc-5e88-42ff-a1dd-37d60a2f3fae"
              binding="VehicleMovement.GateInActionNumber">
              <placeholder
                name="IsReadOnly"
                value="True" />
              <placeholder
                name="Columns"
                value="col-6" />
              <placeholder
                name="Margin"
                value="mb-2" />
               <placeholder
                 name="CaptionOverride"
                 value="Gate in number"
                 resid="ebe3ecd0-efb7-444b-abf7-cac52ca178d7" />
            </control>
            <control
              code="TXT"
              id="d3df9560-88e3-4a9e-a46b-453214a725d1"
              binding="VehicleMovement.GateInVehicleEntry.CreatedByStaff.GS_FullName">
              <placeholder
                name="CaptionOverride"
                value="Entry gate operator"
                resid="efe7dc59-84aa-449d-a6d4-37e17f27b971" />
              <placeholder
                name="Columns"
                value="col-6" />
              <placeholder
                name="IsReadOnly"
                value="True" />
              <placeholder
                name="Margin"
                value="mb-2" />
            </control>
            <control
              code="TXT"
              id="cf36b8b2-3152-4e4b-802d-cb0e97f862ac"
              binding="VehicleMovement.GateInVehicleEntry.GVE_DriverName">
              <placeholder
                name="Columns"
                value="col-6" />
              <placeholder
                name="CaptionOverride"
                value="Entry driver name"
                resid="f4b6ab57-d067-4e0f-b3a2-9e057333add5" />
              <placeholder
                name="IsReadOnly"
                value="True" />
              <placeholder
                name="Margin"
                value="mb-2" />
            </control>
            <control
              code="TXT"
              id="ce5ecdb1-f2e3-45e4-9177-a03401d6d7a6"
              binding="VehicleMovement.GateInVehicleEntry.GVE_DriverLicenseNumber">
              <placeholder
                name="Columns"
                value="col-6" />
              <placeholder
                name="CaptionOverride"
                value="Entry driver license"
                resid="135c430a-aee5-40a6-90d7-c31a0c38b961" />
              <placeholder
                name="IsReadOnly"
                value="True" />
              <placeholder
                name="Margin"
                value="mb-2" />
            </control>
            <control
              code="DTE"
              id="bf7c9c5d-4ca8-4936-91a8-c8cb64c1260e"
              binding="VehicleMovement.GateInVehicleEntry.GVE_EntryTime">
              <placeholder
                name="Columns"
                value="col-6" />
              <placeholder
                name="CaptionOverride"
                value="Entry date and time"
                resid="4faa7c7a-0017-4bcb-874c-efd2ee8aabfd" />
              <placeholder
                name="Required"
                value="False" />
              <placeholder
                name="IsReadOnly"
                value="True" />
              <placeholder
                name="Margin"
                value="mb-2" />
            </control>
            <control
              code="MHS"
              id="7d2f02bb-76d1-4d87-b65f-a86b9a63ffa7"
              binding="VehicleMovement.GateInVehicleEntry.GVE_WeightMeasure">
              <placeholder
                name="Columns"
                value="col-6" />
              <placeholder
                name="CaptionOverride"
                value="Entry weight"
                resid="9af06ae6-101a-41ef-8dce-c47bc0ab1cd5" />
              <placeholder
                name="IsReadOnly"
                value="True" />
              <placeholder
                name="Margin"
                value="mb-2" />
            </control>
            <control
              code="SRC"
              id="75a4a36e-598e-4c44-8d47-3cd1353fdb00"
              binding="VehicleMovement.GateInVehicleEntry.Lane.GLN_GTE_Gate">
              <placeholder
                name="IsReadOnly"
                value="True" />
              <placeholder
                name="CaptionOverride"
                value="Entry gate"
                resid="0a143b32-943b-49ef-8bf6-0e22d5df1c88" />
              <placeholder
                name="Columns"
                value="col-6" />
              <placeholder
                name="Margin"
                value="mb-2" />
            </control>
            <control
              code="SRC"
              id="9fc1b0de-4149-4f72-b1c5-a1c26af6b941"
              binding="VehicleMovement.GateInVehicleEntry.GVE_GLN_Lane">
              <placeholder
                name="Columns"
                value="col-6" />
              <placeholder
                name="CaptionOverride"
                value="Entry lane"
                resid="691322cd-2e72-40df-a604-b9df58794b19" />
              <placeholder
                name="IsReadOnly"
                value="True" />
              <placeholder
                name="Margin"
                value="mb-2" />
            </control>
          </control>
          <control
            code="BOX"
            id="2094efd0-fca0-4397-ac92-af91f1844fdf">
            <placeholder
              name="Columns"
              value="col-6" />
            <control
              code="TXT"
              id="db739a00-7f85-4697-8212-6bf2acbe982d"
              binding="GVE_GateActionNumber">
              <placeholder
                name="Columns"
                value="col-6" />
              <placeholder
                name="IsReadOnly"
                value="True" />
              <placeholder
                name="Margin"
                value="mb-2" />
              <placeholder
                name="CaptionOverride"
                value="Gate out number"
                resid="1fb92d86-f668-4240-adcd-791f2468be8a" />
            </control>
            <control
              code="TXT"
              id="31d1bfe4-ceae-41d1-8d55-dc8375a08106"
              binding="CreatedByStaff.GS_FullName">
              <placeholder
                name="CaptionOverride"
                value="Exit gate operator"
                resid="634ffe25-3397-4647-928c-66708d94cf8d" />
              <placeholder
                name="Columns"
                value="col-6" />
              <placeholder
                name="IsReadOnly"
                value="True" />
              <placeholder
                name="Margin"
                value="mb-2" />
            </control>
            <control
              code="TXT"
              id="d3236534-88e8-4059-9b0b-a400b04ccf0b"
              binding="GVE_DriverName">
              <placeholder
                name="CaptionOverride"
                value="Exit driver name"
                resid="0b846b2a-10c6-4122-a926-df592eed7011" />
              <placeholder
                name="Columns"
                value="col-6" />
              <placeholder
                name="IsReadOnly"
                value="True" />
              <placeholder
                name="Margin"
                value="mb-2" />
            </control>
            <control
              code="TXT"
              id="76f498bc-2d84-4b00-a18b-dc4bf898b90d"
              binding="GVE_DriverLicenseNumber">
              <placeholder
                name="CaptionOverride"
                value="Exit driver license"
                resid="6af83de0-b543-4734-bef1-5b41b47619c1" />
              <placeholder
                name="Columns"
                value="col-6" />
              <placeholder
                name="IsReadOnly"
                value="True" />
              <placeholder
                name="Margin"
                value="mb-2" />
            </control>
            <control
              code="DTE"
              id="94b8f24c-ad3f-4466-9604-4c4eab199551"
              binding="GVE_EntryTime">
              <placeholder
                name="CaptionOverride"
                value="Exit date and time"
                resid="c2b5f7cc-a59c-4302-a608-daf6b0aacc32" />
              <placeholder
                name="Columns"
                value="col-6" />
              <placeholder
                name="IsReadOnly"
                value="True" />
              <placeholder
                name="Margin"
                value="mb-2" />
            </control>
            <control
              code="MHS"
              id="4ea7af7f-8964-4b20-a64b-d9205281c54a"
              binding="GVE_WeightMeasure">
              <placeholder
                name="CaptionOverride"
                value="Exit weight"
                resid="205f5043-4b6d-49ac-84d9-dcb4906f0dc0" />
              <placeholder
                name="Columns"
                value="col-6" />
              <placeholder
                name="IsReadOnly"
                value="True" />
              <placeholder
                name="Margin"
                value="mb-2" />
            </control>
            <control
              code="SRC"
              id="3eaff677-2731-4aa4-a4e4-0b6048d384a1"
              binding="Lane.GLN_GTE_Gate">
              <placeholder
                name="CaptionOverride"
                value="Exit gate"
                resid="eab0ee22-5c0d-47d7-85c4-601d30b724fc" />
              <placeholder
                name="IsReadOnly"
                value="True" />
              <placeholder
                name="Columns"
                value="col-6" />
              <placeholder
                name="Margin"
                value="mb-2" />
            </control>
            <control
              code="SRC"
              id="cb585f0d-39fb-4a0b-932c-fc949e242208"
              binding="GVE_GLN_Lane">
              <placeholder
                name="CaptionOverride"
                value="Exit lane"
                resid="a4d60df2-e64a-4eb3-b310-10b413fbb5e8" />
              <placeholder
                name="Columns"
                value="col-6" />
              <placeholder
                name="IsReadOnly"
                value="True" />
              <placeholder
                name="Margin"
                value="mb-2" />
            </control>
          </control>
        </control>
      </control>
      <control
        code="PNL"
        id="c01d271f-9f8c-4572-9c04-81f2f0c7d2fe"
        binding="">
        <placeholder
          name="FitToHeight"
          value="True" />
        <placeholder
          name="Columns"
          value="col-9" />
        <placeholder
          name="Height"
          value="100%" />
        <control
          code="RDT"
          id="335f86f4-4c4f-4cdc-a70a-85947c56a559"
          binding="VehicleMovement.GteGateMovements">
          <placeholder
            name="AllowAdd"
            value="False" />
          <placeholder
            name="InlineEdit"
            value="cell" />
          <placeholder
            name="CaptionOverride"
            value="Associated Bookings"
            resid="51299bb0-29a8-4a52-b23c-d7766598d2b0" />
          <placeholder
            name="FieldConfiguration">
            <xml>
              <fields xmlns="">
                <field
                  path="MovementType"
                  width="300"
                  mode="Default" />
                <field
                  path="MovementBooking.GBM_SourceReferenceNumber"
                  width="150"
                  mode="Default" />
                <field
                  path="GGM_RC_UnitType"
                  width="300"
                  mode="Default" />
                <field
                  path="GGM_UnitNumber"
                  width="300"
                  mode="Default" />
                <field
                  path="GGM_TransportReference"
                  width="300"
                  mode="Default" />
                <field
                  path="MovementBooking.GBM_SlotStartTime"
                  width="200"
                  mode="Default" />
                <field
                  path="MovementBooking.GBM_SlotEndTime"
                  width="200"
                  mode="Default" />
                <field
                  path="BookingReferenceNumber"
                  width="100"
                  mode="Default" />
                <field
                  path="GGM_RH_NKCargoType"
                  width="180"
                  mode="Default" />
                <field
                  path="GGM_F3_NKPackageType"
                  width="100"
                  mode="Default" />
              </fields>
            </xml>
          </placeholder>
          <placeholder
            name="AllowAttach"
            value="False" />
          <placeholder
            name="AllowDetach"
            value="False" />
          <placeholder
            name="DisabledGridRowActions"
            value="Remove" />
          <placeholder
            name="HideItemActions"
            value="True" />
          <placeholder
            name="IsReadOnly"
            value="True" />
          <placeholder
            name="ActionMenuItems">
            <xml>
              <formFlows xmlns="">
                <formFlow
                  inDrawer="True">af0773685c154e8f91653ebb54b67ad5</formFlow>
              </formFlows>
            </xml>
          </placeholder>
        </control>
      </control>
    </control>
  </form>
