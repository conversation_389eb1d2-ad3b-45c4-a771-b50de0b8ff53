#transformationVersion: 70.0
#
VZ_PK: d9399e3c15034b91af05fd1322c793f1
VZ_ConfigurationKey: d9399e3c-1503-4b91-af05-fd1322c793f1
VZ_FormID: GDM - Pending Movements
VZ_Caption:
  resKey: VZ_Caption|d9399e3c15034b91af05fd1322c793f1
  text: Pending movements
VZ_FormFactor: DSK
VZ_FormType: PAG
VZ_RequiresLogin: true
VZ_MaterialDesign: true
VZ_Dependencies: <dependencies xmlns="http://cargowise.com/glow/2014/11/14/dependencies.xsd" />
VZ_FormData: >-
  <form
    id="f56ae29c-f65f-48de-9404-f4460ba3aee6" xmlns="http://wisetechglobal.com/glow/2017/05/26/form.xsd">
    <placeholder
      name="Layout"
      value="fill" />
    <control
      code="SDT"
      id="84660a3e-246d-80a1-414c-512d7dff83d1"
      binding="">
      <placeholder
        name="CaptionOverride"
        value="Pending movements"
        resid="776e69dc-8d6e-48c4-b516-540a9afb89aa" />
      <placeholder
        name="EntityType"
        value="IGtePendingMovement" />
      <placeholder
        name="FullPage"
        value="True" />
      <placeholder
        name="ShowRefresh"
        value="True" />
      <placeholder
        name="ShowAutoRefresh"
        value="True" />
      <placeholder
        name="AutoRefresh"
        value="fiveMinutes" />
      <placeholder
        name="VisibilityCondition"
        value="%.IsTWHGateInWithoutBookingDisabled &amp;&amp; %.CurrentBranch.Warehouses.Where(WW_IsActive).Count() == 1 &amp;&amp; %.CurrentBranch.TransitWarehouse.Exists &amp;&amp; %.CurrentBranch.TransitWarehouse.WW_IsActive" />
    </control>
    <control
      code="SDT"
      id="0cc92307-3283-4403-b23f-fef673a1a7b5"
      binding="">
      <placeholder
        name="CaptionOverride"
        value="Pending movements"
        resid="9beb5c92-14c7-46dc-af7c-a14a4c082f50" />
      <placeholder
        name="EntityType"
        value="IGtePendingMovement" />
      <placeholder
        name="FullPage"
        value="True" />
      <placeholder
        name="ShowRefresh"
        value="True" />
      <placeholder
        name="ShowAutoRefresh"
        value="True" />
      <placeholder
        name="AutoRefresh"
        value="fiveMinutes" />
      <placeholder
        name="NewCaption"
        value="New gate in without booking" />
      <placeholder
        name="NewFormFlowConfiguration">
        <xml>
          <formFlows xmlns="">
            <formFlow>2f01fc828bea4e19bfd43cfc3d60049d</formFlow>
          </formFlows>
        </xml>
      </placeholder>
      <placeholder
        name="VisibilityCondition"
        value="!(%.IsTWHGateInWithoutBookingDisabled &amp;&amp; %.CurrentBranch.Warehouses.Where(WW_IsActive).Count() == 1 &amp;&amp; %.CurrentBranch.TransitWarehouse.Exists &amp;&amp; %.CurrentBranch.TransitWarehouse.WW_IsActive)" />
    </control>
  </form>
