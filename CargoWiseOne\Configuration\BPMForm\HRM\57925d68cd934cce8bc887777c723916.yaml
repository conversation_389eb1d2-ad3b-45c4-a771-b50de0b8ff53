#transformationVersion: 70.0
#
VZ_PK: 57925d68cd934cce8bc887777c723916
VZ_ConfigurationKey: 57925d68-cd93-4cce-8bc8-87777c723916
VZ_FormID: HRM - Bulk Import - Cost Centers
VZ_Caption:
  resKey: VZ_Caption|57925d68-cd93-4cce-8bc8-87777c723916
  text: Bulk Import/Export - Cost Centers
VZ_FormFactor: DSK
VZ_FormType: PAG
VZ_RequiresLogin: true
VZ_MaterialDesign: true
VZ_Dependencies: <dependencies xmlns="http://cargowise.com/glow/2014/11/14/dependencies.xsd" />
VZ_FormData: >-
  <form
    id="52ea24de-8b0d-4f2c-942b-74145d54eb9a" xmlns="http://wisetechglobal.com/glow/2017/05/26/form.xsd">
    <placeholder
      name="Layout"
      value="fill" />
    <control
      code="SDT"
      id="c72bfb42-42ed-4aa0-bc79-aab8283103be"
      binding="">
      <placeholder
        name="CaptionOverride"
        value="Cost Centers"
        resid="591b1e39-df33-4cbc-be8b-cd82dc9a45a6" />
      <placeholder
        name="EntityType"
        value="IGlbStaffCostCentre" />
      <placeholder
        name="SearchMode"
        value="Entity" />
      <placeholder
        name="FullPage"
        value="True" />
      <placeholder
        name="DefaultFilter">
        <xml>
          <ArrayOfFilterGroup xmlns:a="http://schemas.microsoft.com/2003/10/Serialization/Arrays" xmlns:i="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://cargowise.com/glow/2014/07/16/filters.xsd">
            <FilterGroup>
              <Filters>
                <Filter>
                  <FilterType>IsActiveFilter</FilterType>
                  <Operation>Is</Operation>
                  <PropertyPath>Staff.GS_IsActive</PropertyPath>
                  <Values>
                    <a:string>true</a:string>
                  </Values>
                </Filter>
                <Filter>
                  <FilterType>AdvancedGuidLookupFilter</FilterType>
                  <Operation>Is</Operation>
                  <PropertyPath>GSK_GS_Staff</PropertyPath>
                  <Values />
                </Filter>
                <Filter>
                  <FilterType>StringFilter</FilterType>
                  <Operation>StartsWith</Operation>
                  <PropertyPath>Staff.StaffName</PropertyPath>
                  <Values />
                </Filter>
              </Filters>
              <IsImplicit>false</IsImplicit>
            </FilterGroup>
          </ArrayOfFilterGroup>
        </xml>
      </placeholder>
      <placeholder
        name="FieldConfiguration">
        <xml>
          <fields xmlns="">
            <field
              path="GSK_StartDate"
              width="120"
              mode="Mandatory" />
            <field
              path="GSK_EndDate"
              width="120"
              mode="Default" />
            <field
              path="Branch.GB_AccountingGroupCode"
              width="250"
              mode="Default" />
            <field
              path="BranchManagementCodeDescription"
              width="250"
              mode="Default" />
            <field
              path="GSK_GB_Branch"
              width="200"
              mode="Optional" />
            <field
              path="GSK_GE_Department"
              width="200"
              mode="Optional" />
            <field
              path="IGlbStaffCostCentre_CurrentOn"
              width="80"
              mode="FilterOnly" />
            <field
              path="Staff.GS_IsActive"
              width="100"
              mode="FilterOnly" />
            <field
              path="Staff.GS_Code"
              width="120"
              mode="Optional" />
            <field
              path="Staff.GS_FullName"
              width="200"
              mode="Optional" />
            <field
              path="Staff.Country.RN_Code"
              width="130"
              mode="Optional" />
            <field
              path="Staff.Country.RN_Desc"
              width="200"
              mode="Optional" />
            <field
              path="Staff.GS_EmploymentDate"
              width="120"
              mode="Optional" />
            <field
              path="Department.GE_Code"
              width="120"
              mode="Default" />
            <field
              path="Department.GE_Desc"
              width="200"
              mode="Default" />
            <field
              path="Staff.GS_EmploymentBasis"
              width="110"
              mode="Optional" />
            <field
              path="Staff.CurrentJobTitle"
              width="200"
              mode="Optional" />
            <field
              path="Staff.GS_DepartureDate"
              width="120"
              mode="Optional" />
            <field
              path="Staff.CurrentTeam.GET_GST_NKTeamCode"
              width="200"
              mode="Optional" />
            <field
              path="Staff.GS_GB_HomeBranch"
              width="200"
              mode="Optional" />
            <field
              path="Staff.GS_GE_HomeDepartment"
              width="200"
              mode="Optional" />
            <field
              path="Staff.MostRecentPeopleLeader.GSM_EffectiveDate"
              width="250"
              mode="Optional" />
            <field
              path="Staff.CurrentRemuneration.GSR_EffectiveDate"
              width="150"
              mode="Optional" />
            <field
              path="Staff.CurrentRemuneration.GSR_FullTimeEquivalent"
              width="150"
              mode="Optional" />
            <field
              path="Staff.CurrentRemuneration.GSR_RX_NKCurrency"
              width="200"
              mode="Optional" />
            <field
              path="Staff.CurrentRemuneration.GSR_RN_NKCountry"
              width="200"
              mode="Optional" />
            <field
              path="Staff.CurrentRemuneration.MostRecentSalary.GSI_Value"
              width="150"
              mode="Optional" />
            <field
              path="Staff.CurrentRemuneration.MostRecentSalary.GSI_Frequency"
              width="180"
              mode="Optional" />
            <field
              path="Staff.CurrentRemuneration.MostRecentWaysOfWorkingAllowance.GSI_Value"
              width="150"
              mode="Optional" />
            <field
              path="Staff.CurrentRemuneration.MostRecentWaysOfWorkingAllowance.GSI_Frequency"
              width="180"
              mode="Optional" />
            <field
              path="GSK_GS_Staff"
              width="200"
              mode="FilterOnly" />
            <field
              path="Branch.GB_GC"
              width="200"
              mode="Optional" />
            <field
              path="Branch.GB_BranchName"
              width="250"
              mode="Optional" />
            <field
              path="Branch.GB_Code"
              width="120"
              mode="Optional" />
            <field
              path="Staff.HomeBranch.GlbCompany.GC_Code"
              width="120"
              mode="Optional" />
            <field
              path="Staff.HomeBranch.GlbCompany.GC_Name"
              width="250"
              mode="Optional" />
            <field
              path="Staff.HomeDepartment.GE_Code"
              width="120"
              mode="Optional" />
            <field
              path="Staff.HomeDepartment.GE_Desc"
              width="250"
              mode="Optional" />
            <field
              path="Staff.HomeBranch.GB_GC"
              width="250"
              mode="FilterOnly" />
            <field
              path="Staff.CurrentEmployingEntity.Branch.GB_BranchName"
              width="250"
              mode="Optional" />
            <field
              path="Staff.CurrentEmployingEntity.Branch.GB_Code"
              width="120"
              mode="Optional" />
            <field
              path="Staff.HomeBranch.GB_BranchName"
              width="250"
              mode="Optional" />
            <field
              path="Staff.HomeBranch.GB_Code"
              width="120"
              mode="Optional" />
            <field
              path="Staff.CurrentEmployingEntity.Branch.GlbCompany.GC_Code"
              width="120"
              mode="Optional" />
            <field
              path="Staff.CurrentEmployingEntity.Branch.GlbCompany.GC_Name"
              width="250"
              mode="Optional" />
            <field
              path="Staff.MostRecentPeopleLeader.Manager.GS_FullName"
              width="250"
              mode="Optional" />
            <field
              path="Staff.MostRecentPeopleLeader.Manager.GS_Code"
              width="120"
              mode="Optional" />
            <field
              path="Staff.CurrentRemuneration.Country.RN_Desc"
              width="250"
              mode="Optional" />
            <field
              path="Staff.CurrentRemuneration.Currency.RX_Desc"
              width="250"
              mode="Optional" />
            <field
              path="Branch.GlbCompany.GC_Code"
              width="120"
              mode="Optional" />
            <field
              path="Branch.GlbCompany.GC_Name"
              width="250"
              mode="Optional" />
            <field
              path="Staff.CurrentBeneficiaryEntity.GBB_GB_Branch"
              width="250"
              mode="Optional" />
            <field
              path="Staff.CurrentBeneficiaryEntity.Branch.GlbCompany.GC_Code"
              width="120"
              mode="Optional" />
            <field
              path="Staff.CurrentBeneficiaryEntity.Branch.GlbCompany.GC_Name"
              width="250"
              mode="Optional" />
            <field
              path="Staff.CurrentBeneficiaryEntity.Branch.GB_GC"
              width="250"
              mode="Optional" />
            <field
              path="Staff.CurrentBeneficiaryEntity.Branch.GB_BranchName"
              width="250"
              mode="Optional" />
            <field
              path="Staff.CurrentBeneficiaryEntity.Branch.GB_Code"
              width="120"
              mode="Optional" />
            <field
              path="Staff.CurrentRole.GEH_JobFamily"
              width="250"
              mode="Optional" />
            <field
              path="Staff.StaffName"
              width="250"
              mode="Optional" />
            <field
              path="Staff.CurrentWorkingBasis.WorkingBasisCode"
              width="250"
              mode="Optional" />
          </fields>
        </xml>
      </placeholder>
    </control>
  </form>
