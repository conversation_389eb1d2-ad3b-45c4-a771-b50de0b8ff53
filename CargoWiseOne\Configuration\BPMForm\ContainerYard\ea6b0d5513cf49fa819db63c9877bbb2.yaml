#transformationVersion: 70.0
#
VZ_PK: ea6b0d5513cf49fa819db63c9877bbb2
VZ_ConfigurationKey: ea6b0d55-13cf-49fa-819d-b63c9877bbb2
VZ_FormID: CYS Select unit tasks
VZ_Caption:
  resKey: VZ_Caption|ea6b0d5513cf49fa819db63c9877bbb2
  text: CYS Select unit tasks
VZ_FormFactor: MOB
VZ_FormType: PAG
VZ_RequiresLogin: true
VZ_MaterialDesign: true
VZ_Dependencies: <dependencies xmlns="http://cargowise.com/glow/2014/11/14/dependencies.xsd" />
VZ_FormData: >-
  <form
    id="6848c12d-f368-430d-a57c-1a12f8466cb9" xmlns="http://wisetechglobal.com/glow/2017/05/26/form.xsd">
    <placeholder
      name="MaxWidth"
      value="600" />
    <placeholder
      name="Padding"
      value="pa-4" />
    <control
      code="LBL"
      id="7d992051-6e2b-4a3a-ad9e-0baa0b1177ec">
      <placeholder
        name="Caption"
        value="Enter container number"
        resid="d85ba0f3-ef07-47f9-8ebc-4ddd70237503" />
    </control>
    <control
      code="CMP"
      id="dfa5623e-c09a-47d5-a2d5-3cce75649468">
      <placeholder
        name="Component"
        value="cargoWiseOne.productContainerYard.components.SelectUnitSearchTaskControl" />
      <placeholder
        name="Margin"
        value="my-2 mb-4" />
    </control>
    <control
      code="BOX"
      id="68783ccb-3936-4ce6-ab25-d2e50ccf2a99">
      <placeholder
        name="FillAvailable"
        value="False" />
      <placeholder
        name="FlexAlign"
        value="align-center" />
      <placeholder
        name="FlexJustify"
        value="justify-center" />
      <placeholder
        name="Layout"
        value="flex" />
      <placeholder
        name="Margin"
        value="my-2" />
      <control
        code="BOX"
        id="b5dc5a48-0f4d-4f91-b2e5-8294e4d833f2">
        <placeholder
          name="Width"
          value="50%" />
        <placeholder
          name="FlexAlign"
          value="align-center" />
        <placeholder
          name="FlexJustify"
          value="justify-center" />
        <placeholder
          name="FlexGrow"
          value="flex-grow-1" />
        <placeholder
          name="Padding"
          value="pt-1 pb-1 pl-1 pr-2" />
        <control
          code="DVR"
          id="6fa2b1d3-8d05-4db4-a6f2-86db3a1a27a6" />
      </control>
      <control
        code="BOX"
        id="50b1bdb9-362e-421e-96dc-bdfc6ff0f9a6" />
      <control
        code="LBL"
        id="2556cd04-970a-4d62-a200-5953e562fcd4">
        <placeholder
          name="Caption"
          value="or"
          resid="96eabe66-2c40-4a1b-affb-f520b666e1f0" />
      </control>
      <control
        code="BOX"
        id="a5648391-3f4c-4db2-94ff-28b58da08144" />
      <control
        code="BOX"
        id="64e64e46-f1e0-4f24-b605-cf83e4d86365">
        <placeholder
          name="Width"
          value="50%" />
        <placeholder
          name="FlexAlign"
          value="align-center" />
        <placeholder
          name="FlexJustify"
          value="justify-center" />
        <placeholder
          name="FlexGrow"
          value="flex-grow-1" />
        <placeholder
          name="Padding"
          value="pt-1 pb-1 pl-2 pr-1" />
        <control
          code="DVR"
          id="0d6b6d21-c01d-4a53-beee-f3cbcab78d52" />
      </control>
    </control>
    <control
      code="BOX"
      id="ab27eb76-2774-4427-ab8e-ac0595d59c12">
      <control
        code="BTN"
        id="defd4a1f-437b-425c-951a-868162fb951c">
        <placeholder
          name="Caption"
          value="View tasks via service types"
          resid="5ab7e1a4-4632-466a-b4f8-970a3bb781cb" />
        <placeholder
          name="Width"
          value="100%" />
        <placeholder
          name="FormFlowConfiguration">
          <xml>
            <formFlows xmlns="">
              <formFlow
                newSession="True">903e626db81644f19e4d3873468f07e4</formFlow>
            </formFlows>
          </xml>
        </placeholder>
        <placeholder
          name="Margin"
          value="mt-2" />
      </control>
    </control>
  </form>
