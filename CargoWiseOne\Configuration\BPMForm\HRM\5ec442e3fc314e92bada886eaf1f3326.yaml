#transformationVersion: 70.0
#
VZ_PK: 5ec442e3fc314e92bada886eaf1f3326
VZ_ConfigurationKey: 5ec442e3-fc31-4e92-bada-886eaf1f3326
VZ_FormID: HRM - Configuration - Processing Report (G2)
VZ_Caption:
  resKey: VZ_Caption|5ec442e3fc314e92bada886eaf1f3326
  text: Processing Report
VZ_FormFactor: DSK
VZ_EntityType: IHrlProcessingRun
VZ_FormType: FRM
VZ_RequiresLogin: true
VZ_MaterialDesign: true
VZ_Dependencies: <dependencies xmlns="http://cargowise.com/glow/2014/11/14/dependencies.xsd" />
VZ_FormData: >-
  <form
    id="72708fba-c947-4008-86b9-b678983b4142" xmlns="http://wisetechglobal.com/glow/2017/05/26/form.xsd">
    <control
      code="BOX"
      id="59ed0164-cdb4-4923-9b35-2313b05efe3e">
      <placeholder
        name="Layout"
        value="grid" />
      <control
        code="PNL"
        id="451c88eb-4553-40e2-93a2-933949ef1a05"
        binding="">
        <placeholder
          name="MaxWidth"
          value="540" />
        <control
          code="BOX"
          id="58b855be-31bf-40a7-b3ae-c37b32242007">
          <placeholder
            name="Layout"
            value="grid" />
          <control
            code="BOX"
            id="db8400d0-5367-4b0b-a5f0-af499427aba9">
            <placeholder
              name="Layout"
              value="grid" />
            <placeholder
              name="Columns"
              value="col-6" />
            <control
              code="SRC"
              id="ae914c3b-77dc-4451-b556-dc1167c58167"
              binding="LLR_RN_NKCountry">
              <placeholder
                name="IsReadOnly"
                value="True" />
            </control>
            <control
              code="DTE"
              id="9ea87178-da47-4c20-a44f-fcab852ae700"
              binding="LLR_ProcessTo">
              <placeholder
                name="IsReadOnly"
                value="True" />
            </control>
          </control>
          <control
            code="BOX"
            id="f63f653f-9a22-4298-94fc-41d3362120ba">
            <placeholder
              name="Layout"
              value="grid" />
            <placeholder
              name="Columns"
              value="col-6" />
            <control
              code="DTE"
              id="8011b109-bd42-43e3-9141-fefeafd9217a"
              binding="LLR_SystemCreateTimeUtc">
              <placeholder
                name="CaptionOverride"
                value="Processed On"
                resid="68e82c40-e25c-4e67-984d-aeddc2b18070" />
            </control>
            <control
              code="SRC"
              id="b4ca3ab5-3ca2-4281-a5d3-55fd6386bf0a"
              binding="LLR_SystemCreateUser">
              <placeholder
                name="CaptionOverride"
                value="Processed by"
                resid="b0d75c5d-4c5e-401a-93a9-175edd2cae38" />
            </control>
          </control>
        </control>
      </control>
      <control
        code="PNL"
        id="32ef323a-6fa1-4787-8b2c-7f58eac69da3"
        binding="">
        <control
          code="TBS"
          id="17af4a05-dcbb-4dc8-bc55-c70a7959a631">
          <placeholder
            name="Margin"
            value="mt-n1" />
          <control
            code="TAB"
            id="748e1869-2cdb-4c95-8dbb-d4c64dccf520">
            <placeholder
              name="Caption"
              value="Leave Balances"
              resid="dd13f5fa-e919-4113-ad98-a846b2647a0c" />
          </control>
          <control
            code="TAI"
            id="f61f58e4-19ed-4889-987c-e2a3e4e624e9">
            <control
              code="RDT"
              id="ea28406a-5a92-40c8-905b-680d196c415c"
              binding="HrlConsolidatedBalanceTransactionViews">
              <placeholder
                name="AllowAttach"
                value="False" />
              <placeholder
                name="AllowDetach"
                value="False" />
              <placeholder
                name="IsReadOnly"
                value="True" />
              <placeholder
                name="FieldConfiguration">
                <xml>
                  <fields xmlns="">
                    <field
                      path="Staff.GS_Code"
                      width="100"
                      mode="Mandatory" />
                    <field
                      path="Staff.GS_FullName"
                      width="170"
                      mode="Mandatory" />
                    <field
                      path="LLT_LeaveType"
                      width="120"
                      mode="Mandatory" />
                    <field
                      path="TotalLeaveBalance"
                      width="150"
                      mode="Default" />
                    <field
                      path="NonForfeitureBalance"
                      width="150"
                      mode="Default" />
                    <field
                      path="ForfeitureBalance1"
                      width="150"
                      mode="Default" />
                    <field
                      path="ForfeitureDate1"
                      width="150"
                      mode="Default" />
                    <field
                      path="ForfeitureBalance2"
                      width="150"
                      mode="Default" />
                    <field
                      path="ForfeitureDate2"
                      width="150"
                      mode="Default" />
                    <field
                      path="ForfeitureBalance3"
                      width="150"
                      mode="Default" />
                    <field
                      path="ForfeitureDate3"
                      width="150"
                      mode="Default" />
                    <field
                      path="ForfeitureBalance4"
                      width="150"
                      mode="Default" />
                    <field
                      path="ForfeitureDate4"
                      width="150"
                      mode="Default" />
                    <field
                      path="NumberOfForfeitureBalances"
                      width="150"
                      mode="Default" />
                    <field
                      path="LatestAccrual"
                      width="140"
                      mode="Optional" />
                  </fields>
                </xml>
              </placeholder>
              <placeholder
                name="CaptionOverride"
                value="Leave Balances"
                resid="3fdadf51-d964-4b18-9251-a91d749c1648" />
            </control>
          </control>
          <control
            code="TAB"
            id="396a5a64-c70b-44d6-9bde-58d512d1886a">
            <placeholder
              name="Caption"
              value="Balance Transactions"
              resid="6d9a317f-cc92-4153-bf77-45c341615d8f" />
          </control>
          <control
            code="TAI"
            id="7f962c63-d5ba-478f-973b-a121d60d6a69">
            <control
              code="RDT"
              id="84e3abd7-5733-4d68-a786-4552df567c9d"
              binding="HrlBalanceTransactions">
              <placeholder
                name="CaptionOverride"
                value="Staff Balances"
                resid="dd411577-a09b-4604-899d-9e82de4ed20d" />
              <placeholder
                name="Margin"
                value="ma-1 mt-3" />
              <placeholder
                name="HideItemActions"
                value="True" />
              <placeholder
                name="DefaultFilter">
                <xml>
                  <ArrayOfFilterGroup xmlns:a="http://schemas.microsoft.com/2003/10/Serialization/Arrays" xmlns:i="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://cargowise.com/glow/2014/07/16/filters.xsd">
                    <FilterGroup>
                      <Filters>
                        <Filter>
                          <FilterType>StringFilter</FilterType>
                          <Operation>IsNot</Operation>
                          <PropertyPath>LLT_TransactionType</PropertyPath>
                          <Values>
                            <a:string>FPR</a:string>
                          </Values>
                        </Filter>
                        <Filter>
                          <FilterType>StringFilter</FilterType>
                          <Operation>IsNot</Operation>
                          <PropertyPath>LLT_TransactionType</PropertyPath>
                          <Values>
                            <a:string>FRR</a:string>
                          </Values>
                        </Filter>
                      </Filters>
                      <IsImplicit>true</IsImplicit>
                    </FilterGroup>
                  </ArrayOfFilterGroup>
                </xml>
              </placeholder>
              <placeholder
                name="HideImport"
                value="True" />
              <placeholder
                name="ShowCustomize"
                value="True" />
              <placeholder
                name="FieldConfiguration">
                <xml>
                  <fields xmlns="">
                    <field
                      path="Staff.GS_Code"
                      width="120"
                      mode="Default" />
                    <field
                      path="Staff.GS_FullName"
                      width="250"
                      mode="Default" />
                    <field
                      path="LLT_LeaveType"
                      width="100"
                      mode="Default" />
                    <field
                      path="Accrual"
                      width="70"
                      mode="Default" />
                    <field
                      path="ProcessedLeaveBalance"
                      width="230"
                      mode="Default" />
                    <field
                      path="LLT_Forfeiture"
                      width="170"
                      mode="Default" />
                    <field
                      path="LLT_SystemCreateTimeUtc"
                      width="220"
                      mode="Optional" />
                    <field
                      path="LLT_Comment"
                      width="250"
                      mode="Optional" />
                    <field
                      path="LastProcessedBalance"
                      width="250"
                      mode="Optional" />
                    <field
                      path="LLT_DeltaValueHours"
                      width="170"
                      mode="Optional" />
                  </fields>
                </xml>
              </placeholder>
              <placeholder
                name="ShowFilters"
                value="True" />
            </control>
          </control>
          <control
            code="TAB"
            id="dba4de1c-a67b-4d58-899d-c00ff011d847">
            <placeholder
              name="Caption"
              value="Applications"
              resid="6bcdea20-12e5-42a3-bec9-0780dd8fd095" />
          </control>
          <control
            code="TAI"
            id="627788eb-45db-47e8-928c-8c17ffd3d42f">
            <control
              code="RDT"
              id="1b754f6d-9ef4-45a0-8855-725b5b906864"
              binding="HrlLeaveProcessedLogs">
              <placeholder
                name="CaptionOverride"
                value="Applications Processed"
                resid="dfa78b7b-f233-4fae-a7eb-8fe5b87b112e" />
              <placeholder
                name="Margin"
                value="ma-1" />
              <placeholder
                name="HideItemActions"
                value="True" />
            </control>
          </control>
        </control>
      </control>
    </control>
  </form>
