#transformationVersion: 70.0
#
VZ_PK: b99772b664834ed4977ed7fe5542f04c
VZ_ConfigurationKey: b99772b6-6483-4ed4-977e-d7fe5542f04c
VZ_FormID: ETL - VDV3 - Destination Depot - Manage Exception Items
VZ_Caption:
  resKey: VZ_Caption|b99772b6-6483-4ed4-977e-d7fe5542f04c
  text: Manage Exception Items
VZ_FormFactor: DSK
VZ_FormType: PAG
VZ_RequiresLogin: true
VZ_Dependencies: <dependencies xmlns="http://cargowise.com/glow/2014/11/14/dependencies.xsd" />
VZ_FormData: >-
  <form
    id="da588532-652b-446f-9133-2ec39a69020a" xmlns="http://wisetechglobal.com/glow/2017/05/26/form.xsd">
    <placeholder
      name="BackgroundImagePk"
      value="" />
    <placeholder
      name="BackgroundSize"
      value="Auto" />
    <placeholder
      name="BackgroundImageOverride"
      value="" />
    <placeholder
      name="FormLayout"
      value="Splitview" />
    <placeholder
      name="ExpandPaths"
      value="" />
    <placeholder
      name="ScanInputHandling"
      value="NON" />
    <placeholder
      name="RemovePadding"
      value="false" />
    <placeholder
      name="TurnOnPortableOneToOneDesignMode"
      value="false" />
    <placeholder
      name="ContainsDefaultButton"
      value="False" />
    <control
      code="GRP"
      id="4e73c0d9-f41a-4c0b-8511-4b68af595ecc"
      width="17"
      height="11.3">
      <placeholder
        name="CanBeHidden"
        value="True" />
      <placeholder
        name="VisibilityCondition"
        value="" />
      <placeholder
        name="Header"
        value="New Section"
        resid="3f2ccc0e-1ee8-45fe-a58a-89fabd43f71f" />
      <placeholder
        name="HeaderAlignment"
        value="Left" />
      <placeholder
        name="HeaderDisplayMode"
        value="Hidden" />
      <placeholder
        name="BackgroundImagePk"
        value="" />
      <placeholder
        name="BackgroundImageOverride"
        value="" />
      <control
        code="TIL"
        id="666ead20-8141-412d-b470-e31ad6ae8da3"
        left="0.5"
        top="3"
        width="4"
        height="4">
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="Size"
          value="Large" />
        <placeholder
          name="Color"
          value="Default" />
        <placeholder
          name="DisplayMode"
          value="TextAndIcon" />
        <placeholder
          name="Text"
          value="Scan Exception Items"
          resid="c2f69b10-a6ac-4d4e-9b28-4c58f615fde9" />
        <placeholder
          name="Image"
          value="936a039c478a4b9a89dff1c4136526df" />
        <placeholder
          name="ImageOverride"
          value="" />
        <placeholder
          name="Description"
          value="" />
        <placeholder
          name="Behaviour"
          value="FormFlow" />
        <placeholder
          name="PagePK"
          value="" />
        <placeholder
          name="Url"
          value="" />
        <placeholder
          name="FormFlow"
          value="481fb1246b494fc6915fb9f7ebb0f9cb" />
      </control>
      <control
        code="TIL"
        id="1680d905-6fc6-495f-957e-555d8478248c"
        left="4.5"
        top="3"
        width="4"
        height="4">
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="Size"
          value="Large" />
        <placeholder
          name="Color"
          value="Default" />
        <placeholder
          name="DisplayMode"
          value="TextAndIcon" />
        <placeholder
          name="Text"
          value="Assign to Outer Package / Location"
          resid="1b600e4f-10ac-4e3f-a6c8-6cd2675d1d5a" />
        <placeholder
          name="Image"
          value="af4d6ae99dbd41729937d65b1fecb223" />
        <placeholder
          name="ImageOverride"
          value="" />
        <placeholder
          name="Description"
          value="" />
        <placeholder
          name="Behaviour"
          value="FormFlow" />
        <placeholder
          name="PagePK"
          value="" />
        <placeholder
          name="Url"
          value="" />
        <placeholder
          name="FormFlow"
          value="830df5c933424a0a9f4cd124c8e6d1f4" />
      </control>
      <control
        code="TIL"
        id="f2f46a98-238e-4c99-9496-6d5433f72f06"
        left="8.5"
        top="3"
        width="4"
        height="4">
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="Size"
          value="Large" />
        <placeholder
          name="Color"
          value="Default" />
        <placeholder
          name="DisplayMode"
          value="TextAndIcon" />
        <placeholder
          name="Text"
          value="Release from Outer Package / Location"
          resid="776c8115-592e-456e-b66a-8a9fb76e8749" />
        <placeholder
          name="Image"
          value="f3d3014f6040408394c80c6e8160d450" />
        <placeholder
          name="ImageOverride"
          value="" />
        <placeholder
          name="Description"
          value="" />
        <placeholder
          name="Behaviour"
          value="FormFlow" />
        <placeholder
          name="PagePK"
          value="" />
        <placeholder
          name="Url"
          value="" />
        <placeholder
          name="FormFlow"
          value="0d1697ebda444ac0813c54eac982be61" />
      </control>
      <control
        code="TIL"
        id="ebebcc0a-6174-4898-837b-5a9b59b25dbe"
        left="12.5"
        top="3"
        width="4"
        height="4">
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="Size"
          value="Large" />
        <placeholder
          name="Color"
          value="Default" />
        <placeholder
          name="DisplayMode"
          value="TextAndIcon" />
        <placeholder
          name="Text"
          value="Dispatch to Last Mile Carrier"
          resid="a93b213f-f759-4cff-92a1-adf124da5181" />
        <placeholder
          name="Image"
          value="01e269caf0e344858835ffa00d8f010c" />
        <placeholder
          name="ImageOverride"
          value="" />
        <placeholder
          name="Description"
          value="" />
        <placeholder
          name="Behaviour"
          value="FormFlow" />
        <placeholder
          name="PagePK"
          value="" />
        <placeholder
          name="Url"
          value="" />
        <placeholder
          name="FormFlow"
          value="896dedc2c9ac4ddda8affbc62457f56a" />
      </control>
    </control>
  </form>
