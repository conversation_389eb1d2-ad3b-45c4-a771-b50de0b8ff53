#transformationVersion: 70.0
#
VZ_PK: b45f707420044c658f230dd53ca4c008
VZ_ConfigurationKey: b45f7074-2004-4c65-8f23-0dd53ca4c008
VZ_FormID: CYP Pre-Arrival Instructions
VZ_Caption:
  resKey: VZ_Caption|b45f7074-2004-4c65-8f23-0dd53ca4c008
  text: Pre-arrival instructions
VZ_FormFactor: DSK
VZ_FormType: PAG
VZ_RequiresLogin: true
VZ_MaterialDesign: true
VZ_Dependencies: <dependencies xmlns="http://cargowise.com/glow/2014/11/14/dependencies.xsd" />
VZ_FormData: >-
  <form
    id="cc86b794-5090-4b88-baa8-29018e07a5a5" xmlns="http://wisetechglobal.com/glow/2017/05/26/form.xsd">
    <placeholder
      name="Layout"
      value="fill" />
    <control
      code="SDT"
      id="cbdb9184-aebe-49d9-83c8-891360c1bb00"
      binding="">
      <placeholder
        name="EntityType"
        value="ICYDReceiveAdvice" />
      <placeholder
        name="FullPage"
        value="True" />
      <placeholder
        name="NewFormFlowConfiguration">
        <xml>
          <formFlows xmlns="">
            <formFlow>3e71b6b6941b46629b2e05022b949d72</formFlow>
          </formFlows>
        </xml>
      </placeholder>
      <placeholder
        name="EditFormFlowConfiguration">
        <xml>
          <formFlows xmlns="">
            <formFlow>04f336c5639941beb96021b1763deced</formFlow>
          </formFlows>
        </xml>
      </placeholder>
      <placeholder
        name="SearchMode"
        value="Index" />
      <placeholder
        name="CaptionOverride"
        value="Pre-arrival instructions"
        resid="925901b9-e692-499f-a9b6-9e7abfbdb2f5" />
      <placeholder
        name="ShowGrouping"
        value="True" />
      <placeholder
        name="FieldConfiguration">
        <xml>
          <fields xmlns="">
            <field
              path="YRAJOBNUMBER"
              width="140"
              mode="Default" />
            <field
              path="ACCEPTANCENUMBER"
              width="140"
              mode="Default" />
            <field
              path="YARDFROMDATE"
              width="170"
              mode="Default" />
            <field
              path="YARDTODATE"
              width="170"
              mode="Default" />
            <field
              path="RECEIVEADVICETOTALQUANTITY"
              width="90"
              mode="Default" />
            <field
              path="RECEIVEADVICETOTALQUANTITYIN"
              width="90"
              mode="Default" />
            <field
              path="CLIENT"
              width="200"
              mode="Default" />
            <field
              path="LESSEE"
              width="200"
              mode="Default" />
            <field
              path="CREATEUSERNAME"
              width="124"
              mode="Default" />
            <field
              path="CREATEUSER"
              width="124"
              mode="Default" />
            <field
              path="CREATETIME"
              width="124"
              mode="Default" />
          </fields>
        </xml>
      </placeholder>
      <placeholder
        name="DefaultFilter">
        <xml>
          <ArrayOfFilterGroup xmlns:a="http://schemas.microsoft.com/2003/10/Serialization/Arrays" xmlns:i="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://cargowise.com/glow/2014/07/16/filters.xsd">
            <FilterGroup>
              <Filters>
                <Filter>
                  <FilterType>StringFilter</FilterType>
                  <Operation>Is</Operation>
                  <PropertyPath>YARD</PropertyPath>
                  <Values>
                    <a:string>&lt;%.CurrentBranch.Yard.WW_WarehouseCode&gt;</a:string>
                  </Values>
                </Filter>
              </Filters>
              <IsImplicit>true</IsImplicit>
            </FilterGroup>
          </ArrayOfFilterGroup>
        </xml>
      </placeholder>
    </control>
  </form>
