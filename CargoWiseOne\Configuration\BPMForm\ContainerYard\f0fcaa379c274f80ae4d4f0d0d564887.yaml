#transformationVersion: 70.0
#
VZ_PK: f0fcaa379c274f80ae4d4f0d0d564887
VZ_ConfigurationKey: f0fcaa37-9c27-4f80-ae4d-4f0d0d564887
VZ_FormID: CYP Yard Unit Repair Code Listing
VZ_Caption:
  resKey: VZ_Caption|f0fcaa379c274f80ae4d4f0d0d564887
  text: Repair Codes
VZ_FormFactor: DSK
VZ_FormType: PAG
VZ_RequiresLogin: true
VZ_MaterialDesign: true
VZ_Dependencies: <dependencies xmlns="http://cargowise.com/glow/2014/11/14/dependencies.xsd" />
VZ_FormData: >-
  <form
    id="b598f8d6-8306-4873-839b-21059ecc0a33" xmlns="http://wisetechglobal.com/glow/2017/05/26/form.xsd">
    <placeholder
      name="Layout"
      value="fill" />
    <control
      code="SDT"
      id="2a6a8aa5-7c52-4d0c-adb5-0172e9711d1a"
      binding="">
      <placeholder
        name="CaptionOverride"
        value="Repair codes"
        resid="33ca1c93-2f0e-44ea-a8dd-ee0f14bdf112" />
      <placeholder
        name="EntityType"
        value="IRefRepairCode" />
      <placeholder
        name="FullPage"
        value="True" />
      <placeholder
        name="HideRefresh"
        value="True" />
      <placeholder
        name="SearchMode"
        value="Entity" />
      <placeholder
        name="ShowGrouping"
        value="True" />
      <placeholder
        name="HideActions"
        value="True" />
      <placeholder
        name="HideItemActions"
        value="False" />
      <placeholder
        name="ActionMenuItems">
        <xml>
          <formFlows xmlns="">
            <formFlow>9668de9376124c389e0017748290e2ef</formFlow>
          </formFlows>
        </xml>
      </placeholder>
      <placeholder
        name="EditFormFlowConfiguration">
        <xml>
          <formFlows xmlns="">
            <formFlow>9668de9376124c389e0017748290e2ef</formFlow>
          </formFlows>
        </xml>
      </placeholder>
      <placeholder
        name="FieldConfiguration">
        <xml>
          <fields xmlns="">
            <field
              path="RRC_Code"
              width="300"
              mode="Default" />
            <field
              path="RRC_Description"
              width="300"
              mode="Default" />
            <field
              path="RRC_Group"
              width="300"
              mode="Default" />
            <field
              path="RRC_ServiceType"
              width="300"
              mode="Default" />
            <field
              path="RRC_IsActive"
              width="130"
              mode="FilterOnly" />
          </fields>
        </xml>
      </placeholder>
    </control>
  </form>
