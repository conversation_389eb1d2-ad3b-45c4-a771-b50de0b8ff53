#transformationVersion: 70.0
#
VZ_PK: 70c66e97b80b4a489ef601827346133d
VZ_ConfigurationKey: 70c66e97-b80b-4a48-9ef6-01827346133d
VZ_FormID: CCA - Relevant Sailing Schedules
VZ_Caption:
  resKey: VZ_Caption|70c66e97-b80b-4a48-9ef6-01827346133d
  text: Relevant Sailing Schedules
VZ_FormFactor: DSK
VZ_EntityType: IRatingContractAllocationLine
VZ_FormType: FRM
VZ_RequiresLogin: true
VZ_MaterialDesign: true
VZ_ExtenderFunc: CargoWise.Glow.Business.Forwarding.Contracts.RelevantSailingSchedulesFormExtenderG2.Extend
VZ_Dependencies: <dependencies xmlns="http://cargowise.com/glow/2014/11/14/dependencies.xsd" />
VZ_FormData: >-
  <form
    id="a98a4ea9-c4c0-45ce-bc56-515437e670f7" xmlns="http://wisetechglobal.com/glow/2017/05/26/form.xsd">
    <placeholder
      name="Align"
      value="left" />
    <placeholder
      name="FlexDirection"
      value="flex-column" />
    <placeholder
      name="Layout"
      value="flex" />
    <placeholder
      name="FitToHeight"
      value="True" />
    <control
      code="SDT"
      id="903ff7e4-e997-4dec-9993-9d32c8f211fa"
      binding="">
      <placeholder
        name="EntityType"
        value="IJobSailing" />
      <placeholder
        name="FieldConfiguration">
        <xml>
          <fields xmlns="">
            <field
              path="JX_UniqueReference"
              width="200"
              mode="Mandatory" />
            <field
              path="JX_ContainerReleaseNumber"
              width="240"
              mode="Default" />
            <field
              path="JX_ReservedMasterBill"
              width="200"
              mode="Default" />
            <field
              path="JX_ServiceString"
              width="220"
              mode="Default" />
            <field
              path="JX_OnlineScheduleStatus"
              width="300"
              mode="Default" />
            <field
              path="PortOfOrigin"
              width="250"
              mode="Default" />
            <field
              path="PortOfDischarge"
              width="250"
              mode="Default" />
            <field
              path="JX_Status"
              width="60"
              mode="Default" />
            <field
              path="JX_DepotAvailabilityDate"
              width="120"
              mode="Optional" />
            <field
              path="JX_DepotCutOff"
              width="120"
              mode="Optional" />
            <field
              path="JX_DepotReceivalCommences"
              width="120"
              mode="Optional" />
            <field
              path="JX_DepotStorageDate"
              width="120"
              mode="Optional" />
            <field
              path="JX_IsPublished"
              width="120"
              mode="Optional" />
            <field
              path="JX_JB"
              width="250"
              mode="Default" />
            <field
              path="JX_JA"
              width="250"
              mode="Default" />
            <field
              path="JobVoyOrigin.JobVoyage.JV_OH_Line"
              width="250"
              mode="Optional" />
            <field
              path="JobVoyOrigin.JobVoyage.JV_RV_NKVessel"
              width="250"
              mode="Optional" />
            <field
              path="JobVoyOrigin.JobVoyage.JV_VoyageFlight"
              width="100"
              mode="Optional" />
            <field
              path="JobVoyOrigin.JA_E_DEP"
              width="180"
              mode="Optional" />
            <field
              path="JobVoyOrigin.JA_A_DEP"
              width="180"
              mode="Optional" />
            <field
              path="JobVoyOrigin.PortOfLoading.RL_Code"
              width="60"
              mode="Optional" />
            <field
              path="JobVoyDestination.PortOfDischarge.RL_Code"
              width="60"
              mode="Optional" />
          </fields>
        </xml>
      </placeholder>
      <placeholder
        name="FitToHeight"
        value="True" />
      <placeholder
        name="CaptionOverride"
        value="Schedules"
        resid="d3f805de-d567-4b2c-b888-4131f40ddd6e" />
      <placeholder
        name="HideItemActions"
        value="True" />
      <placeholder
        name="DefaultFilter">
        <xml>
          <ArrayOfFilterGroup xmlns:a="http://schemas.microsoft.com/2003/10/Serialization/Arrays" xmlns:i="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://cargowise.com/glow/2014/07/16/filters.xsd">
            <FilterGroup>
              <Filters>
                <Filter>
                  <FilterType>AdvancedGuidLookupFilter</FilterType>
                  <Operation>Is</Operation>
                  <PropertyPath>JobVoyOrigin.JobVoyage.JV_OH_Line</PropertyPath>
                  <Values>
                    <a:string>&lt;RatingContract.RCT_OH&gt;</a:string>
                  </Values>
                </Filter>
                <Filter>
                  <FilterType>StringFilter</FilterType>
                  <Operation>StartsWith</Operation>
                  <PropertyPath>JobVoyOrigin.PortOfLoading.RL_Code</PropertyPath>
                  <Values>
                    <a:string>&lt;LoadLocationWithProxy&gt;</a:string>
                  </Values>
                </Filter>
                <Filter>
                  <FilterType>StringFilter</FilterType>
                  <Operation>StartsWith</Operation>
                  <PropertyPath>JobVoyDestination.PortOfDischarge.RL_Code</PropertyPath>
                  <Values>
                    <a:string>&lt;DischargeLocationWithProxy&gt;</a:string>
                  </Values>
                </Filter>
                <Filter>
                  <FilterType>StringFilter</FilterType>
                  <Operation>Is</Operation>
                  <PropertyPath>JobVoyOrigin.JobVoyage.JV_VoyageFlight</PropertyPath>
                  <Values>
                    <a:string>&lt;VoyageWithProxy&gt;</a:string>
                  </Values>
                </Filter>
                <Filter>
                  <FilterType>AdvancedStringLookupFilter</FilterType>
                  <Operation>Is</Operation>
                  <PropertyPath>JobVoyOrigin.JobVoyage.JV_RV_NKVessel</PropertyPath>
                  <Values>
                    <a:string>&lt;VesselWithProxy&gt;</a:string>
                  </Values>
                </Filter>
                <Filter>
                  <FilterGroups>
                    <FilterGroup>
                      <Filters>
                        <Filter>
                          <FilterType>DateTimeFilter</FilterType>
                          <Operation>IsInTheDateRange</Operation>
                          <PropertyPath>JobVoyOrigin.JA_A_DEP</PropertyPath>
                          <Values>
                            <a:string>&lt;StartDateWithContractFallback&gt;</a:string>
                            <a:string>&lt;ExpiryDateWithContractFallback&gt;</a:string>
                          </Values>
                        </Filter>
                        <Filter>
                          <FilterType>DateTimeFilter</FilterType>
                          <Operation>IsInTheDateRange</Operation>
                          <PropertyPath>JobVoyOrigin.JA_E_DEP</PropertyPath>
                          <Values>
                            <a:string>&lt;StartDateWithContractFallback&gt;</a:string>
                            <a:string>&lt;ExpiryDateWithContractFallback&gt;</a:string>
                          </Values>
                        </Filter>
                      </Filters>
                      <IsImplicit>false</IsImplicit>
                    </FilterGroup>
                  </FilterGroups>
                  <FilterType></FilterType>
                  <Operation
                    i:nil="true" />
                  <PropertyPath>Exists</PropertyPath>
                </Filter>
              </Filters>
              <IsImplicit>false</IsImplicit>
            </FilterGroup>
          </ArrayOfFilterGroup>
        </xml>
      </placeholder>
      <placeholder
        name="ResetFilterOnInit"
        value="True" />
    </control>
    <control
      code="BOX"
      id="15fdd456-9d0b-44e7-96ad-12ed4dff19b9"
      binding="">
      <placeholder
        name="Layout"
        value="flex" />
      <placeholder
        name="FlexJustify"
        value="justify-end" />
      <placeholder
        name="Margin"
        value="mt-3" />
      <control
        code="BTN"
        id="ae8b15d3-3e29-45f4-85a3-2dd3eb131fb4"
        binding="">
        <placeholder
          name="Caption"
          value="Import Routing Information"
          resid="d96b77c5-69a3-41c0-86a9-db334f511c0c" />
        <placeholder
          name="Transition"
          value="True" />
      </control>
    </control>
  </form>
