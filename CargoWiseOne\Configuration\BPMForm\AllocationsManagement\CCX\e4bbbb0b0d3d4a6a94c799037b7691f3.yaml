#transformationVersion: 70.0
#
VZ_PK: e4bbbb0b0d3d4a6a94c799037b7691f3
VZ_ConfigurationKey: e4bbbb0b-0d3d-4a6a-94c7-99037b7691f3
VZ_FormID: CCX - View Allocation Routes
VZ_Caption:
  resKey: VZ_Caption|e4bbbb0b-0d3d-4a6a-94c7-99037b7691f3
  text: Allocation Routes
VZ_FormFactor: DSK
VZ_EntityType: IRatingContract
VZ_FormType: FRM
VZ_RequiresLogin: true
VZ_Dependencies: >-
  <dependencies xmlns="http://cargowise.com/glow/2014/11/14/dependencies.xsd">
    <expandPath
      path="RatingContractAllocationLines" />
    <datagrid
      path="RatingContractAllocationLines">
      <expandPath
        path="ContainerType" />
      <expandPath
        path="SailingSchedule" />
      <expandPath
        path="SailingSchedule.JobVoyOrigin" />
      <expandPath
        path="SailingSchedule.JobVoyDestination" />
    </datagrid>
  </dependencies>
VZ_FormData: >-
  <form
    id="785c2a87-13c8-40eb-8257-33b647671889" xmlns="http://wisetechglobal.com/glow/2017/05/26/form.xsd">
    <placeholder
      name="BackgroundImagePk"
      value="" />
    <placeholder
      name="BackgroundSize"
      value="Auto" />
    <placeholder
      name="BackgroundImageOverride"
      value="" />
    <placeholder
      name="FormLayout"
      value="Splitview" />
    <placeholder
      name="ExpandPaths"
      value="" />
    <placeholder
      name="ScanInputHandling"
      value="NON" />
    <placeholder
      name="RemovePadding"
      value="false" />
    <placeholder
      name="TurnOnPortableOneToOneDesignMode"
      value="false" />
    <placeholder
      name="ContainsDefaultButton"
      value="False" />
    <control
      code="GRD"
      id="f9316bda-c8bb-46dc-ab93-c1ce0834fa29"
      left="0"
      top="0"
      right="0"
      bottom="0"
      binding="RatingContractAllocationLines">
      <placeholder
        name="IsReadOnly"
        value="True" />
      <placeholder
        name="CanBeHidden"
        value="True" />
      <placeholder
        name="CaptionOverride"
        value="Allocation Routes"
        resid="e88620c9-8bca-44f0-99da-68498f907f55" />
      <placeholder
        name="VisibilityCondition"
        value="" />
      <placeholder
        name="HeaderAlignment"
        value="Left" />
      <placeholder
        name="HeaderDisplayMode"
        value="Visible" />
      <placeholder
        name="AllowAdd"
        value="True" />
      <placeholder
        name="AllowAttach"
        value="True" />
      <placeholder
        name="AllowDetach"
        value="True" />
      <placeholder
        name="AllowInlineEdit"
        value="True" />
      <placeholder
        name="FieldConfiguration">
        <xml>
          <fields xmlns="">
            <field
              path="RCA_AllocationLineID"
              width="180"
              mode="Default" />
            <field
              path="RCA_StartDate"
              width="100"
              mode="Default" />
            <field
              path="RCA_ExpiryDate"
              width="110"
              mode="Default" />
            <field
              path="LoadLocationWithProxy"
              width="130"
              mode="Default" />
            <field
              path="DischargeLocationWithProxy"
              width="180"
              mode="Default" />
            <field
              path="VoyageWithProxy"
              width="130"
              mode="Default" />
            <field
              path="VesselWithProxy"
              width="250"
              mode="Default" />
            <field
              path="RCA_StorageOrFreightRateClass"
              width="200"
              mode="Default" />
            <field
              path="AllocatedQuantity"
              width="180"
              mode="Optional" />
            <field
              path="RCA_AllocatedUQ"
              width="120"
              mode="Default" />
            <field
              path="RCA_AllowRelatedPorts"
              width="170"
              mode="Default" />
            <field
              path="ServiceStringWithProxy"
              width="250"
              mode="Default" />
            <field
              path="HasBookingLimit"
              width="170"
              mode="Default" />
            <field
              path="BookingVariance"
              width="160"
              mode="Default" />
            <field
              path="Utilization"
              width="250"
              mode="Optional" />
            <field
              path="OutstandingCommitted"
              width="250"
              mode="Default" />
            <field
              path="OutstandingWithVariance"
              width="250"
              mode="Default" />
            <field
              path="RCA_RC_ContainerType"
              width="250"
              mode="Default" />
            <field
              path="CapacityWithVariance"
              width="250"
              mode="Optional" />
            <field
              path="SailingSchedule.JX_UniqueReference"
              width="130"
              mode="Optional"
              readOnly="true" />
            <field
              path="SailingSchedule.JobVoyOrigin.JA_E_DEP"
              width="130"
              mode="Optional"
              readOnly="true" />
            <field
              path="LinkedScheduleETDUpdated"
              width="200"
              mode="Optional"
              readOnly="true" />
            <field
              path="SailingSchedule.JobVoyOrigin.JA_S_DEP"
              width="130"
              mode="Optional"
              readOnly="true" />
            <field
              path="SailingSchedule.JobVoyDestination.JB_E_ARV"
              width="130"
              mode="Optional"
              readOnly="true" />
            <field
              path="SailingSchedule.JobVoyDestination.JB_S_ARV"
              width="130"
              mode="Optional"
              readOnly="true" />
          </fields>
        </xml>
      </placeholder>
      <placeholder
        name="DefaultSortFields"
        value="" />
      <placeholder
        name="ShowDocumentsIcon"
        value="True" />
      <placeholder
        name="RowActionsMode"
        value="RowAndMenu" />
      <placeholder
        name="HideGridActions"
        value="False" />
      <placeholder
        name="HideFilters"
        value="False" />
      <placeholder
        name="ActionMenuItems">
        <xml>
          <formFlows xmlns="">
            <formFlow
              newSession="False">5ddfd356d12149f7aba1cc2acf4e3e0d</formFlow>
          </formFlows>
        </xml>
      </placeholder>
      <placeholder
        name="EditFormFlow"
        value="" />
      <placeholder
        name="SuppressDependencyContributions"
        value="False" />
    </control>
  </form>
