#transformationVersion: 70.0
#
VZ_PK: c059776afac44642a289e90aff2d331e
VZ_ConfigurationKey: c059776a-fac4-4642-a289-e90aff2d331e
VZ_FormID: AST - Asset Transaction edit form
VZ_Caption:
  resKey: VZ_Caption|c059776afac44642a289e90aff2d331e
  text: Edit Asset Transaction
VZ_FormFactor: DSK
VZ_EntityType: IAccAssetTransactionHeader
VZ_FormType: FRM
VZ_RequiresLogin: true
VZ_MaterialDesign: true
VZ_Dependencies: <dependencies xmlns="http://cargowise.com/glow/2014/11/14/dependencies.xsd" />
VZ_FormData: >-
  <form
    id="fcb4bec5-2b7d-4e06-8791-f496f7fef5e4" xmlns="http://wisetechglobal.com/glow/2017/05/26/form.xsd">
    <placeholder
      name="Layout"
      value="fill" />
    <control
      code="TBS"
      id="8c38e25a-7756-40bc-bedc-72bccda63dd8">
      <control
        code="TAB"
        id="db3dc920-85ec-48cb-ae11-98d1b4620435">
        <placeholder
          name="Caption"
          value="Main information"
          resid="119aa7da-cc79-4c72-94d0-9d68028388f3" />
      </control>
      <control
        code="TAI"
        id="48b20954-faab-4c13-8bc3-2be72264eaea">
        <control
          code="PNL"
          id="2e0957d3-8a5b-4a6a-a010-e44ff49f327d"
          binding="">
          <placeholder
            name="Layout"
            value="grid" />
          <placeholder
            name="Margin"
            value="mb-3" />
          <control
            code="PNL"
            id="ee260d7c-8c37-48f4-8756-7dd76a5ee76b">
            <control
              code="LBL"
              id="85509dc1-9f9d-44f4-8674-ab7097957f98">
              <placeholder
                name="Caption"
                value="Asset Purchase Transaction"
                resid="7a4aaf66-ffb3-43b8-8a23-35092fe4baa7" />
              <placeholder
                name="Columns"
                value="col-4" />
              <placeholder
                name="VisibilityCondition"
                value="ASH_TransactionType == &quot;APT&quot;" />
              <placeholder
                name="Typography"
                value="h6" />
            </control>
            <control
              code="LBL"
              id="33cce369-ce58-43f1-a86f-03489682c24d">
              <placeholder
                name="Caption"
                value="Asset Depreciation Transaction"
                resid="e84f5daf-b76d-4995-995c-00f0342994e5" />
              <placeholder
                name="Columns"
                value="col-4" />
              <placeholder
                name="VisibilityCondition"
                value="ASH_TransactionType == &quot;ADT&quot;" />
              <placeholder
                name="Typography"
                value="h6" />
            </control>
          </control>
          <control
            code="SRC"
            id="7eacb0e3-16f0-4c48-b70a-d4ee470941ec"
            binding="ASH_AAH_Asset">
            <placeholder
              name="Columns"
              value="col-6 col-sm-6 col-md-6 col-lg-6 col-xl-6" />
            <placeholder
              name="Required"
              value="True" />
          </control>
          <control
            code="TXT"
            id="86a0b71f-3e86-430b-8b7d-fd70f587999d"
            binding="ASH_Description">
            <placeholder
              name="Columns"
              value="col-6 col-sm-6 col-md-6 col-lg-6 col-xl-6" />
            <placeholder
              name="Required"
              value="True" />
          </control>
          <control
            code="DAE"
            id="ad131eae-f7b4-4551-b49f-d3e5ef04a67f"
            binding="ASH_PostDate">
            <placeholder
              name="Columns"
              value="col-6 col-sm-6 col-md-6 col-lg-6 col-xl-6" />
            <placeholder
              name="Required"
              value="True" />
          </control>
          <control
            code="TXT"
            id="6d166b3d-181f-4000-97e7-435992fce1e8"
            binding="ASH_TransactionNumber">
            <placeholder
              name="Columns"
              value="col-6 col-sm-6 col-md-6 col-lg-6 col-xl-6" />
            <placeholder
              name="Required"
              value="True" />
          </control>
          <control
            code="NUM"
            id="76a51c14-6747-4dcc-8144-6395f60ec6b5"
            binding="ASH_OSAmount">
            <placeholder
              name="Columns"
              value="col-6 col-sm-6 col-md-6 col-lg-6 col-xl-6" />
            <placeholder
              name="Required"
              value="True" />
          </control>
          <control
            code="SRC"
            id="acdc7c20-ea65-4b25-8b2b-f501c0bc4ef3"
            binding="ASH_RX_NKTransactionCurrency">
            <placeholder
              name="Columns"
              value="col-6 col-sm-6 col-md-6 col-lg-6 col-xl-6" />
            <placeholder
              name="Required"
              value="True" />
          </control>
          <control
            code="NUM"
            id="7f6234bc-6c77-4d33-a820-535368f2d80d"
            binding="ASH_LocalAmount">
            <placeholder
              name="Columns"
              value="col-6 col-sm-6 col-md-6 col-lg-6 col-xl-6" />
            <placeholder
              name="Required"
              value="False" />
            <placeholder
              name="IsReadOnly"
              value="True" />
          </control>
          <control
            code="NUM"
            id="623504fb-0644-4590-881f-7473a9bbdea1"
            binding="ASH_ExchangeRate">
            <placeholder
              name="Columns"
              value="col-6 col-sm-6 col-md-6 col-lg-6 col-xl-6" />
            <placeholder
              name="Required"
              value="True" />
          </control>
          <control
            code="DAE"
            id="de4ba69b-6a71-4521-afb0-2b334b869803"
            binding="ASH_SupplierReferenceDate">
            <placeholder
              name="Columns"
              value="col-6 col-sm-6 col-md-6 col-lg-6 col-xl-6" />
          </control>
          <control
            code="TXT"
            id="1fe0d9dd-5cc6-4cfa-8e61-8c82a3c3b23e"
            binding="ASH_SupplierReferenceNumber">
            <placeholder
              name="Columns"
              value="col-6 col-sm-6 col-md-6 col-lg-6 col-xl-6" />
          </control>
          <control
            code="SRC"
            id="8ef20e50-dc76-4ee3-b980-1a050eb5781b"
            binding="ASH_GB_Branch">
            <placeholder
              name="Columns"
              value="col-6 col-sm-6 col-md-6 col-lg-6 col-xl-6" />
            <placeholder
              name="Required"
              value="False" />
          </control>
          <control
            code="SRC"
            id="aec61006-896d-48cc-9dc9-050449fcace7"
            binding="ASH_GE_Department">
            <placeholder
              name="Columns"
              value="col-6 col-sm-6 col-md-6 col-lg-6 col-xl-6" />
            <placeholder
              name="Required"
              value="False" />
          </control>
          <control
            code="RDT"
            id="0fa80816-1e6a-4c6b-ae33-cdda7f80583c"
            binding="AccAssetTransactionLines">
            <placeholder
              name="HideItemActions"
              value="True" />
            <placeholder
              name="ShowCustomize"
              value="True" />
            <placeholder
              name="IsReadOnly"
              value="True" />
            <placeholder
              name="FieldConfiguration">
              <xml>
                <fields xmlns="">
                  <field
                    path="ASL_Sequence"
                    width="150"
                    mode="Mandatory" />
                  <field
                    path="ASL_LineType"
                    width="300"
                    mode="Mandatory" />
                  <field
                    path="ASL_LocalAmount"
                    width="300"
                    mode="Mandatory" />
                  <field
                    path="ASL_Description"
                    width="300"
                    mode="Optional" />
                  <field
                    path="ASL_RX_NKTransactionCurrency"
                    width="300"
                    mode="Optional" />
                  <field
                    path="ASL_ExchangeRate"
                    width="300"
                    mode="Optional" />
                  <field
                    path="ASL_OSAmount"
                    width="300"
                    mode="Optional" />
                  <field
                    path="ASL_AG_Account"
                    width="300"
                    mode="Optional" />
                  <field
                    path="ASL_GB_Branch"
                    width="300"
                    mode="Optional" />
                  <field
                    path="ASL_GE_Department"
                    width="300"
                    mode="Optional" />
                </fields>
              </xml>
            </placeholder>
            <placeholder
              name="DefaultSortFields">
              <xml>
                <ArrayOfFieldSortDefinition xmlns:i="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://schemas.datacontract.org/2004/07/CargoWise.Glow.Infrastructure.Interfaces">
                  <FieldSortDefinition>
                    <FieldName>ASL_Sequence</FieldName>
                    <IsAscending>true</IsAscending>
                  </FieldSortDefinition>
                </ArrayOfFieldSortDefinition>
              </xml>
            </placeholder>
            <placeholder
              name="HideDefaultFooter"
              value="True" />
            <placeholder
              name="CaptionOverride"
              value="Lines"
              resid="fbb94955-4ec9-4f5b-a409-c326506d3515" />
          </control>
        </control>
      </control>
      <control
        code="TAB"
        id="45c6fd86-c636-4559-8c4d-65bd07fa21f6">
        <placeholder
          name="Caption"
          value="Event Log"
          resid="81898a5d-ce53-4d3c-b89a-8245ad12e859" />
      </control>
      <control
        code="TAI"
        id="ed763914-9bf5-4d31-805b-a3db5ae27511">
        <control
          code="RDT"
          id="9688fc2f-05c7-474d-9365-260625b6453c"
          binding="Logs">
          <placeholder
            name="CaptionOverride"
            value="Header events"
            resid="25cc1a65-4c6e-4380-86ff-f7596e8110bc" />
          <placeholder
            name="ShowFilters"
            value="True" />
          <placeholder
            name="AllowAttach"
            value="False" />
          <placeholder
            name="AllowDetach"
            value="False" />
          <placeholder
            name="IsReadOnly"
            value="True" />
          <placeholder
            name="HideItemActions"
            value="True" />
          <placeholder
            name="HideImport"
            value="True" />
          <placeholder
            name="ShowCustomize"
            value="True" />
          <placeholder
            name="InlineEdit"
            value="none" />
          <placeholder
            name="FieldConfiguration">
            <xml>
              <fields xmlns="">
                <field
                  path="SL_SE_NKEvent"
                  width="50"
                  mode="Mandatory" />
                <field
                  path="Event.SE_Desc"
                  width="100"
                  mode="Default" />
                <field
                  path="SL_EventTime"
                  width="180"
                  mode="Mandatory" />
                <field
                  path="SL_GS_NKUser"
                  width="50"
                  mode="Mandatory" />
                <field
                  path="User.GS_FullName"
                  width="200"
                  mode="Default" />
                <field
                  path="EventDetails"
                  width="250"
                  mode="Optional" />
                <field
                  path="SL_EventTimeUtc"
                  width="180"
                  mode="Optional" />
                <field
                  path="SL_PostedTimeUtc"
                  width="180"
                  mode="Optional" />
                <field
                  path="SL_GB_NKBranch"
                  width="60"
                  mode="Optional" />
                <field
                  path="SL_GE_NKDepartment"
                  width="100"
                  mode="Optional" />
                <field
                  path="SL_DataSource"
                  width="110"
                  mode="Optional" />
              </fields>
            </xml>
          </placeholder>
        </control>
        <control
          code="RDT"
          id="47fbd60e-6a65-43e6-9bd1-8f6004caeaa5"
          binding="AccAssetTransactionLines/Logs">
          <placeholder
            name="CaptionOverride"
            value="Lines events"
            resid="eda7f544-d6aa-4ac5-b81d-f6c6d3f32325" />
          <placeholder
            name="IsReadOnly"
            value="True" />
          <placeholder
            name="AllowDetach"
            value="False" />
          <placeholder
            name="AllowAttach"
            value="False" />
          <placeholder
            name="ShowFilters"
            value="True" />
          <placeholder
            name="HideItemActions"
            value="True" />
          <placeholder
            name="HideImport"
            value="True" />
          <placeholder
            name="ShowCustomize"
            value="True" />
          <placeholder
            name="InlineEdit"
            value="none" />
          <placeholder
            name="FieldConfiguration">
            <xml>
              <fields xmlns="">
                <field
                  path="SL_SE_NKEvent"
                  width="50"
                  mode="Mandatory" />
                <field
                  path="Event.SE_Desc"
                  width="100"
                  mode="Default" />
                <field
                  path="SL_EventTime"
                  width="180"
                  mode="Mandatory" />
                <field
                  path="SL_GS_NKUser"
                  width="50"
                  mode="Mandatory" />
                <field
                  path="User.GS_FullName"
                  width="200"
                  mode="Default" />
                <field
                  path="EventDetails"
                  width="250"
                  mode="Optional" />
                <field
                  path="SL_EventTimeUtc"
                  width="180"
                  mode="Optional" />
                <field
                  path="SL_PostedTimeUtc"
                  width="180"
                  mode="Optional" />
                <field
                  path="SL_GB_NKBranch"
                  width="60"
                  mode="Optional" />
                <field
                  path="SL_GE_NKDepartment"
                  width="100"
                  mode="Optional" />
                <field
                  path="SL_DataSource"
                  width="110"
                  mode="Optional" />
              </fields>
            </xml>
          </placeholder>
        </control>
      </control>
    </control>
  </form>
