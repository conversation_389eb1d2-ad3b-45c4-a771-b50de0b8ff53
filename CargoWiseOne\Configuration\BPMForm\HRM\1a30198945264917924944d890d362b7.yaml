#transformationVersion: 70.0
#
VZ_PK: 1a30198945264917924944d890d362b7
VZ_ConfigurationKey: 1a301989-4526-4917-9249-44d890d362b7
VZ_FormID: HRM - Remuneration - Manager Review
VZ_Caption:
  resKey: VZ_Caption|1a301989-4526-4917-9249-44d890d362b7
  text: Review
VZ_FormFactor: DSK
VZ_EntityType: IReviewProcessNode
VZ_FormType: FRM
VZ_RequiresLogin: true
VZ_MaterialDesign: true
VZ_Dependencies: <dependencies xmlns="http://cargowise.com/glow/2014/11/14/dependencies.xsd" />
VZ_FormData: >-
  <form
    id="edffba44-d000-41fc-aec9-34bb782e8a52" xmlns="http://wisetechglobal.com/glow/2017/05/26/form.xsd">
    <control
      code="TBS"
      id="017be2dc-a800-4712-bce6-d4623e455823">
      <control
        code="TAB"
        id="82492ee8-d996-4af9-b732-f7a84dff5c81">
        <placeholder
          name="Caption"
          value="Direct Reports"
          resid="9b13dfdc-59b1-4a9a-964b-bc31f7e19997" />
      </control>
      <control
        code="TAI"
        id="e6bc2730-39dc-47ad-ad6e-3cf108172715">
        <placeholder
          name="Padding"
          value="py-2" />
        <control
          code="PNL"
          id="1ad4eb80-cc48-4056-a5ed-946192f149a1"
          binding="">
          <placeholder
            name="Margin"
            value="mb-2" />
          <placeholder
            name="VisibilityCondition"
            value="ReviewProcess.IncludesRemuneration" />
          <placeholder
            name="Layout"
            value="grid" />
          <control
            code="LBL"
            id="35584b39-0d69-415b-bfcf-82a77577231b">
            <placeholder
              name="Caption"
              value="Budget"
              resid="28fdbdcf-5cf3-4100-9156-a09669be60fb" />
            <placeholder
              name="Typography"
              value="title-sm-default" />
          </control>
          <control
            code="SRC"
            id="b8d2c05f-ed12-47d7-a460-6f618d662356"
            binding="CurrencyLookup">
            <placeholder
              name="Columns"
              value="col-sm-3" />
            <placeholder
              name="CaptionType"
              value="none" />
          </control>
          <control
            code="CMP"
            id="dac4eb49-0a79-4fcb-a1e1-5a8fe64a73b9"
            binding="">
            <placeholder
              name="Component"
              value="cargoWiseOne.productHrm.components.DirectReportsBudgetDetails" />
          </control>
        </control>
        <control
          code="PNL"
          id="4d4fdd42-7f45-462a-ab3d-da9be2480b68"
          binding="">
          <placeholder
            name="Margin"
            value="mb-2" />
          <placeholder
            name="Layout"
            value="grid" />
          <control
            code="LBL"
            id="833f21ff-3b81-4e54-878a-7fa67caec2f2">
            <placeholder
              name="Caption"
              value="This is the list of staff who report directly to you. For each staff member, make a proposal and add a comment to explain the proposal. Add messages and documents using the buttons in the top right."
              resid="c7a7c875-f038-4613-8407-8c3ca2482798" />
          </control>
          <control
            code="RDT"
            id="5237cb7b-7627-4929-bc34-a3435a02b3f0"
            binding="ReviewProposals">
            <placeholder
              name="DefaultSortFields">
              <xml>
                <ArrayOfFieldSortDefinition xmlns:i="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://schemas.datacontract.org/2004/07/CargoWise.Glow.Infrastructure.Interfaces" />
              </xml>
            </placeholder>
            <placeholder
              name="ShowItemActions"
              value="True" />
            <placeholder
              name="VisibilityCondition"
              value="ReviewProcess.RPR_Type == &quot;REM&quot; " />
            <placeholder
              name="FieldConfiguration">
              <xml>
                <fields xmlns="">
                  <field
                    path="RRP_GS_Staff"
                    width="220"
                    mode="Mandatory"
                    readOnly="true" />
                  <field
                    path="Staff.CurrentJobTitle"
                    width="250"
                    mode="Default"
                    readOnly="true" />
                  <field
                    path="Staff.CurrentRemuneration.Currency.RX_Code"
                    width="100"
                    caption="Currency"
                    resid="b58a7941-24f6-492c-840a-031964ed54fe"
                    mode="Default"
                    readOnly="true" />
                  <field
                    path="Staff.CurrentRemuneration.Country.RN_Desc"
                    width="200"
                    caption="Country/Region"
                    resid="bc2d444a-0366-4a5c-889e-3e678241ef4c"
                    mode="Default"
                    readOnly="true" />
                  <field
                    path="Staff.CurrentPerformanceReview.GSV_Score"
                    width="100"
                    caption="Performance score"
                    resid="464fc2f0-a91e-476e-805a-f8544c21ef1b"
                    mode="Default"
                    readOnly="true" />
                  <field
                    path="BASCurrentValue"
                    width="120"
                    caption="Current Base"
                    resid="714b6cd5-c36a-4a64-8cfe-09535e000a6a"
                    mode="Default" />
                  <field
                    path="RMECurrentValue"
                    width="120"
                    caption="Current Rem Equity"
                    resid="cf34c6f1-032b-404c-a8a3-d6a03a91ca26"
                    mode="Default" />
                  <field
                    path="PBECurrentValue"
                    width="120"
                    caption="Current PB Equity"
                    resid="5818c2ac-6af8-424d-a354-b2b6df670a17"
                    mode="Optional" />
                  <field
                    path="PBSCurrentValue"
                    width="120"
                    caption="Current PB Stretch"
                    resid="2dd28d9d-5925-4765-a03d-9d6621798cef"
                    mode="Optional" />
                  <field
                    path="PBCCurrentValue"
                    width="120"
                    caption="Current PB Cash"
                    resid="2d743a06-70df-4279-b4b2-060a26be082b"
                    mode="Optional" />
                  <field
                    path="TotalCurrentValue"
                    width="120"
                    caption="Current Total"
                    resid="2d558d42-e080-49bd-bf22-49796c9ee010"
                    mode="Default" />
                  <field
                    path="BASProposedValue"
                    width="120"
                    caption="Proposed Base"
                    resid="013b7141-8a4b-4a76-acd0-c1b0682a20cc"
                    mode="Default" />
                  <field
                    path="RMEProposedValue"
                    width="120"
                    caption="Proposed Rem Equity"
                    resid="dc892c99-488e-4a24-9799-3e974cabaf63"
                    mode="Default" />
                  <field
                    path="PBEProposedValue"
                    width="120"
                    caption="Proposed PB Equity"
                    resid="025956ed-c073-41c7-9f6f-ba4a61ed0a09"
                    mode="Optional" />
                  <field
                    path="PBSProposedValue"
                    width="120"
                    caption="Proposed PB Stretch"
                    resid="2148f4a4-23cd-45cf-a151-53c71963b041"
                    mode="Optional" />
                  <field
                    path="PBCProposedValue"
                    width="120"
                    caption="Proposed PB Cash"
                    resid="be620cd9-82ec-496d-8f5d-327e1a58e3c8"
                    mode="Optional" />
                  <field
                    path="TotalProposedValue"
                    width="120"
                    caption="Proposed Total"
                    resid="c9455721-46ca-48a4-8f72-b703255c0078"
                    mode="Default" />
                  <field
                    path="TotalPercentageIncrease"
                    width="120"
                    caption="% Increase"
                    resid="b4eab1e8-7eb2-4097-8341-eb70fce996e3"
                    mode="Default" />
                  <field
                    path="RRP_IsExcluded"
                    width="150"
                    mode="Default"
                    readOnly="!IsEditableToCurrentUser" />
                  <field
                    path="Staff.LatestPromotionStatus"
                    width="300"
                    mode="Default" />
                  <field
                    path="Staff.LatestPromotionDate"
                    width="300"
                    mode="Optional" />
                  <field
                    path="EmployeeBudgetPercent"
                    width="300"
                    mode="Optional" />
                  <field
                    path="Staff.CurrentRemuneration.GSR_RX_NKCurrency"
                    width="300"
                    mode="Optional" />
                  <field
                    path="IsStartable"
                    width="120"
                    mode="Optional" />
                  <field
                    path="RRP_Comments"
                    width="300"
                    mode="Default" />
                </fields>
              </xml>
            </placeholder>
            <placeholder
              name="InlineEdit"
              value="cell" />
            <placeholder
              name="CaptionOverride"
              value="Staff to review"
              resid="481023cd-697e-4f9e-8483-fd79a9eb82a9" />
            <placeholder
              name="ShowCustomize"
              value="True" />
            <placeholder
              name="ShowFilters"
              value="True" />
            <placeholder
              name="ItemsPerPage"
              value="100" />
            <placeholder
              name="ActionMenuItems">
              <xml>
                <formFlows xmlns="">
                  <formFlow
                    inDrawer="True">3f785ce23c214ce89d9d16c958900d48</formFlow>
                  <formFlow
                    inDialog="True">1eff8ee6d76c480dbe99221f96abbc32</formFlow>
                </formFlows>
              </xml>
            </placeholder>
            <placeholder
              name="EditFormFlowConfiguration">
              <xml>
                <formFlows xmlns="">
                  <formFlow
                    inDrawer="True">3f785ce23c214ce89d9d16c958900d48</formFlow>
                </formFlows>
              </xml>
            </placeholder>
            <placeholder
              name="DisabledGridRowActions"
              value="Documents" />
            <placeholder
              name="HideImport"
              value="True" />
          </control>
          <control
            code="RDT"
            id="e76ced0d-c67d-4756-b4f9-12d0eb95b190"
            binding="ReviewProposals">
            <placeholder
              name="VisibilityCondition"
              value="ReviewProcess.RPR_Type == &quot;PER&quot; " />
            <placeholder
              name="FieldConfiguration">
              <xml>
                <fields xmlns="">
                  <field
                    path="RRP_GS_Staff"
                    width="300"
                    mode="Mandatory"
                    readOnly="true" />
                  <field
                    path="Staff.CurrentJobTitle"
                    width="300"
                    mode="Default"
                    readOnly="true" />
                  <field
                    path="Staff.CurrentPerformanceReview.GSV_Score"
                    width="200"
                    caption="Previous Performance Score"
                    resid="cb535a07-4cdb-4ca1-8b30-4e428a9e8c10"
                    mode="Default"
                    readOnly="true" />
                  <field
                    path="RRP_PerformanceScore"
                    width="200"
                    caption="Proposed Performance Score"
                    resid="8f69cd70-0bea-41d0-9b64-6b031f293de4"
                    mode="Mandatory" />
                  <field
                    path="RRP_IsExcluded"
                    width="150"
                    mode="Default"
                    readOnly="!IsEditableToCurrentUser" />
                  <field
                    path="RRP_Comments"
                    width="300"
                    mode="Mandatory" />
                  <field
                    path="IsStartable"
                    width="150"
                    mode="Optional" />
                  <field
                    path="Staff.LatestPromotionStatus"
                    width="300"
                    mode="Default" />
                  <field
                    path="Staff.LatestPromotionDate"
                    width="300"
                    mode="Default" />
                </fields>
              </xml>
            </placeholder>
            <placeholder
              name="InlineEdit"
              value="cell" />
            <placeholder
              name="ShowCustomize"
              value="True" />
            <placeholder
              name="ShowFilters"
              value="True" />
            <placeholder
              name="CaptionOverride"
              value="Staff to review"
              resid="9eecbc13-1204-4c16-adeb-d15f413bdf71" />
            <placeholder
              name="ItemsPerPage"
              value="100" />
            <placeholder
              name="ActionMenuItems">
              <xml>
                <formFlows xmlns="">
                  <formFlow
                    inDrawer="True">3f785ce23c214ce89d9d16c958900d48</formFlow>
                  <formFlow
                    inDialog="True">1eff8ee6d76c480dbe99221f96abbc32</formFlow>
                </formFlows>
              </xml>
            </placeholder>
            <placeholder
              name="EditFormFlowConfiguration">
              <xml>
                <formFlows xmlns="">
                  <formFlow
                    inDrawer="True">3f785ce23c214ce89d9d16c958900d48</formFlow>
                </formFlows>
              </xml>
            </placeholder>
            <placeholder
              name="HideImport"
              value="True" />
            <placeholder
              name="DisabledGridRowActions"
              value="Documents" />
          </control>
          <control
            code="RDT"
            id="dc353377-d192-411c-916d-fc9e99224453"
            binding="ReviewProposals">
            <placeholder
              name="VisibilityCondition"
              value="ReviewProcess.RPR_Type == &quot;CLA&quot; " />
            <placeholder
              name="FieldConfiguration">
              <xml>
                <fields xmlns="">
                  <field
                    path="RRP_GS_Staff"
                    width="300"
                    mode="Mandatory"
                    readOnly="true" />
                  <field
                    path="Staff.CurrentJobTitle"
                    width="300"
                    mode="Mandatory"
                    readOnly="true" />
                  <field
                    path="Staff.CurrentClassification.GSL_Classification"
                    width="300"
                    mode="Mandatory"
                    readOnly="true" />
                  <field
                    path="RRP_IsExcluded"
                    width="110"
                    mode="Mandatory"
                    readOnly="!IsEditableToCurrentUser" />
                  <field
                    path="RRP_Classification"
                    width="300"
                    mode="Mandatory" />
                  <field
                    path="RRP_Comments"
                    width="300"
                    mode="Mandatory" />
                  <field
                    path="RRP_PerformanceScore"
                    width="170"
                    mode="Optional" />
                  <field
                    path="RRP_SystemCreateTimeUtc"
                    width="220"
                    mode="Optional" />
                  <field
                    path="RRP_SystemLastEditTimeUtc"
                    width="250"
                    mode="Optional" />
                  <field
                    path="RRP_SystemCreateUser"
                    width="160"
                    mode="Optional" />
                  <field
                    path="RRP_SystemLastEditUser"
                    width="200"
                    mode="Optional" />
                  <field
                    path="Staff.CurrentPerformanceReview.GSV_Score"
                    width="170"
                    mode="Optional"
                    readOnly="true" />
                  <field
                    path="Staff.CurrentRemuneration.GSR_RX_NKCurrency"
                    width="300"
                    mode="Optional"
                    readOnly="true" />
                  <field
                    path="Staff.CurrentRemuneration.GSR_RN_NKCountry"
                    width="300"
                    mode="Default"
                    readOnly="ReviewProcess.RPR_Type == &quot;CLA&quot; " />
                  <field
                    path="IsStartable"
                    width="120"
                    mode="Optional" />
                  <field
                    path="Staff.LatestPromotionStatus"
                    width="300"
                    mode="Default" />
                  <field
                    path="Staff.LatestPromotionDate"
                    width="300"
                    mode="Default" />
                </fields>
              </xml>
            </placeholder>
            <placeholder
              name="InlineEdit"
              value="cell" />
            <placeholder
              name="ShowCustomize"
              value="True" />
            <placeholder
              name="ShowFilters"
              value="True" />
            <placeholder
              name="CaptionOverride"
              value="Staff to review"
              resid="b3a8a8ef-0e8f-4017-8c76-c38924834ae6" />
            <placeholder
              name="ItemsPerPage"
              value="100" />
            <placeholder
              name="ActionMenuItems">
              <xml>
                <formFlows xmlns="">
                  <formFlow
                    inDrawer="True">3f785ce23c214ce89d9d16c958900d48</formFlow>
                  <formFlow
                    inDialog="True">1eff8ee6d76c480dbe99221f96abbc32</formFlow>
                </formFlows>
              </xml>
            </placeholder>
            <placeholder
              name="HideImport"
              value="True" />
            <placeholder
              name="DisabledGridRowActions"
              value="Documents" />
          </control>
          <control
            code="RDT"
            id="2f54d88b-092a-4fda-90b8-f41f6c0c07c2"
            binding="ReviewProposals">
            <placeholder
              name="FieldConfiguration">
              <xml>
                <fields xmlns="">
                  <field
                    path="RRP_GS_Staff"
                    width="300"
                    mode="Mandatory"
                    readOnly="true" />
                  <field
                    path="Staff.CurrentJobTitle"
                    width="300"
                    mode="Mandatory"
                    readOnly="true" />
                  <field
                    path="Staff.CurrentClassification.GSL_Classification"
                    width="300"
                    mode="Mandatory"
                    readOnly="true" />
                  <field
                    path="RRP_IsExcluded"
                    width="110"
                    mode="Mandatory"
                    readOnly="!IsEditableToCurrentUser" />
                  <field
                    path="RRP_Classification"
                    width="300"
                    mode="Mandatory" />
                  <field
                    path="Staff.CurrentPerformanceReview.GSV_Score"
                    width="180"
                    mode="Mandatory"
                    readOnly="true" />
                  <field
                    path="RRP_PerformanceScore"
                    width="180"
                    mode="Mandatory" />
                  <field
                    path="RRP_Comments"
                    width="300"
                    mode="Mandatory" />
                  <field
                    path="RRP_SystemCreateTimeUtc"
                    width="220"
                    mode="Optional" />
                  <field
                    path="RRP_SystemLastEditTimeUtc"
                    width="250"
                    mode="Optional" />
                  <field
                    path="RRP_SystemCreateUser"
                    width="160"
                    mode="Optional" />
                  <field
                    path="RRP_SystemLastEditUser"
                    width="200"
                    mode="Optional" />
                  <field
                    path="Staff.CurrentRemuneration.GSR_RX_NKCurrency"
                    width="300"
                    mode="Optional"
                    readOnly="true" />
                  <field
                    path="Staff.CurrentRemuneration.GSR_RN_NKCountry"
                    width="300"
                    mode="Default"
                    readOnly="ReviewProcess.RPR_Type == &quot;C&amp;P&quot; " />
                  <field
                    path="IsStartable"
                    width="120"
                    mode="Optional" />
                  <field
                    path="Staff.LatestPromotionStatus"
                    width="300"
                    mode="Default" />
                  <field
                    path="Staff.LatestPromotionDate"
                    width="300"
                    mode="Default" />
                </fields>
              </xml>
            </placeholder>
            <placeholder
              name="VisibilityCondition"
              value="ReviewProcess.RPR_Type == &quot;C&amp;P&quot; " />
            <placeholder
              name="InlineEdit"
              value="cell" />
            <placeholder
              name="ShowFilters"
              value="True" />
            <placeholder
              name="ShowCustomize"
              value="True" />
            <placeholder
              name="CaptionOverride"
              value="Staff to review"
              resid="7dbca818-1be4-4f8e-8742-7bf8a922c231" />
            <placeholder
              name="ItemsPerPage"
              value="100" />
            <placeholder
              name="ActionMenuItems">
              <xml>
                <formFlows xmlns="">
                  <formFlow
                    inDrawer="True">3f785ce23c214ce89d9d16c958900d48</formFlow>
                  <formFlow
                    inDialog="True">1eff8ee6d76c480dbe99221f96abbc32</formFlow>
                </formFlows>
              </xml>
            </placeholder>
            <placeholder
              name="HideImport"
              value="True" />
            <placeholder
              name="DisabledGridRowActions"
              value="Documents" />
          </control>
        </control>
      </control>
      <control
        code="TAB"
        id="a8eaaba0-20af-4c64-b5f7-f72b0d3c9299"
        binding="">
        <placeholder
          name="Caption"
          value="All Reports"
          resid="835d1668-cf70-4cdc-9f03-844332b5cca9" />
        <placeholder
          name="VisibilityCondition"
          value="DescendantNodes.Any()" />
      </control>
      <control
        code="TAI"
        id="7b5b5431-b25b-4868-8510-5fce0b5703e0"
        binding="">
        <placeholder
          name="Padding"
          value="py-2" />
        <placeholder
          name="VisibilityCondition"
          value="DescendantNodes.Any()" />
        <control
          code="PNL"
          id="792f11ac-a6a6-4382-9a64-e5016c3a5c33"
          binding="">
          <placeholder
            name="Margin"
            value="mb-2" />
          <placeholder
            name="VisibilityCondition"
            value="ReviewProcess.IncludesRemuneration" />
          <placeholder
            name="Layout"
            value="grid" />
          <control
            code="LBL"
            id="81c1240e-ac23-4aa1-9f3f-870ff8ee7f93">
            <placeholder
              name="Caption"
              value="Budget"
              resid="fb3b0407-0cec-4c81-88bc-b00fd7a8122e" />
            <placeholder
              name="Typography"
              value="title-sm-default" />
          </control>
          <control
            code="SRC"
            id="850cde40-c1c0-466e-98e2-58f2f9c8667b"
            binding="CurrencyLookup">
            <placeholder
              name="Columns"
              value="col-sm-3" />
            <placeholder
              name="CaptionType"
              value="none" />
          </control>
          <control
            code="CMP"
            id="5c739a93-8704-41b5-99ef-04acad807b60"
            binding="">
            <placeholder
              name="Component"
              value="cargoWiseOne.productHrm.components.AllReportsBudgetDetails" />
          </control>
        </control>
        <control
          code="PNL"
          id="0fd0a665-be4d-4f69-a701-ff0dcd5d1640"
          binding="">
          <placeholder
            name="Margin"
            value="mb-2" />
          <placeholder
            name="Layout"
            value="grid" />
          <control
            code="LBL"
            id="d063b52b-5987-4a68-9e9a-9483afee4769">
            <placeholder
              name="Caption"
              value="This is the list of all staff who report to you, including staff who report to you via other staff members. For each staff member, make a proposal and add a comment to explain the proposal. Add messages and documents using the buttons in the top right."
              resid="90c4c4cb-e751-4579-99e6-a5a5f9e5dd48" />
          </control>
          <control
            code="RDT"
            id="0ac09c35-a347-483c-bef7-b03e960649c4"
            binding="AllReviewProposals">
            <placeholder
              name="VisibilityCondition"
              value="ReviewProcess.RPR_Type == &quot;REM&quot; " />
            <placeholder
              name="FieldConfiguration">
              <xml>
                <fields xmlns="">
                  <field
                    path="RRP_GS_Staff"
                    width="220"
                    mode="Mandatory"
                    readOnly="true" />
                  <field
                    path="Staff.CurrentJobTitle"
                    width="250"
                    mode="Default"
                    readOnly="true" />
                  <field
                    path="Staff.CurrentRemuneration.Currency.RX_Code"
                    width="80"
                    caption="Currency"
                    resid="9783e0a5-18ae-425d-b05b-fca4f97f42b6"
                    mode="Default"
                    readOnly="true" />
                  <field
                    path="Staff.CurrentRemuneration.Country.RN_Desc"
                    width="200"
                    caption="Country/Region"
                    resid="53863582-be5c-4c7b-bf6c-c734cfcbec8c"
                    mode="Default"
                    readOnly="true" />
                  <field
                    path="Staff.CurrentPerformanceReview.GSV_Score"
                    width="100"
                    caption="Performance score"
                    resid="bfbbac86-4e7b-4374-91b0-5cc302cae0bf"
                    mode="Default"
                    readOnly="true" />
                  <field
                    path="BASCurrentValue"
                    width="120"
                    caption="Current Base"
                    resid="f24dc2d1-fab9-4a9a-a816-f9f23b1ecd30"
                    mode="Default" />
                  <field
                    path="RMECurrentValue"
                    width="120"
                    caption="Current Rem Equity"
                    resid="41d08787-d66c-4d16-b1a5-f5d78d492f6d"
                    mode="Default" />
                  <field
                    path="PBSCurrentValue"
                    width="120"
                    caption="Current PB Stretch"
                    resid="3bb2278d-9473-4ae3-a927-6576bed1aab0"
                    mode="Optional" />
                  <field
                    path="PBECurrentValue"
                    width="120"
                    caption="Current PB Equity"
                    resid="c84e5457-ff37-4282-9928-72fc506b9e9b"
                    mode="Optional" />
                  <field
                    path="PBCCurrentValue"
                    width="120"
                    caption="Current PB Cash"
                    resid="3cbf9a5a-36a9-4c8f-97af-5971e6289228"
                    mode="Optional" />
                  <field
                    path="TotalCurrentValue"
                    width="120"
                    caption="Current Total"
                    resid="b83f74e7-57db-4ecf-a78d-aefaba6003f6"
                    mode="Default" />
                  <field
                    path="BASProposedValue"
                    width="120"
                    caption="Proposed Base"
                    resid="2505d34f-6b68-4340-9e79-11b0225c5ca1"
                    mode="Default" />
                  <field
                    path="RMEProposedValue"
                    width="120"
                    caption="Proposed Rem Equity"
                    resid="5a52348a-046b-4639-824b-366f7fa21140"
                    mode="Default" />
                  <field
                    path="PBEProposedValue"
                    width="120"
                    caption="Proposed PB Equity"
                    resid="a7fb21ab-b42f-42c5-8485-2f9d08e49e2a"
                    mode="Optional" />
                  <field
                    path="PBSProposedValue"
                    width="120"
                    caption="Proposed PB Stretch"
                    resid="1df595a4-d1ac-4dd5-bbcb-af010f3aa33c"
                    mode="Optional" />
                  <field
                    path="PBCProposedValue"
                    width="120"
                    caption="Proposed PB Cash"
                    resid="bf3e6249-fb23-4f5a-a783-bc5c5475ebb9"
                    mode="Optional" />
                  <field
                    path="TotalProposedValue"
                    width="120"
                    caption="Proposed Total"
                    resid="a0944d68-b701-48e6-aee8-001f60df9e60"
                    mode="Default" />
                  <field
                    path="TotalPercentageIncrease"
                    width="120"
                    caption="% Increase"
                    resid="0f8b1ce3-fa6e-42ba-a62b-4c13a5133d15"
                    mode="Default" />
                  <field
                    path="RRP_IsExcluded"
                    width="150"
                    mode="Default"
                    readOnly="!IsEditableToCurrentUser" />
                  <field
                    path="RRP_Comments"
                    width="300"
                    mode="Default"
                    readOnly="!IsEditableToCurrentUser" />
                  <field
                    path="IsStartable"
                    width="100"
                    mode="Default" />
                  <field
                    path="ReviewManager"
                    width="250"
                    mode="Optional" />
                  <field
                    path="Staff.LatestPromotionStatus"
                    width="300"
                    mode="Default" />
                  <field
                    path="Staff.LatestPromotionDate"
                    width="300"
                    mode="Optional" />
                  <field
                    path="EmployeeBudgetPercent"
                    width="300"
                    mode="Optional" />
                  <field
                    path="Staff.CurrentRemuneration.GSR_RX_NKCurrency"
                    width="150"
                    mode="Optional" />
                </fields>
              </xml>
            </placeholder>
            <placeholder
              name="InlineEdit"
              value="cell" />
            <placeholder
              name="CaptionOverride"
              value="All staff to review"
              resid="9d58e877-90c1-4cca-83f0-4d0703d2b8cf" />
            <placeholder
              name="ShowFilters"
              value="True" />
            <placeholder
              name="ShowCustomize"
              value="True" />
            <placeholder
              name="ItemsPerPage"
              value="50" />
            <placeholder
              name="ActionMenuItems">
              <xml>
                <formFlows xmlns="">
                  <formFlow
                    inDrawer="True">3f785ce23c214ce89d9d16c958900d48</formFlow>
                  <formFlow
                    inDialog="True">968f0c218cfc42feb568dcdf694fcfd2</formFlow>
                  <formFlow
                    inDialog="True">1eff8ee6d76c480dbe99221f96abbc32</formFlow>
                </formFlows>
              </xml>
            </placeholder>
            <placeholder
              name="EditFormFlowConfiguration">
              <xml>
                <formFlows xmlns="">
                  <formFlow
                    inDrawer="True">3f785ce23c214ce89d9d16c958900d48</formFlow>
                </formFlows>
              </xml>
            </placeholder>
            <placeholder
              name="HideImport"
              value="True" />
            <placeholder
              name="DisabledGridRowActions"
              value="Documents" />
          </control>
          <control
            code="RDT"
            id="d0648c5f-98d0-4b34-8fd4-c6ac8f6656c6"
            binding="AllReviewProposals">
            <placeholder
              name="VisibilityCondition"
              value="ReviewProcess.RPR_Type == &quot;PER&quot; " />
            <placeholder
              name="FieldConfiguration">
              <xml>
                <fields xmlns="">
                  <field
                    path="RRP_GS_Staff"
                    width="300"
                    mode="Mandatory"
                    readOnly="true" />
                  <field
                    path="Staff.CurrentJobTitle"
                    width="300"
                    mode="Default"
                    readOnly="true" />
                  <field
                    path="Staff.CurrentPerformanceReview.GSV_Score"
                    width="200"
                    caption="Previous Performance Score"
                    resid="1617c3f8-cab8-4b1c-8ec5-e77e8f173918"
                    mode="Default"
                    readOnly="true" />
                  <field
                    path="RRP_PerformanceScore"
                    width="200"
                    caption="Proposed Performance Score"
                    resid="eb5d6cd7-369f-4f0a-bf1d-c85464c54872"
                    mode="Mandatory"
                    readOnly="!IsEditableToCurrentUser" />
                  <field
                    path="RRP_IsExcluded"
                    width="150"
                    mode="Default"
                    readOnly="!IsEditableToCurrentUser" />
                  <field
                    path="RRP_Comments"
                    width="300"
                    mode="Mandatory"
                    readOnly="!IsEditableToCurrentUser" />
                  <field
                    path="IsStartable"
                    width="150"
                    mode="Mandatory" />
                  <field
                    path="ReviewManager"
                    width="300"
                    mode="Default" />
                  <field
                    path="Staff.LatestPromotionDate"
                    width="300"
                    mode="Optional" />
                </fields>
              </xml>
            </placeholder>
            <placeholder
              name="InlineEdit"
              value="cell" />
            <placeholder
              name="ShowCustomize"
              value="True" />
            <placeholder
              name="ShowFilters"
              value="True" />
            <placeholder
              name="CaptionOverride"
              value="All staff to review"
              resid="a5d7f667-6343-405f-bbef-02ed8afc6bbd" />
            <placeholder
              name="ItemsPerPage"
              value="50" />
            <placeholder
              name="ActionMenuItems">
              <xml>
                <formFlows xmlns="">
                  <formFlow
                    inDrawer="True">3f785ce23c214ce89d9d16c958900d48</formFlow>
                  <formFlow
                    inDialog="True">968f0c218cfc42feb568dcdf694fcfd2</formFlow>
                  <formFlow
                    inDialog="True">1eff8ee6d76c480dbe99221f96abbc32</formFlow>
                </formFlows>
              </xml>
            </placeholder>
            <placeholder
              name="EditFormFlowConfiguration">
              <xml>
                <formFlows xmlns="">
                  <formFlow
                    inDrawer="True">3f785ce23c214ce89d9d16c958900d48</formFlow>
                </formFlows>
              </xml>
            </placeholder>
            <placeholder
              name="HideImport"
              value="True" />
            <placeholder
              name="DisabledGridRowActions"
              value="Documents" />
          </control>
          <control
            code="RDT"
            id="f4be6b0f-9ff7-4b0a-ad01-ff37a1b16d5e"
            binding="AllReviewProposals">
            <placeholder
              name="VisibilityCondition"
              value="ReviewProcess.RPR_Type == &quot;CLA&quot; " />
            <placeholder
              name="FieldConfiguration">
              <xml>
                <fields xmlns="">
                  <field
                    path="RRP_GS_Staff"
                    width="250"
                    mode="Mandatory"
                    readOnly="true" />
                  <field
                    path="Staff.CurrentJobTitle"
                    width="300"
                    mode="Default"
                    readOnly="true" />
                  <field
                    path="Staff.CurrentClassification.GSL_Classification"
                    width="300"
                    mode="Mandatory"
                    readOnly="true" />
                  <field
                    path="RRP_IsExcluded"
                    width="110"
                    mode="Mandatory"
                    readOnly="!IsEditableToCurrentUser" />
                  <field
                    path="RRP_Classification"
                    width="300"
                    mode="Mandatory"
                    readOnly="!IsEditableToCurrentUser" />
                  <field
                    path="RRP_Comments"
                    width="300"
                    mode="Mandatory"
                    readOnly="!IsEditableToCurrentUser" />
                  <field
                    path="IsStartable"
                    width="100"
                    mode="Mandatory" />
                  <field
                    path="ReviewManager"
                    width="250"
                    mode="Default" />
                  <field
                    path="RRP_PerformanceScore"
                    width="170"
                    mode="Optional" />
                  <field
                    path="RRP_SystemCreateTimeUtc"
                    width="220"
                    mode="Optional" />
                  <field
                    path="RRP_SystemLastEditTimeUtc"
                    width="250"
                    mode="Optional" />
                  <field
                    path="RRP_SystemCreateUser"
                    width="160"
                    mode="Optional" />
                  <field
                    path="RRP_SystemLastEditUser"
                    width="200"
                    mode="Optional" />
                  <field
                    path="Staff.CurrentPerformanceReview.GSV_Score"
                    width="170"
                    mode="Optional"
                    readOnly="true" />
                  <field
                    path="Staff.CurrentRemuneration.GSR_RX_NKCurrency"
                    width="300"
                    mode="Optional"
                    readOnly="true" />
                  <field
                    path="Staff.CurrentRemuneration.GSR_RN_NKCountry"
                    width="300"
                    mode="Default"
                    readOnly="ReviewProcess.RPR_Type == &quot;CLA&quot; " />
                  <field
                    path="Staff.LatestPromotionStatus"
                    width="300"
                    mode="Default" />
                  <field
                    path="Staff.LatestPromotionDate"
                    width="300"
                    mode="Default" />
                </fields>
              </xml>
            </placeholder>
            <placeholder
              name="InlineEdit"
              value="cell" />
            <placeholder
              name="CaptionOverride"
              value="All staff to review"
              resid="360b8f24-a84d-4c8e-8827-154d9afb7cfc" />
            <placeholder
              name="ShowCustomize"
              value="True" />
            <placeholder
              name="ShowFilters"
              value="True" />
            <placeholder
              name="ItemsPerPage"
              value="50" />
            <placeholder
              name="ActionMenuItems">
              <xml>
                <formFlows xmlns="">
                  <formFlow
                    inDrawer="True">3f785ce23c214ce89d9d16c958900d48</formFlow>
                  <formFlow
                    inDialog="True">968f0c218cfc42feb568dcdf694fcfd2</formFlow>
                  <formFlow
                    inDialog="True">1eff8ee6d76c480dbe99221f96abbc32</formFlow>
                </formFlows>
              </xml>
            </placeholder>
            <placeholder
              name="EditFormFlowConfiguration">
              <xml>
                <formFlows xmlns="">
                  <formFlow
                    inDrawer="True">3f785ce23c214ce89d9d16c958900d48</formFlow>
                </formFlows>
              </xml>
            </placeholder>
            <placeholder
              name="HideImport"
              value="True" />
            <placeholder
              name="DisabledGridRowActions"
              value="Documents" />
          </control>
          <control
            code="RDT"
            id="d124bb65-6ae7-414a-bb77-e5ce12eca8f7"
            binding="AllReviewProposals">
            <placeholder
              name="VisibilityCondition"
              value="ReviewProcess.RPR_Type == &quot;C&amp;P&quot; " />
            <placeholder
              name="FieldConfiguration">
              <xml>
                <fields xmlns="">
                  <field
                    path="RRP_GS_Staff"
                    width="250"
                    mode="Mandatory"
                    readOnly="true" />
                  <field
                    path="Staff.CurrentJobTitle"
                    width="300"
                    mode="Default"
                    readOnly="true" />
                  <field
                    path="Staff.CurrentClassification.GSL_Classification"
                    width="300"
                    mode="Mandatory"
                    readOnly="true" />
                  <field
                    path="RRP_IsExcluded"
                    width="110"
                    mode="Mandatory"
                    readOnly="!IsEditableToCurrentUser" />
                  <field
                    path="RRP_Classification"
                    width="300"
                    mode="Mandatory"
                    readOnly="!IsEditableToCurrentUser" />
                  <field
                    path="Staff.CurrentPerformanceReview.GSV_Score"
                    width="180"
                    mode="Mandatory"
                    readOnly="true" />
                  <field
                    path="RRP_PerformanceScore"
                    width="180"
                    mode="Mandatory"
                    readOnly="!IsEditableToCurrentUser" />
                  <field
                    path="RRP_Comments"
                    width="300"
                    mode="Mandatory"
                    readOnly="!IsEditableToCurrentUser" />
                  <field
                    path="IsStartable"
                    width="100"
                    mode="Mandatory" />
                  <field
                    path="ReviewManager"
                    width="250"
                    mode="Default" />
                  <field
                    path="RRP_SystemCreateTimeUtc"
                    width="220"
                    mode="Optional" />
                  <field
                    path="RRP_SystemLastEditTimeUtc"
                    width="250"
                    mode="Optional" />
                  <field
                    path="RRP_SystemCreateUser"
                    width="160"
                    mode="Optional" />
                  <field
                    path="RRP_SystemLastEditUser"
                    width="200"
                    mode="Optional" />
                  <field
                    path="Staff.CurrentRemuneration.GSR_RX_NKCurrency"
                    width="300"
                    mode="Optional"
                    readOnly="true" />
                  <field
                    path="Staff.CurrentRemuneration.GSR_RN_NKCountry"
                    width="300"
                    mode="Default"
                    readOnly="ReviewProcess.RPR_Type == &quot;C&amp;P&quot; " />
                  <field
                    path="Staff.LatestPromotionStatus"
                    width="300"
                    mode="Default" />
                  <field
                    path="Staff.LatestPromotionDate"
                    width="300"
                    mode="Default" />
                </fields>
              </xml>
            </placeholder>
            <placeholder
              name="InlineEdit"
              value="cell" />
            <placeholder
              name="ShowFilters"
              value="True" />
            <placeholder
              name="ShowCustomize"
              value="True" />
            <placeholder
              name="CaptionOverride"
              value="All staff to review"
              resid="3286126e-d06b-4dcd-bddd-a1847c9902ae" />
            <placeholder
              name="ItemsPerPage"
              value="50" />
            <placeholder
              name="ActionMenuItems">
              <xml>
                <formFlows xmlns="">
                  <formFlow
                    inDrawer="True">3f785ce23c214ce89d9d16c958900d48</formFlow>
                  <formFlow
                    inDialog="True">968f0c218cfc42feb568dcdf694fcfd2</formFlow>
                  <formFlow
                    inDialog="True">1eff8ee6d76c480dbe99221f96abbc32</formFlow>
                </formFlows>
              </xml>
            </placeholder>
            <placeholder
              name="EditFormFlowConfiguration">
              <xml>
                <formFlows xmlns="">
                  <formFlow
                    inDrawer="True">3f785ce23c214ce89d9d16c958900d48</formFlow>
                </formFlows>
              </xml>
            </placeholder>
            <placeholder
              name="HideImport"
              value="True" />
            <placeholder
              name="DisabledGridRowActions"
              value="Documents" />
          </control>
        </control>
      </control>
      <control
        code="TAB"
        id="41e92c51-777a-406b-9152-b6101df5a0d7"
        binding="">
        <placeholder
          name="Caption"
          value="Tracking"
          resid="8f73a565-aaf6-416c-8463-15000d701db4" />
        <placeholder
          name="VisibilityCondition"
          value="DescendantNodes.Any()" />
      </control>
      <control
        code="TAI"
        id="24e95e75-ca8f-4b8b-96fc-c0b91d66782c"
        binding="">
        <placeholder
          name="VisibilityCondition"
          value="DescendantNodes.Any()" />
        <control
          code="PNL"
          id="ddfe9076-01aa-4034-aaee-ebc69ac42ab5"
          binding="">
          <placeholder
            name="Margin"
            value="mt-2" />
          <control
            code="LBL"
            id="194b22f2-35aa-43ca-a89b-ba6e16efc7a1"
            binding="">
            <placeholder
              name="Align"
              value="left" />
            <placeholder
              name="Display"
              value="block" />
            <placeholder
              name="Caption"
              value="These are all the staff that pass their proposals to you. You can see the status of the review and who is currently assigned. When you are assigned, those staff will be editable under &quot;All Reports&quot;. Use the actions menu to pull the review up if they are taking too long, or push them back down if you are not satisfied with the proposals."
              resid="d549f113-d07b-443f-8e50-045a981b8f16" />
          </control>
          <control
            code="RDT"
            id="e03e6457-1835-4ead-9af7-cf835b13f060"
            binding="DescendantNodes">
            <placeholder
              name="CaptionOverride"
              value="Review tracking"
              resid="0e3581fe-b95a-4b50-8fc8-9b244a9af415" />
            <placeholder
              name="FieldConfiguration">
              <xml>
                <fields xmlns="">
                  <field
                    path="RRN_GS_Reviewer"
                    width="300"
                    mode="Mandatory" />
                  <field
                    path="RRN_Status"
                    width="300"
                    mode="Mandatory" />
                  <field
                    path="RRN_SystemLastEditTimeUtc"
                    width="300"
                    mode="Default" />
                  <field
                    path="Messages"
                    width="300"
                    mode="Default" />
                  <field
                    path="NumberOfDirectReports"
                    width="300"
                    mode="Optional" />
                  <field
                    path="NumberOfTeams"
                    width="300"
                    mode="Optional" />
                  <field
                    path="ReviewProcess.RPR_Name"
                    width="300"
                    mode="Optional" />
                  <field
                    path="ReviewProcess.RPR_Type"
                    width="260"
                    mode="Optional" />
                  <field
                    path="ReviewProcess.RPR_Status"
                    width="230"
                    mode="Optional" />
                  <field
                    path="ReviewProcess.RPR_SubmissionDate"
                    width="250"
                    mode="Optional" />
                  <field
                    path="Reviewer.Country.RN_Desc"
                    width="250"
                    mode="Optional" />
                  <field
                    path="Reviewer.CurrentRole.GEH_JobFamily"
                    width="250"
                    mode="Optional" />
                  <field
                    path="RRN_SystemCreateTimeUtc"
                    width="220"
                    mode="Optional" />
                  <field
                    path="RRN_SystemCreateUser"
                    width="160"
                    mode="Optional" />
                  <field
                    path="RRN_SystemLastEditUser"
                    width="200"
                    mode="Optional" />
                  <field
                    path="Reviewer.GS_FullName"
                    width="250"
                    mode="Optional" />
                  <field
                    path="ReviewProcess.RPR_ExchangeRateEffectiveDate"
                    width="250"
                    mode="Optional" />
                  <field
                    path="Reviewer.CurrentJobTitle"
                    width="250"
                    mode="Optional" />
                </fields>
              </xml>
            </placeholder>
            <placeholder
              name="IsReadOnly"
              value="True" />
            <placeholder
              name="ShowCustomize"
              value="True" />
            <placeholder
              name="ShowFilters"
              value="True" />
            <placeholder
              name="ActionMenuItems">
              <xml>
                <formFlows xmlns="">
                  <formFlow>ba0ec0fb513644019594f08977cc6ef6</formFlow>
                  <formFlow>63dbb92f5fb74a198eda0a8cbd4ac3b4</formFlow>
                  <formFlow>3cb4c2569c0f4d17a1f5646ae373b275</formFlow>
                  <formFlow>0ac88cdf733849edbeafb8cd7f9569b9</formFlow>
                </formFlows>
              </xml>
            </placeholder>
            <placeholder
              name="ItemsPerPage"
              value="100" />
            <placeholder
              name="HideImport"
              value="True" />
            <placeholder
              name="DisabledGridRowActions"
              value="Documents" />
          </control>
        </control>
      </control>
      <control
        code="TAB"
        id="82870049-eb09-4884-94ef-fb2a93048ec7"
        binding="">
        <placeholder
          name="Caption"
          value="Approval"
          resid="bd66234c-63b4-483c-bcdd-93964c72b9f9" />
        <placeholder
          name="VisibilityCondition"
          value="%.CurrentUserCheckpoints.Contains(&quot;CanReviewRemuneration&quot;)" />
      </control>
      <control
        code="TAI"
        id="92f09ff2-9053-49b8-a69e-33d0ea03021c"
        binding="">
        <placeholder
          name="VisibilityCondition"
          value="%.CurrentUserCheckpoints.Contains(&quot;CanReviewRemuneration&quot;)" />
        <control
          code="PNL"
          id="7f88c569-6f81-4172-b63b-0d50eb421349"
          binding="">
          <placeholder
            name="Margin"
            value="mt-2" />
          <control
            code="BOX"
            id="162296e0-06ab-436d-97b2-c719e6d7ef62">
            <placeholder
              name="Layout"
              value="grid" />
            <placeholder
              name="FlexJustify"
              value="justify-end" />
            <control
              code="LBL"
              id="167c2381-67d2-493d-a9ba-7b7683d0903b"
              binding="">
              <placeholder
                name="Caption"
                value="Below are all the reviewers who pass their reviews directly to moderation. The actions menu has a number of options to enable you to complete the review. When the reviewer submits their review, it is ready for approval. Approved reviews will be committed to the employee's staff profile when the review is completed. If there are any notifications associated with this they will also be triggered."
                resid="c654f27b-1057-46ab-a9d1-155e2bd167ac" />
            </control>
            <control
              code="BOX"
              id="e14b81a5-bfa5-47f8-9f8e-15e96503e643">
              <placeholder
                name="Layout"
                value="flex" />
              <placeholder
                name="FlexJustify"
                value="justify-space-between" />
              <control
                code="LBL"
                id="7839cd35-711e-43e1-b338-c70d94484d21">
                <placeholder
                  name="Caption"
                  value="If exchange rates need to be updated at any point this can be done using the button on the right."
                  resid="95728d1e-2c26-4e93-a301-72ce5e470de4" />
              </control>
              <control
                code="BTN"
                id="93261628-cd8e-4e71-911a-6bd32fb7f453"
                binding="">
                <placeholder
                  name="Caption"
                  value="View &amp; Update Exchange Rates"
                  resid="2d7109a2-9240-40e9-9fd8-798746e442b1" />
                <placeholder
                  name="Transition"
                  value="True" />
                <placeholder
                  name="Variant"
                  value="default" />
              </control>
            </control>
          </control>
          <control
            code="RDT"
            id="ecba071b-e4a5-4a7f-9d88-8b905a0b8a82"
            binding="ReviewProcess.TopLevelNodes">
            <placeholder
              name="CaptionOverride"
              value="Moderate reviews"
              resid="bc30cb62-226c-47ce-9fdb-5a09993b9318" />
            <placeholder
              name="FieldConfiguration">
              <xml>
                <fields xmlns="">
                  <field
                    path="RRN_GS_Reviewer"
                    width="300"
                    mode="Mandatory" />
                  <field
                    path="RRN_Status"
                    width="300"
                    mode="Mandatory" />
                  <field
                    path="Reviewer.Country.RN_Desc"
                    width="250"
                    mode="Optional" />
                  <field
                    path="Reviewer.CurrentRole.GEH_JobFamily"
                    width="250"
                    mode="Optional" />
                  <field
                    path="RRN_SystemCreateTimeUtc"
                    width="220"
                    mode="Optional" />
                  <field
                    path="RRN_SystemLastEditTimeUtc"
                    width="250"
                    mode="Optional" />
                  <field
                    path="RRN_SystemCreateUser"
                    width="160"
                    mode="Optional" />
                  <field
                    path="RRN_SystemLastEditUser"
                    width="200"
                    mode="Optional" />
                  <field
                    path="Reviewer.GS_FullName"
                    width="250"
                    mode="Optional" />
                  <field
                    path="Parent.Reviewer.GS_FullName"
                    width="250"
                    mode="Optional" />
                  <field
                    path="Messages"
                    width="80"
                    mode="Optional" />
                  <field
                    path="Reviewer.CurrentJobTitle"
                    width="250"
                    mode="Optional" />
                  <field
                    path="NumberOfDirectReports"
                    width="240"
                    mode="Optional" />
                  <field
                    path="NumberOfTeams"
                    width="150"
                    mode="Optional" />
                </fields>
              </xml>
            </placeholder>
            <placeholder
              name="ShowFilters"
              value="True" />
            <placeholder
              name="ShowCustomize"
              value="True" />
            <placeholder
              name="ActionMenuItems">
              <xml>
                <formFlows xmlns="">
                  <formFlow>0ac88cdf733849edbeafb8cd7f9569b9</formFlow>
                  <formFlow>ba0ec0fb513644019594f08977cc6ef6</formFlow>
                  <formFlow>63dbb92f5fb74a198eda0a8cbd4ac3b4</formFlow>
                  <formFlow>3cb4c2569c0f4d17a1f5646ae373b275</formFlow>
                </formFlows>
              </xml>
            </placeholder>
            <placeholder
              name="ItemsPerPage"
              value="50" />
            <placeholder
              name="HideImport"
              value="True" />
            <placeholder
              name="DisabledGridRowActions"
              value="Documents" />
          </control>
        </control>
        <control
          code="SPC"
          id="386756ac-acc0-4bbe-9582-014560cf191a" />
      </control>
      <control
        code="TAB"
        id="c4daf03a-7f16-414a-abc1-cdda6479ec5f"
        binding="">
        <placeholder
          name="Caption"
          value="Budget Comparison"
          resid="9123e801-c126-431e-99c8-933dd6367cf8" />
        <placeholder
          name="VisibilityCondition"
          value="ReviewProcess.IncludesRemuneration" />
      </control>
      <control
        code="TAI"
        id="c2283ef6-f373-422c-b898-67bc263685a9"
        binding="">
        <placeholder
          name="VisibilityCondition"
          value="ReviewProcess.IncludesRemuneration" />
        <control
          code="PNL"
          id="ca229190-eef7-435d-94f4-170f1f451f2a"
          binding="">
          <placeholder
            name="Layout"
            value="grid" />
          <placeholder
            name="Margin"
            value="mb-2" />
          <control
            code="LBL"
            id="ba68743d-99bb-4240-8a2f-98d73485d826">
            <placeholder
              name="Caption"
              value="Compare your total proposed budget using the filters below"
              resid="92665275-e58b-4c83-a6ec-f03b38cf15db" />
          </control>
          <control
            code="SRC"
            id="372a9ef4-c94b-4bed-bcd9-493729b717f0"
            binding="CurrencyLookup">
            <placeholder
              name="Columns"
              value="col-sm-3" />
            <placeholder
              name="CaptionType"
              value="none" />
          </control>
          <control
            code="BOX"
            id="749dd7dd-80bc-490e-9ba5-9d646092a6e2">
            <placeholder
              name="MaxHeight"
              value="34px" />
            <placeholder
              name="Overflow"
              value="overflow-hidden" />
            <control
              code="SDT"
              id="3cf473f3-35c8-4d39-aa83-4a7e7b084075"
              binding="">
              <placeholder
                name="EntityType"
                value="IGlbStaff" />
              <placeholder
                name="BindingForFilter"
                value="BudgetComparisonGroup1" />
              <placeholder
                name="ItemsPerPage"
                value="1" />
              <placeholder
                name="FieldConfiguration">
                <xml>
                  <fields xmlns="">
                    <field
                      path="GS_EmploymentDate"
                      width="300"
                      mode="Optional"
                      isVisible="false" />
                    <field
                      path="GS_FullName"
                      width="300"
                      mode="Optional"
                      isVisible="false" />
                    <field
                      path="Country.RN_Desc"
                      width="300"
                      mode="Optional"
                      isVisible="false" />
                    <field
                      path="GS_FriendlyName"
                      width="300"
                      mode="Optional"
                      isVisible="false" />
                    <field
                      path="CurrentRemuneration.GSR_FullTimeEquivalent"
                      width="300"
                      mode="Optional"
                      isVisible="false" />
                    <field
                      path="CurrentTeam.ParentTeam"
                      width="300"
                      mode="Optional"
                      isVisible="false" />
                    <field
                      path="CurrentTeam.TeamNameDescription"
                      width="300"
                      mode="Optional"
                      isVisible="false" />
                    <field
                      path="GS_EmploymentBasis"
                      width="300"
                      mode="Optional"
                      isVisible="false" />
                    <field
                      path="CurrentJobTitle"
                      width="300"
                      mode="Optional"
                      isVisible="false" />
                    <field
                      path="CurrentEmployingEntity.Branch.GlbCompany.GC_Name"
                      width="300"
                      mode="Optional"
                      isVisible="false" />
                    <field
                      path="GS_Code"
                      width="300"
                      mode="Optional"
                      isVisible="false" />
                    <field
                      path="GS_Birthdate"
                      width="150"
                      mode="Optional" />
                    <field
                      path="GS_NextReviewDate"
                      width="190"
                      mode="Optional" />
                    <field
                      path="GS_DepartureDate"
                      width="120"
                      mode="Optional" />
                    <field
                      path="GS_LastDayOfWork"
                      width="160"
                      mode="Optional" />
                    <field
                      path="GS_ProbationEndDate"
                      width="180"
                      mode="Optional" />
                    <field
                      path="GS_ResidencyExpiry"
                      width="180"
                      mode="Optional" />
                    <field
                      path="GS_SystemCreateTimeUtc"
                      width="220"
                      mode="Optional" />
                    <field
                      path="GS_SystemLastEditTimeUtc"
                      width="250"
                      mode="Optional" />
                    <field
                      path="CreatedByStaff.GS_Code"
                      width="190"
                      mode="Optional" />
                    <field
                      path="CreatedByStaff.GS_FullName"
                      width="230"
                      mode="Optional" />
                    <field
                      path="GS_UserAddress1"
                      width="200"
                      mode="Optional" />
                    <field
                      path="GS_UserAddress2"
                      width="200"
                      mode="Optional" />
                    <field
                      path="GS_WagesBankAccount"
                      width="220"
                      mode="Optional" />
                    <field
                      path="GS_WagesBankBsb"
                      width="160"
                      mode="Optional" />
                    <field
                      path="GS_WagesBankName"
                      width="220"
                      mode="Optional" />
                    <field
                      path="GS_WagesBankSwift"
                      width="250"
                      mode="Optional" />
                    <field
                      path="GS_EmailAddress"
                      width="220"
                      mode="Optional" />
                    <field
                      path="GS_EmergencyContactEmail"
                      width="250"
                      mode="Optional" />
                    <field
                      path="GS_EmergencyContactName"
                      width="220"
                      mode="Optional" />
                    <field
                      path="GS_EmergencyHomePhone"
                      width="200"
                      mode="Optional" />
                    <field
                      path="GS_HomePhone"
                      width="200"
                      mode="Optional" />
                    <field
                      path="GS_State"
                      width="170"
                      mode="Optional" />
                    <field
                      path="GS_Postcode"
                      width="170"
                      mode="Optional" />
                    <field
                      path="GS_MobilePhone"
                      width="200"
                      mode="Optional" />
                    <field
                      path="GS_LoginName"
                      width="180"
                      mode="Optional" />
                    <field
                      path="GS_NextOfKinEmail"
                      width="250"
                      mode="Optional" />
                    <field
                      path="GS_NextOfKinHomePhone"
                      width="200"
                      mode="Optional" />
                    <field
                      path="GS_NextOfKin"
                      width="250"
                      mode="Optional" />
                    <field
                      path="GS_ResidencyStatus"
                      width="160"
                      mode="Optional" />
                    <field
                      path="TotalWorkingHoursText"
                      width="200"
                      mode="Optional" />
                    <field
                      path="GS_WorkPhone"
                      width="200"
                      mode="Optional" />
                    <field
                      path="GS_IsActive"
                      width="100"
                      mode="Optional" />
                    <field
                      path="GS_IsController"
                      width="100"
                      mode="Optional" />
                    <field
                      path="GS_IsOperational"
                      width="100"
                      mode="Optional" />
                    <field
                      path="GS_PublishHomePhone"
                      width="140"
                      mode="Optional" />
                    <field
                      path="GS_PublishMobilePhone"
                      width="200"
                      mode="Optional" />
                    <field
                      path="CurrentEmployingEntity.GHB_EffectiveDate"
                      width="250"
                      mode="Optional" />
                    <field
                      path="CurrentEmployingEntity.Branch.GB_BranchName"
                      width="190"
                      mode="Optional" />
                    <field
                      path="CurrentEmployingEntity.Branch.GB_Code"
                      width="190"
                      mode="Optional" />
                    <field
                      path="CurrentEmployingEntity.Department.GE_Code"
                      width="210"
                      mode="Optional" />
                    <field
                      path="CurrentEmployingEntity.Department.GE_Desc"
                      width="250"
                      mode="Optional" />
                    <field
                      path="CurrentPhysicalWorkAddress.GEL_EffectiveDate"
                      width="250"
                      mode="Optional" />
                    <field
                      path="CurrentPhysicalWorkAddress.GEL_Address1"
                      width="220"
                      mode="Optional" />
                    <field
                      path="CurrentPhysicalWorkAddress.GEL_Address2"
                      width="220"
                      mode="Optional" />
                    <field
                      path="CurrentPhysicalWorkAddress.GEL_City"
                      width="200"
                      mode="Optional" />
                    <field
                      path="CurrentPhysicalWorkAddress.GEL_PostCode"
                      width="250"
                      mode="Optional" />
                    <field
                      path="CurrentPhysicalWorkAddress.GEL_State"
                      width="200"
                      mode="Optional" />
                    <field
                      path="CurrentRemuneration.GSR_EffectiveDate"
                      width="220"
                      mode="Optional" />
                    <field
                      path="CurrentRemuneration.MostRecentSalary"
                      width="250"
                      mode="Optional" />
                    <field
                      path="CurrentRemuneration.MostRecentSalary.GSI_Value"
                      width="200"
                      mode="Optional" />
                    <field
                      path="CurrentRemuneration.MostRecentSalary.GSI_GrantDate"
                      width="200"
                      mode="Optional" />
                    <field
                      path="CurrentRemuneration.MostRecentSalary.GSI_IsFTEScalable"
                      width="220"
                      mode="Optional" />
                    <field
                      path="CurrentRemuneration.MostRecentSalary.IsOneOff"
                      width="200"
                      mode="Optional" />
                    <field
                      path="CurrentRemuneration.MostRecentSalary.GSI_IsPartOfPackage"
                      width="220"
                      mode="Optional" />
                    <field
                      path="CurrentRemuneration.MostRecentSalary.GSI_EntitlementCode"
                      width="200"
                      mode="Optional" />
                    <field
                      path="CurrentRemuneration.MostRecentSalary.GSI_Frequency"
                      width="220"
                      mode="Optional" />
                    <field
                      path="CurrentRemuneration.MostRecentSalary.GSI_Comment"
                      width="200"
                      mode="Optional" />
                    <field
                      path="CurrentRole.GEH_EffectiveDate"
                      width="190"
                      mode="Optional" />
                    <field
                      path="CurrentRole.GEH_DepartureReason"
                      width="200"
                      mode="Optional" />
                    <field
                      path="CurrentRole.GEH_DepartureComments"
                      width="220"
                      mode="Optional" />
                    <field
                      path="CurrentTeam.EffectiveDate"
                      width="200"
                      mode="Optional" />
                    <field
                      path="CurrentTeam.GET_GST_NKTeamCode"
                      width="200"
                      mode="Optional" />
                    <field
                      path="LastEditedByStaff.GS_Code"
                      width="190"
                      mode="Optional" />
                    <field
                      path="LastEditedByStaff.GS_FullName"
                      width="190"
                      mode="Optional" />
                    <field
                      path="MostRecentCostCenter.GSK_EndDate"
                      width="230"
                      mode="Optional" />
                    <field
                      path="MostRecentCostCenter.GSK_StartDate"
                      width="230"
                      mode="Optional" />
                    <field
                      path="MostRecentCostCenter.Branch.GB_BranchName"
                      width="200"
                      mode="Optional" />
                    <field
                      path="MostRecentCostCenter.Branch.GB_Code"
                      width="220"
                      mode="Optional" />
                    <field
                      path="MostRecentCostCenter.Department.GE_Code"
                      width="200"
                      mode="Optional" />
                    <field
                      path="MostRecentCostCenter.Department.GE_Desc"
                      width="200"
                      mode="Optional" />
                    <field
                      path="MostRecentPeopleLeader.GSM_EndDate"
                      width="230"
                      mode="Optional" />
                    <field
                      path="Nationality.RN_Desc"
                      width="200"
                      mode="Optional" />
                    <field
                      path="CurrentRole.GEH_JobFamily"
                      width="190"
                      mode="Optional" />
                    <field
                      path="MostRecentCostCenter"
                      width="230"
                      mode="Optional" />
                    <field
                      path="CurrentRemuneration"
                      width="200"
                      mode="Optional" />
                    <field
                      path="MostRecentPeopleLeader.GSM_EffectiveDate"
                      width="240"
                      mode="Optional" />
                    <field
                      path="MostRecentPeopleLeader.Manager.GS_FullName"
                      width="220"
                      mode="Optional" />
                    <field
                      path="MostRecentPeopleLeader.Manager.GS_Code"
                      width="200"
                      mode="Optional" />
                    <field
                      path="GS_GE_HomeDepartment"
                      width="200"
                      mode="Optional" />
                    <field
                      path="GS_ActiveDirectoryObjectGuid"
                      width="100"
                      mode="FilterOnly" />
                    <field
                      path="GS_CanLogin"
                      width="100"
                      mode="Optional" />
                    <field
                      path="GS_City"
                      width="100"
                      mode="Optional" />
                    <field
                      path="GS_GC_PreferredPaymentCompany"
                      width="100"
                      mode="Optional" />
                    <field
                      path="GS_PublishEmailAddress"
                      width="100"
                      mode="Optional" />
                    <field
                      path="GS_RN_NKCountryCode"
                      width="100"
                      mode="FilterOnly" />
                    <field
                      path="GS_RN_NKNationalityCode"
                      width="100"
                      mode="FilterOnly" />
                    <field
                      path="GS_SystemLastEditUser"
                      width="160"
                      mode="FilterOnly" />
                    <field
                      path="CurrentPhysicalWorkAddress.GEL_LocationSource"
                      width="220"
                      mode="Optional" />
                    <field
                      path="MostRecentCostCenter.BranchManagementCodeDescription"
                      width="250"
                      mode="Optional" />
                    <field
                      path="MostRecentCostCenter.Branch.GB_AccountingGroupCode"
                      width="250"
                      mode="Optional" />
                    <field
                      path="CurrentWorkingBasis.IsFullTime"
                      width="250"
                      mode="Optional" />
                    <field
                      path="GlbStaffWorkingBases"
                      width="190"
                      mode="FilterOnly" />
                    <field
                      path="GlbStaffReviews"
                      width="130"
                      mode="FilterOnly" />
                    <field
                      path="GlbStaffCostCentres"
                      width="180"
                      mode="FilterOnly" />
                    <field
                      path="Managers"
                      width="80"
                      mode="FilterOnly" />
                    <field
                      path="GlbEmploymentTeams"
                      width="160"
                      mode="FilterOnly" />
                    <field
                      path="DirectReports"
                      width="140"
                      mode="FilterOnly" />
                    <field
                      path="GlbEmploymentLocations"
                      width="200"
                      mode="FilterOnly" />
                    <field
                      path="HRMRemHistories"
                      width="170"
                      mode="FilterOnly" />
                    <field
                      path="GlbStaffClassifications"
                      width="210"
                      mode="FilterOnly" />
                    <field
                      path="GlbStaffRemunerations"
                      width="190"
                      mode="FilterOnly" />
                    <field
                      path="GlbEmploymentHistories"
                      width="200"
                      mode="FilterOnly" />
                    <field
                      path="GlbBeneficiaryBranchDepartments"
                      width="250"
                      mode="FilterOnly" />
                    <field
                      path="IndirectManagers"
                      width="250"
                      mode="FilterOnly" />
                    <field
                      path="GlbEmployingBranchDepartments"
                      width="250"
                      mode="FilterOnly" />
                    <field
                      path="MostRecentPeopleLeader.GSM_ManagerType"
                      width="250"
                      mode="Optional" />
                    <field
                      path="CurrentBeneficiaryEntity.Branch.GlbCompany.GC_Name"
                      width="220"
                      mode="Optional" />
                    <field
                      path="CurrentBeneficiaryEntity.Branch.GlbCompany.GC_Code"
                      width="190"
                      mode="Optional" />
                    <field
                      path="CurrentEmployingEntity.Branch.GlbCompany.GC_Code"
                      width="220"
                      mode="Optional" />
                    <field
                      path="CurrentRemuneration.MostRecentWaysOfWorkingAllowance.GSI_Frequency"
                      width="220"
                      mode="Optional" />
                    <field
                      path="CurrentRemuneration.MostRecentWaysOfWorkingAllowance.GSI_Value"
                      width="190"
                      mode="Optional" />
                    <field
                      path="CurrentRemuneration.MostRecentWaysOfWorkingAllowance.GSI_GrantDate"
                      width="250"
                      mode="Optional" />
                    <field
                      path="CurrentRemuneration.MostRecentWaysOfWorkingAllowance.GSI_IsPartOfPackage"
                      width="200"
                      mode="Optional" />
                    <field
                      path="CurrentRemuneration.MostRecentWaysOfWorkingAllowance.IsOneOff"
                      width="200"
                      mode="Optional" />
                    <field
                      path="CurrentRemuneration.MostRecentWaysOfWorkingAllowance.GSI_IsFTEScalable"
                      width="200"
                      mode="Optional" />
                    <field
                      path="GS_Gender"
                      width="120"
                      mode="Optional" />
                    <field
                      path="GS_DueBack"
                      width="180"
                      mode="Optional" />
                    <field
                      path="GS_WorkExtension"
                      width="100"
                      mode="Optional" />
                    <field
                      path="GS_IsDriver"
                      width="90"
                      mode="Optional" />
                    <field
                      path="GS_EmergencyContactRelationship"
                      width="150"
                      mode="Optional" />
                    <field
                      path="GS_NextOfKinRelationship"
                      width="150"
                      mode="Optional" />
                    <field
                      path="CurrentBeneficiaryEntity.Branch.GB_Code"
                      width="220"
                      mode="Optional" />
                    <field
                      path="CurrentBeneficiaryEntity.Branch.GB_BranchName"
                      width="220"
                      mode="Optional" />
                    <field
                      path="CurrentBeneficiaryEntity.Department.GE_Code"
                      width="220"
                      mode="Optional" />
                    <field
                      path="CurrentBeneficiaryEntity.Department.GE_Desc"
                      width="220"
                      mode="Optional" />
                    <field
                      path="GS_GB_HomeBranch"
                      width="250"
                      mode="FilterOnly" />
                    <field
                      path="CurrentRemuneration.Country.RN_Desc"
                      width="200"
                      mode="Optional" />
                    <field
                      path="CurrentPhysicalWorkAddress.Country.RN_Desc"
                      width="200"
                      mode="Optional" />
                    <field
                      path="GS_SystemCreateUser"
                      width="160"
                      mode="FilterOnly" />
                    <field
                      path="CurrentRemuneration.Currency.RX_Desc"
                      width="200"
                      mode="Optional" />
                    <field
                      path="GS_GivenName"
                      width="250"
                      mode="Optional" />
                    <field
                      path="GS_MiddleName"
                      width="250"
                      mode="Optional" />
                    <field
                      path="GS_Surname"
                      width="250"
                      mode="Optional" />
                    <field
                      path="GS_PreferredSurname"
                      width="250"
                      mode="Optional" />
                    <field
                      path="GS_FullNameInMotherLanguage"
                      width="250"
                      mode="Optional" />
                    <field
                      path="StaffName"
                      width="250"
                      mode="FilterOnly"
                      isFilterable="%.CurrentUserCheckpoints.Contains(&quot;CanSearchStaffLegalName&quot;)"
                      isVisible="%.CurrentUserCheckpoints.Contains(&quot;CanSearchStaffLegalName&quot;)" />
                    <field
                      path="CurrentEmployingBranch"
                      width="250"
                      mode="FilterOnly" />
                    <field
                      path="CurrentEmployingCompany"
                      width="250"
                      mode="FilterOnly" />
                    <field
                      path="CurrentEmployingDepartment"
                      width="250"
                      mode="FilterOnly" />
                    <field
                      path="CurrentBeneficiaryBranch"
                      width="250"
                      mode="FilterOnly" />
                    <field
                      path="CurrentBeneficiaryCompany"
                      width="250"
                      mode="FilterOnly" />
                    <field
                      path="CurrentBeneficiaryDepartment"
                      width="250"
                      mode="FilterOnly" />
                    <field
                      path="CurrentRemuneration.Country.RN_Code"
                      width="120"
                      mode="Optional" />
                    <field
                      path="CurrentRemuneration.Currency.RX_Code"
                      width="120"
                      mode="Optional" />
                    <field
                      path="CurrentTeam.Team.GST_Code"
                      width="250"
                      mode="Optional" />
                    <field
                      path="HomeBranch.GB_BranchName"
                      width="250"
                      mode="Optional" />
                    <field
                      path="HomeBranch.GB_Code"
                      width="250"
                      mode="Optional" />
                    <field
                      path="HrlStaffPolicies"
                      width="200"
                      mode="FilterOnly" />
                    <field
                      path="HasSubordinates"
                      width="160"
                      mode="FilterOnly" />
                    <field
                      path="StaffNameOrCodeGeneral"
                      width="250"
                      mode="FilterOnly" />
                    <field
                      path="GlbGroupLinks.GK_GG"
                      width="250"
                      mode="FilterOnly" />
                    <field
                      path="GS_IsResource"
                      width="110"
                      mode="Optional" />
                    <field
                      path="GS_IsSystemAccount"
                      width="140"
                      mode="Optional" />
                    <field
                      path="Exists"
                      width="70"
                      mode="Default" />
                    <field
                      path="IndirectManagers.GSS_GS_Manager"
                      width="250"
                      mode="FilterOnly" />
                    <field
                      path="LatestPromotionDate"
                      width="300"
                      mode="Optional" />
                    <field
                      path="LatestPromotionStatus"
                      width="300"
                      mode="Optional" />
                  </fields>
                </xml>
              </placeholder>
              <placeholder
                name="HideDefaultFooter"
                value="True" />
              <placeholder
                name="HideCustomize"
                value="True" />
              <placeholder
                name="HideCaption"
                value="True" />
              <placeholder
                name="HideActions"
                value="True" />
              <placeholder
                name="HideItemActions"
                value="True" />
              <placeholder
                name="ShowAddActions"
                value="false" />
            </control>
          </control>
          <control
            code="CMP"
            id="f2c74880-ee5a-4df0-94b5-1ca00f0a3cae"
            binding="">
            <placeholder
              name="Component"
              value="cargoWiseOne.productHrm.components.PrimaryFilteredBudgetDetails" />
          </control>
        </control>
        <control
          code="PNL"
          id="77ebe301-051b-42ce-9931-556ee7fc00f5"
          binding="">
          <control
            code="BOX"
            id="83d3206d-4564-410f-9d0d-a69ccefeb65b">
            <placeholder
              name="Overflow"
              value="overflow-hidden" />
            <placeholder
              name="MaxHeight"
              value="34px" />
            <control
              code="SDT"
              id="c000a218-b019-4fa1-850e-b3439ed0dea2"
              binding="">
              <placeholder
                name="EntityType"
                value="IGlbStaff" />
              <placeholder
                name="ItemsPerPage"
                value="1" />
              <placeholder
                name="BindingForFilter"
                value="BudgetComparisonGroup2" />
              <placeholder
                name="FieldConfiguration">
                <xml>
                  <fields xmlns="">
                    <field
                      path="GS_EmploymentDate"
                      width="300"
                      mode="Optional"
                      isVisible="false" />
                    <field
                      path="GS_FullName"
                      width="300"
                      mode="Optional"
                      isVisible="false" />
                    <field
                      path="Country.RN_Desc"
                      width="300"
                      mode="Optional"
                      isVisible="false" />
                    <field
                      path="GS_FriendlyName"
                      width="300"
                      mode="Optional"
                      isVisible="false" />
                    <field
                      path="CurrentRemuneration.GSR_FullTimeEquivalent"
                      width="300"
                      mode="Optional"
                      isVisible="false" />
                    <field
                      path="CurrentTeam.ParentTeam"
                      width="300"
                      mode="Optional"
                      isVisible="false" />
                    <field
                      path="CurrentTeam.TeamNameDescription"
                      width="300"
                      mode="Optional"
                      isVisible="false" />
                    <field
                      path="GS_EmploymentBasis"
                      width="300"
                      mode="Optional"
                      isVisible="false" />
                    <field
                      path="CurrentJobTitle"
                      width="300"
                      mode="Optional"
                      isVisible="false" />
                    <field
                      path="CurrentEmployingEntity.Branch.GlbCompany.GC_Name"
                      width="300"
                      mode="Optional"
                      isVisible="false" />
                    <field
                      path="GS_Code"
                      width="300"
                      mode="Optional"
                      isVisible="false" />
                    <field
                      path="GS_Birthdate"
                      width="150"
                      mode="Optional" />
                    <field
                      path="GS_NextReviewDate"
                      width="190"
                      mode="Optional" />
                    <field
                      path="GS_DepartureDate"
                      width="120"
                      mode="Optional" />
                    <field
                      path="GS_LastDayOfWork"
                      width="160"
                      mode="Optional" />
                    <field
                      path="GS_ProbationEndDate"
                      width="180"
                      mode="Optional" />
                    <field
                      path="GS_ResidencyExpiry"
                      width="180"
                      mode="Optional" />
                    <field
                      path="GS_SystemCreateTimeUtc"
                      width="220"
                      mode="Optional" />
                    <field
                      path="GS_SystemLastEditTimeUtc"
                      width="250"
                      mode="Optional" />
                    <field
                      path="CreatedByStaff.GS_Code"
                      width="190"
                      mode="Optional" />
                    <field
                      path="CreatedByStaff.GS_FullName"
                      width="230"
                      mode="Optional" />
                    <field
                      path="GS_UserAddress1"
                      width="200"
                      mode="Optional" />
                    <field
                      path="GS_UserAddress2"
                      width="200"
                      mode="Optional" />
                    <field
                      path="GS_WagesBankAccount"
                      width="220"
                      mode="Optional" />
                    <field
                      path="GS_WagesBankBsb"
                      width="160"
                      mode="Optional" />
                    <field
                      path="GS_WagesBankName"
                      width="220"
                      mode="Optional" />
                    <field
                      path="GS_WagesBankSwift"
                      width="250"
                      mode="Optional" />
                    <field
                      path="GS_EmailAddress"
                      width="220"
                      mode="Optional" />
                    <field
                      path="GS_EmergencyContactEmail"
                      width="250"
                      mode="Optional" />
                    <field
                      path="GS_EmergencyContactName"
                      width="220"
                      mode="Optional" />
                    <field
                      path="GS_EmergencyHomePhone"
                      width="200"
                      mode="Optional" />
                    <field
                      path="GS_HomePhone"
                      width="200"
                      mode="Optional" />
                    <field
                      path="GS_State"
                      width="170"
                      mode="Optional" />
                    <field
                      path="GS_Postcode"
                      width="170"
                      mode="Optional" />
                    <field
                      path="GS_MobilePhone"
                      width="200"
                      mode="Optional" />
                    <field
                      path="GS_LoginName"
                      width="180"
                      mode="Optional" />
                    <field
                      path="GS_NextOfKinEmail"
                      width="250"
                      mode="Optional" />
                    <field
                      path="GS_NextOfKinHomePhone"
                      width="200"
                      mode="Optional" />
                    <field
                      path="GS_NextOfKin"
                      width="250"
                      mode="Optional" />
                    <field
                      path="GS_ResidencyStatus"
                      width="160"
                      mode="Optional" />
                    <field
                      path="TotalWorkingHoursText"
                      width="200"
                      mode="Optional" />
                    <field
                      path="GS_WorkPhone"
                      width="200"
                      mode="Optional" />
                    <field
                      path="GS_IsActive"
                      width="100"
                      mode="Optional" />
                    <field
                      path="GS_IsController"
                      width="100"
                      mode="Optional" />
                    <field
                      path="GS_IsOperational"
                      width="100"
                      mode="Optional" />
                    <field
                      path="GS_PublishHomePhone"
                      width="140"
                      mode="Optional" />
                    <field
                      path="GS_PublishMobilePhone"
                      width="200"
                      mode="Optional" />
                    <field
                      path="CurrentEmployingEntity.GHB_EffectiveDate"
                      width="250"
                      mode="Optional" />
                    <field
                      path="CurrentEmployingEntity.Branch.GB_BranchName"
                      width="190"
                      mode="Optional" />
                    <field
                      path="CurrentEmployingEntity.Branch.GB_Code"
                      width="190"
                      mode="Optional" />
                    <field
                      path="CurrentEmployingEntity.Department.GE_Code"
                      width="210"
                      mode="Optional" />
                    <field
                      path="CurrentEmployingEntity.Department.GE_Desc"
                      width="250"
                      mode="Optional" />
                    <field
                      path="CurrentPhysicalWorkAddress.GEL_EffectiveDate"
                      width="250"
                      mode="Optional" />
                    <field
                      path="CurrentPhysicalWorkAddress.GEL_Address1"
                      width="220"
                      mode="Optional" />
                    <field
                      path="CurrentPhysicalWorkAddress.GEL_Address2"
                      width="220"
                      mode="Optional" />
                    <field
                      path="CurrentPhysicalWorkAddress.GEL_City"
                      width="200"
                      mode="Optional" />
                    <field
                      path="CurrentPhysicalWorkAddress.GEL_PostCode"
                      width="250"
                      mode="Optional" />
                    <field
                      path="CurrentPhysicalWorkAddress.GEL_State"
                      width="200"
                      mode="Optional" />
                    <field
                      path="CurrentRemuneration.GSR_EffectiveDate"
                      width="220"
                      mode="Optional" />
                    <field
                      path="CurrentRemuneration.MostRecentSalary"
                      width="250"
                      mode="Optional" />
                    <field
                      path="CurrentRemuneration.MostRecentSalary.GSI_Value"
                      width="200"
                      mode="Optional" />
                    <field
                      path="CurrentRemuneration.MostRecentSalary.GSI_GrantDate"
                      width="200"
                      mode="Optional" />
                    <field
                      path="CurrentRemuneration.MostRecentSalary.GSI_IsFTEScalable"
                      width="220"
                      mode="Optional" />
                    <field
                      path="CurrentRemuneration.MostRecentSalary.IsOneOff"
                      width="200"
                      mode="Optional" />
                    <field
                      path="CurrentRemuneration.MostRecentSalary.GSI_IsPartOfPackage"
                      width="220"
                      mode="Optional" />
                    <field
                      path="CurrentRemuneration.MostRecentSalary.GSI_EntitlementCode"
                      width="200"
                      mode="Optional" />
                    <field
                      path="CurrentRemuneration.MostRecentSalary.GSI_Frequency"
                      width="220"
                      mode="Optional" />
                    <field
                      path="CurrentRemuneration.MostRecentSalary.GSI_Comment"
                      width="200"
                      mode="Optional" />
                    <field
                      path="CurrentRole.GEH_EffectiveDate"
                      width="190"
                      mode="Optional" />
                    <field
                      path="CurrentRole.GEH_DepartureReason"
                      width="200"
                      mode="Optional" />
                    <field
                      path="CurrentRole.GEH_DepartureComments"
                      width="220"
                      mode="Optional" />
                    <field
                      path="CurrentTeam.EffectiveDate"
                      width="200"
                      mode="Optional" />
                    <field
                      path="CurrentTeam.GET_GST_NKTeamCode"
                      width="200"
                      mode="Optional" />
                    <field
                      path="LastEditedByStaff.GS_Code"
                      width="190"
                      mode="Optional" />
                    <field
                      path="LastEditedByStaff.GS_FullName"
                      width="190"
                      mode="Optional" />
                    <field
                      path="MostRecentCostCenter.GSK_EndDate"
                      width="230"
                      mode="Optional" />
                    <field
                      path="MostRecentCostCenter.GSK_StartDate"
                      width="230"
                      mode="Optional" />
                    <field
                      path="MostRecentCostCenter.Branch.GB_BranchName"
                      width="200"
                      mode="Optional" />
                    <field
                      path="MostRecentCostCenter.Branch.GB_Code"
                      width="220"
                      mode="Optional" />
                    <field
                      path="MostRecentCostCenter.Department.GE_Code"
                      width="200"
                      mode="Optional" />
                    <field
                      path="MostRecentCostCenter.Department.GE_Desc"
                      width="200"
                      mode="Optional" />
                    <field
                      path="MostRecentPeopleLeader.GSM_EndDate"
                      width="230"
                      mode="Optional" />
                    <field
                      path="Nationality.RN_Desc"
                      width="200"
                      mode="Optional" />
                    <field
                      path="CurrentRole.GEH_JobFamily"
                      width="190"
                      mode="Optional" />
                    <field
                      path="MostRecentCostCenter"
                      width="230"
                      mode="Optional" />
                    <field
                      path="CurrentRemuneration"
                      width="200"
                      mode="Optional" />
                    <field
                      path="MostRecentPeopleLeader.GSM_EffectiveDate"
                      width="240"
                      mode="Optional" />
                    <field
                      path="MostRecentPeopleLeader.Manager.GS_FullName"
                      width="220"
                      mode="Optional" />
                    <field
                      path="MostRecentPeopleLeader.Manager.GS_Code"
                      width="200"
                      mode="Optional" />
                    <field
                      path="GS_GE_HomeDepartment"
                      width="200"
                      mode="Optional" />
                    <field
                      path="GS_ActiveDirectoryObjectGuid"
                      width="100"
                      mode="FilterOnly" />
                    <field
                      path="GS_CanLogin"
                      width="100"
                      mode="Optional" />
                    <field
                      path="GS_City"
                      width="100"
                      mode="Optional" />
                    <field
                      path="GS_GC_PreferredPaymentCompany"
                      width="100"
                      mode="Optional" />
                    <field
                      path="GS_PublishEmailAddress"
                      width="100"
                      mode="Optional" />
                    <field
                      path="GS_RN_NKCountryCode"
                      width="100"
                      mode="FilterOnly" />
                    <field
                      path="GS_RN_NKNationalityCode"
                      width="100"
                      mode="FilterOnly" />
                    <field
                      path="GS_SystemLastEditUser"
                      width="160"
                      mode="FilterOnly" />
                    <field
                      path="CurrentPhysicalWorkAddress.GEL_LocationSource"
                      width="220"
                      mode="Optional" />
                    <field
                      path="MostRecentCostCenter.BranchManagementCodeDescription"
                      width="250"
                      mode="Optional" />
                    <field
                      path="MostRecentCostCenter.Branch.GB_AccountingGroupCode"
                      width="250"
                      mode="Optional" />
                    <field
                      path="CurrentWorkingBasis.IsFullTime"
                      width="250"
                      mode="Optional" />
                    <field
                      path="GlbStaffWorkingBases"
                      width="190"
                      mode="FilterOnly" />
                    <field
                      path="GlbStaffReviews"
                      width="130"
                      mode="FilterOnly" />
                    <field
                      path="GlbStaffCostCentres"
                      width="180"
                      mode="FilterOnly" />
                    <field
                      path="Managers"
                      width="80"
                      mode="FilterOnly" />
                    <field
                      path="GlbEmploymentTeams"
                      width="160"
                      mode="FilterOnly" />
                    <field
                      path="DirectReports"
                      width="140"
                      mode="FilterOnly" />
                    <field
                      path="GlbEmploymentLocations"
                      width="200"
                      mode="FilterOnly" />
                    <field
                      path="HRMRemHistories"
                      width="170"
                      mode="FilterOnly" />
                    <field
                      path="GlbStaffClassifications"
                      width="210"
                      mode="FilterOnly" />
                    <field
                      path="GlbStaffRemunerations"
                      width="190"
                      mode="FilterOnly" />
                    <field
                      path="GlbEmploymentHistories"
                      width="200"
                      mode="FilterOnly" />
                    <field
                      path="GlbBeneficiaryBranchDepartments"
                      width="250"
                      mode="FilterOnly" />
                    <field
                      path="IndirectManagers"
                      width="250"
                      mode="FilterOnly" />
                    <field
                      path="GlbEmployingBranchDepartments"
                      width="250"
                      mode="FilterOnly" />
                    <field
                      path="MostRecentPeopleLeader.GSM_ManagerType"
                      width="250"
                      mode="Optional" />
                    <field
                      path="CurrentBeneficiaryEntity.Branch.GlbCompany.GC_Name"
                      width="220"
                      mode="Optional" />
                    <field
                      path="CurrentBeneficiaryEntity.Branch.GlbCompany.GC_Code"
                      width="190"
                      mode="Optional" />
                    <field
                      path="CurrentEmployingEntity.Branch.GlbCompany.GC_Code"
                      width="220"
                      mode="Optional" />
                    <field
                      path="CurrentRemuneration.MostRecentWaysOfWorkingAllowance.GSI_Frequency"
                      width="220"
                      mode="Optional" />
                    <field
                      path="CurrentRemuneration.MostRecentWaysOfWorkingAllowance.GSI_Value"
                      width="190"
                      mode="Optional" />
                    <field
                      path="CurrentRemuneration.MostRecentWaysOfWorkingAllowance.GSI_GrantDate"
                      width="250"
                      mode="Optional" />
                    <field
                      path="CurrentRemuneration.MostRecentWaysOfWorkingAllowance.GSI_IsPartOfPackage"
                      width="200"
                      mode="Optional" />
                    <field
                      path="CurrentRemuneration.MostRecentWaysOfWorkingAllowance.IsOneOff"
                      width="200"
                      mode="Optional" />
                    <field
                      path="CurrentRemuneration.MostRecentWaysOfWorkingAllowance.GSI_IsFTEScalable"
                      width="200"
                      mode="Optional" />
                    <field
                      path="GS_Gender"
                      width="120"
                      mode="Optional" />
                    <field
                      path="GS_DueBack"
                      width="180"
                      mode="Optional" />
                    <field
                      path="GS_WorkExtension"
                      width="100"
                      mode="Optional" />
                    <field
                      path="GS_IsDriver"
                      width="90"
                      mode="Optional" />
                    <field
                      path="GS_EmergencyContactRelationship"
                      width="150"
                      mode="Optional" />
                    <field
                      path="GS_NextOfKinRelationship"
                      width="150"
                      mode="Optional" />
                    <field
                      path="CurrentBeneficiaryEntity.Branch.GB_Code"
                      width="220"
                      mode="Optional" />
                    <field
                      path="CurrentBeneficiaryEntity.Branch.GB_BranchName"
                      width="220"
                      mode="Optional" />
                    <field
                      path="CurrentBeneficiaryEntity.Department.GE_Code"
                      width="220"
                      mode="Optional" />
                    <field
                      path="CurrentBeneficiaryEntity.Department.GE_Desc"
                      width="220"
                      mode="Optional" />
                    <field
                      path="GS_GB_HomeBranch"
                      width="250"
                      mode="FilterOnly" />
                    <field
                      path="CurrentRemuneration.Country.RN_Desc"
                      width="200"
                      mode="Optional" />
                    <field
                      path="CurrentPhysicalWorkAddress.Country.RN_Desc"
                      width="200"
                      mode="Optional" />
                    <field
                      path="GS_SystemCreateUser"
                      width="160"
                      mode="FilterOnly" />
                    <field
                      path="CurrentRemuneration.Currency.RX_Desc"
                      width="200"
                      mode="Optional" />
                    <field
                      path="GS_GivenName"
                      width="250"
                      mode="Optional" />
                    <field
                      path="GS_MiddleName"
                      width="250"
                      mode="Optional" />
                    <field
                      path="GS_Surname"
                      width="250"
                      mode="Optional" />
                    <field
                      path="GS_PreferredSurname"
                      width="250"
                      mode="Optional" />
                    <field
                      path="GS_FullNameInMotherLanguage"
                      width="250"
                      mode="Optional" />
                    <field
                      path="StaffName"
                      width="250"
                      mode="FilterOnly"
                      isFilterable="%.CurrentUserCheckpoints.Contains(&quot;CanSearchStaffLegalName&quot;)"
                      isVisible="%.CurrentUserCheckpoints.Contains(&quot;CanSearchStaffLegalName&quot;)" />
                    <field
                      path="CurrentEmployingBranch"
                      width="250"
                      mode="FilterOnly" />
                    <field
                      path="CurrentEmployingCompany"
                      width="250"
                      mode="FilterOnly" />
                    <field
                      path="CurrentEmployingDepartment"
                      width="250"
                      mode="FilterOnly" />
                    <field
                      path="CurrentBeneficiaryBranch"
                      width="250"
                      mode="FilterOnly" />
                    <field
                      path="CurrentBeneficiaryCompany"
                      width="250"
                      mode="FilterOnly" />
                    <field
                      path="CurrentBeneficiaryDepartment"
                      width="250"
                      mode="FilterOnly" />
                    <field
                      path="CurrentRemuneration.Country.RN_Code"
                      width="120"
                      mode="Optional" />
                    <field
                      path="CurrentRemuneration.Currency.RX_Code"
                      width="120"
                      mode="Optional" />
                    <field
                      path="CurrentTeam.Team.GST_Code"
                      width="250"
                      mode="Optional" />
                    <field
                      path="HomeBranch.GB_BranchName"
                      width="250"
                      mode="Optional" />
                    <field
                      path="HomeBranch.GB_Code"
                      width="250"
                      mode="Optional" />
                    <field
                      path="HrlStaffPolicies"
                      width="200"
                      mode="FilterOnly" />
                    <field
                      path="HasSubordinates"
                      width="160"
                      mode="FilterOnly" />
                    <field
                      path="StaffNameOrCodeGeneral"
                      width="250"
                      mode="FilterOnly" />
                    <field
                      path="GlbGroupLinks.GK_GG"
                      width="250"
                      mode="FilterOnly" />
                    <field
                      path="GS_IsResource"
                      width="110"
                      mode="Optional" />
                    <field
                      path="GS_IsSystemAccount"
                      width="140"
                      mode="Optional" />
                    <field
                      path="Exists"
                      width="70"
                      mode="Default" />
                    <field
                      path="IndirectManagers.GSS_GS_Manager"
                      width="250"
                      mode="FilterOnly" />
                    <field
                      path="LatestPromotionDate"
                      width="300"
                      mode="Optional" />
                    <field
                      path="LatestPromotionStatus"
                      width="300"
                      mode="Optional" />
                  </fields>
                </xml>
              </placeholder>
              <placeholder
                name="HideDefaultFooter"
                value="True" />
              <placeholder
                name="HideActions"
                value="True" />
              <placeholder
                name="HideCaption"
                value="True" />
              <placeholder
                name="HideCustomize"
                value="True" />
              <placeholder
                name="HideItemActions"
                value="True" />
              <placeholder
                name="ShowAddActions"
                value="false" />
            </control>
          </control>
          <control
            code="CMP"
            id="9949856c-1424-424c-8af6-aa591b9f1cf6"
            binding="">
            <placeholder
              name="Component"
              value="cargoWiseOne.productHrm.components.SecondaryFilteredBudgetDetails" />
          </control>
        </control>
      </control>
      <control
        code="TAB"
        id="15a97808-e14a-420f-86c3-cb6cfdb18d24"
        binding="">
        <placeholder
          name="Caption"
          value="Budget Tracking"
          resid="f797d3fb-b422-4748-b619-b6945c83fa7d" />
        <placeholder
          name="VisibilityCondition"
          value="DescendantNodes.Any() &amp;&amp; ReviewProcess.IncludesRemuneration" />
      </control>
      <control
        code="TAI"
        id="440dea1a-4768-4472-ba76-ba1f365c298b"
        binding="">
        <placeholder
          name="VisibilityCondition"
          value="DescendantNodes.Any() &amp;&amp; ReviewProcess.IncludesRemuneration" />
        <control
          code="PNL"
          id="bd5e7917-e984-4ad5-ad95-2d2827d02be2"
          binding="">
          <placeholder
            name="VisibilityCondition"
            value="ReviewProcess.IncludesRemuneration" />
          <placeholder
            name="Margin"
            value="mb-2" />
          <placeholder
            name="Layout"
            value="grid" />
          <control
            code="LBL"
            id="fbbb593d-3924-4752-b83c-3a5ff4fdd027">
            <placeholder
              name="Caption"
              value="View the Budget of any of your Direct Reports who are assigned reviews"
              resid="71e1da6c-5864-4763-b139-499895db5510" />
          </control>
          <control
            code="SRC"
            id="0467414a-068a-4726-a867-2d1c3cc6d2af"
            binding="Reviewer.CurrentRemuneration.GSR_RX_NKCurrency">
            <placeholder
              name="IsReadOnly"
              value="True" />
            <placeholder
              name="Columns"
              value="col-sm-3" />
            <placeholder
              name="CaptionType"
              value="none" />
          </control>
          <control
            code="CMP"
            id="b24330e0-b69d-4bdd-83a8-20b7256cdd81"
            binding="">
            <placeholder
              name="Component"
              value="cargoWiseOne.productHrm.components.ManagerBudgetTracking" />
          </control>
        </control>
      </control>
      <control
        code="TAB"
        id="f952e4b8-41d9-4496-8eb6-811c3283ef70">
        <placeholder
          name="Caption"
          value="Promotions"
          resid="a0ebeda3-7f99-42bc-bac1-2815f307bba0" />
      </control>
      <control
        code="TAI"
        id="a6d19036-fbfb-4a76-bf6c-1e4014d06efb">
        <control
          code="SDT"
          id="4a21155c-f5c7-4771-94a1-c5022a285f6b">
          <placeholder
            name="CaptionOverride"
            value="Promotions raised by me"
            resid="60a2b91d-baaf-4916-b136-c2df173b312d" />
          <placeholder
            name="EntityType"
            value="IGlbStaffChangeRequest" />
          <placeholder
            name="FullPage"
            value="True" />
          <placeholder
            name="Margin"
            value="mb-2" />
          <placeholder
            name="DefaultFilter">
            <xml>
              <ArrayOfFilterGroup xmlns:a="http://schemas.microsoft.com/2003/10/Serialization/Arrays" xmlns:i="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://cargowise.com/glow/2014/07/16/filters.xsd">
                <FilterGroup>
                  <Filters>
                    <Filter>
                      <FilterType>StringFilter</FilterType>
                      <Operation>Is</Operation>
                      <PropertyPath>CreatedByStaff.GS_Code</PropertyPath>
                      <Values>
                        <a:string>&lt;%.CurrentStaffCode&gt;</a:string>
                      </Values>
                    </Filter>
                    <Filter>
                      <FilterType>UtcDateTimeFilter</FilterType>
                      <Operation>WasInTheLast12Months</Operation>
                      <PropertyPath>GCR_SystemCreateTimeUtc</PropertyPath>
                      <Values />
                    </Filter>
                    <Filter>
                      <FilterType>SimpleLookupFilter</FilterType>
                      <Operation>Is</Operation>
                      <PropertyPath>Template.GSG_Usage</PropertyPath>
                      <Values>
                        <a:string>REV</a:string>
                      </Values>
                    </Filter>
                  </Filters>
                  <IsImplicit>true</IsImplicit>
                </FilterGroup>
              </ArrayOfFilterGroup>
            </xml>
          </placeholder>
          <placeholder
            name="FieldConfiguration">
            <xml>
              <fields xmlns="">
                <field
                  path="Staff.GS_FullName"
                  width="250"
                  mode="Default" />
                <field
                  path="GCR_GSG_Template"
                  width="250"
                  mode="Default" />
                <field
                  path="GCR_SystemCreateTimeUtc"
                  width="220"
                  mode="Optional" />
                <field
                  path="GCR_Status"
                  width="60"
                  mode="Default" />
                <field
                  path="EarliestEffectiveDate"
                  width="230"
                  mode="Default" />
                <field
                  path="CurrentlyAssignedTo"
                  width="250"
                  mode="Default" />
              </fields>
            </xml>
          </placeholder>
          <placeholder
            name="DisabledGridRowActions"
            value="Documents" />
          <placeholder
            name="HideImport"
            value="True" />
        </control>
        <control
          code="SDT"
          id="15209cb5-c744-4bc1-bdcb-129bdee974fb">
          <placeholder
            name="CaptionOverride"
            value="Promotions assigned to me"
            resid="ca8e77ff-54c6-40d9-ab1e-dd503a5a436a" />
          <placeholder
            name="EntityType"
            value="IWorkflowTask[[IGlbStaffChangeRequest]]" />
          <placeholder
            name="FullPage"
            value="True" />
          <placeholder
            name="DefaultFilter">
            <xml>
              <ArrayOfFilterGroup xmlns:a="http://schemas.microsoft.com/2003/10/Serialization/Arrays" xmlns:i="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://cargowise.com/glow/2014/07/16/filters.xsd">
                <FilterGroup>
                  <Filters>
                    <Filter>
                      <FilterType>StringFilter</FilterType>
                      <Operation>Is</Operation>
                      <PropertyPath>AssignedStaffMember.GS_Code</PropertyPath>
                      <Values>
                        <a:string>&lt;%.CurrentStaffCode&gt;</a:string>
                      </Values>
                    </Filter>
                    <Filter>
                      <FilterType>UtcDateTimeFilter</FilterType>
                      <Operation>WasInTheLast12Months</Operation>
                      <PropertyPath>P9_SystemCreateTimeUtc</PropertyPath>
                      <Values />
                    </Filter>
                    <Filter>
                      <FilterType>SimpleLookupFilter</FilterType>
                      <Operation>Is</Operation>
                      <PropertyPath>Parent.Template.GSG_Usage</PropertyPath>
                      <Values>
                        <a:string>REV</a:string>
                      </Values>
                    </Filter>
                  </Filters>
                  <IsImplicit>true</IsImplicit>
                </FilterGroup>
              </ArrayOfFilterGroup>
            </xml>
          </placeholder>
          <placeholder
            name="FieldConfiguration">
            <xml>
              <fields xmlns="">
                <field
                  path="Parent.Staff.GS_FullName"
                  width="250"
                  mode="Default" />
                <field
                  path="P9_GS_NKAssignedStaffMember"
                  width="210"
                  mode="Default" />
                <field
                  path="Parent.CreatedByStaff.GS_FullName"
                  width="250"
                  mode="Default" />
                <field
                  path="P9_CompletedTimeUtc"
                  width="180"
                  mode="Optional" />
                <field
                  path="P9_Status"
                  width="60"
                  mode="Default" />
                <field
                  path="IsApprovalTask"
                  width="150"
                  mode="Optional" />
                <field
                  path="ApprovalType"
                  width="250"
                  mode="Optional" />
                <field
                  path="ApprovalStatus"
                  width="250"
                  mode="Default" />
                <field
                  path="P9_Outcome"
                  width="70"
                  mode="Optional" />
                <field
                  path="Parent.Template.GSG_TemplateName"
                  width="250"
                  mode="Default" />
                <field
                  path="Parent.GCR_SystemCreateTimeUtc"
                  width="250"
                  mode="Optional" />
                <field
                  path="EarliestEffectiveDate"
                  width="230"
                  mode="Default" />
              </fields>
            </xml>
          </placeholder>
          <placeholder
            name="HideImport"
            value="True" />
          <placeholder
            name="DisabledGridRowActions"
            value="Documents" />
        </control>
      </control>
    </control>
  </form>
