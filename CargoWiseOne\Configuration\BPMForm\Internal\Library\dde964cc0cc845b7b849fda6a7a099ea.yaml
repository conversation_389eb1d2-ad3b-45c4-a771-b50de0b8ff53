#transformationVersion: 70.0
#
VZ_PK: dde964cc0cc845b7b849fda6a7a099ea
VZ_ConfigurationKey: dde964cc-0cc8-45b7-b849-fda6a7a099ea
VZ_FormID: Header Properties Page
VZ_Caption:
  resKey: VZ_Caption|dde964cc0cc845b7b849fda6a7a099ea
  text: Header Properties Page
VZ_FormFactor: DSK
VZ_FormType: PAG
VZ_RequiresLogin: true
VZ_MaterialDesign: true
VZ_Dependencies: <dependencies xmlns="http://cargowise.com/glow/2014/11/14/dependencies.xsd" />
VZ_FormData: >-
  <form
    id="bf55b78b-6ad4-4db1-ac32-61a68327699a" xmlns="http://wisetechglobal.com/glow/2017/05/26/form.xsd">
    <placeholder
      name="FitToHeight"
      value="True" />
    <placeholder
      name="Layout"
      value="fill" />
    <control
      code="PNL"
      id="aa13b003-652b-486c-aa0a-9aacc8ca1fc7"
      binding="">
      <placeholder
        name="Margin"
        value="mb-3" />
      <placeholder
        name="Class"
        value="blue lighten-4 bg-blue-lighten-4" />
      <control
        code="BOX"
        id="7c25f33c-fbfa-42a7-9bd8-7b0faa0e52c5">
        <placeholder
          name="Layout"
          value="flex" />
        <control
          code="IBT"
          id="830e2782-0d62-4134-863e-6258334435c9"
          binding="">
          <placeholder
            name="Icon"
            value="s-icon-documentation" />
          <placeholder
            name="Hyperlink"
            value="https://wisetechglobal.sharepoint.com/sites/Content-as-Code/Shared%20Documents/GLOW-Content/_permalinks/PB-FF-HeaderProperties.aspx" />
          <placeholder
            name="Tooltip"
            value="Documentation"
            resid="d92d4976-0644-4700-8256-40cbafd4c168" />
          <placeholder
            name="Target"
            value="_blank" />
        </control>
        <control
          code="IBT"
          id="0d9e2887-c3da-436a-ab69-ec0dcbcf3f86"
          binding="">
          <placeholder
            name="Icon"
            value="s-icon-pt-devtools" />
          <placeholder
            name="Margin"
            value="ml-2" />
          <placeholder
            name="Target"
            value="_blank" />
          <placeholder
            name="Hyperlink"
            value="https://github.com/WiseTechGlobal/Glow/blob/master/CargoWiseOne/Configuration/BPMFormFlow/Internal/Library/4b98b24a245f47738ee388aaf4ef6874.yaml" />
          <placeholder
            name="Tooltip"
            value="YAML"
            resid="538f925f-e1b8-40ca-9ebb-fea67c172d8c" />
        </control>
        <control
          code="IBT"
          id="1ad6853e-5f3e-46c7-b5f8-d1f1940a1609"
          binding="">
          <placeholder
            name="Icon"
            value="s-icon-settings" />
          <placeholder
            name="Margin"
            value="ml-2" />
          <placeholder
            name="Tooltip"
            value="Platform Builder"
            resid="53425f55-4621-4a8d-80ca-5683660b2f07" />
          <placeholder
            name="Hyperlink"
            value="platformbuilder:?target=BPMFormFlow&amp;identifier=4b98b24a245f47738ee388aaf4ef6874" />
          <placeholder
            name="Target"
            value="_blank" />
        </control>
        <control
          code="BOX"
          id="ab662a10-26ad-4bf7-b242-41e5fb1e50b1">
          <placeholder
            name="Margin"
            value="ml-3" />
          <placeholder
            name="Layout"
            value="flex" />
          <placeholder
            name="FlexDirection"
            value="flex-column" />
          <placeholder
            name="FlexJustify"
            value="justify-center" />
          <placeholder
            name="FlexAlign"
            value="align-center" />
          <control
            code="LBL"
            id="20fa8069-65fa-4690-b763-b92e9d9c5718"
            binding="">
            <placeholder
              name="Display"
              value="block" />
            <placeholder
              name="Caption"
              value="To have a hands on experience of the example within the Documentation, run the Action Menu Item called &quot;Header Properties Example&quot;."
              resid="44d8afde-aca3-4118-873f-9c285a4671ee" />
            <placeholder
              name="Typography"
              value="body-strong" />
          </control>
        </control>
      </control>
    </control>
    <control
      code="PNL"
      id="bf89367e-0e3e-4b63-b49a-1869ffe575bd"
      binding="">
      <placeholder
        name="Layout"
        value="fill" />
      <placeholder
        name="FillAvailable"
        value="True" />
      <control
        code="SDT"
        id="db531709-667d-47e2-aa34-65a12615a073"
        binding="">
        <placeholder
          name="EntityType"
          value="IDtbLandTransportConsignment" />
        <placeholder
          name="CaptionOverride"
          value="List to explain the usage of the Form-flow Header properties"
          resid="7eafb5ff-b40b-44dd-a758-d8560433266e" />
        <placeholder
          name="HideActions"
          value="True" />
        <placeholder
          name="HideFilters"
          value="True" />
        <placeholder
          name="DefaultFilter">
          <xml>
            <ArrayOfFilterGroup xmlns:a="http://schemas.microsoft.com/2003/10/Serialization/Arrays" xmlns:i="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://cargowise.com/glow/2014/07/16/filters.xsd">
              <FilterGroup>
                <Filters>
                  <Filter>
                    <FilterType>SimpleLookupFilter</FilterType>
                    <Operation>IsNotBlank</Operation>
                    <PropertyPath>AdditionalServices.ES_ServiceCode</PropertyPath>
                    <Values />
                  </Filter>
                </Filters>
                <IsImplicit>true</IsImplicit>
              </FilterGroup>
            </ArrayOfFilterGroup>
          </xml>
        </placeholder>
        <placeholder
          name="ActionMenuItems">
          <xml>
            <formFlows xmlns="">
              <formFlow>4b98b24a245f47738ee388aaf4ef6874</formFlow>
            </formFlows>
          </xml>
        </placeholder>
        <placeholder
          name="FitToHeight"
          value="True" />
      </control>
    </control>
  </form>
