#transformationVersion: 70.0
#
VZ_PK: 0378245240cf48c1bdd07d4c9be049a9
VZ_ConfigurationKey: 03782452-40cf-48c1-bdd0-7d4c9be049a9
VZ_FormID: GDM - Completed Movements
VZ_Caption:
  resKey: VZ_Caption|0378245240cf48c1bdd07d4c9be049a9
  text: Completed movements
VZ_FormFactor: DSK
VZ_FormType: PAG
VZ_RequiresLogin: true
VZ_MaterialDesign: true
VZ_Dependencies: <dependencies xmlns="http://cargowise.com/glow/2014/11/14/dependencies.xsd" />
VZ_FormData: >-
  <form
    id="971fb4e3-daf6-45f2-81cb-40d807e168f1" xmlns="http://wisetechglobal.com/glow/2017/05/26/form.xsd">
    <placeholder
      name="Layout"
      value="fill" />
    <control
      code="SDT"
      id="70880499-d2ce-469f-bfd2-ed74aba47bce"
      binding="">
      <placeholder
        name="CaptionOverride"
        value="Completed movements"
        resid="72b6baa1-8dfe-4391-ab89-046c880ef7e1" />
      <placeholder
        name="EntityType"
        value="IGteCompletedMovement" />
      <placeholder
        name="DefaultSortFields">
        <xml>
          <ArrayOfFieldSortDefinition xmlns:i="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://schemas.datacontract.org/2004/07/CargoWise.Glow.Infrastructure.Interfaces">
            <FieldSortDefinition>
              <FieldName>VehicleEntryDateTime</FieldName>
              <IsAscending>true</IsAscending>
            </FieldSortDefinition>
          </ArrayOfFieldSortDefinition>
        </xml>
      </placeholder>
      <placeholder
        name="FullPage"
        value="True" />
      <placeholder
        name="FieldConfiguration">
        <xml>
          <fields xmlns="">
            <field
              path="VehicleMovement.GateInActionNumber"
              width="300"
              mode="Default"
              readOnly="true" />
            <field
              path="VehicleRegistrationNumber"
              width="300"
              mode="Default"
              readOnly="true" />
            <field
              path="GVM_RC_VehicleType"
              width="300"
              mode="Default"
              readOnly="true"/>
            <field
              path="VehicleEntryDateTime"
              width="300"
              mode="Default"
              readOnly="true"/>
            <field
              path="VehicleEntryCreatedBy"
              width="300"
              mode="Default"
              readOnly="true"/>
            <field
              path="VehicleMovement.BookingType"
              width="300"
              mode="Default"
              readOnly="true"/>
            <field
              path="GateBooking.GBK_ReferenceNumber"
              width="300"
              mode="Default"
              readOnly="true"/>
            <field
              path="VehicleMovement.MovementType"
              width="300"
              mode="Default"
              readOnly="true"/>
            <field
              path="VehicleMovement.GateOutActionNumber"
              width="300"
              mode="Default"
              readOnly="true"/>
            <field
              path="VehicleExitDateTime"
              width="300"
              mode="Default"
              readOnly="true"/>
            <field
              path="VehicleExitCreatedBy"
              width="300"
              mode="Default"
              readOnly="true"/>
            <field
              path="VehicleMovement.TransportProvider"
              width="300"
              mode="Default"
              readOnly="true"/>
          </fields>
        </xml>
      </placeholder>
      <placeholder
        name="ShowAutoRefresh"
        value="True" />
      <placeholder
        name="AutoRefresh"
        value="fiveMinutes" />
    </control>
  </form>
