#transformationVersion: 70.0
#
VZ_PK: db491932988b416da39a5b2a60e8033b
VZ_ConfigurationKey: db491932-988b-416d-a39a-5b2a60e8033b
VZ_FormID: GDM - View Booking
VZ_Caption:
  resKey: Caption|db491932-988b-416d-a39a-5b2a60e8033b
  text: View booking
VZ_FormFactor: DSK
VZ_EntityType: IGteBooking
VZ_FormType: FRM
VZ_RequiresLogin: true
VZ_MaterialDesign: true
VZ_Dependencies: <dependencies xmlns="http://cargowise.com/glow/2014/11/14/dependencies.xsd" />
VZ_StatusBindingPath: Status
VZ_EntityStatusDisplayMode: ReadOnly
VZ_FormData: >-
  <form
    id="a76ea58a-ee5d-4ea2-95bf-e481d51533ea" xmlns="http://wisetechglobal.com/glow/2017/05/26/form.xsd">
    <placeholder
      name="Align"
      value="left" />
    <placeholder
      name="FitToHeight"
      value="True" />
    <placeholder
      name="Layout"
      value="flex" />
    <placeholder
      name="FlexDirection"
      value="flex-column" />
    <control
      code="BOX"
      id="8d691e88-32b4-45d2-91c2-829851f484a2"
      binding="">
      <placeholder
        name="Height"
        value="100%" />
      <placeholder
        name="Margin"
        value="mt-2" />
      <placeholder
        name="Layout"
        value="grid" />
      <control
        code="BOX"
        id="6ac328c5-f89e-4894-bdc8-cec5337cf80d"
        binding="">
        <placeholder
          name="Height"
          value="100%" />
        <placeholder
          name="Margin"
          value="mr-4" />
        <placeholder
          name="Columns"
          value="col-3" />
        <control
          code="PNL"
          id="7ab8d565-9577-4eb6-839a-4934d025d394"
          binding="">
          <placeholder
            name="Caption"
            value="Transport details"
            resid="bbd2a686-4020-4a4c-b357-8b2109acf231" />
          <control
            code="BOX"
            id="dae81f7c-cfe8-42a4-882d-9837025b7777"
            binding="">
            <placeholder
              name="Layout"
              value="grid" />
            <placeholder
              name="Margin"
              value="mb-2" />
            <control
              code="TXT"
              id="4cb0f8bb-ef12-4aee-8097-af3d8c574f24"
              binding="FirstVehicleMovementBooking.GBV_VehicleRegistration">
              <placeholder
                name="Margin"
                value="mb-2" />
              <placeholder
                name="Columns"
                value="col-6" />
              <placeholder
                name="CaptionOverride"
                value="Vehicle registration"
                resid="a32c960e-2494-4a98-8545-d16ccbca6e0b" />
            </control>
            <control
              code="SRC"
              id="7f4c8f63-7372-4ca7-99c7-bb05a274391g"
              binding="FirstVehicleMovementBooking.GBV_RC_VehicleType">
              <placeholder
                name="Margin"
                value="mb-2" />
              <placeholder
                name="Columns"
                value="col-6" />
              <placeholder
                name="CaptionOverride"
                value="Vehicle type"
                resid="87d8601d-062a-4234-8090-6b311f579618" />
            </control>
            <control
              code="SRC"
              id="708133a0-1adc-4881-86c7-15895df271c8"
              binding="GBK_OH_TransportCompany">
              <placeholder
                name="CaptionOverride"
                value="Transport provider"
                resid="637d91c0-d802-4f6b-8f13-99a7aed0ad48" />
              <placeholder
                name="IsReadOnly"
                value="False" />
              <placeholder
                name="Margin"
                value="mb-2" />
            </control>
            <control
              code="TXT"
              id="5aee77a8-a94b-4946-81da-8d10f0a5a2ef"
              binding="GBK_ReferenceNumber">
              <placeholder
                name="CaptionOverride"
                value="Gate booking"
                resid="bcc60ac9-46d1-4337-b423-2ac0950010dd" />
              <placeholder
                name="Columns"
                value="col-6" />
              <placeholder
                name="Margin"
                value="mb-2" />
            </control>
            <control
              code="CMB"
              id="79a795e2-193e-448b-941a-ecb543bb3455"
              binding="GBK_BookingType">
              <placeholder
                name="CaptionOverride"
                value="Booking type"
                resid="73b1c7b3-4d4c-4765-9912-293142254d8c" />
              <placeholder
                name="Columns"
                value="col-6" />
              <placeholder
                name="Margin"
                value="mb-2" />
            </control>
            <control
              code="SRC"
              id="19203995-2b3d-4f6c-97cc-abcdfc404d95"
              binding="GBK_WW_Facility">
              <placeholder
                name="IsReadOnly"
                value="False" />
              <placeholder
                name="CaptionOverride"
                value="Facility"
                resid="7c07d3b3-3d77-4540-8464-00260e79f094" />
              <placeholder
                name="Margin"
                value="mb-2" />
            </control>
            <control
              code="DTE"
              id="4dd21304-117f-425c-bc5c-94604fdbc036"
              binding="EarliestBookedSlot">
              <placeholder
                name="Margin"
                value="mb-2" />
              <placeholder
                name="CaptionOverride"
                value="Earliest booking slot"
                resid="597ffec9-b82a-40a0-9a49-06b39d0f1f85" />
            </control>
            <control
              code="DVR"
              id="c8e97568-b986-4d1f-a2d3-0b8f93f18929">
              <placeholder
                name="Variant"
                value="solid" />
              <placeholder
                name="Margin"
                value="mb-2" />
            </control>
            <control
              code="TXT"
              id="9e6686b6-af4e-4619-bbf6-b6103cbc5941"
              binding="FirstVehicleDriverBooking.GBD_DriverName">
              <placeholder
                name="CaptionOverride"
                value="Entry driver name"
                resid="775ba55d-f33e-441b-9249-84e245ed739b" />
              <placeholder
                name="Margin"
                value="mb-2" />
              <placeholder
                name="Columns"
                value="col-6" />
            </control>
            <control
              code="TXT"
              id="fb103167-86cd-46b6-9db7-0f8e8fa3c742"
              binding="FirstVehicleDriverBooking.GBD_DriverLicenseNumber">
              <placeholder
                name="CaptionOverride"
                value="Entry driver license"
                resid="1c97cbed-ed9d-4661-af96-7cf01c2fe85c" />
              <placeholder
                name="Margin"
                value="mb-2" />
              <placeholder
                name="Columns"
                value="col-6" />
            </control>
          </control>
        </control>
      </control>
      <control
        code="PNL"
        id="c01d271f-9f8c-4572-9c04-81f2f0c7d2ff"
        binding="">
        <placeholder
          name="FitToHeight"
          value="True" />
        <placeholder
          name="Columns"
          value="col-9" />
        <placeholder
          name="Height"
          value="100%" />
        <control
          code="RDT"
          id="335f86f4-4c4f-4cdc-a70a-85947c56a559"
          binding="GteGateMovementBookings">
          <placeholder
            name="CaptionOverride"
            value="Associated bookings"
            resid="8775e92b-1d71-46a2-b99f-a23c70ce2b28" />
          <placeholder
            name="ShowFilters"
            value="True" />
          <placeholder
            name="DefaultFilter">
            <xml>
              <ArrayOfFilterGroup xmlns:a="http://schemas.microsoft.com/2003/10/Serialization/Arrays" xmlns:i="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://cargowise.com/glow/2014/07/16/filters.xsd">
                <FilterGroup>
                  <Filters>
                    <Filter>
                      <FilterType>StringFilter</FilterType>
                      <Operation>IsBlank</Operation>
                      <PropertyPath>GBM_CancelledReason</PropertyPath>
                      <Values />
                    </Filter>
                  </Filters>
                  <IsImplicit>true</IsImplicit>
                </FilterGroup>
              </ArrayOfFilterGroup>
            </xml>
          </placeholder>
          <placeholder
            name="AllowAdd"
            value="True" />
          <placeholder
            name="InlineEdit"
            value="cell" />
          <placeholder
            name="AllowUpdateOnReadonlyParent"
            value="False" />
          <placeholder
            name="AllowAttach"
            value="False" />
          <placeholder
            name="AllowDetach"
            value="False" />
          <placeholder
            name="FieldConfiguration">
            <xml>
              <fields xmlns="">
                <field
                  path="Status"
                  width="250"
                  mode="Default" />
                <field
                  path="MovementType"
                  width="250"
                  mode="Default" />
                <field
                  path="GBM_SlotStartTime"
                  width="150"
                  mode="Default" />
                <field
                  path="GBM_SlotEndTime"
                  width="130"
                  mode="Default" />
                <field
                  path="GBM_RC_UnitType"
                  width="250"
                  mode="Default" />
                <field
                  path="GBM_UnitNumber"
                  width="200"
                  mode="Default" />
                <field
                  path="GBM_Source"
                  width="100"
                  mode="Default" />
                <field
                  path="GBM_SourceReferenceNumber"
                  width="250"
                  mode="Default" />
                <field
                  path="GBM_BookingReferenceNumber"
                  width="250"
                  mode="Mandatory" />
                <field
                  path="GBM_TransportReference"
                  width="250"
                  mode="Default" />
                <field
                  path="GateInNumber"
                  width="250"
                  mode="Default" />
                <field
                  path="GateOutNumber"
                  width="250"
                  mode="Default" />
              </fields>
            </xml>
          </placeholder>
          <placeholder
            name="DefaultSortFields">
            <xml>
              <ArrayOfFieldSortDefinition xmlns:i="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://schemas.datacontract.org/2004/07/CargoWise.Glow.Infrastructure.Interfaces" />
            </xml>
          </placeholder>
          <placeholder
            name="ShowCustomize"
            value="True" />
          <placeholder
            name="ActionMenuItems">
            <xml>
              <formFlows xmlns="">
                <formFlow
                  inDrawer="True">5410a6b4a4954461af9ea92573097a46</formFlow>
              </formFlows>
            </xml>
          </placeholder>
        </control>
      </control>
    </control>
  </form>
