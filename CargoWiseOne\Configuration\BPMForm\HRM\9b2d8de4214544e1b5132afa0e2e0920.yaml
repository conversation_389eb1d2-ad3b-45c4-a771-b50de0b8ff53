#transformationVersion: 70.0
#
VZ_PK: 9b2d8de4214544e1b5132afa0e2e0920
VZ_ConfigurationKey: 9b2d8de4-2145-44e1-b513-2afa0e2e0920
VZ_FormID: HRM - Remuneration - My Review Process Nodes
VZ_Caption:
  resKey: VZ_Caption|9b2d8de4214544e1b5132afa0e2e0920
  text: My review tasks
VZ_FormFactor: DSK
VZ_FormType: PAG
VZ_RequiresLogin: true
VZ_MaterialDesign: true
VZ_Dependencies: <dependencies xmlns="http://cargowise.com/glow/2014/11/14/dependencies.xsd" />
VZ_FormData: >-
  <form
    id="ccd63e01-7608-4a14-8c1b-7d76c76374a2" xmlns="http://wisetechglobal.com/glow/2017/05/26/form.xsd">
    <placeholder
      name="Layout"
      value="fill" />
    <control
      code="SDT"
      id="749eecbc-d2f2-4455-a897-6a49e4acc836"
      binding="">
      <placeholder
        name="CaptionOverride"
        value="My review tasks"
        resid="cf4d980c-1132-4ae6-8ee2-c02fc3cd38fa" />
      <placeholder
        name="EntityType"
        value="IReviewProcessNode" />
      <placeholder
        name="EditFormFlowConfiguration">
        <xml>
          <formFlows xmlns="">
            <formFlow>ba0ec0fb513644019594f08977cc6ef6</formFlow>
          </formFlows>
        </xml>
      </placeholder>
      <placeholder
        name="FullPage"
        value="True" />
      <placeholder
        name="DefaultFilter">
        <xml>
          <ArrayOfFilterGroup xmlns:a="http://schemas.microsoft.com/2003/10/Serialization/Arrays" xmlns:i="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://cargowise.com/glow/2014/07/16/filters.xsd">
            <FilterGroup>
              <Filters>
                <Filter>
                  <FilterType>SimpleLookupFilter</FilterType>
                  <Operation>Is</Operation>
                  <PropertyPath>RRN_Status</PropertyPath>
                  <Values>
                    <a:string>ASN</a:string>
                  </Values>
                </Filter>
                <Filter>
                  <FilterType>SimpleLookupFilter</FilterType>
                  <Operation>Is</Operation>
                  <PropertyPath>ReviewProcess.RPR_Status</PropertyPath>
                  <Values>
                    <a:string>WRK</a:string>
                  </Values>
                </Filter>
              </Filters>
              <IsImplicit>false</IsImplicit>
            </FilterGroup>
            <FilterGroup>
              <Filters>
                <Filter>
                  <FilterType>StringFilter</FilterType>
                  <Operation>Is</Operation>
                  <PropertyPath>Reviewer.GS_Code</PropertyPath>
                  <Values>
                    <a:string>&lt;%.CurrentStaffCode&gt;</a:string>
                  </Values>
                </Filter>
              </Filters>
              <IsImplicit>true</IsImplicit>
            </FilterGroup>
          </ArrayOfFilterGroup>
        </xml>
      </placeholder>
      <placeholder
        name="ActionMenuItems">
        <xml>
          <formFlows xmlns="">
            <formFlow>ba0ec0fb513644019594f08977cc6ef6</formFlow>
          </formFlows>
        </xml>
      </placeholder>
      <placeholder
        name="FieldConfiguration">
        <xml>
          <fields xmlns="">
            <field
              path="RRN_GS_Reviewer"
              width="300"
              mode="Mandatory" />
            <field
              path="RRN_Status"
              width="150"
              mode="Mandatory" />
            <field
              path="Messages"
              width="150"
              mode="Default" />
            <field
              path="ReviewProcess.RPR_Name"
              width="300"
              mode="Default" />
            <field
              path="ReviewProcess.RPR_Type"
              width="300"
              mode="Default" />
            <field
              path="ReviewProcess.RPR_Status"
              width="300"
              mode="Default" />
            <field
              path="ReviewProcess.RPR_SubmissionDate"
              width="300"
              mode="Default" />
            <field
              path="Reviewer.Country.RN_Desc"
              width="250"
              mode="Optional" />
            <field
              path="Reviewer.CurrentRole.GEH_JobFamily"
              width="250"
              mode="Optional" />
            <field
              path="RRN_SystemCreateTimeUtc"
              width="220"
              mode="Optional" />
            <field
              path="RRN_SystemLastEditTimeUtc"
              width="250"
              mode="Optional" />
            <field
              path="RRN_SystemCreateUser"
              width="160"
              mode="Optional" />
            <field
              path="RRN_SystemLastEditUser"
              width="200"
              mode="Optional" />
            <field
              path="Reviewer.GS_FullName"
              width="250"
              mode="Optional" />
            <field
              path="ReviewProcess.RPR_ExchangeRateEffectiveDate"
              width="250"
              mode="Optional" />
            <field
              path="Parent.Reviewer.GS_FullName"
              width="250"
              mode="Optional" />
            <field
              path="Reviewer.CurrentJobTitle"
              width="250"
              mode="Optional" />
            <field
              path="NumberOfDirectReports"
              width="240"
              mode="Optional" />
            <field
              path="NumberOfTeams"
              width="150"
              mode="Optional" />
          </fields>
        </xml>
      </placeholder>
    </control>
  </form>
