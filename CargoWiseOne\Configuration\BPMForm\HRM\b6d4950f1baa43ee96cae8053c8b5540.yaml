#transformationVersion: 70.0
#
VZ_PK: b6d4950f1baa43ee96cae8053c8b5540
VZ_ConfigurationKey: b6d4950f-1baa-43ee-96ca-e8053c8b5540
VZ_FormID: HRM - Bulk Import - Working Bases
VZ_Caption:
  resKey: VZ_Caption|b6d4950f-1baa-43ee-96ca-e8053c8b5540
  text: Bulk Import/Export - Working Bases
VZ_FormFactor: DSK
VZ_FormType: PAG
VZ_RequiresLogin: true
VZ_MaterialDesign: true
VZ_Dependencies: <dependencies xmlns="http://cargowise.com/glow/2014/11/14/dependencies.xsd" />
VZ_FormData: >-
  <form
    id="52ea24de-8b0d-4f2c-942b-74145d54eb9a" xmlns="http://wisetechglobal.com/glow/2017/05/26/form.xsd">
    <placeholder
      name="Layout"
      value="fill" />
    <control
      code="SDT"
      id="aae931a1-1c9d-449b-a051-57084da07bc2"
      binding="">
      <placeholder
        name="CaptionOverride"
        value="Working Bases"
        resid="90436724-07c0-4403-99c7-80ce9afd9431" />
      <placeholder
        name="EntityType"
        value="IGlbStaffWorkingBasis" />
      <placeholder
        name="FullPage"
        value="True" />
      <placeholder
        name="SearchMode"
        value="Entity" />
      <placeholder
        name="DefaultFilter">
        <xml>
          <ArrayOfFilterGroup xmlns:a="http://schemas.microsoft.com/2003/10/Serialization/Arrays" xmlns:i="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://cargowise.com/glow/2014/07/16/filters.xsd">
            <FilterGroup>
              <Filters>
                <Filter>
                  <FilterType>IsActiveFilter</FilterType>
                  <Operation>Is</Operation>
                  <PropertyPath>Staff.GS_IsActive</PropertyPath>
                  <Values>
                    <a:string>true</a:string>
                  </Values>
                </Filter>
                <Filter>
                  <FilterType>AdvancedGuidLookupFilter</FilterType>
                  <Operation>Is</Operation>
                  <PropertyPath>GSW_GS_Staff</PropertyPath>
                  <Values />
                </Filter>
                <Filter>
                  <FilterType>StringFilter</FilterType>
                  <Operation>Contains</Operation>
                  <PropertyPath>Staff.StaffName</PropertyPath>
                  <Values />
                </Filter>
              </Filters>
              <IsImplicit>false</IsImplicit>
            </FilterGroup>
          </ArrayOfFilterGroup>
        </xml>
      </placeholder>
      <placeholder
        name="FieldConfiguration">
        <xml>
          <fields xmlns="">
            <field
              path="EffectiveDate"
              width="140"
              mode="Default" />
            <field
              path="GSW_WorkingBasis"
              width="200"
              mode="Default" />
            <field
              path="GSW_SystemCreateTimeUtc"
              width="220"
              mode="Optional" />
            <field
              path="GSW_SystemLastEditTimeUtc"
              width="250"
              mode="Optional" />
            <field
              path="GSW_SystemCreateUser"
              width="200"
              mode="FilterOnly" />
            <field
              path="GSW_SystemLastEditUser"
              width="200"
              mode="FilterOnly" />
            <field
              path="Staff.GS_FullName"
              width="250"
              mode="Default" />
            <field
              path="Staff.GS_FriendlyName"
              width="250"
              mode="Default" />
            <field
              path="Staff.GS_Code"
              width="120"
              mode="Optional" />
            <field
              path="Staff.CurrentJobTitle"
              width="250"
              mode="Optional" />
            <field
              path="Staff.CurrentRole.GEH_JobFamily"
              width="250"
              mode="Optional" />
            <field
              path="Staff.GS_IsActive"
              width="100"
              mode="FilterOnly" />
            <field
              path="Staff.GS_RN_NKCountryCode"
              width="150"
              mode="FilterOnly" />
            <field
              path="Staff.GS_EmploymentDate"
              width="130"
              mode="Optional" />
            <field
              path="Staff.GS_EmailAddress"
              width="250"
              mode="Optional" />
            <field
              path="Staff.GS_GB_HomeBranch"
              width="250"
              mode="FilterOnly" />
            <field
              path="Staff.GS_GE_HomeDepartment"
              width="250"
              mode="FilterOnly" />
            <field
              path="GSW_GS_Staff"
              width="100"
              mode="FilterOnly" />
            <field
              path="GSW_AutoEffectiveEndDate"
              width="140"
              mode="Optional" />
            <field
              path="CreatedByStaff.GS_Code"
              width="120"
              mode="Optional" />
            <field
              path="CreatedByStaff.GS_FullName"
              width="250"
              mode="Optional" />
            <field
              path="LastEditedByStaff.GS_Code"
              width="120"
              mode="Optional" />
            <field
              path="LastEditedByStaff.GS_FullName"
              width="250"
              mode="Optional" />
            <field
              path="Staff.HomeBranch.GB_BranchName"
              width="250"
              mode="Optional" />
            <field
              path="Staff.HomeBranch.GB_Code"
              width="120"
              mode="Optional" />
            <field
              path="Staff.HomeBranch.GlbCompany.GC_Code"
              width="120"
              mode="Optional" />
            <field
              path="Staff.HomeBranch.GlbCompany.GC_Name"
              width="250"
              mode="Optional" />
            <field
              path="Staff.HomeDepartment.GE_Desc"
              width="250"
              mode="Optional" />
            <field
              path="Staff.HomeDepartment.GE_Code"
              width="120"
              mode="Optional" />
            <field
              path="Staff.Country.RN_Desc"
              width="250"
              mode="Optional" />
            <field
              path="Staff.CurrentBeneficiaryEntity.GBB_GB_Branch"
              width="250"
              mode="Optional" />
            <field
              path="Staff.CurrentBeneficiaryEntity.Branch.GB_BranchName"
              width="250"
              mode="Optional" />
            <field
              path="Staff.CurrentBeneficiaryEntity.Branch.GB_Code"
              width="120"
              mode="Optional" />
            <field
              path="Staff.CurrentBeneficiaryEntity.Branch.GlbCompany.GC_Code"
              width="120"
              mode="Optional" />
            <field
              path="Staff.CurrentBeneficiaryEntity.Branch.GlbCompany.GC_Name"
              width="250"
              mode="Optional" />
            <field
              path="Staff.CurrentRemuneration.Country.RN_Desc"
              width="250"
              mode="Optional" />
            <field
              path="Staff.CurrentRemuneration.Country.RN_Code"
              width="250"
              mode="Optional" />
            <field
              path="Staff.CurrentRemuneration.Currency.RX_Code"
              width="250"
              mode="Optional" />
            <field
              path="Staff.CurrentRemuneration.Currency.RX_Desc"
              width="250"
              mode="Optional" />
            <field
              path="Staff.CurrentTeam.TeamNameDescription"
              width="250"
              mode="Optional" />
            <field
              path="Staff.StaffName"
              width="250"
              mode="FilterOnly" />
            <field
              path="Staff.CurrentEmployingEntity.Branch.GB_BranchName"
              width="250"
              mode="Optional" />
            <field
              path="Staff.CurrentEmployingEntity.Branch.GB_Code"
              width="250"
              mode="Optional" />
            <field
              path="Staff.CurrentEmployingEntity.Branch.GlbCompany.GC_Code"
              width="250"
              mode="Optional" />
            <field
              path="Staff.CurrentEmployingEntity.Branch.GlbCompany.GC_Name"
              width="250"
              mode="Optional" />
            <field
              path="IGlbStaffWorkingBasis_Filter_CurrentOn"
              width="100"
              mode="FilterOnly" />
            <field
              path="IGlbStaffWorkingBasis_Filter_CurrentOn_IgnoreTimezone"
              width="250"
              mode="FilterOnly" />
            <field
              path="WorkingBasisCode"
              width="250"
              mode="Optional" />
            <field
              path="GSW_EffectiveDate"
              width="140"
              mode="Optional" />
          </fields>
        </xml>
      </placeholder>
    </control>
  </form>
