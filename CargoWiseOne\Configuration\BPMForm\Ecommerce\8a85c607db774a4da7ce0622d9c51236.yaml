#transformationVersion: 70.0
#
VZ_PK: 8a85c607db774a4da7ce0622d9c51236
VZ_ConfigurationKey: 8a85c607-db77-4a4d-a7ce-0622d9c51236
VZ_FormID: ETL - VDV3 - Destination Depot - Consignment Tracking
VZ_Caption:
  resKey: VZ_Caption|8a85c607-db77-4a4d-a7ce-0622d9c51236
  text: Consignment Tracking
VZ_FormFactor: DSK
VZ_FormType: PAG
VZ_RequiresLogin: true
VZ_Dependencies: <dependencies xmlns="http://cargowise.com/glow/2014/11/14/dependencies.xsd" />
VZ_FormData: >-
  <form
    id="ac13f136-3831-490b-8156-e5fa21ea5151" xmlns="http://wisetechglobal.com/glow/2017/05/26/form.xsd">
    <placeholder
      name="BackgroundImagePk"
      value="" />
    <placeholder
      name="BackgroundSize"
      value="Auto" />
    <placeholder
      name="BackgroundImageOverride"
      value="" />
    <placeholder
      name="FormLayout"
      value="Splitview" />
    <placeholder
      name="ExpandPaths"
      value="" />
    <placeholder
      name="ScanInputHandling"
      value="NON" />
    <placeholder
      name="RemovePadding"
      value="false" />
    <placeholder
      name="TurnOnPortableOneToOneDesignMode"
      value="false" />
    <placeholder
      name="ContainsDefaultButton"
      value="False" />
    <control
      code="GRP"
      id="eb391f86-59f7-470a-9b60-42450456e509"
      left="0"
      top="0"
      width="6"
      height="4">
      <placeholder
        name="CanBeHidden"
        value="True" />
      <placeholder
        name="VisibilityCondition"
        value="" />
      <placeholder
        name="Header"
        value="New Section"
        resid="67153c92-39cb-4c7f-bd02-81ea12f2d043" />
      <placeholder
        name="HeaderAlignment"
        value="Left" />
      <placeholder
        name="HeaderDisplayMode"
        value="Hidden" />
      <placeholder
        name="BackgroundImagePk"
        value="" />
      <placeholder
        name="BackgroundImageOverride"
        value="" />
      <control
        code="TIL"
        id="10cba223-90ef-4353-8e4a-fb98e1dc8ac3"
        left="0"
        top="0"
        width="2"
        height="2">
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="Size"
          value="Standard" />
        <placeholder
          name="Color"
          value="Default" />
        <placeholder
          name="DisplayMode"
          value="TextAndIcon" />
        <placeholder
          name="Text"
          value="Shipments"
          resid="df87425c-faad-4079-9b1d-d7be963649a9" />
        <placeholder
          name="Image"
          value="d4c02b187e9d4e37be59b173a7caa29a" />
        <placeholder
          name="ImageOverride"
          value="" />
        <placeholder
          name="Description"
          value="" />
        <placeholder
          name="Behaviour"
          value="Page" />
        <placeholder
          name="PagePK"
          value="6304763e5b644c069fb98fe353716da5" />
        <placeholder
          name="Url"
          value="" />
        <placeholder
          name="FormFlow"
          value="" />
      </control>
      <control
        code="TIL"
        id="60f6ccba-a845-444d-87c3-2f8598172bf4"
        left="2"
        top="0"
        width="2"
        height="2">
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="Size"
          value="Standard" />
        <placeholder
          name="Color"
          value="Primary" />
        <placeholder
          name="DisplayMode"
          value="TextAndIcon" />
        <placeholder
          name="Text"
          value="Consignments"
          resid="27a4ec1a-e71e-4506-8dc4-1c242774a7d7" />
        <placeholder
          name="Image"
          value="2c348de953fe4e8db9967317a1a942c4" />
        <placeholder
          name="ImageOverride"
          value="" />
        <placeholder
          name="Description"
          value="" />
        <placeholder
          name="Behaviour"
          value="Page" />
        <placeholder
          name="PagePK"
          value="8a85c607db774a4da7ce0622d9c51236" />
        <placeholder
          name="Url"
          value="" />
        <placeholder
          name="FormFlow"
          value="" />
      </control>
      <control
        code="TIL"
        id="1dd582cd-dc1e-4095-9f09-339fb4ab6b10"
        left="4"
        top="0"
        width="2"
        height="2">
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="Size"
          value="Standard" />
        <placeholder
          name="Color"
          value="Default" />
        <placeholder
          name="DisplayMode"
          value="TextAndIcon" />
        <placeholder
          name="Text"
          value="Headers"
          resid="4bda0c63-5d04-43a9-9770-fb16e20aafc5" />
        <placeholder
          name="Image"
          value="05e1a4771ad542aeadfef74283eec66b" />
        <placeholder
          name="ImageOverride"
          value="" />
        <placeholder
          name="Description"
          value="" />
        <placeholder
          name="Behaviour"
          value="Page" />
        <placeholder
          name="PagePK"
          value="44cbb83e146740d3871b38de444582a1" />
        <placeholder
          name="Url"
          value="" />
        <placeholder
          name="FormFlow"
          value="" />
      </control>
      <control
        code="TIL"
        id="2cdc483a-42c4-43d5-9ed4-791427c9da28"
        left="0"
        top="2"
        width="2"
        height="2">
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="Size"
          value="Standard" />
        <placeholder
          name="Color"
          value="Default" />
        <placeholder
          name="DisplayMode"
          value="TextAndIcon" />
        <placeholder
          name="Text"
          value="Outer Packages"
          resid="1b1d2fd7-8f9e-4bbf-85aa-ecb1169d2172" />
        <placeholder
          name="Image"
          value="3e36f6d7c44842d8b2faa22f952210e2" />
        <placeholder
          name="ImageOverride"
          value="" />
        <placeholder
          name="Description"
          value="" />
        <placeholder
          name="Behaviour"
          value="Page" />
        <placeholder
          name="PagePK"
          value="ae28dbacc0f140399bcdf3782c10b04b" />
        <placeholder
          name="Url"
          value="" />
        <placeholder
          name="FormFlow"
          value="" />
      </control>
      <control
        code="TIL"
        id="df94f7f2-506f-49b2-a945-040db9879664"
        left="2"
        top="2"
        width="2"
        height="2">
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="Size"
          value="Standard" />
        <placeholder
          name="Color"
          value="Default" />
        <placeholder
          name="DisplayMode"
          value="TextAndIcon" />
        <placeholder
          name="Text"
          value="Reports"
          resid="a5e6c90e-ce27-4623-9899-2518fcc11f08" />
        <placeholder
          name="Image"
          value="fbcf0fdf52074ddf97ec2ce94d6f47a1" />
        <placeholder
          name="ImageOverride"
          value="" />
        <placeholder
          name="Description"
          value="" />
        <placeholder
          name="Behaviour"
          value="Page" />
        <placeholder
          name="PagePK"
          value="9de1a081d3124bb383082d0cc82f24d8" />
        <placeholder
          name="Url"
          value="" />
        <placeholder
          name="FormFlow"
          value="" />
      </control>
    </control>
    <control
      code="SRL"
      id="a6831736-1b93-47b8-aad4-1cd36bd301ff"
      left="7"
      top="0"
      right="0"
      bottom="0">
      <placeholder
        name="CanBeHidden"
        value="True" />
      <placeholder
        name="IsInitialFocus"
        value="False" />
      <placeholder
        name="CaptionOverride"
        value="Consignment Tracking Results"
        resid="0878f6b5-c6df-42c4-880d-0958378b18bb" />
      <placeholder
        name="VisibilityCondition"
        value="" />
      <placeholder
        name="HeaderAlignment"
        value="Left" />
      <placeholder
        name="HeaderDisplayMode"
        value="Visible" />
      <placeholder
        name="FilterType"
        value="Global" />
      <placeholder
        name="EntityType"
        value="IHVLVConsignment" />
      <placeholder
        name="ItemDisplayPath"
        value="" />
      <placeholder
        name="DisabledGridRowActions"
        value="" />
      <placeholder
        name="DefaultFilter">
        <xml>
          <ArrayOfFilterGroup xmlns:a="http://schemas.microsoft.com/2003/10/Serialization/Arrays" xmlns:i="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://cargowise.com/glow/2014/07/16/filters.xsd">
            <FilterGroup>
              <Filters>
                <Filter>
                  <FilterType>StringFilter</FilterType>
                  <Operation>StartsWith</Operation>
                  <PropertyPath>CONSIGNMENTID</PropertyPath>
                  <Values />
                </Filter>
                <Filter>
                  <FilterType>StringFilter</FilterType>
                  <Operation>StartsWith</Operation>
                  <PropertyPath>ITEMID</PropertyPath>
                  <Values />
                </Filter>
                <Filter>
                  <FilterType>StringFilter</FilterType>
                  <Operation>StartsWith</Operation>
                  <PropertyPath>WAYBILLNUMBER</PropertyPath>
                  <Values />
                </Filter>
              </Filters>
              <IsImplicit>false</IsImplicit>
            </FilterGroup>
          </ArrayOfFilterGroup>
        </xml>
      </placeholder>
      <placeholder
        name="DefaultFilterPropertyPath"
        value="" />
      <placeholder
        name="FieldConfiguration">
        <xml>
          <fields xmlns="">
            <field
              path="EMANIFEST"
              width="100"
              mode="Default" />
            <field
              path="WAYBILLNUMBER"
              width="100"
              mode="Default" />
            <field
              path="CONSIGNMENTSHIPPERREFERENCE"
              width="100"
              mode="Default" />
            <field
              path="ITEMCOUNT"
              width="100"
              mode="Default" />
            <field
              path="SHIPPERNAME"
              width="100"
              mode="Default" />
            <field
              path="CONSIGNEE"
              width="100"
              mode="Default" />
            <field
              path="ORIGINPORT"
              width="100"
              mode="Default" />
            <field
              path="DESTINATIONPORT"
              width="100"
              mode="Default" />
            <field
              path="GOODSDESCRIPTION"
              width="100"
              mode="Default" />
            <field
              path="MANIFESTEDWEIGHT"
              width="100"
              mode="Default" />
            <field
              path="MANIFESTEDVOLUME"
              width="100"
              mode="Default" />
            <field
              path="ACTUALWEIGHT"
              width="100"
              mode="Default" />
            <field
              path="ACTUALVOLUME"
              width="100"
              mode="Default" />
            <field
              path="ISACTIVE"
              width="100"
              mode="Default" />
            <field
              path="CREATETIME"
              width="100"
              mode="Optional" />
            <field
              path="CONSIGNEECOUNTRY"
              width="100"
              mode="Optional" />
            <field
              path="CREATEUSER"
              width="100"
              mode="Optional" />
            <field
              path="CURRENCY"
              width="100"
              mode="Optional" />
            <field
              path="LASTMILECARRIERSERVICELEVEL"
              width="100"
              mode="Optional" />
            <field
              path="SHIPPERCOUNTRY"
              width="100"
              mode="Optional" />
            <field
              path="CONSIGNEEADDRESS1"
              width="100"
              mode="Optional" />
            <field
              path="CONSIGNEEADDRESS2"
              width="100"
              mode="Optional" />
            <field
              path="CONSIGNEECITY"
              width="100"
              mode="Optional" />
            <field
              path="CONSIGNEECONTACT"
              width="100"
              mode="Optional" />
            <field
              path="CONSIGNEEEMAIL"
              width="100"
              mode="Optional" />
            <field
              path="CONSIGNEEFAX"
              width="100"
              mode="Optional" />
            <field
              path="CONSIGNEEINSTRUCTIONS"
              width="100"
              mode="Optional" />
            <field
              path="CONSIGNEEMOBILE"
              width="100"
              mode="Optional" />
            <field
              path="CONSIGNEEPHONE"
              width="100"
              mode="Optional" />
            <field
              path="CONSIGNEEPOSTCODE"
              width="100"
              mode="Optional" />
            <field
              path="CONSIGNEESTATE"
              width="100"
              mode="Optional" />
            <field
              path="CONSIGNMENTID"
              width="100"
              mode="Optional" />
            <field
              path="SHIPPERADDRESS1"
              width="100"
              mode="Optional" />
            <field
              path="SHIPPERADDRESS2"
              width="100"
              mode="Optional" />
            <field
              path="SHIPPERCITY"
              width="100"
              mode="Optional" />
            <field
              path="SHIPPERCONTACT"
              width="100"
              mode="Optional" />
            <field
              path="SHIPPEREMAIL"
              width="100"
              mode="Optional" />
            <field
              path="SHIPPERFAX"
              width="100"
              mode="Optional" />
            <field
              path="SHIPPERMOBILE"
              width="100"
              mode="Optional" />
            <field
              path="SHIPPERPHONE"
              width="100"
              mode="Optional" />
            <field
              path="SHIPPERPOSTCODE"
              width="100"
              mode="Optional" />
            <field
              path="SHIPPERSTATE"
              width="100"
              mode="Optional" />
            <field
              path="ETAILCONSIGNMENTSTATUS"
              width="100"
              mode="Optional" />
            <field
              path="DGCLASS"
              width="100"
              mode="Optional" />
            <field
              path="AUTHORITYTOLEAVE"
              width="100"
              mode="Optional" />
            <field
              path="HAZARDOUS"
              width="100"
              mode="Optional" />
            <field
              path="ISPERISHABLE"
              width="100"
              mode="Optional" />
            <field
              path="ISSELFBOOKED"
              width="100"
              mode="Optional" />
            <field
              path="ISSIGNATUREREQUIRED"
              width="100"
              mode="Optional" />
            <field
              path="ISTIMBER"
              width="100"
              mode="Optional" />
            <field
              path="REQUIRESFUMIGATION"
              width="100"
              mode="Optional" />
            <field
              path="DESTINATIONDEPOT"
              width="100"
              mode="Optional" />
            <field
              path="ISPERSONALEFFECTS"
              width="100"
              mode="Optional" />
            <field
              path="LASTMILECARRIER"
              width="100"
              mode="Optional" />
            <field
              path="ORIGINPORTIATA"
              width="100"
              mode="Optional" />
            <field
              path="ORIGINPORTCOUNTRY"
              width="100"
              mode="Optional" />
            <field
              path="DESTINATIONPORTIATA"
              width="100"
              mode="Optional" />
            <field
              path="DESTINATIONPORTCOUNTRY"
              width="100"
              mode="Optional" />
            <field
              path="TRANSPORTMODE"
              width="100"
              mode="Optional" />
            <field
              path="GOODSVALUE"
              width="100"
              mode="Optional" />
            <field
              path="SHIPMENTID"
              width="100"
              mode="Optional" />
            <field
              path="CONSOLID"
              width="100"
              mode="Optional" />
            <field
              path="MASTERBILLNUMBER"
              width="100"
              mode="Optional" />
            <field
              path="RELEASESTATUS"
              width="100"
              mode="Optional" />
            <field
              path="ETDLOCAL"
              width="100"
              mode="Optional" />
            <field
              path="ETALOCAL"
              width="100"
              mode="Optional" />
            <field
              path="DEPARTUREDATELOCAL"
              width="100"
              mode="Optional" />
            <field
              path="ARRIVALDATELOCAL"
              width="100"
              mode="Optional" />
            <field
              path="SERVICELEVEL"
              width="100"
              mode="Optional" />
            <field
              path="TRANSPORTVALUE"
              width="100"
              mode="Optional" />
            <field
              path="INSURANCEVALUE"
              width="100"
              mode="Optional" />
          </fields>
        </xml>
      </placeholder>
      <placeholder
        name="DefaultSortFields"
        value="" />
      <placeholder
        name="DefaultGroupFields"
        value="" />
      <placeholder
        name="EnableGrouping"
        value="False" />
      <placeholder
        name="AutoRefreshInterval"
        value="0" />
      <placeholder
        name="AllowMultiSelect"
        value="True" />
      <placeholder
        name="ShowWorkflowLabel"
        value="True" />
      <placeholder
        name="RowActionsMode"
        value="RowAndMenu" />
      <placeholder
        name="HideGridActions"
        value="False" />
      <placeholder
        name="MinimizeFilters"
        value="False" />
      <placeholder
        name="WorkflowOpenInNewWindow"
        value="False" />
      <placeholder
        name="SearchOnInit"
        value="True" />
      <placeholder
        name="ShowQuickSearch"
        value="False" />
      <placeholder
        name="QuickSearchCaption"
        value="" />
      <placeholder
        name="ValueField"
        value="" />
      <placeholder
        name="Category"
        value="Trackable" />
      <placeholder
        name="ActionMenuItems"
        value="" />
      <placeholder
        name="EditFormFlow"
        value="" />
      <placeholder
        name="ResetFilterOnInit"
        value="False" />
      <placeholder
        name="FilterDisplayMode"
        value="InlineAndPopup" />
    </control>
    <control
      code="GRP"
      id="8400ba45-d648-4763-ae2b-779d340d5948"
      left="0"
      top="4"
      width="6"
      height="5">
      <placeholder
        name="CanBeHidden"
        value="True" />
      <placeholder
        name="VisibilityCondition"
        value="" />
      <placeholder
        name="Header"
        value="Tracking"
        resid="61945089-ddf3-40d3-9126-91a6cfae57b3" />
      <placeholder
        name="HeaderAlignment"
        value="Left" />
      <placeholder
        name="HeaderDisplayMode"
        value="Visible" />
      <placeholder
        name="BackgroundImagePk"
        value="" />
      <placeholder
        name="BackgroundImageOverride"
        value="" />
      <control
        code="TRC"
        id="0655cd91-cb6b-43dc-a0f2-b28b61e119a8"
        left="0"
        top="0"
        width="6"
        height="4">
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="VisibilityCondition"
          value="" />
      </control>
    </control>
    <control
      code="FAV"
      id="76e7ae8f-38e4-438d-b4e3-b5ca68dc7c9d"
      left="0"
      top="9"
      width="6"
      bottom="2">
      <placeholder
        name="CanBeHidden"
        value="True" />
      <placeholder
        name="VisibilityCondition"
        value="" />
      <placeholder
        name="EntityType"
        value="" />
      <placeholder
        name="HeaderDisplayMode"
        value="Visible" />
      <placeholder
        name="ShowFavorites"
        value="True" />
      <placeholder
        name="ShowRecents"
        value="True" />
    </control>
  </form>
