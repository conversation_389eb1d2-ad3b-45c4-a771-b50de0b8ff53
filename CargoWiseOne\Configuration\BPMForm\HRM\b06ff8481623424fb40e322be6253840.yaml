#transformationVersion: 70.0
#
VZ_PK: b06ff8481623424fb40e322be6253840
VZ_ConfigurationKey: b06ff848-1623-424f-b40e-322be6253840
VZ_FormID: HRM - Bulk Import - Physical Work Addresses
VZ_Caption:
  resKey: VZ_Caption|b06ff848-1623-424f-b40e-322be6253840
  text: Bulk Import/Export - Physical Work Addresses
VZ_FormFactor: DSK
VZ_FormType: PAG
VZ_RequiresLogin: true
VZ_MaterialDesign: true
VZ_Dependencies: <dependencies xmlns="http://cargowise.com/glow/2014/11/14/dependencies.xsd" />
VZ_FormData: >-
  <form
    id="52ea24de-8b0d-4f2c-942b-74145d54eb9a" xmlns="http://wisetechglobal.com/glow/2017/05/26/form.xsd">
    <placeholder
      name="Layout"
      value="fill" />
    <control
      code="SDT"
      id="d870caf7-54ce-4857-8f5f-c9a3f3263f81"
      binding="">
      <placeholder
        name="CaptionOverride"
        value="Physical Work Addresses"
        resid="1ff3cff1-5715-4d17-8be4-3394ebf30920" />
      <placeholder
        name="EntityType"
        value="IGlbEmploymentLocation" />
      <placeholder
        name="FullPage"
        value="True" />
      <placeholder
        name="SearchMode"
        value="Entity" />
      <placeholder
        name="DefaultFilter">
        <xml>
          <ArrayOfFilterGroup xmlns:a="http://schemas.microsoft.com/2003/10/Serialization/Arrays" xmlns:i="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://cargowise.com/glow/2014/07/16/filters.xsd">
            <FilterGroup>
              <Filters>
                <Filter>
                  <FilterType>IsActiveFilter</FilterType>
                  <Operation>Is</Operation>
                  <PropertyPath>Staff.GS_IsActive</PropertyPath>
                  <Values>
                    <a:string>true</a:string>
                  </Values>
                </Filter>
                <Filter>
                  <FilterType>SimpleLookupFilter</FilterType>
                  <Operation>Is</Operation>
                  <PropertyPath>GEL_LocationSource</PropertyPath>
                  <Values />
                </Filter>
                <Filter>
                  <FilterType>AdvancedGuidLookupFilter</FilterType>
                  <Operation>Is</Operation>
                  <PropertyPath>GEL_GS_Staff</PropertyPath>
                  <Values />
                </Filter>
                <Filter>
                  <FilterType>StringFilter</FilterType>
                  <Operation>Contains</Operation>
                  <PropertyPath>Staff.StaffName</PropertyPath>
                  <Values />
                </Filter>
              </Filters>
              <IsImplicit>false</IsImplicit>
            </FilterGroup>
          </ArrayOfFilterGroup>
        </xml>
      </placeholder>
      <placeholder
        name="DefaultSortFields">
        <xml>
          <ArrayOfFieldSortDefinition xmlns:i="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://schemas.datacontract.org/2004/07/CargoWise.Glow.Infrastructure.Interfaces">
            <FieldSortDefinition>
              <FieldName>Staff.GS_FullName</FieldName>
              <IsAscending>true</IsAscending>
            </FieldSortDefinition>
          </ArrayOfFieldSortDefinition>
        </xml>
      </placeholder>
      <placeholder
        name="FieldConfiguration">
        <xml>
          <fields xmlns="">
            <field
              path="EffectiveDate"
              width="120"
              mode="Default" />
            <field
              path="GEL_LocationSource"
              width="100"
              mode="Default" />
            <field
              path="GEL_Address1"
              width="250"
              mode="Default" />
            <field
              path="GEL_City"
              width="200"
              mode="Default" />
            <field
              path="GEL_State"
              width="120"
              mode="Default" />
            <field
              path="GEL_PostCode"
              width="100"
              mode="Default" />
            <field
              path="GEL_RN_NKCountryCode"
              width="250"
              mode="Optional" />
            <field
              path="GEL_GS_Staff"
              width="100"
              mode="FilterOnly" />
            <field
              path="GEL_GB_SourceBranch"
              width="250"
              mode="Optional" />
            <field
              path="GEL_Address2"
              width="250"
              mode="Optional" />
            <field
              path="GEL_SystemCreateTimeUtc"
              width="100"
              mode="Optional" />
            <field
              path="GEL_SystemCreateUser"
              width="100"
              mode="FilterOnly" />
            <field
              path="GEL_SystemLastEditTimeUtc"
              width="100"
              mode="Optional" />
            <field
              path="GEL_SystemLastEditUser"
              width="100"
              mode="FilterOnly" />
            <field
              path="GEL_ValidationStatus"
              width="100"
              mode="Optional" />
            <field
              path="Staff.GS_FullName"
              width="250"
              mode="Optional" />
            <field
              path="GEL_EffectiveDate"
              width="120"
              mode="Optional" />
            <field
              path="GEL_AutoEffectiveEndDate"
              width="120"
              mode="Optional" />
            <field
              path="Country.RN_Desc"
              width="250"
              mode="Default" />
            <field
              path="Staff.GS_Code"
              width="120"
              mode="Optional" />
            <field
              path="SourceBranch.GB_Code"
              width="120"
              mode="Optional" />
            <field
              path="SourceBranch.GB_BranchName"
              width="250"
              mode="Optional" />
            <field
              path="CreatedByStaff.GS_Code"
              width="120"
              mode="Optional" />
            <field
              path="CreatedByStaff.GS_FullName"
              width="250"
              mode="Optional" />
            <field
              path="LastEditedByStaff.GS_Code"
              width="120"
              mode="Optional" />
            <field
              path="LastEditedByStaff.GS_FullName"
              width="250"
              mode="Optional" />
            <field
              path="Staff.GS_EmploymentBasis"
              width="250"
              mode="Optional" />
            <field
              path="Staff.StaffName"
              width="250"
              mode="FilterOnly" />
            <field
              path="Staff.CurrentJobTitle"
              width="250"
              mode="Optional" />
            <field
              path="Staff.GS_GB_HomeBranch"
              width="250"
              mode="FilterOnly" />
            <field
              path="Staff.GS_GE_HomeDepartment"
              width="250"
              mode="FilterOnly" />
            <field
              path="Staff.GS_IsActive"
              width="70"
              mode="FilterOnly" />
            <field
              path="Staff.CurrentBeneficiaryEntity.Branch.GB_Code"
              width="250"
              mode="Optional" />
            <field
              path="Staff.CurrentBeneficiaryEntity.Branch.GB_BranchName"
              width="250"
              mode="Optional" />
            <field
              path="Staff.CurrentBeneficiaryEntity.Branch.GlbCompany.GC_Name"
              width="250"
              mode="Optional" />
            <field
              path="Staff.CurrentBeneficiaryEntity.Branch.GlbCompany.GC_Code"
              width="250"
              mode="Optional" />
            <field
              path="Staff.CurrentEmployingEntity.Branch.GB_BranchName"
              width="250"
              mode="Optional" />
            <field
              path="Staff.CurrentEmployingEntity.Branch.GB_Code"
              width="250"
              mode="Optional" />
            <field
              path="Staff.CurrentEmployingEntity.Branch.GlbCompany.GC_Code"
              width="250"
              mode="Optional" />
            <field
              path="Staff.CurrentEmployingEntity.Branch.GlbCompany.GC_Name"
              width="250"
              mode="Optional" />
            <field
              path="Staff.HomeBranch.GB_BranchName"
              width="250"
              mode="Optional" />
            <field
              path="Staff.HomeBranch.GB_Code"
              width="250"
              mode="Optional" />
            <field
              path="Staff.HomeDepartment.GE_Desc"
              width="250"
              mode="Optional" />
            <field
              path="Staff.HomeDepartment.GE_Code"
              width="250"
              mode="Optional" />
            <field
              path="Staff.CurrentRemuneration.Country.RN_Desc"
              width="250"
              mode="Optional" />
            <field
              path="Staff.CurrentRemuneration.Country.RN_Code"
              width="250"
              mode="Optional" />
            <field
              path="Staff.CurrentRemuneration.Currency.RX_Desc"
              width="250"
              mode="Optional" />
            <field
              path="Staff.CurrentRemuneration.Currency.RX_Code"
              width="250"
              mode="Optional" />
            <field
              path="Staff.CurrentRole.GEH_JobFamily"
              width="250"
              mode="Optional" />
            <field
              path="Staff.CurrentTeam.TeamNameDescription"
              width="250"
              mode="Optional" />
            <field
              path="IGlbEmploymentLocation_Filter_CurrentOn"
              width="100"
              mode="FilterOnly" />
            <field
              path="IGlbEmploymentLocation_Filter_CurrentOn_IgnoreTimezone"
              width="250"
              mode="FilterOnly" />
            <field
              path="GEL_OA_SourceOrgAddress"
              width="250"
              mode="Optional" />
            <field
              path="Staff.CurrentWorkingBasis.WorkingBasisCode"
              width="250"
              mode="Optional" />
          </fields>
        </xml>
      </placeholder>
    </control>
  </form>
