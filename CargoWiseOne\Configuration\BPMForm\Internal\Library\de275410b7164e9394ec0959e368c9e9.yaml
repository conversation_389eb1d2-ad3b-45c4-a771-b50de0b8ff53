#transformationVersion: 70.0
#
VZ_PK: de275410b7164e9394ec0959e368c9e9
VZ_ConfigurationKey: de275410-b716-4e93-94ec-0959e368c9e9
VZ_FormID: Action Menu Items on Related Data Table control example
VZ_Caption:
  resKey: VZ_Caption|de275410b7164e9394ec0959e368c9e9
  text: Action Menu Items on Related Data Table controls example
VZ_FormFactor: DSK
VZ_EntityType: IWhsItemReceiveTransportationUnit
VZ_FormType: FRM
VZ_RequiresLogin: true
VZ_MaterialDesign: true
VZ_Dependencies: <dependencies xmlns="http://cargowise.com/glow/2014/11/14/dependencies.xsd" />
VZ_FormData: >-
  <form
    id="0a4418bd-fd17-4fbe-9a9a-c66da2bf30bc" xmlns="http://wisetechglobal.com/glow/2017/05/26/form.xsd">
    <control
      code="PNL"
      id="446e7ab1-d335-4ba0-904d-a11ed7857400"
      binding="">
      <placeholder
        name="Margin"
        value="mb-3" />
      <placeholder
        name="Class"
        value="blue lighten-4 bg-blue-lighten-4" />
      <control
        code="BOX"
        id="f85faafd-5168-4e2c-bbe7-193d057d4915">
        <placeholder
          name="Layout"
          value="flex" />
        <control
          code="IBT"
          id="75b2f696-db69-4a97-8b39-432b4376140f"
          binding="">
          <placeholder
            name="Icon"
            value="s-icon-documentation" />
          <placeholder
            name="Tooltip"
            value="Documentation"
            resid="2624b1e0-88fd-4b0c-802d-2d9c41729505" />
          <placeholder
            name="Target"
            value="_blank" />
        </control>
        <control
          code="IBT"
          id="e760bbda-c70f-41c7-a473-2f91ce0e885c"
          binding="">
          <placeholder
            name="Icon"
            value="s-icon-pt-devtools" />
          <placeholder
            name="Margin"
            value="ml-2" />
          <placeholder
            name="Target"
            value="_blank" />
          <placeholder
            name="Tooltip"
            value="YAML"
            resid="14d1fc58-6f18-4cf3-87ef-3f9f913671f0" />
          <placeholder
            name="Hyperlink"
            value="https://github.com/WiseTechGlobal/Glow/blob/master/CargoWiseOne/Configuration/BPMForm/de275410b7164e9394ec0959e368c9e9.yaml" />
        </control>
        <control
          code="IBT"
          id="85a73f8c-d9dc-4a7f-a59b-ef4a560f6785"
          binding="">
          <placeholder
            name="Icon"
            value="s-icon-settings" />
          <placeholder
            name="Margin"
            value="ml-2" />
          <placeholder
            name="Tooltip"
            value="Platform Builder"
            resid="38dc34e7-e1df-471c-95b6-afc18276fb63" />
          <placeholder
            name="Target"
            value="_blank" />
          <placeholder
            name="Hyperlink"
            value="platformbuilder:?target=BPMForm&amp;identifier=de275410b7164e9394ec0959e368c9e9" />
        </control>
        <control
          code="BOX"
          id="ada17f7a-d31a-4bc7-9dbf-96f394575a08">
          <placeholder
            name="Margin"
            value="ml-4" />
          <placeholder
            name="Layout"
            value="flex" />
          <placeholder
            name="FlexDirection"
            value="flex-column" />
          <placeholder
            name="FlexJustify"
            value="justify-center" />
          <control
            code="LBL"
            id="cc6d3fcb-8de8-446a-8363-a463d9845b7e">
            <placeholder
              name="Caption"
              value="The example you see here is Related List Form-flow restriction vs Control Form-flow Restriction."
              resid="42b03e2e-3ad7-4f7c-ac7d-8223cf03ee80" />
          </control>
        </control>
      </control>
    </control>
    <control
      code="PNL"
      id="fae2cd4a-ea09-4afb-ab90-fad422d69e5a"
      binding="">
      <control
        code="RDT"
        id="b6906891-3e16-4c5d-b99b-50eab71762cf"
        binding="WhsItemPackageStates">
        <placeholder
          name="CaptionOverride"
          value="RDT with no &quot;Action Menu Item&quot; configured"
          resid="f29acacd-e97e-4417-9800-7bbbde6ad220" />
        <placeholder
          name="ItemsPerPage"
          value="5" />
        <placeholder
          name="DisabledGridRowActions"
          value="Logs" />
      </control>
      <control
        code="DVR"
        id="8e59e84b-7052-4da6-a9c7-235c46ed0bcc">
        <placeholder
          name="Margin"
          value="mt-2" />
      </control>
      <control
        code="RDT"
        id="730b3c93-f398-43d6-8028-a73c0e2e7c8b"
        binding="WhsItemPackageStates">
        <placeholder
          name="Margin"
          value="mt-2" />
        <placeholder
          name="CaptionOverride"
          value="RDT with &quot;Action Menu Item&quot; configured"
          resid="8e73bcc8-050c-46ac-94d8-f38074503f60" />
        <placeholder
          name="ItemsPerPage"
          value="5" />
        <placeholder
          name="ActionMenuItems">
          <xml>
            <formFlows xmlns="">
              <formFlow>c1e15c785f8e4f8285461b2200447a8f</formFlow>
            </formFlows>
          </xml>
        </placeholder>
        <placeholder
          name="DisabledGridRowActions"
          value="Logs" />
      </control>
    </control>
  </form>
