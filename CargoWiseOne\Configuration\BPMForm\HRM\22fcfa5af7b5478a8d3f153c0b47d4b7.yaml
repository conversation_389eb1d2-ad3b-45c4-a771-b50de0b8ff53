#transformationVersion: 70.0
#
VZ_PK: 22fcfa5af7b5478a8d3f153c0b47d4b7
VZ_ConfigurationKey: 22fcfa5a-f7b5-478a-8d3f-153c0b47d4b7
VZ_FormID: HRM - Change Request Approval
VZ_Caption:
  resKey: VZ_Caption|22fcfa5a-f7b5-478a-8d3f-153c0b47d4b7
  text: Change Request Approval
VZ_FormFactor: DSK
VZ_EntityType: IGlbStaffChangeRequest
VZ_FormType: FRM
VZ_RequiresLogin: true
VZ_MaterialDesign: true
VZ_Dependencies: <dependencies xmlns="http://cargowise.com/glow/2014/11/14/dependencies.xsd" />
VZ_FormData: >-
  <form
    id="e85b32b3-3bcf-4c0b-9f50-2bededdd6d62" xmlns="http://wisetechglobal.com/glow/2017/05/26/form.xsd">
    <placeholder
      name="Layout"
      value="grid" />
    <control
      code="APR"
      id="c2f40448-c771-4928-a251-31da5b0e15f6"
      binding="">
      <placeholder
        name="ViewModelDataItem"
        value="ApprovalTask" />
    </control>
    <control
      code="DSF"
      id="7c5acac2-fb39-48e8-a3f6-fdc228b9680a"
      binding="Template.GSG_TemplateName">
      <placeholder
        name="Typography"
        value="title-large" />
      <placeholder
        name="CaptionType"
        value="none" />
    </control>
    <control
      code="BOX"
      id="b3236596-ea83-4bba-b556-f2a8a9199819"
      binding="">
      <placeholder
        name="Columns"
        value="col-12 col-sm-3 col-md-3 col-lg-3 col-xl-3" />
      <control
        code="PNL"
        id="e34e898b-e2a3-4e23-83af-97dd0f1afcd2"
        binding="">
        <placeholder
          name="Caption"
          value="Change details"
          resid="ed533534-d82b-4af0-8b63-e6585a799b82" />
        <control
          code="TXT"
          id="47446740-0769-4f5b-ac51-6eff2b0f2788"
          binding="Staff.GS_FullName">
          <placeholder
            name="CaptionOverride"
            value="Name of staff member"
            resid="e45bf989-0afc-4588-9990-e1695366372e" />
          <placeholder
            name="IsReadOnly"
            value="True" />
        </control>
        <control
          code="TXT"
          id="dfb7e233-83a8-42f7-9eb9-fab83e9ebf92"
          binding="Staff.CurrentJobTitle">
          <placeholder
            name="CaptionOverride"
            value="Job Title of staff member"
            resid="77444985-dc69-476b-8454-af2c41cc9cde" />
          <placeholder
            name="IsReadOnly"
            value="True" />
        </control>
        <control
          code="TXT"
          id="880bd594-c38a-4573-ba33-9c40970ab141"
          binding="CreatedByStaff.GS_FullName">
          <placeholder
            name="CaptionOverride"
            value="Change requested by"
            resid="1c8ddaa4-8b57-4a66-a165-1dde0493530a" />
          <placeholder
            name="Margin"
            value="mt-5" />
          <placeholder
            name="IsReadOnly"
            value="True" />
        </control>
        <control
          code="DTE"
          id="5e8c2eb3-7df3-454c-b7cd-c3c14b6d2b78"
          binding="GCR_SystemCreateTimeUtc">
          <placeholder
            name="CaptionOverride"
            value="Date requested"
            resid="4f3e79a3-0487-401e-a15c-bd57d9681b5d" />
          <placeholder
            name="IsReadOnly"
            value="True" />
        </control>
      </control>
      <control
        code="PNL"
        id="3a46e291-81f8-49a3-89b0-b25853c01ca8"
        binding="">
        <placeholder
          name="Caption"
          value="Messages"
          resid="65a81307-512b-4c67-82c0-3dc3d22e7f5d" />
        <placeholder
          name="Margin"
          value="mt-5" />
        <control
          code="CNV"
          id="45e2d439-ca2c-4c1d-b304-715b4d8f7110"
          binding="Conversations" />
      </control>
    </control>
    <control
      code="BOX"
      id="349be9d6-4e8e-490e-8706-126644402fa2">
      <placeholder
        name="Columns"
        value="col-12 col-sm-9 col-md-9 col-lg-9 col-xl-9" />
      <placeholder
        name="Layout"
        value="grid" />
      <control
        code="CLO"
        id="ac6d23d1-9e12-4744-bc0b-ad80b0f0ba99">
        <placeholder
          name="Sentiment"
          value="info" />
        <placeholder
          name="VisibilityCondition"
          value="Template.IsBenefitOnDemand &amp;&amp; HrlBenefitOnDemands.First().LeaveInstructions != null" />
        <control
          code="RTF"
          id="e0e237d6-d7b7-4dd5-8059-6cdf4e952a8f"
          binding="HrlBenefitOnDemands/LeaveInstructions">
          <placeholder
            name="IsReadOnly"
            value="True" />
          <placeholder
            name="Toolbar"
            value="false" />
          <placeholder
            name="CaptionType"
            value="none" />
        </control>
      </control>
      <control
        code="PNL"
        id="dc9c94bc-8eff-49dc-b153-168b315bcdc0"
        binding="">
        <placeholder
          name="Columns"
          value="col-12 col-sm-8 col-md-8 col-lg-8 col-xl-8" />
        <placeholder
          name="Layout"
          value="grid" />
        <control
          code="PNL"
          id="b0130bc9-4174-48bc-86e0-bd4d3fa89ae1"
          binding="">
          <placeholder
            name="Caption"
            value="Role before change"
            resid="ff8cae1a-374a-43e8-b22f-2d1885f9d3db" />
          <placeholder
            name="Columns"
            value="col-6 col-sm-6 col-md-6 col-lg-6 col-xl-6" />
          <placeholder
            name="VisibilityCondition"
            value="Template.HasJobRole" />
          <control
            code="DAE"
            id="a281eb73-e484-4c44-9990-d33a6ec157ef"
            binding="Staff.CurrentRole.EffectiveDate">
            <placeholder
              name="IsReadOnly"
              value="True" />
          </control>
          <control
            code="TXT"
            id="276a41fd-ba32-4f63-9b95-e1e8c110b75e"
            binding="Staff.CurrentRole.GEH_JobTitle">
            <placeholder
              name="IsReadOnly"
              value="True" />
          </control>
          <control
            code="TXT"
            id="bc2e1c14-1595-423d-861f-db37c7736d3e"
            binding="Staff.CurrentRole.GEH_JobFamily">
            <placeholder
              name="CaptionOverride"
              value="Job Family"
              resid="cd9bd47b-ffad-4d2a-8896-c809c7acd283" />
            <placeholder
              name="IsReadOnly"
              value="True" />
          </control>
          <control
            code="OPT"
            id="00745458-c2a8-41b3-b6ee-ee878bea79e5"
            binding="JobRoleOnEffectiveDate.GEH_IsPromotion">
            <placeholder
              name="Margin"
              value="mt-0" />
            <placeholder
              name="IsReadOnly"
              value="True" />
          </control>
        </control>
        <control
          code="PNL"
          id="5a78f6e0-5a3b-44b4-b92e-0ea269a41925"
          binding="">
          <placeholder
            name="Caption"
            value="New role"
            resid="029cb1cd-3610-4633-8c39-4ef5b047cbfa" />
          <placeholder
            name="Columns"
            value="col-6 col-sm-6 col-md-6 col-lg-6 col-xl-6" />
          <placeholder
            name="VisibilityCondition"
            value="Template.HasJobRole" />
          <control
            code="DAE"
            id="ac99e887-707a-4fe0-8b90-5736a36824c3"
            binding="GlbEmploymentHistories/EffectiveDate" />
          <control
            code="TXT"
            id="6a5e8d04-12f7-48ec-9b2c-ce025df397fb"
            binding="GlbEmploymentHistories/GEH_JobTitle" />
          <control
            code="TXT"
            id="00ebbe32-d6ec-41db-9833-6ca05902d78a"
            binding="GlbEmploymentHistories/GEH_JobFamily">
            <placeholder
              name="CaptionOverride"
              value="Job Family"
              resid="031101f1-3324-49e7-8aea-7c11a1d99a45" />
          </control>
          <control
            code="OPT"
            id="2459d068-7c7d-4241-bd53-e8e191f939bf"
            binding="GlbEmploymentHistories/GEH_IsPromotion">
            <placeholder
              name="CaptionOverride"
              value="Promotion"
              resid="6f16ecf0-0cc1-4b31-9459-ca95bddaaa85" />
            <placeholder
              name="Margin"
              value="mt-0" />
          </control>
        </control>
        <control
          code="PNL"
          id="d8b2f8ee-f77a-47d1-8f01-7564d7667003"
          binding="">
          <placeholder
            name="Caption"
            value="Team before change"
            resid="9e6fea12-b0aa-4d2e-bd62-3cfb7e96920d" />
          <placeholder
            name="Columns"
            value="col-6 col-sm-6 col-md-6 col-lg-6 col-xl-6" />
          <placeholder
            name="VisibilityCondition"
            value="Template.HasTeam" />
          <control
            code="DAE"
            id="78864c16-070a-4f48-ba85-e9a4fddd0556"
            binding="Staff.CurrentTeam.EffectiveDate">
            <placeholder
              name="IsReadOnly"
              value="True" />
          </control>
          <control
            code="SRC"
            id="febe12eb-1962-4586-9fda-f75ab26d7efe"
            binding="Staff.CurrentTeam.GET_GST_NKTeamCode">
            <placeholder
              name="IsReadOnly"
              value="True" />
          </control>
          <control
            code="TXT"
            id="115e897c-77bd-42d8-9c49-fd8285b0c1e9"
            binding="Staff.CurrentTeam.ParentTeam">
            <placeholder
              name="IsReadOnly"
              value="True" />
          </control>
        </control>
        <control
          code="PNL"
          id="13465fef-1360-4c83-b872-b14471a9bba5"
          binding="">
          <placeholder
            name="Caption"
            value="New team"
            resid="6152d9b9-9265-4ee6-ac0a-9937413b4f74" />
          <placeholder
            name="Columns"
            value="col-6 col-sm-6 col-md-6 col-lg-6 col-xl-6" />
          <placeholder
            name="VisibilityCondition"
            value="Template.HasTeam" />
          <control
            code="DAE"
            id="ec6d45bb-cc09-4fcc-91e3-23ed7ab8de86"
            binding="GlbEmploymentTeams/EffectiveDate" />
          <control
            code="SRC"
            id="79d6ea49-2e9b-42c6-ac35-51a49c36b428"
            binding="GlbEmploymentTeams/GET_GST_NKTeamCode" />
          <control
            code="TXT"
            id="41b3a917-010e-40c0-8911-540bef31c574"
            binding="GlbEmploymentTeams/ParentTeam" />
        </control>
        <control
          code="PNL"
          id="9fef6c29-bcdf-44ed-9824-207bb46b4ded"
          binding="">
          <placeholder
            name="Caption"
            value="Manager before change"
            resid="de915840-2983-4b2f-b779-188c2c0630c8" />
          <placeholder
            name="Columns"
            value="col-6 col-sm-6 col-md-6 col-lg-6 col-xl-6" />
          <placeholder
            name="VisibilityCondition"
            value="Template.HasManager" />
          <control
            code="DAE"
            id="9c279151-eafe-4303-822b-5c3540ba7b24"
            binding="StaffCurrentManagerOfType.GSM_EffectiveDate">
            <placeholder
              name="IsReadOnly"
              value="True" />
          </control>
          <control
            code="CMB"
            id="d98b9742-6cc1-4e1a-a4d7-94244d4cdc17"
            binding="StaffCurrentManagerOfType.GSM_ManagerType">
            <placeholder
              name="IsReadOnly"
              value="True" />
          </control>
          <control
            code="SRC"
            id="ff5410ec-bc77-48b5-b94f-55573345b136"
            binding="StaffCurrentManagerOfType.GSM_GS_Manager">
            <placeholder
              name="IsReadOnly"
              value="True" />
          </control>
        </control>
        <control
          code="PNL"
          id="260abe34-0002-4c26-b217-9934698b3d25"
          binding="">
          <placeholder
            name="Caption"
            value="New manager"
            resid="b68a9d42-29e7-4f2a-92d3-274fb2e1605a" />
          <placeholder
            name="Columns"
            value="col-6 col-sm-6 col-md-6 col-lg-6 col-xl-6" />
          <placeholder
            name="VisibilityCondition"
            value="Template.HasManager" />
          <control
            code="DAE"
            id="1fcaef33-2b42-4f23-bbb7-a1d726a44169"
            binding="GlbStaffManagers/GSM_EffectiveDate" />
          <control
            code="CMB"
            id="8902b76f-42de-4618-8bc9-b9e4850a1038"
            binding="GlbStaffManagers/GSM_ManagerType" />
          <control
            code="SRC"
            id="a6f7e2e8-2f3d-491c-96e5-baadf881278f"
            binding="GlbStaffManagers/GSM_GS_Manager" />
        </control>
        <control
          code="PNL"
          id="a51bc638-e6dd-4862-a323-069c06e66dc3"
          binding="">
          <placeholder
            name="Caption"
            value="Work pattern before change"
            resid="3556ae84-a4da-4ea3-86eb-c8f724b471f6" />
          <placeholder
            name="Columns"
            value="col-12 col-sm-6 col-md-6 col-lg-6 col-xl-6" />
          <placeholder
            name="VisibilityCondition"
            value="Template.HasWorkPattern" />
          <control
            code="DAE"
            id="ee465c1c-be47-4ac5-a478-d5b44536cad9"
            binding="Staff.CurrentWorkPattern.GWP_EffectiveDate">
            <placeholder
              name="IsReadOnly"
              value="True" />
          </control>
          <control
            code="DUR"
            id="f5949f9d-d70e-469a-84f0-918749915c24"
            binding="Staff.CurrentWorkPattern.GWP_StandardDuration">
            <placeholder
              name="IsReadOnly"
              value="True" />
          </control>
          <control
            code="SCH"
            id="39428e08-57ac-42d3-88c6-0e2205c64d4b"
            binding="">
            <placeholder
              name="ViewModel"
              value="WeekScheduleViewModel" />
            <placeholder
              name="ViewModelDataItem"
              value="Staff.CurrentWorkPattern" />
            <placeholder
              name="IsReadOnly"
              value="True" />
          </control>
        </control>
        <control
          code="PNL"
          id="04a2544c-f519-46cc-8b51-606ec9a15615"
          binding="">
          <placeholder
            name="Caption"
            value="New work pattern"
            resid="172e6fcf-2e28-4699-9863-202acea61f40" />
          <placeholder
            name="Columns"
            value="col-6 col-sm-6 col-md-6 col-lg-6 col-xl-6" />
          <placeholder
            name="VisibilityCondition"
            value="Template.HasWorkPattern" />
          <control
            code="DAE"
            id="cd92d7f1-3d5e-4b81-aaf8-a70a523b05f3"
            binding="GlbWorkPatterns/GWP_EffectiveDate">
            <placeholder
              name="IsReadOnly"
              value="True" />
          </control>
          <control
            code="DUR"
            id="9fc52d90-5000-4665-8d26-5e188c875ba9"
            binding="GlbWorkPatterns/GWP_StandardDuration">
            <placeholder
              name="IsReadOnly"
              value="True" />
          </control>
          <control
            code="SCH"
            id="970d2be1-af54-45ff-aeab-14d36ca57b80"
            binding="">
            <placeholder
              name="ViewModel"
              value="WeekScheduleViewModel" />
            <placeholder
              name="ViewModelDataItem"
              value="WorkPattern" />
            <placeholder
              name="IsReadOnly"
              value="True" />
          </control>
        </control>
        <control
          code="PNL"
          id="a1d321f9-026e-4308-afc5-d27fdfa0d701">
          <placeholder
            name="Caption"
            value="Leave entitlement"
            resid="9eef0335-9bed-4ca4-844a-506caef3b0c2" />
          <placeholder
            name="Columns"
            value="col-12 col-sm-12 col-md-12 col-lg-12 col-xl-12" />
          <placeholder
            name="VisibilityCondition"
            value="Template.IsBenefitOnDemand &amp;&amp; !%.CurrentUserCheckpoints.Contains(&quot;CanViewAllLeaveRequests&quot;)" />
          <control
            code="CMB"
            id="051fad54-b748-49c6-9232-0cab00bf0346"
            binding="HrlBenefitOnDemands/LBD_LeaveType">
            <placeholder
              name="IsReadOnly"
              value="True" />
          </control>
          <control
            code="TXT"
            id="444b96f9-55cd-44da-be9c-a4bd96de66ff"
            binding="HrlBenefitOnDemands/LBD_Comment">
            <placeholder
              name="CaptionOverride"
              value="Reason for leave"
              resid="17a8d3ea-d5a6-4b60-86f1-1107bdd4a591" />
            <placeholder
              name="Margin"
              value="mt-2" />
            <placeholder
              name="IsReadOnly"
              value="True" />
          </control>
          <control
            code="BOX"
            id="b62389c5-fe7f-48d5-854d-aa6ee290795d">
            <placeholder
              name="Margin"
              value="mt-1" />
            <placeholder
              name="Layout"
              value="grid" />
            <placeholder
              name="Columns"
              value="col-sm-6" />
            <control
              code="NUM"
              id="5ae188b2-77a4-4c81-9595-324c48b6c634"
              binding="HrlBenefitOnDemands/LBD_Duration">
              <placeholder
                name="Columns"
                value="col-sm-6" />
              <placeholder
                name="IsReadOnly"
                value="True" />
            </control>
            <control
              code="CMB"
              id="9f1c81cd-71d9-4b0e-93ff-4634961069a8"
              binding="HrlBenefitOnDemands/LBD_DurationUnit">
              <placeholder
                name="Margin"
                value="mt-5" />
              <placeholder
                name="Columns"
                value="col-sm-6" />
              <placeholder
                name="CaptionType"
                value="none" />
              <placeholder
                name="IsReadOnly"
                value="True" />
            </control>
          </control>
          <control
            code="OPT"
            id="ae9e72e1-521d-4b7a-8ee2-38301fab3f77"
            binding="HrlBenefitOnDemands/LBD_IncludesStatutoryLeave">
            <placeholder
              name="CaptionOverride"
              value="Includes statutory leave"
              resid="e6842f38-8da4-4f55-a21a-eeee5690a5e8" />
            <placeholder
              name="Padding"
              value="pt-2" />
            <placeholder
              name="IsReadOnly"
              value="True" />
          </control>
        </control>
        <control
          code="PNL"
          id="f5b513eb-b235-4a6b-ab6f-df28e7679152">
          <placeholder
            name="Caption"
            value="Leave entitlement"
            resid="9eef0335-9bed-4ca4-844a-506caef3b0c2" />
          <placeholder
            name="Columns"
            value="col-12 col-sm-12 col-md-12 col-lg-12 col-xl-12" />
          <placeholder
            name="VisibilityCondition"
            value="Template.IsBenefitOnDemand &amp;&amp; %.CurrentUserCheckpoints.Contains(&quot;CanViewAllLeaveRequests&quot;)" />
          <control
            code="CMB"
            id="ef023094-c864-4279-af00-ad9d10004595"
            binding="HrlBenefitOnDemands/LBD_LeaveType">
            <placeholder
              name="Required"
              value="True" />
          </control>
          <control
            code="TXT"
            id="8e90ad4e-83e8-45c6-99e6-4930e8c746a8"
            binding="HrlBenefitOnDemands/LBD_Comment">
            <placeholder
              name="CaptionOverride"
              value="Reason for leave"
              resid="17a8d3ea-d5a6-4b60-86f1-1107bdd4a591" />
            <placeholder
              name="Margin"
              value="mt-2" />
          </control>
          <control
            code="BOX"
            id="7a163f20-ea99-46d9-b2bf-b40a46538524">
            <placeholder
              name="Margin"
              value="mt-1" />
            <placeholder
              name="Layout"
              value="grid" />
            <placeholder
              name="Columns"
              value="col-sm-6" />
            <control
              code="NUM"
              id="3f1dea46-ecce-4b23-b803-31b9638c520f"
              binding="HrlBenefitOnDemands/LBD_Duration">
              <placeholder
                name="Columns"
                value="col-sm-6" />
              <placeholder
                name="Required"
                value="True" />
            </control>
            <control
              code="CMB"
              id="1b9c177c-38a1-478c-abdc-c2d729e20bae"
              binding="HrlBenefitOnDemands/LBD_DurationUnit">
              <placeholder
                name="Margin"
                value="mt-5" />
              <placeholder
                name="Columns"
                value="col-sm-6" />
              <placeholder
                name="CaptionType"
                value="none" />
              <placeholder
                name="Required"
                value="True" />
            </control>
          </control>
          <control
            code="OPT"
            id="2630db25-fb5a-4f47-aa1e-2d72c70ed6eb"
            binding="HrlBenefitOnDemands/LBD_IncludesStatutoryLeave">
            <placeholder
              name="CaptionOverride"
              value="Includes statutory leave"
              resid="e6842f38-8da4-4f55-a21a-eeee5690a5e8" />
            <placeholder
              name="Padding"
              value="pt-2" />
          </control>
        </control>
        <control
          code="PNL"
          id="3c9a978c-fbc6-442d-9999-bf3ab5807751"
          binding="">
          <placeholder
            name="Caption"
            value="Work pattern history"
            resid="f055d759-4f9d-4922-80f2-4b17125c84ec" />
          <placeholder
            name="Columns"
            value="col-12 col-sm-12 col-md-12 col-lg-12 col-xl-12" />
          <placeholder
            name="VisibilityCondition"
            value="Template.IsBenefitOnDemand" />
          <control
            code="DAE"
            id="84bf2ede-1efd-43ac-ba00-bffc5cb2ba08"
            binding="Staff.CurrentWorkPattern.GWP_EffectiveDate">
            <placeholder
              name="IsReadOnly"
              value="True" />
          </control>
          <control
            code="DUR"
            id="4967da90-9874-4ccf-97b1-f7ecf964c18e"
            binding="Staff.CurrentWorkPattern.GWP_StandardDuration">
            <placeholder
              name="IsReadOnly"
              value="True" />
          </control>
          <control
            code="SCH"
            id="1f4bd86c-5a1e-42cc-a3ee-06a489b29d09"
            binding="">
            <placeholder
              name="ViewModel"
              value="WeekScheduleViewModel" />
            <placeholder
              name="ViewModelDataItem"
              value="Staff.CurrentWorkPattern" />
            <placeholder
              name="IsReadOnly"
              value="True" />
          </control>
        </control>
      </control>
      <control
        code="BOX"
        id="6bbd71b0-5fc8-4f77-89f2-f91b3576de5c">
        <placeholder
          name="Columns"
          value="col-12 col-sm-4 col-md-4 col-lg-4 col-xl-4" />
        <placeholder
          name="VisibilityCondition"
          value="Template.IsBenefitOnDemand" />
        <control
          code="PNL"
          id="903ab598-1236-4014-b601-ec80296c7a5b">
          <placeholder
            name="Caption"
            value="Eligibility check"
            resid="8d98ca49-2f87-42c0-8cf7-b017e767fd96" />
          <control
            code="DAE"
            id="449f1895-198f-4692-bd47-13d07532956d"
            binding="Staff.GS_EmploymentDate">
            <placeholder
              name="IsReadOnly"
              value="True" />
            <placeholder
              name="CaptionOverride"
              value="Employment Date"
              resid="666a5a8c-89df-4a7a-b4f3-ae2577f7fb6e" />
          </control>
          <control
            code="CMB"
            id="8c5fc981-7f7b-451d-90e8-39b5cc34e10d"
            binding="Staff.GS_Gender">
            <placeholder
              name="IsReadOnly"
              value="True" />
          </control>
          <control
            code="CMB"
            id="7036e74b-6f56-4dcc-8f9e-4e29ab21a870"
            binding="Staff.GS_ResidencyStatus">
            <placeholder
              name="IsReadOnly"
              value="True" />
            <placeholder
              name="CaptionOverride"
              value="Visa Status"
              resid="ce520814-9183-4c86-97d1-774d83438d2c" />
          </control>
          <control
            code="CMB"
            id="00781dab-ee30-4ccb-892b-2df620ff1836"
            binding="Staff.GS_EmploymentBasis">
            <placeholder
              name="IsReadOnly"
              value="True" />
          </control>
        </control>
        <control
          code="PNL"
          id="ec421a2b-5c5e-473b-8c7d-f830bc47626f">
          <placeholder
            name="Margin"
            value="mt-5" />
          <control
            code="RDT"
            id="8ac82e8a-4b30-4062-b2f1-d8d03a7c82cc"
            binding="Staff.GlbStaffHolidays">
            <placeholder
              name="FieldConfiguration">
              <xml>
                <fields xmlns="">
                  <field
                    path="GA_WorkHolidayType"
                    width="180"
                    mode="Default" />
                  <field
                    path="GA_StartTime"
                    width="180"
                    mode="Default" />
                  <field
                    path="GA_EndTime"
                    width="300"
                    mode="Optional" />
                  <field
                    path="ApprovalTime"
                    width="130"
                    mode="Optional" />
                </fields>
              </xml>
            </placeholder>
            <placeholder
              name="HideItemActions"
              value="True" />
            <placeholder
              name="IsReadOnly"
              value="True" />
            <placeholder
              name="CaptionOverride"
              value="Recent leave applications"
              resid="ad490536-f724-4f05-bf38-a8ba09b8eb1e" />
            <placeholder
              name="HideExport"
              value="True" />
            <placeholder
              name="HideImport"
              value="True" />
            <placeholder
              name="ShowCustomize"
              value="True" />
          </control>
        </control>
      </control>
    </control>
  </form>
