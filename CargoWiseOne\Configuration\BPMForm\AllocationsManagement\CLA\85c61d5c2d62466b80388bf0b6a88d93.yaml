#transformationVersion: 70.0
#
VZ_PK: 85c61d5c2d62466b80388bf0b6a88d93
VZ_ConfigurationKey: 85c61d5c-2d62-466b-8038-8bf0b6a88d93
VZ_FormID: CLA - Client Contracts Landing Page
VZ_Caption:
  resKey: VZ_Caption|85c61d5c2d62466b80388bf0b6a88d93
  text: Client Contract & Allocations
VZ_FormFactor: DSK
VZ_FormType: PAG
VZ_RequiresLogin: true
VZ_MaterialDesign: true
VZ_Dependencies: <dependencies xmlns="http://cargowise.com/glow/2014/11/14/dependencies.xsd" />
VZ_FormData: >-
  <form
    id="929ff03a-9a04-4bfe-8ed6-0951748286a3" xmlns="http://wisetechglobal.com/glow/2017/05/26/form.xsd">
    <placeholder
      name="Layout"
      value="fill" />
    <control
      code="SDT"
      id="6cf3cf25-9a5e-41c0-935f-910f2a5d15ba">
      <placeholder
        name="SearchMode"
        value="Entity" />
      <placeholder
        name="EntityType"
        value="IClientContract" />
      <placeholder
        name="FullPage"
        value="True" />
      <placeholder
        name="CaptionOverride"
        value="Client Contracts"
        resid="38ea4481-0fb8-4e88-b5d5-77f836a471b3" />
      <placeholder
        name="CaptionType"
        value="short" />
      <placeholder
        name="ShowGrouping"
        value="True" />
      <placeholder
        name="ShowQuickSearch"
        value="True" />
      <placeholder
        name="ShowSelect"
        value="True" />
      <placeholder
        name="ShowAddActions"
        value="True" />
      <placeholder
        name="DefaultFilter">
        <xml>
          <ArrayOfFilterGroup xmlns:a="http://schemas.microsoft.com/2003/10/Serialization/Arrays" xmlns:i="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://cargowise.com/glow/2014/07/16/filters.xsd">
            <FilterGroup>
              <Filters>
                <Filter>
                  <FilterType>StringFilter</FilterType>
                  <Operation>Is</Operation>
                  <PropertyPath>RCT_ContractType</PropertyPath>
                  <Values>
                    <a:string>CLI</a:string>
                  </Values>
                </Filter>
              </Filters>
              <IsImplicit>true</IsImplicit>
            </FilterGroup>
          </ArrayOfFilterGroup>
        </xml>
      </placeholder>
      <placeholder
        name="NewCaption"
        value="New Contract" />
      <placeholder
        name="ShowToolbar"
        value="large" />
      <placeholder
        name="ActionMenuItems">
        <xml>
          <formFlows xmlns="">
            <formFlow
              newSession="True">494f9d144c49458b9df4589bb5ca3194</formFlow>
            <formFlow
              newSession="True">e2ed371be0d74a4cadaa912f32b7e986</formFlow>
            <formFlow>b66b9bfa45b74a359a41538a93afdeec</formFlow>
          </formFlows>
        </xml>
      </placeholder>
      <placeholder
        name="EditFormFlowConfiguration">
        <xml>
          <formFlows xmlns="">
            <formFlow
              newSession="True">494f9d144c49458b9df4589bb5ca3194</formFlow>
          </formFlows>
        </xml>
      </placeholder>
      <placeholder
        name="NewFormFlowConfiguration">
        <xml>
          <formFlows xmlns="">
            <formFlow
              newSession="True">fdf86d73a1854cfba2c175b206ed3893</formFlow>
          </formFlows>
        </xml>
      </placeholder>
    </control>
  </form>
