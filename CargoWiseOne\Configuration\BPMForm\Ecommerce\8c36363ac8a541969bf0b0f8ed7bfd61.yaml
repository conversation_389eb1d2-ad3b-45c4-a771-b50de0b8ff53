#transformationVersion: 70.0
#
VZ_PK: 8c36363ac8a541969bf0b0f8ed7bfd61
VZ_ConfigurationKey: 8c36363a-c8a5-4196-9bf0-b0f8ed7bfd61
VZ_FormID: ETL - VDV3 - Destination Depot - Shipment Selection
VZ_Caption:
  resKey: VZ_Caption|8c36363a-c8a5-4196-9bf0-b0f8ed7bfd61
  text: Shipment Selection
VZ_FormFactor: DSK
VZ_FormType: PAG
VZ_RequiresLogin: true
VZ_Dependencies: <dependencies xmlns="http://cargowise.com/glow/2014/11/14/dependencies.xsd" />
VZ_FormData: >-
  <form
    id="96e18ecd-6770-4577-963f-02d015353ec0" xmlns="http://wisetechglobal.com/glow/2017/05/26/form.xsd">
    <placeholder
      name="BackgroundImagePk"
      value="" />
    <placeholder
      name="BackgroundSize"
      value="Auto" />
    <placeholder
      name="BackgroundImageOverride"
      value="" />
    <placeholder
      name="FormLayout"
      value="Splitview" />
    <placeholder
      name="ExpandPaths"
      value="" />
    <placeholder
      name="ScanInputHandling"
      value="NON" />
    <placeholder
      name="RemovePadding"
      value="false" />
    <placeholder
      name="TurnOnPortableOneToOneDesignMode"
      value="false" />
    <placeholder
      name="ContainsDefaultButton"
      value="False" />
    <control
      code="SRL"
      id="d061b0cd-f730-4521-a106-2cfc9fdc046e"
      left="0"
      top="0"
      right="0"
      bottom="0">
      <placeholder
        name="CanBeHidden"
        value="True" />
      <placeholder
        name="IsInitialFocus"
        value="False" />
      <placeholder
        name="CaptionOverride"
        value="Shipments"
        resid="c8b4a121-cf09-46c1-a10f-681aa34204a7" />
      <placeholder
        name="VisibilityCondition"
        value="" />
      <placeholder
        name="HeaderAlignment"
        value="Left" />
      <placeholder
        name="HeaderDisplayMode"
        value="Visible" />
      <placeholder
        name="FilterType"
        value="Global" />
      <placeholder
        name="EntityType"
        value="IJobShipment" />
      <placeholder
        name="ItemDisplayPath"
        value="" />
      <placeholder
        name="DisabledGridRowActions"
        value="" />
      <placeholder
        name="DefaultFilter">
        <xml>
          <ArrayOfFilterGroup xmlns:a="http://schemas.microsoft.com/2003/10/Serialization/Arrays" xmlns:i="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://cargowise.com/glow/2014/07/16/filters.xsd">
            <FilterGroup>
              <Filters>
                <Filter>
                  <FilterType>StringFilter</FilterType>
                  <Operation>Is</Operation>
                  <PropertyPath>SHIPMENTTYPE</PropertyPath>
                  <Values>
                    <a:string>HVL</a:string>
                  </Values>
                  <Visibility>Hidden</Visibility>
                </Filter>
              </Filters>
              <IsImplicit>true</IsImplicit>
            </FilterGroup>
            <FilterGroup>
              <Filters>
                <Filter>
                  <FilterType>StringFilter</FilterType>
                  <Operation>StartsWith</Operation>
                  <PropertyPath>SHIPMENTID</PropertyPath>
                  <Values />
                </Filter>
                <Filter>
                  <FilterType>StringFilter</FilterType>
                  <Operation>StartsWith</Operation>
                  <PropertyPath>MASTERBILLNUMBER</PropertyPath>
                  <Values />
                </Filter>
                <Filter>
                  <FilterType>StringFilter</FilterType>
                  <Operation>StartsWith</Operation>
                  <PropertyPath>HOUSEBILLNUMBER</PropertyPath>
                  <Values />
                </Filter>
              </Filters>
              <IsImplicit>false</IsImplicit>
            </FilterGroup>
          </ArrayOfFilterGroup>
        </xml>
      </placeholder>
      <placeholder
        name="DefaultFilterPropertyPath"
        value="" />
      <placeholder
        name="FieldConfiguration">
        <xml>
          <fields xmlns="">
            <field
              path="SHIPMENTID"
              width="150"
              mode="Default" />
            <field
              path="MASTERBILLNUMBER"
              width="150"
              mode="Default" />
            <field
              path="HOUSEBILLNUMBER"
              width="150"
              mode="Default" />
            <field
              path="ORIGINPORT"
              width="150"
              mode="Default" />
            <field
              path="DESTINATIONPORT"
              width="150"
              mode="Default" />
            <field
              path="ETDLOCAL"
              width="150"
              mode="Default" />
            <field
              path="ETALOCAL"
              width="125"
              mode="Default" />
            <field
              path="FIRSTLEGATD"
              width="125"
              mode="Default" />
            <field
              path="LASTLEGATA"
              width="125"
              mode="Default" />
            <field
              path="CTO"
              width="200"
              mode="Default" />
            <field
              path="CTOAVAILABLEDATE"
              width="150"
              mode="Default" />
            <field
              path="CFS"
              width="200"
              mode="Default" />
            <field
              path="SHIPMENTCFSAVAILABILITY"
              width="175"
              mode="Default" />
            <field
              path="COUNTALL"
              width="100"
              mode="Default" />
            <field
              path="COUNTSCANNEDALL"
              width="125"
              mode="Default" />
            <field
              path="COUNTCLEARED"
              width="250"
              mode="Default" />
            <field
              path="COUNTHELD"
              width="100"
              mode="Default" />
            <field
              path="COUNTSURPLUSITEMS"
              width="250"
              mode="Default" />
            <field
              path="COUNTSHORTITEMS"
              width="250"
              mode="Default" />
            <field
              path="OUTTURNDATE"
              width="250"
              mode="Default" />
            <field
              path="ISCANCELLED"
              width="120"
              mode="Optional" />
            <field
              path="INCOTERMS"
              width="250"
              mode="Optional" />
            <field
              path="ONBOARDDATE"
              width="250"
              mode="Optional" />
            <field
              path="TRANSPORTMODE"
              width="250"
              mode="Optional" />
            <field
              path="WEIGHT"
              width="250"
              mode="Optional" />
            <field
              path="VOLUME"
              width="250"
              mode="Optional" />
            <field
              path="INTERIMRECEIPTDATE"
              width="250"
              mode="Optional" />
            <field
              path="ISSUEDATE"
              width="250"
              mode="Optional" />
            <field
              path="SHIPMENTSTATUS"
              width="250"
              mode="Optional" />
            <field
              path="TOTALPACKAGES"
              width="250"
              mode="Optional" />
            <field
              path="SHIPPERREFERENCE"
              width="250"
              mode="Optional" />
            <field
              path="CONTAINERMODE"
              width="250"
              mode="Optional" />
            <field
              path="SERVICELEVEL"
              width="250"
              mode="Optional" />
            <field
              path="GOODSDESCRIPTION"
              width="250"
              mode="Optional" />
            <field
              path="ADDITIONALTERMS"
              width="250"
              mode="Optional" />
            <field
              path="RELEASETYPE"
              width="250"
              mode="Optional" />
            <field
              path="SHIPPEDONBOARD"
              width="250"
              mode="Optional" />
            <field
              path="CHARGESAPPLY"
              width="250"
              mode="Optional" />
            <field
              path="ESTIMATEDPICKUP"
              width="250"
              mode="Optional" />
            <field
              path="ESTIMATEDDELIVERYTIME"
              width="250"
              mode="Optional" />
            <field
              path="GOODSPICKEDUP"
              width="250"
              mode="Optional" />
            <field
              path="GOODSDELIVERED"
              width="250"
              mode="Optional" />
            <field
              path="CONSIGNORNAME"
              width="250"
              mode="Optional" />
            <field
              path="CONSIGNEENAME"
              width="250"
              mode="Optional" />
            <field
              path="PICKUPADDRESS"
              width="250"
              mode="Optional" />
            <field
              path="DELIVERYADDRESS"
              width="250"
              mode="Optional" />
            <field
              path="VOYAGEFLIGHT"
              width="250"
              mode="Optional" />
            <field
              path="VESSELCODE"
              width="250"
              mode="Optional" />
            <field
              path="COUNTNONENOTREPORTED"
              width="250"
              mode="Optional" />
            <field
              path="COUNTSCANNEDCLEARED"
              width="250"
              mode="Optional" />
            <field
              path="COUNTSCANNEDHELD"
              width="250"
              mode="Optional" />
            <field
              path="COUNTSCANNEDNONENOTREPORTED"
              width="250"
              mode="Optional" />
            <field
              path="CONSOLREFERENCE"
              width="250"
              mode="Optional" />
            <field
              path="CARRIER"
              width="250"
              mode="Optional" />
            <field
              path="OUTERPACKS"
              width="250"
              mode="Optional" />
            <field
              path="CONSOLCFSAVAILABILITY"
              width="250"
              mode="Optional" />
            <field
              path="SHIPMENTTYPE"
              width="250"
              mode="Optional" />
            <field
              path="STORAGECOMMENCES"
              width="250"
              mode="Optional" />
            <field
              path="ROUTINGSTATUS"
              width="250"
              mode="Optional" />
            <field
              path="ARRIVALDATELOCAL"
              width="250"
              mode="Optional" />
            <field
              path="DEPARTUREDATELOCAL"
              width="250"
              mode="Optional" />
            <field
              path="ORIGINPORTIATA"
              width="250"
              mode="Optional" />
            <field
              path="ORIGINPORTCOUNTRY"
              width="250"
              mode="Optional" />
            <field
              path="DESTINATIONPORTIATA"
              width="250"
              mode="Optional" />
            <field
              path="DESTINATIONPORTCOUNTRY"
              width="250"
              mode="Optional" />
            <field
              path="ENTRYNUMBER"
              width="250"
              mode="Optional" />
            <field
              path="INNERPACKAGES"
              width="250"
              mode="Optional" />
            <field
              path="CTOAVAILABILITY"
              width="250"
              mode="Optional" />
            <field
              path="FLIGHTATA"
              width="250"
              mode="Optional" />
            <field
              path="FLIGHTETA"
              width="250"
              mode="Optional" />
            <field
              path="LASTCOMPLETEDMILESTONE"
              width="250"
              mode="Optional" />
            <field
              path="BOOKINGDATE"
              width="250"
              mode="Optional" />
            <field
              path="CREATETIME"
              width="100"
              mode="Optional" />
            <field
              path="LASTEDITTIME"
              width="100"
              mode="Optional" />
            <field
              path="CONSOLID"
              width="100"
              mode="Optional" />
            <field
              path="COUNTSHORTHELD"
              width="100"
              mode="Optional" />
            <field
              path="SCANSTARTTIME"
              width="100"
              mode="Optional" />
            <field
              path="SCANCOMPLETETIME"
              width="100"
              mode="Optional" />
          </fields>
        </xml>
      </placeholder>
      <placeholder
        name="DefaultSortFields">
        <xml>
          <ArrayOfFieldSortDefinition xmlns:i="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://schemas.datacontract.org/2004/07/CargoWise.Glow.Infrastructure.Interfaces">
            <FieldSortDefinition>
              <FieldName>ARRIVALDATELOCAL</FieldName>
              <IsAscending>true</IsAscending>
            </FieldSortDefinition>
            <FieldSortDefinition>
              <FieldName>COUNTALL</FieldName>
              <IsAscending>true</IsAscending>
            </FieldSortDefinition>
            <FieldSortDefinition>
              <FieldName>COUNTCLEARED</FieldName>
              <IsAscending>true</IsAscending>
            </FieldSortDefinition>
            <FieldSortDefinition>
              <FieldName>COUNTHELD</FieldName>
              <IsAscending>true</IsAscending>
            </FieldSortDefinition>
            <FieldSortDefinition>
              <FieldName>COUNTNONENOTREPORTED</FieldName>
              <IsAscending>true</IsAscending>
            </FieldSortDefinition>
            <FieldSortDefinition>
              <FieldName>COUNTSCANNEDALL</FieldName>
              <IsAscending>true</IsAscending>
            </FieldSortDefinition>
            <FieldSortDefinition>
              <FieldName>COUNTSCANNEDCLEARED</FieldName>
              <IsAscending>true</IsAscending>
            </FieldSortDefinition>
            <FieldSortDefinition>
              <FieldName>COUNTSCANNEDHELD</FieldName>
              <IsAscending>true</IsAscending>
            </FieldSortDefinition>
            <FieldSortDefinition>
              <FieldName>COUNTSCANNEDNONENOTREPORTED</FieldName>
              <IsAscending>true</IsAscending>
            </FieldSortDefinition>
            <FieldSortDefinition>
              <FieldName>CTOAVAILABLEDATE</FieldName>
              <IsAscending>true</IsAscending>
            </FieldSortDefinition>
            <FieldSortDefinition>
              <FieldName>DEPARTUREDATELOCAL</FieldName>
              <IsAscending>true</IsAscending>
            </FieldSortDefinition>
            <FieldSortDefinition>
              <FieldName>ESTIMATEDDELIVERYTIME</FieldName>
              <IsAscending>true</IsAscending>
            </FieldSortDefinition>
            <FieldSortDefinition>
              <FieldName>ETALOCAL</FieldName>
              <IsAscending>true</IsAscending>
            </FieldSortDefinition>
            <FieldSortDefinition>
              <FieldName>ETDLOCAL</FieldName>
              <IsAscending>true</IsAscending>
            </FieldSortDefinition>
            <FieldSortDefinition>
              <FieldName>FIRSTLEGATD</FieldName>
              <IsAscending>true</IsAscending>
            </FieldSortDefinition>
            <FieldSortDefinition>
              <FieldName>LASTLEGATA</FieldName>
              <IsAscending>true</IsAscending>
            </FieldSortDefinition>
            <FieldSortDefinition>
              <FieldName>STORAGECOMMENCES</FieldName>
              <IsAscending>true</IsAscending>
            </FieldSortDefinition>
            <FieldSortDefinition>
              <FieldName>CREATETIME</FieldName>
              <IsAscending>true</IsAscending>
            </FieldSortDefinition>
          </ArrayOfFieldSortDefinition>
        </xml>
      </placeholder>
      <placeholder
        name="DefaultGroupFields"
        value="" />
      <placeholder
        name="EnableGrouping"
        value="True" />
      <placeholder
        name="AutoRefreshInterval"
        value="0" />
      <placeholder
        name="AllowMultiSelect"
        value="True" />
      <placeholder
        name="ShowWorkflowLabel"
        value="True" />
      <placeholder
        name="RowActionsMode"
        value="RowAndMenu" />
      <placeholder
        name="HideGridActions"
        value="False" />
      <placeholder
        name="MinimizeFilters"
        value="False" />
      <placeholder
        name="WorkflowOpenInNewWindow"
        value="False" />
      <placeholder
        name="SearchOnInit"
        value="True" />
      <placeholder
        name="ShowQuickSearch"
        value="False" />
      <placeholder
        name="QuickSearchCaption"
        value="" />
      <placeholder
        name="ValueField"
        value="" />
      <placeholder
        name="Category"
        value="" />
      <placeholder
        name="ActionMenuItems"
        value="" />
      <placeholder
        name="EditFormFlow"
        value="" />
      <placeholder
        name="ResetFilterOnInit"
        value="False" />
      <placeholder
        name="FilterDisplayMode"
        value="InlineAndPopup" />
    </control>
  </form>
