#transformationVersion: 70.0
#
VZ_PK: d0db7f1e97ca482583862f3e24301648
VZ_ConfigurationKey: d0db7f1e-97ca-4825-8386-2f3e24301648
VZ_FormID: OPP - Edit Opportunity
VZ_Caption:
  resKey: VZ_Caption|d0db7f1e-97ca-4825-8386-2f3e24301648
  text: Edit opportunity
VZ_FormFactor: DSK
VZ_EntityType: ICrmOpportunity
VZ_FormType: FRM
VZ_RequiresLogin: true
VZ_MaterialDesign: true
VZ_ExtenderFunc: CargoWise.Glow.Business.CustomerRelationshipManagement.Opportunity.OpportunityExtension.ExtendEditForm
VZ_Dependencies: <dependencies xmlns="http://cargowise.com/glow/2014/11/14/dependencies.xsd" />
VZ_StatusBindingPath: COP_Status
VZ_EntityStatusDisplayMode: Editable
VZ_FormData: >-
  <form
    id="f266027d-63d6-4f7c-9180-73307f3223be" xmlns="http://wisetechglobal.com/glow/2017/05/26/form.xsd">
    <placeholder
      name="Align"
      value="left" />
    <placeholder
      name="MaxWidth"
      value="1920" />
    <placeholder
      name="Padding"
      value="pa-0" />
    <control
      code="TBS"
      id="58de67be-b396-44aa-9380-ef145d03f3a6"
      binding="">
      <placeholder
        name="Application"
        value="True" />
      <control
        code="TAB"
        id="38b415e4-6bb6-4300-a742-0d1f651aa0b2"
        binding="">
        <placeholder
          name="Caption"
          value="Overview"
          resid="188d2f40-f347-413b-8e47-48a5a9496762" />
      </control>
      <control
        code="TAI"
        id="2cc637a9-2f89-43d4-b1d0-98ba98f6e1c2">
        <placeholder
          name="FitToHeight"
          value="True" />
        <placeholder
          name="Padding"
          value="pa-2" />
        <control
          code="BOX"
          id="988352b9-a73d-4313-be30-977a2794a80a">
          <placeholder
            name="Style"
            value="overflow-y: auto" />
          <placeholder
            name="FlexGrow"
            value="flex-grow-1" />
          <placeholder
            name="Class"
            value="d-md-flex flex-md-row" />
          <control
            code="BOX"
            id="caacf0a8-8316-42c3-8fe1-7e06acbfda3a">
            <placeholder
              name="Overflow"
              value="overflow-hidden" />
            <placeholder
              name="MinWidth"
              value="300px" />
            <placeholder
              name="Layout"
              value="flex" />
            <placeholder
              name="Padding"
              value="px-1 pb-2" />
            <control
              code="PNL"
              id="0049a440-aa92-48e9-ab0b-f5f328f4c0ee"
              binding="">
              <placeholder
                name="Caption"
                value="Summary"
                resid="b4e58609-096d-4c45-8eac-5385bab4f782" />
              <placeholder
                name="Style"
                value="height: 100%; overflow-y: auto" />
              <control
                code="BOX"
                id="89690cb5-694e-4f30-926a-077665bc73be"
                binding="">
                <placeholder
                  name="Style"
                  value="border-top: 1px solid var(--s-neutral-border-weak-default);" />
                <control
                  code="CMP"
                  id="4aa6b0f6-b323-4d7c-822d-59fa6e9c9e7f"
                  binding="">
                  <placeholder
                    name="Component"
                    value="cargoWiseOne.productCrm.components.OpportunityStageProgression" />
                  <placeholder
                    name="Padding"
                    value="py-3" />
                </control>
              </control>
              <control
                code="BOX"
                id="2b27e299-2ceb-4d64-8a6a-28bff8015dda"
                binding="">
                <placeholder
                  name="Style"
                  value="border-top: 1px solid var(--s-neutral-border-weak-default);" />
                <placeholder
                  name="Layout"
                  value="flex" />
                <placeholder
                  name="FlexJustify"
                  value="justify-space-between" />
                <placeholder
                  name="FlexAlign"
                  value="align-center" />
                <control
                  code="LBL"
                  id="ecbd5e7f-5fd4-47ef-a236-1b558c1ba570"
                  binding="">
                  <placeholder
                    name="Caption"
                    value="Win probability"
                    resid="ac7c7a9d-0432-4888-a1e6-c568a19bc05d" />
                  <placeholder
                    name="Padding"
                    value="py-3" />
                </control>
                <control
                  code="BOX"
                  id="6078caa4-fe4d-4f64-852f-550f70c15845"
                  binding="">
                  <placeholder
                    name="Width"
                    value="80px" />
                  <control
                    code="NUM"
                    id="2234c312-79bd-4a9e-b4dc-987605cec294"
                    binding="COP_WinProbability">
                    <placeholder
                      name="CaptionType"
                      value="none" />
                    <placeholder
                      name="Padding"
                      value="py-3" />
                    <placeholder
                      name="Suffix"
                      value="%" />
                  </control>
                </control>
              </control>
              <control
                code="BOX"
                id="27ea489e-3ff7-4046-a8f6-e85e7fa09c74"
                binding="">
                <placeholder
                  name="Layout"
                  value="flex" />
                <placeholder
                  name="FlexJustify"
                  value="justify-space-between" />
                <placeholder
                  name="Style"
                  value="border-top: 1px solid var(--s-neutral-border-weak-default);" />
                <placeholder
                  name="FlexAlign"
                  value="align-center" />
                <control
                  code="LBL"
                  id="b296ebf2-2e23-4bf2-bc0a-8ef146495b35"
                  binding="">
                  <placeholder
                    name="Caption"
                    value="Next follow up date"
                    resid="065d6d7e-4e3d-4f2f-abcc-106bca51b494" />
                  <placeholder
                    name="Padding"
                    value="py-3" />
                </control>
                <control
                  code="BOX"
                  id="a4413de1-ac49-4022-b313-d403d86efae1"
                  binding="">
                  <placeholder
                    name="Width"
                    value="180px" />
                  <control
                    code="DTE"
                    id="23ca3966-ccbe-4d6a-92ad-9297ce971a43"
                    binding="COP_NextFollowUp">
                    <placeholder
                      name="CaptionType"
                      value="none" />
                    <placeholder
                      name="Padding"
                      value="py-3" />
                  </control>
                </control>
              </control>
              <control
                code="BOX"
                id="0d6c0c68-a015-4ceb-bcdd-be24d5b8f817"
                binding="">
                <placeholder
                  name="Style"
                  value="border-top: 1px solid var(--s-neutral-border-weak-default);" />
                <placeholder
                  name="Layout"
                  value="flex" />
                <placeholder
                  name="FlexJustify"
                  value="justify-space-between" />
                <placeholder
                  name="FlexAlign"
                  value="align-center" />
                <control
                  code="LBL"
                  id="2480be51-37a4-4778-92b7-6a7ca12a97df"
                  binding="">
                  <placeholder
                    name="Caption"
                    value="Estimated close date"
                    resid="426b378e-1762-4ca1-a9c0-24e9ed6e2eb2" />
                </control>
                <control
                  code="BOX"
                  id="07afa6da-bb65-4baa-b7f9-8c8374134797"
                  binding="">
                  <placeholder
                    name="Width"
                    value="180px" />
                  <control
                    code="DAE"
                    id="810550f8-ca64-4c6b-841d-122a457bb4ff"
                    binding="COP_EstimatedCloseDate">
                    <placeholder
                      name="Padding"
                      value="py-3" />
                    <placeholder
                      name="CaptionType"
                      value="none" />
                  </control>
                </control>
              </control>
              <control
                code="BOX"
                id="1407ea21-fa1d-449d-8ff3-c2f34184b976"
                binding="">
                <placeholder
                  name="Layout"
                  value="flex" />
                <placeholder
                  name="FlexJustify"
                  value="justify-space-between" />
                <placeholder
                  name="FlexAlign"
                  value="align-center" />
                <placeholder
                  name="Style"
                  value="border-top: 1px solid var(--s-neutral-border-weak-default);" />
                <control
                  code="LBL"
                  id="23ef0f53-be3a-428a-84f1-543b3063eb2b"
                  binding="">
                  <placeholder
                    name="Caption"
                    value="Close date"
                    resid="59a49d99-5d22-42ca-83e3-53f22aeb9c55" />
                </control>
                <control
                  code="BOX"
                  id="a86af66a-1496-4267-ba3d-b5720c2c7e13"
                  binding="">
                  <placeholder
                    name="Width"
                    value="180px" />
                  <control
                    code="DAE"
                    id="1d829c12-3d1c-4269-a668-193c70aa06a3"
                    binding="COP_CloseDate">
                    <placeholder
                      name="CaptionType"
                      value="none" />
                    <placeholder
                      name="Padding"
                      value="py-3" />
                  </control>
                </control>
              </control>
              <control
                code="BOX"
                id="4b70ff0d-6df8-4fa9-9bcf-4ae6ee499f80"
                binding="">
                <placeholder
                  name="Style"
                  value="border-top: 1px solid var(--s-neutral-border-weak-default);" />
                <control
                  code="BOX"
                  id="321bf5b4-fef8-4171-9fff-f1bacef73723">
                  <placeholder
                    name="Layout"
                    value="flex" />
                  <placeholder
                    name="FlexJustify"
                    value="justify-space-between" />
                  <placeholder
                    name="FlexAlign"
                    value="align-center" />
                  <control
                    code="LBL"
                    id="95ebee98-c432-43cc-b9e0-3e4e208ef581"
                    binding="">
                    <placeholder
                      name="Caption"
                      value="Customer notes"
                      resid="a8fbcaba-275c-41bb-ad38-d94037e61224" />
                    <placeholder
                      name="Padding"
                      value="py-3" />
                    <placeholder
                      name="Typography"
                      value="title-sm-default" />
                  </control>
                </control>
                <control
                  code="RTF"
                  id="05a0833e-7edb-4dae-8e07-8f6f01c22b5f"
                  binding="COP_OpportunityNotes">
                  <placeholder
                    name="CaptionType"
                    value="none" />
                </control>
              </control>
            </control>
          </control>
          <control
            code="BOX"
            id="66f1b5bd-ef33-472e-ab30-c8828d6c2a5e">
            <placeholder
              name="FlexGrow"
              value="flex-grow-1" />
            <placeholder
              name="Overflow"
              value="overflow-hidden" />
            <placeholder
              name="FlexDirection"
              value="flex-column" />
            <placeholder
              name="Layout"
              value="flex" />
            <placeholder
              name="Style"
              value="gap:8px" />
            <placeholder
              name="Padding"
              value="px-1 pb-2" />
            <control
              code="PNL"
              id="6541cb82-6ab9-410f-a09d-f9ddb8191851"
              binding="">
              <placeholder
                name="Layout"
                value="flex" />
              <placeholder
                name="FlexGrow"
                value="flex-grow-1" />
              <placeholder
                name="FlexDirection"
                value="flex-column" />
              <placeholder
                name="Height"
                value="100%" />
              <placeholder
                name="FitToHeight"
                value="False" />
              <placeholder
                name="Style"
                value="overflow-y: auto" />
              <placeholder
                name="VisibilityCondition"
                value="CanViewOpportunityEstimates" />
              <control
                code="LBL"
                id="9ab073f4-cb83-4b90-9ce6-fc7b7d5958d6">
                <placeholder
                  name="Caption"
                  value="Estimate and scope"
                  resid="6fd86777-cd0e-4c12-8ddf-e11b433d0277" />
                <placeholder
                  name="Typography"
                  value="title-sm-default" />
                <placeholder
                  name="Padding"
                  value="pb-2" />
              </control>
              <control
                code="RDT"
                id="81ce8893-9323-4ddf-ad43-7bb858b437dd"
                binding="CrmOpportunityScopes">
                <placeholder
                  name="FieldConfiguration">
                  <xml>
                    <fields xmlns="">
                      <field
                        path="COS_ScopeID"
                        width="120"
                        caption="ID"
                        resid="9a99775c-4a0e-4d74-b7c7-99d9ccd5d4f1"
                        mode="Mandatory" />
                      <field
                        path="COS_Enabled"
                        width="80"
                        caption="Status"
                        resid="629c29af-02ca-45f2-81ad-eab63f616666"
                        mode="Mandatory" />
                      <field
                        path="ScopeSummary"
                        width="300"
                        caption="Scope"
                        resid="e44582ab-2696-4b39-b04c-9130d464cf01"
                        mode="Mandatory" />
                      <field
                        path="ScopeShippingDetails"
                        width="300"
                        caption="Details"
                        resid="0b6821e6-ce0a-42da-b6d7-0b602e744fab"
                        mode="Mandatory" />
                      <field
                        path="COS_EstimatedProfit"
                        width="120"
                        caption="Est. GP"
                        resid="52a6737e-010d-4437-9aa8-2a866272906d"
                        mode="Default" />
                      <field
                        path="COS_RX_NKScopeCurrency"
                        width="200"
                        caption="Currency"
                        resid="a09fea7a-dae0-49a9-99fd-786a7c9a979b"
                        mode="Default" />
                      <field
                        path="COS_ScopeFrequencyUnit"
                        width="120"
                        caption="Frequency"
                        resid="0cf0b391-96af-4582-8b05-5584c8ebfb62"
                        mode="Optional" />
                      <field
                        path="COS_EstimatedRevenue"
                        width="120"
                        caption="Est. Revenue"
                        resid="9be43667-9a6f-4ba8-89a3-d5a1cfef08d4"
                        mode="Optional" />
                      <field
                        path="EstimatedMargin"
                        width="120"
                        caption="Est. Gross Margin"
                        resid="0e9bc056-9966-4590-a062-a0e2ac3b203c"
                        mode="Optional" />
                      <field
                        path="COS_EstimatedVolumeMeasure"
                        width="120"
                        caption="Volume"
                        resid="96e62519-9ea8-4e49-8334-d559cc88a7f8"
                        mode="Optional" />
                      <field
                        path="COS_EstimatedWeightMeasure"
                        width="120"
                        caption="Weight"
                        resid="760cd754-c2df-434f-b962-9655eff4cd26"
                        mode="Optional" />
                      <field
                        path="COS_RC_ContainerType"
                        width="120"
                        caption="Container Type"
                        resid="6a0d7845-5b18-4088-a1e8-79964390d8ce"
                        mode="Optional" />
                      <field
                        path="COS_RH_NKCommodityCode"
                        width="120"
                        mode="Optional" />
                      <field
                        path="COS_IncoTerm"
                        width="120"
                        mode="Optional" />
                      <field
                        path="COS_RS_NKServiceLevel"
                        width="200"
                        caption="Service Level"
                        resid="f1d45072-c116-4ac3-8bdd-36e1519b3d9d"
                        mode="Optional" />
                      <field
                        path="EffectiveStartDate"
                        width="200"
                        caption="Effective From"
                        resid="b9365b8f-8529-4f67-8e45-c94dda628bf4"
                        mode="Optional" />
                      <field
                        path="EffectiveEndDate"
                        width="200"
                        caption="Effective To"
                        resid="74bfefe7-52ba-4701-8291-f6063f62c459"
                        mode="Optional" />
                    </fields>
                  </xml>
                </placeholder>
                <placeholder
                  name="HideCaption"
                  value="True" />
                <placeholder
                  name="HideItemActions"
                  value="False" />
                <placeholder
                  name="ShowAddActions"
                  value="True" />
                <placeholder
                  name="FitToHeight"
                  value="False" />
                <placeholder
                  name="DefaultSortFields">
                  <xml>
                    <ArrayOfFieldSortDefinition xmlns:i="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://schemas.datacontract.org/2004/07/CargoWise.Glow.Infrastructure.Interfaces">
                      <FieldSortDefinition>
                        <FieldName>COS_ScopeID</FieldName>
                        <IsAscending>true</IsAscending>
                      </FieldSortDefinition>
                    </ArrayOfFieldSortDefinition>
                  </xml>
                </placeholder>
                <placeholder
                  name="ActionMenuItems">
                  <xml>
                    <formFlows xmlns="">
                      <formFlow
                        newSession="True"
                        inDialog="True">20d147a9ce4e4007a0b7293f45260715</formFlow>
                      <formFlow
                        newSession="True"
                        inDialog="True">270d743f60b14933b319345448dc7438</formFlow>
                      <formFlow>41596478df1f4e0685fdaffc69a61daf</formFlow>
                      <formFlow>735df8dae80840e2848c6951758a6f59</formFlow>
                      <formFlow>9ee3e6db218c49b2a21802a953181154</formFlow>
                    </formFlows>
                  </xml>
                </placeholder>
                <placeholder
                  name="VisibilityCondition"
                  value="CrmOpportunityScopes.Any()" />
                <placeholder
                  name="DisabledGridRowActions"
                  value="Documents" />
                <placeholder
                  name="HideExport"
                  value="True" />
                <placeholder
                  name="HideImport"
                  value="True" />
                <placeholder
                  name="ShowCustomize"
                  value="True" />
                <placeholder
                  name="ShowSelect"
                  value="True" />
              </control>
              <control
                code="BOX"
                id="81e7223b-4acc-4aeb-bf86-250cf56c9d03">
                <placeholder
                  name="Style"
                  value="background-color: rgb(240, 240, 240); border-style: solid; border-radius: 5px; border-color: grey;" />
                <placeholder
                  name="Layout"
                  value="flex" />
                <placeholder
                  name="FlexJustify"
                  value="justify-center" />
                <placeholder
                  name="FlexAlign"
                  value="align-center" />
                <placeholder
                  name="FlexDirection"
                  value="flex-column" />
                <placeholder
                  name="FlexGrow"
                  value="flex-grow-1" />
                <placeholder
                  name="VisibilityCondition"
                  value="!CrmOpportunityScopes.Any()" />
                <control
                  code="LBL"
                  id="e4620bef-32a2-4fae-99ad-63a7e920661c">
                  <placeholder
                    name="Caption"
                    value="You have not created any estimates yet"
                    resid="1d982c4a-7059-4ab1-a310-c6b1c48997ee" />
                </control>
                <control
                  code="BTN"
                  id="0096c818-3c22-41e0-9f59-c7cb7dfde04a">
                  <placeholder
                    name="Caption"
                    value="Add scope"
                    resid="3fec031e-ae7c-4dd3-8942-06ac63fd0fe2" />
                  <placeholder
                    name="LeadingIcon"
                    value="s-icon-add" />
                  <placeholder
                    name="Variant"
                    value="primary" />
                  <placeholder
                    name="Transition"
                    value="True" />
                  <placeholder
                    name="VisibilityCondition"
                    value="CanEditOpportunityEstimates" />
                </control>
              </control>
              <control
                code="BOX"
                id="0b10259f-b9c7-4e3c-ac47-66746aadde91">
                <placeholder
                  name="Layout"
                  value="grid" />
                <placeholder
                  name="Margin"
                  value="my-2" />
                <control
                  code="BOX"
                  id="ff82dfca-7404-4cfd-85aa-a6e8d5157528">
                  <placeholder
                    name="Style"
                    value="border-radius: var(--s-radius-s); border: 1px solid var(--s-neutral-border-weak-default); background: var(--s-neutral-bg-weak-default);padding: var(--s-padding-l);" />
                  <placeholder
                    name="Layout"
                    value="flex" />
                  <placeholder
                    name="VisibilityCondition"
                    value="TotalLocalYearlyRevenue &gt; 0 || TotalLocalYearlyProfit &gt; 0" />
                  <placeholder
                    name="FlexJustify"
                    value="justify-space-between" />
                  <control
                    code="BOX"
                    id="9d7ea6ea-b99d-4536-bf5e-441988851fe6">
                    <placeholder
                      name="Layout"
                      value="flex" />
                    <placeholder
                      name="FlexDirection"
                      value="flex-column" />
                    <placeholder
                      name="Columns"
                      value="col-6 col-sm-6 col-md-6 col-lg-6 col-xl-6" />
                    <control
                      code="LBL"
                      id="8abd6666-cc9a-4dc2-8b65-844d82a83ef1">
                      <placeholder
                        name="Caption"
                        value="Total annual value"
                        resid="be28fdb3-e81b-462e-9e29-48860e7a6bfe" />
                      <placeholder
                        name="Typography"
                        value="title-sm-default" />
                    </control>
                    <control
                      code="LBL"
                      id="74728659-5cfa-4345-bdb5-7efef214e617">
                      <placeholder
                        name="Caption"
                        value="Opportunity scope based estimate"
                        resid="57df1348-611e-4052-a8e7-51ff33ea2e49" />
                      <placeholder
                        name="Typography"
                        value="text-sm-default" />
                    </control>
                  </control>
                  <control
                    code="BOX"
                    id="016ef17b-ecf7-42e6-963c-5abdc9e06393">
                    <placeholder
                      name="Columns"
                      value="col-6 col-sm-6 col-md-6 col-lg-6 col-xl-6" />
                    <placeholder
                      name="Layout"
                      value="flex" />
                    <control
                      code="BOX"
                      id="9e2554ac-ff57-4320-a346-ab6210e78130">
                      <placeholder
                        name="Columns"
                        value="col-6 col-sm-6 col-md-6 col-lg-6 col-xl-6" />
                      <placeholder
                        name="Layout"
                        value="flex" />
                      <placeholder
                        name="FlexDirection"
                        value="flex-column" />
                      <placeholder
                        name="FlexAlign"
                        value="align-end" />
                      <placeholder
                        name="FlexJustify"
                        value="justify-end" />
                      <placeholder
                        name="Margin"
                        value="mr-4" />
                      <control
                        code="BOX"
                        id="2362e848-ae5b-4795-abc9-56be39327253">
                        <placeholder
                          name="FlexGrow"
                          value="flex-grow-1" />
                        <placeholder
                          name="Style"
                          value="height:100%" />
                        <placeholder
                          name="Padding"
                          value="pb-1" />
                      </control>
                      <control
                        code="LBL"
                        id="34949460-65c2-4f4e-924a-088490684c5b">
                        <placeholder
                          name="Caption"
                          value="calc(&quot;Revenue (&quot; + LocalCurrency + &quot;)&quot;)" />
                        <placeholder
                          name="Typography"
                          value="text-sm-default" />
                        <placeholder
                          name="Style"
                          value="height:100%" />
                        <placeholder
                          name="Align"
                          value="right" />
                      </control>
                      <control
                        code="LBL"
                        id="22e0414f-7646-4edc-93b3-ea50bbc2e06e">
                        <placeholder
                          name="Caption"
                          value="calc(&quot;Gross profit (&quot; + LocalCurrency + &quot;)&quot;)" />
                        <placeholder
                          name="Typography"
                          value="text-sm-default" />
                        <placeholder
                          name="Style"
                          value="height:100%" />
                        <placeholder
                          name="Align"
                          value="right" />
                      </control>
                      <control
                        code="LBL"
                        id="2e5a677b-6e6f-4251-aa88-6765d914993d">
                        <placeholder
                          name="Caption"
                          value="Volume (TEU)"
                          resid="7468f095-ebc8-44ad-9b76-64840114920c" />
                        <placeholder
                          name="Typography"
                          value="text-sm-default" />
                        <placeholder
                          name="Style"
                          value="height:100%" />
                        <placeholder
                          name="Align"
                          value="right" />
                      </control>
                    </control>
                    <control
                      code="BOX"
                      id="0d28e9a8-4132-4465-8bc7-3568b27e6516">
                      <placeholder
                        name="Columns"
                        value="col-3 col-sm-3 col-md-3 col-lg-3 col-xl-3" />
                      <placeholder
                        name="Layout"
                        value="flex" />
                      <placeholder
                        name="FlexDirection"
                        value="flex-column" />
                      <placeholder
                        name="Margin"
                        value="mr-4" />
                      <placeholder
                        name="FlexAlign"
                        value="align-end" />
                      <control
                        code="LBL"
                        id="a02cf2e7-b3bc-4724-93ac-fd2dbf43a1a5">
                        <placeholder
                          name="Caption"
                          value="Estimate (PY)"
                          resid="f4d4a0b0-d5e0-4ff2-80a9-c12e8cf19c35" />
                        <placeholder
                          name="Typography"
                          value="text-sm-default" />
                        <placeholder
                          name="Align"
                          value="right" />
                        <placeholder
                          name="Padding"
                          value="pb-1" />
                      </control>
                      <control
                        code="LBL"
                        id="08b5d4c8-3d24-40bb-86f6-dfabcc5b9359">
                        <placeholder
                          name="Caption"
                          value="calc(TotalLocalYearlyRevenueFormatted)" />
                        <placeholder
                          name="Typography"
                          value="text-md-strong-num" />
                        <placeholder
                          name="Align"
                          value="right" />
                        <placeholder
                          name="Style"
                          value="height:100%" />
                      </control>
                      <control
                        code="LBL"
                        id="d5bbd19b-e75c-4f20-a773-473d6c39f3b1">
                        <placeholder
                          name="Caption"
                          value="calc(TotalLocalYearlyProfitFormatted)" />
                        <placeholder
                          name="Typography"
                          value="text-md-strong-num" />
                        <placeholder
                          name="Align"
                          value="right" />
                        <placeholder
                          name="Style"
                          value="height:100%" />
                      </control>
                      <control
                        code="LBL"
                        id="D19A0245-5A31-4925-B578-F2C2D4FD2F0D">
                        <placeholder
                          name="Typography"
                          value="text-md-strong-num" />
                        <placeholder
                          name="Caption"
                          value="-"
                          resid="829e9eac-c4f7-4e8f-8c1f-ecdebb7ba154" />
                        <placeholder
                          name="Align"
                          value="right" />
                        <placeholder
                          name="Style"
                          value="height:100%" />
                      </control>
                    </control>
                    <control
                      code="BOX"
                      id="0a7b18e7-8a8e-4def-9755-2fa32a95a0f8">
                      <placeholder
                        name="Columns"
                        value="col-3 col-sm-3 col-md-3 col-lg-3 col-xl-3" />
                      <placeholder
                        name="Layout"
                        value="flex" />
                      <placeholder
                        name="FlexDirection"
                        value="flex-column" />
                      <placeholder
                        name="FlexAlign"
                        value="align-end" />
                      <control
                        code="LBL"
                        id="25713cd6-8294-40c3-aa63-e701c8ac8de5">
                        <placeholder
                          name="Caption"
                          value="Actual (YTD)"
                          resid="29c78723-1098-42a3-9af6-c1b328186c65" />
                        <placeholder
                          name="Typography"
                          value="text-sm-default" />
                        <placeholder
                          name="Align"
                          value="right" />
                        <placeholder
                          name="Padding"
                          value="pb-1" />
                      </control>
                      <control
                        code="LBL"
                        id="a5f7953e-083f-4008-a1b1-efa56385922f">
                        <placeholder
                          name="Caption"
                          value="-"
                          resid="8fc78290-0173-469a-b179-8df74abf3b44" />
                        <placeholder
                          name="Typography"
                          value="text-md-strong-num" />
                        <placeholder
                          name="Align"
                          value="right" />
                        <placeholder
                          name="Style"
                          value="height:100%" />
                      </control>
                      <control
                        code="LBL"
                        id="49d67f4f-f024-45b5-8f51-086ad2bc89de">
                        <placeholder
                          name="Caption"
                          value="-"
                          resid="453c2988-7ddd-4279-b195-04491984c92a" />
                        <placeholder
                          name="Typography"
                          value="text-md-strong-num" />
                        <placeholder
                          name="Align"
                          value="right" />
                        <placeholder
                          name="Style"
                          value="height:100%" />
                      </control>
                      <control
                        code="LBL"
                        id="baef7647-e384-48d5-83dc-4390c5d9ca6c">
                        <placeholder
                          name="Caption"
                          value="-"
                          resid="eb84da9f-0e52-4a1e-9ee8-a6e5b44c94cf" />
                        <placeholder
                          name="Typography"
                          value="text-md-strong-num" />
                        <placeholder
                          name="Align"
                          value="right" />
                        <placeholder
                          name="Style"
                          value="height:100%" />
                      </control>
                    </control>
                  </control>
                </control>
                <control
                  code="BOX"
                  id="66a0b8dd-d3a0-43a9-80d5-3b857a05bf9b">
                  <placeholder
                    name="Style"
                    value="border-radius: var(--s-radius-s); border: 1px solid var(--s-neutral-border-weak-default); background: var(--s-neutral-bg-weak-default);padding: var(--s-padding-l);" />
                  <placeholder
                    name="Layout"
                    value="flex" />
                  <placeholder
                    name="VisibilityCondition"
                    value="TotalLocalYearlyRevenue == 0 &amp;&amp; YearlyRevenue == 0 &amp;&amp; YearlyProfit == 0 &amp;&amp;  TotalLocalYearlyProfit == 0" />
                  <placeholder
                    name="FlexDirection"
                    value="flex-column" />
                  <control
                    code="BOX"
                    id="c9783da0-d452-4d03-9758-70aea3e8bde1">
                    <control
                      code="BOX"
                      id="b2a62e50-2160-4d59-bac0-9a37b28803e2">
                      <placeholder
                        name="Layout"
                        value="flex" />
                      <placeholder
                        name="FlexDirection"
                        value="flex-column" />
                      <control
                        code="LBL"
                        id="cdb3cdb7-7ef1-49d3-b900-7495a406e912">
                        <placeholder
                          name="Caption"
                          value="Total annual value"
                          resid="8bf40252-9c03-49c0-adf4-808809865df1" />
                        <placeholder
                          name="Typography"
                          value="title-sm-default" />
                      </control>
                      <control
                        code="LBL"
                        id="3cfceb34-dadc-42c8-8472-a637cff14245">
                        <placeholder
                          name="Caption"
                          value="No estimate"
                          resid="8b10f9a1-be9a-4461-9012-46ee0ba4f6fc" />
                        <placeholder
                          name="Typography"
                          value="text-sm-default" />
                      </control>
                    </control>
                    <control
                      code="BTN"
                      id="B09932FB-9CE1-4AE1-8AB2-6B694B88F7CD">
                      <placeholder
                        name="Caption"
                        value="Add estimate"
                        resid="fc0c8481-59d1-444d-a867-e46c75a068a3" />
                      <placeholder
                        name="Transition"
                        value="True" />
                      <placeholder
                        name="Margin"
                        value="mt-2" />
                    </control>
                  </control>
                </control>
                <control
                  code="BOX"
                  id="62168ec5-d5a1-4f6b-86b8-4099c9e585d7">
                  <placeholder
                    name="Style"
                    value="border-radius: var(--s-radius-s); border: 1px solid var(--s-neutral-border-weak-default); background: var(--s-neutral-bg-weak-default);padding: var(--s-padding-l);" />
                  <placeholder
                    name="VisibilityCondition"
                    value="(YearlyRevenue &gt; 0 || YearlyProfit &gt; 0) &amp;&amp; TotalLocalYearlyRevenue == 0 &amp;&amp; TotalLocalYearlyProfit == 0" />
                  <placeholder
                    name="Layout"
                    value="flex" />
                  <placeholder
                    name="FlexJustify"
                    value="justify-space-between" />
                  <control
                    code="BOX"
                    id="dd315ce4-9161-43cf-a0c9-4e0199c2743c">
                    <placeholder
                      name="Layout"
                      value="flex" />
                    <placeholder
                      name="FlexDirection"
                      value="flex-column" />
                    <control
                      code="LBL"
                      id="479d1359-3a24-4883-825b-47b8329bf89a">
                      <placeholder
                        name="Caption"
                        value="Total annual value"
                        resid="f3e42ec1-2a94-470d-b952-6f9020a46c51" />
                      <placeholder
                        name="Typography"
                        value="title-sm-default" />
                    </control>
                    <control
                      code="LBL"
                      id="cf99ec0a-f6ff-4537-ae0e-98647d3443a6">
                      <placeholder
                        name="Caption"
                        value="Initial estimate only"
                        resid="5d0293aa-6c50-4a90-a038-99c27c8d9279" />
                      <placeholder
                        name="Typography"
                        value="text-sm-default" />
                    </control>
                    <control
                      code="BTN"
                      id="71acf917-cc7b-4dbe-9b43-eba5730b926c">
                      <placeholder
                        name="Caption"
                        value="Edit estimate"
                        resid="ed16e70a-278b-49fc-a625-ae869a7ded23" />
                      <placeholder
                        name="Transition"
                        value="True" />
                      <placeholder
                        name="Margin"
                        value="mt-2" />
                    </control>
                  </control>
                  <control
                    code="BOX"
                    id="1da71d86-df66-4414-aec7-53467e8c1509">
                    <placeholder
                      name="Columns"
                      value="col-6 col-sm-6 col-md-6 col-lg-6 col-xl-6" />
                    <placeholder
                      name="Layout"
                      value="flex" />
                    <control
                      code="BOX"
                      id="4e0ed6bf-7169-4231-804b-7d61e59f4546">
                      <placeholder
                        name="Columns"
                        value="col-6 col-sm-6 col-md-6 col-lg-6 col-xl-6" />
                      <placeholder
                        name="FlexAlign"
                        value="align-end" />
                      <placeholder
                        name="FlexDirection"
                        value="flex-column" />
                      <placeholder
                        name="Layout"
                        value="flex" />
                      <placeholder
                        name="Margin"
                        value="mr-4" />
                      <placeholder
                        name="FlexJustify"
                        value="justify-end" />
                      <control
                        code="BOX"
                        id="D7E5D892-2961-408B-A161-BF2888D9FA53">
                        <placeholder
                          name="Style"
                          value="height:100%" />
                      </control>
                      <control
                        code="LBL"
                        id="55056d03-1d3d-43e3-b968-b370aa4c5d45">
                        <placeholder
                          name="Caption"
                          value="calc(&quot;Revenue (&quot; + LocalCurrency + &quot;)&quot;)" />
                        <placeholder
                          name="Typography"
                          value="text-sm-default" />
                        <placeholder
                          name="Align"
                          value="right" />
                        <placeholder
                          name="Style"
                          value="height:100%" />
                      </control>
                      <control
                        code="LBL"
                        id="7a990e8a-344c-4030-960d-b29ee2144b58">
                        <placeholder
                          name="Caption"
                          value="calc(&quot;Gross profit (&quot; + LocalCurrency + &quot;)&quot;)" />
                        <placeholder
                          name="Typography"
                          value="text-sm-default" />
                        <placeholder
                          name="Align"
                          value="right" />
                        <placeholder
                          name="Style"
                          value="height:100%" />
                      </control>
                    </control>
                    <control
                      code="BOX"
                      id="951a1443-ad4e-434b-98e1-1b5f05c1d5c9">
                      <placeholder
                        name="Columns"
                        value="col-3 col-sm-3 col-md-3 col-lg-3 col-xl-3" />
                      <placeholder
                        name="FlexDirection"
                        value="flex-column" />
                      <placeholder
                        name="Layout"
                        value="flex" />
                      <placeholder
                        name="Margin"
                        value="mr-4" />
                      <control
                        code="LBL"
                        id="e2f0d7e0-5344-40c1-b6d6-66cbbc1080de">
                        <placeholder
                          name="Caption"
                          value="Estimate (PY)"
                          resid="487a6858-8564-449e-9e0b-f4dc126a4a0a" />
                        <placeholder
                          name="Typography"
                          value="text-sm-default" />
                        <placeholder
                          name="Align"
                          value="right" />
                        <placeholder
                          name="Padding"
                          value="pb-1" />
                      </control>
                      <control
                        code="LBL"
                        id="6e537a54-9da8-4b29-898b-6f0131ecd3be">
                        <placeholder
                          name="Typography"
                          value="text-md-strong-num" />
                        <placeholder
                          name="Caption"
                          value="calc(YearlyRevenueFormatted)" />
                        <placeholder
                          name="Align"
                          value="right" />
                        <placeholder
                          name="Style"
                          value="height:100%" />
                      </control>
                      <control
                        code="LBL"
                        id="e2dcd79f-ed75-451d-ba6d-1109057a5f40">
                        <placeholder
                          name="Caption"
                          value="calc(YearlyProfitFormatted)" />
                        <placeholder
                          name="Typography"
                          value="text-md-strong-num" />
                        <placeholder
                          name="Align"
                          value="right" />
                        <placeholder
                          name="Style"
                          value="height:100%" />
                      </control>
                    </control>
                    <control
                      code="BOX"
                      id="d8394d3f-1f9f-4096-92d2-7c98a4e0cddb">
                      <placeholder
                        name="Columns"
                        value="col-3 col-sm-3 col-md-3 col-lg-3 col-xl-3" />
                      <placeholder
                        name="VisibilityCondition"
                        value="CrmOpportunityScopes.Any()" />
                      <placeholder
                        name="FlexDirection"
                        value="flex-column" />
                      <placeholder
                        name="Layout"
                        value="flex" />
                      <control
                        code="LBL"
                        id="8f133728-a37c-46d7-a2e8-5662b1dd0899">
                        <placeholder
                          name="Caption"
                          value="Actual (YTD)"
                          resid="2fcffb13-3293-467e-bfa8-2e0e29346c0a" />
                        <placeholder
                          name="Typography"
                          value="text-sm-default" />
                        <placeholder
                          name="Align"
                          value="right" />
                        <placeholder
                          name="Padding"
                          value="pb-1" />
                      </control>
                      <control
                        code="LBL"
                        id="81a0c389-4291-4b0a-afd0-5d7ead0364c7">
                        <placeholder
                          name="Caption"
                          value="-"
                          resid="cf0f2622-90ba-42f9-a0a9-213df2f50638" />
                        <placeholder
                          name="Typography"
                          value="text-md-strong-num" />
                        <placeholder
                          name="Align"
                          value="right" />
                        <placeholder
                          name="Style"
                          value="height:100%" />
                      </control>
                      <control
                        code="LBL"
                        id="b6d9918a-680d-4203-96ac-4f871206f20f">
                        <placeholder
                          name="Caption"
                          value="-"
                          resid="22184b83-1d0d-463a-9865-8e4a6d636378" />
                        <placeholder
                          name="Typography"
                          value="text-md-strong-num" />
                        <placeholder
                          name="Align"
                          value="right" />
                        <placeholder
                          name="Style"
                          value="height:100%" />
                      </control>
                    </control>
                  </control>
                </control>
              </control>
            </control>
            <control
              code="PNL"
              id="d5ca4427-f3bc-4847-86c2-a3574121f457">
              <placeholder
                name="VisibilityCondition"
                value="!CanViewOpportunityEstimates" />
              <placeholder
                name="Style"
                value="overflow-y: auto" />
              <placeholder
                name="FlexDirection"
                value="flex-column" />
              <placeholder
                name="FlexGrow"
                value="flex-grow-1" />
              <placeholder
                name="Height"
                value="100%" />
              <placeholder
                name="Layout"
                value="flex" />
              <control
                code="LBL"
                id="f532a9ec-1515-4c93-9924-4abfe50db151">
                <placeholder
                  name="Caption"
                  value="Estimate and scope"
                  resid="f8c9438d-674c-49be-b64f-b6544598c103" />
                <placeholder
                  name="Typography"
                  value="title-sm-default" />
                <placeholder
                  name="Padding"
                  value="pb-2" />
              </control>
              <control
                code="BOX"
                id="add10eff-aa60-401c-8069-0e540ef9e0c6">
                <placeholder
                  name="Layout"
                  value="flex" />
                <placeholder
                  name="FlexDirection"
                  value="flex-column" />
                <placeholder
                  name="FlexJustify"
                  value="justify-center" />
                <placeholder
                  name="FlexAlign"
                  value="align-center" />
                <placeholder
                  name="Style"
                  value="background-color: rgb(240, 240, 240); border-style: solid; border-radius: 5px; border-color: grey;" />
                <placeholder
                  name="FlexGrow"
                  value="flex-grow-1" />
                <control
                  code="LBL"
                  id="dfd67726-cc17-4e81-bd91-249fd682ea73">
                  <placeholder
                    name="Caption"
                    value="You do not have access to this section"
                    resid="c9664d29-f184-4423-b2de-b31ad25d4280" />
                </control>
              </control>
            </control>
            <control
              code="PNL"
              id="38bb4aef-847a-4113-bb4f-96d1c48b18b9"
              binding="">
              <placeholder
                name="Caption"
                value="Recent activity"
                resid="7666da7d-88f2-4384-b2ee-f1798668e6bb" />
              <placeholder
                name="Layout"
                value="flex" />
              <placeholder
                name="FlexGrow"
                value="flex-grow-1" />
              <placeholder
                name="Overflow"
                value="overflow-hidden" />
              <placeholder
                name="Height"
                value="100%" />
            </control>
          </control>
          <control
            code="BOX"
            id="72c35d71-e6a8-4ad5-a5fb-45e0a51a1455">
            <placeholder
              name="Columns"
              value="col-lg-3" />
            <placeholder
              name="Overflow"
              value="overflow-hidden" />
            <placeholder
              name="FlexDirection"
              value="flex-column" />
            <placeholder
              name="MinWidth"
              value="300px" />
            <placeholder
              name="Layout"
              value="flex" />
            <placeholder
              name="Padding"
              value="px-1 pb-2" />
            <control
              code="PNL"
              id="0250fad9-f42d-486d-bd21-45bd4865d613"
              binding="">
              <placeholder
                name="Caption"
                value="Customer details"
                resid="6e0257e6-d749-4ba6-8ec5-ec17192752c3" />
              <placeholder
                name="Style"
                value="height: 100%; overflow-y: auto" />
              <control
                code="BOX"
                id="790e2fd4-a56a-49ee-ad58-530f6d2b410a">
                <placeholder
                  name="Layout"
                  value="grid" />
                <control
                  code="BOX"
                  id="e444219b-cd32-435e-ba8e-c8800707f00f">
                  <placeholder
                    name="Layout"
                    value="flex" />
                  <placeholder
                    name="FlexDirection"
                    value="flex-column" />
                  <control
                    code="LBL"
                    id="eccc26d2-d25d-4a3f-855a-ba1283aa807a">
                    <placeholder
                      name="Caption"
                      value="Opportunity name"
                      resid="26ee291a-0f7e-484e-8ef7-4ed01e8ffbe5" />
                    <placeholder
                      name="Typography"
                      value="text-sm-default" />
                  </control>
                  <control
                    code="LBL"
                    id="b9683f86-57f3-4dae-843b-85204a029f62">
                    <placeholder
                      name="Caption"
                      value="calc(EclipsedOpportunityName)" />
                    <placeholder
                      name="Typography"
                      value="text-md-strong" />
                    <placeholder
                      name="NoWrap"
                      value="False" />
                  </control>
                </control>
                <control
                  code="BOX"
                  id="536998e0-c18d-49a5-b82f-8a77e98bd5be">
                  <placeholder
                    name="Layout"
                    value="flex" />
                  <placeholder
                    name="FlexDirection"
                    value="flex-column" />
                  <control
                    code="LBL"
                    id="5260aec3-c0e6-4e00-98b8-9d542d2ed5ee">
                    <placeholder
                      name="Caption"
                      value="Organization"
                      resid="8a7a8ca9-b7db-4cd0-ae46-edd653c438fb" />
                    <placeholder
                      name="Typography"
                      value="text-sm-default" />
                  </control>
                  <control
                    code="LBL"
                    id="a1bb0509-42e8-4ecb-9f00-3e59b8a5e443">
                    <placeholder
                      name="Caption"
                      value="calc(EclipsedOrganizationName)" />
                    <placeholder
                      name="Typography"
                      value="text-md-strong" />
                    <placeholder
                      name="NoWrap"
                      value="False" />
                  </control>
                </control>
                <control
                  code="BOX"
                  id="ae774225-a44c-435c-9a84-37f123f2173f">
                  <placeholder
                    name="VisibilityCondition"
                    value="Organization!=null" />
                  <control
                    code="LBL"
                    id="a1a31f65-4977-4dd8-ac7e-34fd30f6b98c">
                    <placeholder
                      name="Caption"
                      value="Screening"
                      resid="2281cb29-87e9-4dd8-adb9-51c13864ee0d" />
                    <placeholder
                      name="Align"
                      value="left" />
                    <placeholder
                      name="Typography"
                      value="text-sm-default" />
                  </control>
                  <control
                    code="STA"
                    id="22831f81-f8ca-4166-9bd3-36b55e23aea4"
                    binding="ScreeningStatusText">
                    <placeholder
                      name="IsReadOnly"
                      value="True" />
                    <placeholder
                      name="Variant"
                      value="outline" />
                  </control>
                </control>
              </control>
              <control
                code="BOX"
                id="80df56aa-81c2-41c8-bc27-17d4b665cf2c"
                binding="">
                <placeholder
                  name="Style"
                  value="border-top: 1px solid var(--s-neutral-border-weak-default);" />
                <placeholder
                  name="Layout"
                  value="grid" />
                <control
                  code="BOX"
                  id="1be15a71-4ac8-47da-8333-9f7be98a6bd2">
                  <placeholder
                    name="Layout"
                    value="flex" />
                  <placeholder
                    name="FlexDirection"
                    value="flex-column" />
                  <control
                    code="LBL"
                    id="7d468b55-a212-4813-a9f6-8cc443954f53">
                    <placeholder
                      name="Caption"
                      value="Primary contact"
                      resid="197cff9a-62e7-44e6-961e-da7e49df6009" />
                    <placeholder
                      name="Typography"
                      value="text-sm-default" />
                  </control>
                  <control
                    code="LBL"
                    id="cc8231b4-e8bd-46c1-b4f8-9414b2fbef86">
                    <placeholder
                      name="Caption"
                      value="calc(PrimaryContact)" />
                    <placeholder
                      name="Typography"
                      value="text-md-strong" />
                    <placeholder
                      name="NoWrap"
                      value="False" />
                  </control>
                </control>
                <control
                  code="BOX"
                  id="c252d986-eb41-4142-98d8-ea714d8a0a7c">
                  <placeholder
                    name="Layout"
                    value="flex" />
                  <placeholder
                    name="FlexDirection"
                    value="flex-column" />
                  <control
                    code="LBL"
                    id="ddd0b1c1-c283-4efe-bbcb-9cdd2cc00ec9">
                    <placeholder
                      name="Caption"
                      value="Contact number (mobile)"
                      resid="daccb4b0-1864-4480-9439-91d3db5d767a" />
                    <placeholder
                      name="Typography"
                      value="text-sm-default" />
                  </control>
                  <control
                    code="LBL"
                    id="0af2b783-2c58-4731-8c07-5b0212290bb9">
                    <placeholder
                      name="Caption"
                      value="calc(PrimaryContactNumber)" />
                    <placeholder
                      name="Typography"
                      value="text-md-strong" />
                    <placeholder
                      name="NoWrap"
                      value="False" />
                  </control>
                </control>
              </control>
              <control
                code="BOX"
                id="dd8a7289-81ba-4d1a-bd92-f0eb258aeede"
                binding="">
                <placeholder
                  name="Style"
                  value="border-top: 1px solid var(--s-neutral-border-weak-default);" />
                <control
                  code="BOX"
                  id="3afb7b8e-3a00-4301-ad39-afd5ab308f14">
                  <placeholder
                    name="Layout"
                    value="flex" />
                  <placeholder
                    name="FlexDirection"
                    value="flex-column" />
                  <control
                    code="LBL"
                    id="7e347d6f-8544-4506-bf37-de2b6bcf4d44">
                    <placeholder
                      name="Caption"
                      value="Sales person"
                      resid="bdb97f6b-752e-4016-98cb-e2ccf28c6090" />
                    <placeholder
                      name="Typography"
                      value="text-sm-default" />
                  </control>
                  <control
                    code="LBL"
                    id="bbd602fd-98fc-40c9-9996-6f8cdb284145">
                    <placeholder
                      name="Caption"
                      value="calc(EclipsedSalesPerson)" />
                    <placeholder
                      name="Typography"
                      value="text-md-strong" />
                    <placeholder
                      name="NoWrap"
                      value="False" />
                  </control>
                </control>
              </control>
              <control
                code="BOX"
                id="1c2d0707-6e1c-49e7-b35c-6ba98e8eb825">
                <placeholder
                  name="Layout"
                  value="flex" />
                <placeholder
                  name="FlexJustify"
                  value="justify-center" />
                <control
                  code="BTN"
                  id="62325f52-c324-46e5-bb25-1c6059c4a67e"
                  binding="">
                  <placeholder
                    name="Caption"
                    value="See more details"
                    resid="9fca8393-0fdd-4a62-bfdc-4a78aafed5fd" />
                  <placeholder
                    name="Variant"
                    value="ghost" />
                </control>
              </control>
            </control>
            <control
              code="PNL"
              id="a5a5c403-d4f4-4979-bb4d-fe7c8db15394"
              binding="">
              <placeholder
                name="Caption"
                value="Related items"
                resid="4787b874-3b5f-4383-a406-5ccc73d6f777" />
              <placeholder
                name="Style"
                value="height: 100%; overflow-y: auto" />
              <control
                code="BOX"
                id="8be70efd-6fed-4e29-b073-088670ed1ec3" />
              <control
                code="BOX"
                id="0db943d7-79d4-4287-b29d-9e30c67b46c7">
                <placeholder
                  name="Layout"
                  value="flex" />
                <placeholder
                  name="FlexJustify"
                  value="justify-center" />
                <control
                  code="BTN"
                  id="f87ecc37-d158-42fb-8a6f-2c3e7cf0ebc2"
                  binding="">
                  <placeholder
                    name="Caption"
                    value="See more related"
                    resid="40cf9069-f26e-455e-ae87-24dc1cb73bc7" />
                  <placeholder
                    name="Variant"
                    value="ghost" />
                </control>
              </control>
            </control>
          </control>
        </control>
      </control>
      <control
        code="TAB"
        id="16205bbc-d082-4514-bf39-c213c7b97e5d">
        <placeholder
          name="Caption"
          value="Details"
          resid="38ce6b45-6da0-4334-8895-38785864428b" />
      </control>
      <control
        code="TAI"
        id="7daca25d-50f5-47ae-9d41-54b76e6a31dd">
        <placeholder
          name="Padding"
          value="pa-2" />
        <control
          code="BOX"
          id="e8f8c408-80c4-4d08-bcd6-3e8296c94c40"
          binding="">
          <placeholder
            name="Layout"
            value="grid" />
          <control
            code="BOX"
            id="9d639323-cbf5-458c-b8cd-1d4f928ad8a1"
            binding="">
            <placeholder
              name="Columns"
              value="col-md-6" />
            <placeholder
              name="Layout"
              value="grid" />
            <control
              code="PNL"
              id="12ebe1cf-c3e7-4042-8ae3-0adb7ce51f18"
              binding="">
              <placeholder
                name="Caption"
                value="Opportunity details"
                resid="dee7b87c-e74b-49a5-b7a2-83c944970987" />
              <placeholder
                name="Layout"
                value="grid" />
              <control
                code="BOX"
                id="6fa7cab2-edd3-42d3-af98-49a40bbd0199">
                <placeholder
                  name="FlexWrap"
                  value="flex-nowrap" />
                <placeholder
                  name="Layout"
                  value="flex" />
                <placeholder
                  name="FlexJustify"
                  value="justify-center" />
                <placeholder
                  name="FlexAlign"
                  value="align-end" />
                <control
                  code="TXT"
                  id="04d633ec-1de0-463d-8069-4bb8a043a934"
                  binding="COP_OpportunityName">
                  <placeholder
                    name="CaptionOverride"
                    value="Opportunity name"
                    resid="080ade43-4247-436d-9f87-26dec5ba6657" />
                </control>
                <control
                  code="OPT"
                  id="6676916e-efae-4874-83fd-8e09022ac4bd"
                  binding="COP_IsRestricted">
                  <placeholder
                    name="IsReadOnly"
                    value="False" />
                  <placeholder
                    name="CaptionOverride"
                    value="Restricted opportunity"
                    resid="d3ad220c-56f7-4da1-b3fe-cdf01cd6ecfd" />
                  <placeholder
                    name="CaptionType"
                    value="medium" />
                  <placeholder
                    name="Margin"
                    value="ml-2" />
                  <placeholder
                    name="Class"
                    value="font-weight-bold" />
                  <placeholder
                    name="VisibilityCondition"
                    value="%.IsRestrictedOpportunitiesEnabled || COP_IsRestricted" />
                </control>
              </control>
              <control
                code="BOX"
                id="5ca3d716-84a6-443f-8ee2-281ce0278168">
                <placeholder
                  name="FlexWrap"
                  value="flex-nowrap" />
                <placeholder
                  name="Layout"
                  value="flex" />
                <placeholder
                  name="FlexJustify"
                  value="justify-end" />
                <placeholder
                  name="VisibilityCondition"
                  value="!ShouldDisplayTemporaryOrganization &amp;&amp; ShouldAllowCreatingTemporaryOrganization" />
                <control
                  code="CMP"
                  id="78c0483f-6b17-4fd5-a461-3a8115b01e27"
                  binding="">
                  <placeholder
                    name="Component"
                    value="cargoWiseOne.productCrm.components.EditOpportunityOrgSearchField" />
                </control>
                <control
                  code="BOX"
                  id="08e0a76c-c8c1-4abc-b7b9-e29b21888c17">
                  <placeholder
                    name="Margin"
                    value="ml-2" />
                  <placeholder
                    name="VisibilityCondition"
                    value="Organization!=null" />
                  <control
                    code="LBL"
                    id="c01bddb0-6948-4843-b464-73ae3767d221">
                    <placeholder
                      name="Caption"
                      value="Screening"
                      resid="2c0a7c5a-da69-488c-9e35-36e0f6f36725" />
                    <placeholder
                      name="Align"
                      value="left" />
                    <placeholder
                      name="Typography"
                      value="label" />
                  </control>
                  <control
                    code="BOX"
                    id="d2abbdd3-0d12-4bae-a623-ac58fa1c505c">
                    <placeholder
                      name="Padding"
                      value="pt-1" />
                    <control
                      code="STA"
                      id="dbdf7361-b092-425b-8024-22151bc38e83"
                      binding="ScreeningStatusText">
                      <placeholder
                        name="IsReadOnly"
                        value="True" />
                      <placeholder
                        name="Variant"
                        value="outline" />
                    </control>
                  </control>
                </control>
              </control>
              <control
                code="BOX"
                id="ed30592e-59c4-4b63-a42c-01d8719a194f">
                <placeholder
                  name="FlexWrap"
                  value="flex-nowrap" />
                <placeholder
                  name="Layout"
                  value="flex" />
                <placeholder
                  name="FlexJustify"
                  value="justify-end" />
                <placeholder
                  name="VisibilityCondition"
                  value="!ShouldAllowCreatingTemporaryOrganization" />
                <control
                  code="SRC"
                  id="bc49c07e-bd58-4ca7-8042-13727a9494a7"
                  binding="COP_OH_Organization" />
                <control
                  code="BOX"
                  id="2fab0a68-f3ef-4004-8bdf-60c00f0ac693">
                  <placeholder
                    name="Margin"
                    value="ml-2" />
                  <placeholder
                    name="VisibilityCondition"
                    value="Organization!=null" />
                  <control
                    code="LBL"
                    id="001c0e23-be34-4f31-b316-c83ae3c622c8">
                    <placeholder
                      name="Caption"
                      value="Screening"
                      resid="2c0a7c5a-da69-488c-9e35-36e0f6f36725" />
                    <placeholder
                      name="Align"
                      value="left" />
                    <placeholder
                      name="Typography"
                      value="label" />
                  </control>
                  <control
                    code="BOX"
                    id="630665a6-ce9e-4970-83a6-fac62b29f240">
                    <placeholder
                      name="Padding"
                      value="pt-1" />
                    <control
                      code="STA"
                      id="af870517-f634-4303-884f-742a0160312a"
                      binding="ScreeningStatusText">
                      <placeholder
                        name="IsReadOnly"
                        value="True" />
                      <placeholder
                        name="Variant"
                        value="outline" />
                    </control>
                  </control>
                </control>
              </control>
              <control
                code="BOX"
                id="7c3ead5c-2fa5-4128-bf34-60be94ad59d5"
                binding="">
                <placeholder
                  name="VisibilityCondition"
                  value="ShouldDisplayTemporaryOrganization" />
                <placeholder
                  name="NoGutters"
                  value="True" />
                <control
                  code="LBL"
                  id="1848d045-667e-4f48-b423-44f83068e63c">
                  <placeholder
                    name="Caption"
                    value="Organization details"
                    resid="c579eeae-423f-45c1-8d96-bd2ffcca8aec" />
                  <placeholder
                    name="Typography"
                    value="title-sm-default" />
                </control>
                <control
                  code="PNL"
                  id="c91e7cf6-4a09-4307-bed3-d68c7dbfcfb6">
                  <placeholder
                    name="Margin"
                    value="mt-1" />
                  <control
                    code="BOX"
                    id="d98aa127-c183-43fe-afdb-cb647d0c0940"
                    binding="">
                    <placeholder
                      name="Layout"
                      value="grid" />
                    <placeholder
                      name="NoGutters"
                      value="True" />
                    <control
                      code="BOX"
                      id="c725c139-f4ad-49e4-9345-0d09516468a8"
                      binding="">
                      <placeholder
                        name="Columns"
                        value="col-11" />
                      <placeholder
                        name="Layout"
                        value="flex" />
                      <placeholder
                        name="FlexAlign"
                        value="align-center" />
                      <control
                        code="LBL"
                        id="c02d5c22-1c48-42f2-9793-fe917f0bbc5c">
                        <placeholder
                          name="Caption"
                          value="calc(Organization.OH_FullName)" />
                        <placeholder
                          name="Typography"
                          value="text-md-strong" />
                        <placeholder
                          name="Margin"
                          value="mr-1" />
                      </control>
                      <control
                        code="STA"
                        id="5645db5a-85e1-4759-85a9-8cd9abb41e56"
                        binding="NewStatusText">
                        <placeholder
                          name="Variant"
                          value="outline" />
                        <placeholder
                          name="Sentiment"
                          value="success" />
                        <placeholder
                          name="IsReadOnly"
                          value="True" />
                      </control>
                    </control>
                    <control
                      code="BOX"
                      id="9c5facff-415a-4c73-9dc2-7b31253d804c">
                      <placeholder
                        name="Columns"
                        value="col-1" />
                      <placeholder
                        name="Layout"
                        value="flex" />
                      <placeholder
                        name="FlexDirection"
                        value="flex-row-reverse" />
                      <control
                        code="IBT"
                        id="a8361452-b3c7-44da-88a9-fd18d0a7fde3"
                        binding="">
                        <placeholder
                          name="Icon"
                          value="s-icon-delete" />
                        <placeholder
                          name="Variant"
                          value="ghost" />
                        <placeholder
                          name="Size"
                          value="xs" />
                        <placeholder
                          name="Transition"
                          value="True" />
                        <placeholder
                          name="Tooltip"
                          value="Clear and search existing organizations"
                          resid="f3d609b2-cd20-41b2-9141-25aa7b1e97e2" />
                        <placeholder
                          name="Padding"
                          value="pt-s" />
                      </control>
                      <control
                        code="IBT"
                        id="628ddfd5-438c-4e61-97d1-39bca4c147c9"
                        binding="">
                        <placeholder
                          name="Variant"
                          value="ghost" />
                        <placeholder
                          name="Margin"
                          value="ml-1" />
                        <placeholder
                          name="Icon"
                          value="s-icon-edit" />
                        <placeholder
                          name="Size"
                          value="xs" />
                        <placeholder
                          name="Tooltip"
                          value="Edit"
                          resid="eb946962-5b70-48e4-b0a0-5588b323cc20" />
                        <placeholder
                          name="Transition"
                          value="True" />
                        <placeholder
                          name="Padding"
                          value="pt-s" />
                      </control>
                    </control>
                    <control
                      code="BOX"
                      id="c30a98d5-b8ec-4094-917a-27868f036e11"
                      binding="">
                      <placeholder
                        name="VisibilityCondition"
                        value="(TemporaryOrganizationAddress.OA_Address1.Length() &gt; 0 &amp;&amp; TemporaryOrganizationAddress.OA_Address1 != NoAddressOnFileValue) || TemporaryOrganizationAddress.OA_Address2.Length() &gt; 0" />
                      <control
                        code="LBL"
                        id="ae2849a9-d974-4429-9fdc-02593c6757fa"
                        binding="">
                        <placeholder
                          name="Caption"
                          value="calc(TemporaryOrganizationAddress.OA_Address1)" />
                        <placeholder
                          name="VisibilityCondition"
                          value="TemporaryOrganizationAddress.OA_Address1.Length() &gt; 0 &amp;&amp; TemporaryOrganizationAddress.OA_Address1 != NoAddressOnFileValue" />
                        <placeholder
                          name="Typography"
                          value="text-md-default" />
                      </control>
                      <control
                        code="LBL"
                        id="daa7027b-de80-4abf-9bd6-371aeb41e86f"
                        binding="">
                        <placeholder
                          name="Caption"
                          value=","
                          resid="f8158906-7dd2-49bb-a6a0-ed603eaf643c" />
                        <placeholder
                          name="Margin"
                          value="mr-1" />
                        <placeholder
                          name="VisibilityCondition"
                          value="(TemporaryOrganizationAddress.OA_Address1.Length() &gt; 0 &amp;&amp; TemporaryOrganizationAddress.OA_Address1 != NoAddressOnFileValue) &amp;&amp; TemporaryOrganizationAddress.OA_Address2.Length() &gt; 0" />
                        <placeholder
                          name="Typography"
                          value="label" />
                      </control>
                      <control
                        code="LBL"
                        id="ecff6a17-a842-4a26-b31b-5a6cd034ea9f"
                        binding="">
                        <placeholder
                          name="Caption"
                          value="calc(TemporaryOrganizationAddress.OA_Address2)" />
                        <placeholder
                          name="VisibilityCondition"
                          value="TemporaryOrganizationAddress.OA_Address2.Length() &gt; 0" />
                        <placeholder
                          name="Typography"
                          value="text-md-default" />
                      </control>
                    </control>
                    <control
                      code="BOX"
                      id="cdff622a-82c4-4cef-947d-b8cf96f414f3"
                      binding="">
                      <placeholder
                        name="VisibilityCondition"
                        value="TemporaryOrganizationAddress.OA_City.Length() &gt; 0 || TemporaryOrganizationAddress.OA_PostCode.Length() &gt; 0 || TemporaryOrganizationAddress.OA_State.Length() &gt; 0" />
                      <placeholder
                        name="Margin"
                        value="mt-n1" />
                      <control
                        code="LBL"
                        id="bb964d29-03f0-4e00-8aba-d809f06adb6a"
                        binding="">
                        <placeholder
                          name="Caption"
                          value="calc(TemporaryOrganizationAddress.OA_City)" />
                        <placeholder
                          name="VisibilityCondition"
                          value="TemporaryOrganizationAddress.OA_City.Length() &gt; 0" />
                        <placeholder
                          name="Typography"
                          value="text-md-default" />
                      </control>
                      <control
                        code="LBL"
                        id="02943f39-248c-414c-b7ab-a5a473ed3fbd"
                        binding="">
                        <placeholder
                          name="Caption"
                          value=","
                          resid="0094abae-2775-48f0-9670-32e23d5db656" />
                        <placeholder
                          name="Margin"
                          value="mr-1" />
                        <placeholder
                          name="VisibilityCondition"
                          value="TemporaryOrganizationAddress.OA_City.Length() &gt; 0 &amp;&amp;  (TemporaryOrganizationAddress.OA_PostCode.Length() &gt; 0 || TemporaryOrganizationAddress.OA_State.Length() &gt; 0)" />
                        <placeholder
                          name="Typography"
                          value="label" />
                      </control>
                      <control
                        code="LBL"
                        id="b8853e2c-c840-4628-882a-89bcec2804a5"
                        binding="">
                        <placeholder
                          name="Caption"
                          value="calc(TemporaryOrganizationAddress.OA_PostCode)" />
                        <placeholder
                          name="VisibilityCondition"
                          value="TemporaryOrganizationAddress.OA_PostCode.Length() &gt; 0" />
                        <placeholder
                          name="Typography"
                          value="text-md-default" />
                      </control>
                      <control
                        code="LBL"
                        id="824c29d0-bc4a-402a-b3bc-7a55620151cd"
                        binding="">
                        <placeholder
                          name="Caption"
                          value=","
                          resid="9fce155f-e5e4-4c61-b3b0-af377faa0376" />
                        <placeholder
                          name="Margin"
                          value="mr-1" />
                        <placeholder
                          name="VisibilityCondition"
                          value="TemporaryOrganizationAddress.OA_PostCode.Length() &gt; 0 &amp;&amp; TemporaryOrganizationAddress.OA_State.Length() &gt; 0" />
                        <placeholder
                          name="Typography"
                          value="label" />
                      </control>
                      <control
                        code="LBL"
                        id="db3ade11-7b0b-4911-8afb-c80c3fd26341"
                        binding="">
                        <placeholder
                          name="Caption"
                          value="calc(TemporaryOrganizationAddress.OA_State)" />
                        <placeholder
                          name="VisibilityCondition"
                          value="TemporaryOrganizationAddress.OA_State.Length() &gt; 0" />
                        <placeholder
                          name="Typography"
                          value="text-md-default" />
                      </control>
                    </control>
                    <control
                      code="BOX"
                      id="84eac322-8bb8-4321-904d-354bec6c07a1"
                      binding="">
                      <placeholder
                        name="VisibilityCondition"
                        value="Organization.OH_RL_NKClosestPort.Length() &gt; 0 || TemporaryOrganizationAddress.OA_RN_NKCountryCode.Length() &gt; 0" />
                      <placeholder
                        name="Margin"
                        value="mt-n1" />
                      <control
                        code="LBL"
                        id="6c1615a3-f4a1-45c0-a43a-937fd74b9c88"
                        binding="">
                        <placeholder
                          name="Caption"
                          value="calc(Organization.OH_RL_NKClosestPort)" />
                        <placeholder
                          name="VisibilityCondition"
                          value="Organization.OH_RL_NKClosestPort.Length() &gt; 0" />
                        <placeholder
                          name="Typography"
                          value="text-md-default" />
                      </control>
                      <control
                        code="LBL"
                        id="d01abc9b-9f34-4161-aa2d-fcb832e99f90"
                        binding="">
                        <placeholder
                          name="Caption"
                          value=","
                          resid="c7fca17e-da37-4f99-b918-84a122780345" />
                        <placeholder
                          name="Margin"
                          value="mr-1" />
                        <placeholder
                          name="VisibilityCondition"
                          value="Organization.OH_RL_NKClosestPort.Length() &gt; 0 &amp;&amp; TemporaryOrganizationAddress.OA_RN_NKCountryCode.Length() &gt; 0" />
                        <placeholder
                          name="Typography"
                          value="label" />
                      </control>
                      <control
                        code="LBL"
                        id="ff071d73-076e-4974-8a32-41c7790d77c9"
                        binding="">
                        <placeholder
                          name="Caption"
                          value="calc(TemporaryOrganizationAddress.OA_RN_NKCountryCode)" />
                        <placeholder
                          name="VisibilityCondition"
                          value="TemporaryOrganizationAddress.OA_RN_NKCountryCode.Length() &gt; 0" />
                        <placeholder
                          name="Typography"
                          value="text-md-default" />
                      </control>
                    </control>
                    <control
                      code="BOX"
                      id="675443b3-489c-4d29-9fef-5bafae649bfc"
                      binding="">
                      <placeholder
                        name="VisibilityCondition"
                        value="TemporaryOrganizationAddress.OA_Mobile.Length() &gt; 0" />
                      <placeholder
                        name="Margin"
                        value="mt-n1" />
                      <control
                        code="ICO"
                        id="64822089-7865-418e-a736-3998aa6fe72a"
                        binding="">
                        <placeholder
                          name="IconName"
                          value="s-icon-mobile" />
                        <placeholder
                          name="Margin"
                          value="mr-1" />
                        <placeholder
                          name="Tooltip"
                          value="Mobile"
                          resid="a67b20a4-c021-4675-b94b-0a77a3f5bbde" />
                      </control>
                      <control
                        code="LBL"
                        id="091a2e90-f9ff-4acd-80ee-d81f3bbbfe1d"
                        binding="">
                        <placeholder
                          name="Caption"
                          value="calc(TemporaryOrganizationAddress.OA_Mobile)" />
                        <placeholder
                          name="Typography"
                          value="text-md-default" />
                      </control>
                    </control>
                    <control
                      code="BOX"
                      id="49114733-7d3d-4899-98a9-ab30550a5d12"
                      binding="">
                      <placeholder
                        name="VisibilityCondition"
                        value="TemporaryOrganizationAddress.OA_Phone.Length() &gt; 0" />
                      <placeholder
                        name="Margin"
                        value="mt-n1" />
                      <control
                        code="ICO"
                        id="3ac85d55-c0c4-4e50-b592-27178d05f4be"
                        binding="">
                        <placeholder
                          name="IconName"
                          value="s-icon-phone" />
                        <placeholder
                          name="Margin"
                          value="mr-1" />
                        <placeholder
                          name="Tooltip"
                          value="Phone"
                          resid="f0b627d5-a426-4ebd-8af9-14248e7aa17d" />
                      </control>
                      <control
                        code="LBL"
                        id="93d95d21-8600-499a-b0ca-43d841310af5"
                        binding="">
                        <placeholder
                          name="Caption"
                          value="calc(TemporaryOrganizationAddress.OA_Phone)" />
                        <placeholder
                          name="Typography"
                          value="text-md-default" />
                      </control>
                    </control>
                    <control
                      code="BOX"
                      id="e07e852d-283f-4ddf-bf54-15ed8a40f6d5"
                      binding="">
                      <placeholder
                        name="VisibilityCondition"
                        value="TemporaryOrganizationAddress.OA_Fax.Length() &gt; 0" />
                      <placeholder
                        name="Margin"
                        value="mt-n1" />
                      <control
                        code="ICO"
                        id="4387eb58-f472-41e8-8a7c-1fe4a78cc1ac"
                        binding="">
                        <placeholder
                          name="IconName"
                          value="s-icon-print" />
                        <placeholder
                          name="Margin"
                          value="mr-1" />
                        <placeholder
                          name="Tooltip"
                          value="Fax"
                          resid="85c47f0d-8043-43d8-93ee-b8fc0883208c" />
                      </control>
                      <control
                        code="LBL"
                        id="dcdd5677-b1e7-4c18-97d7-fd6643e3e9b0"
                        binding="">
                        <placeholder
                          name="Caption"
                          value="calc(TemporaryOrganizationAddress.OA_Fax)" />
                        <placeholder
                          name="Typography"
                          value="text-md-default" />
                      </control>
                    </control>
                    <control
                      code="BOX"
                      id="93fe96b1-f561-44f8-95bc-27fb1deead81"
                      binding="">
                      <placeholder
                        name="VisibilityCondition"
                        value="TemporaryOrganizationAddress.OA_Email.Length() &gt; 0" />
                      <placeholder
                        name="Margin"
                        value="mt-n1" />
                      <control
                        code="ICO"
                        id="f927ec2d-0c5d-43f5-b0e9-803bdb3c911d"
                        binding="">
                        <placeholder
                          name="IconName"
                          value="s-icon-email" />
                        <placeholder
                          name="Margin"
                          value="mr-1" />
                        <placeholder
                          name="Tooltip"
                          value="Email"
                          resid="735f497d-2e56-4de8-ad85-70a14f1cca90" />
                      </control>
                      <control
                        code="LBL"
                        id="1aaf32fd-87b6-402b-b557-133d99bf9ac5"
                        binding="">
                        <placeholder
                          name="Caption"
                          value="calc(TemporaryOrganizationAddress.OA_Email)" />
                        <placeholder
                          name="Typography"
                          value="text-md-default" />
                      </control>
                    </control>
                    <control
                      code="BOX"
                      id="4af60fe3-4426-4233-af1c-7006462ecf50"
                      binding="">
                      <placeholder
                        name="VisibilityCondition"
                        value="TemporaryOrganizationWebURL.PU_URL.Length() &gt; 0" />
                      <placeholder
                        name="Margin"
                        value="mt-n1" />
                      <control
                        code="ICO"
                        id="ba9324e5-0f00-4fda-8588-1140f04323dd"
                        binding="">
                        <placeholder
                          name="IconName"
                          value="s-icon-earth" />
                        <placeholder
                          name="Margin"
                          value="mr-1" />
                        <placeholder
                          name="Tooltip"
                          value="Website URL"
                          resid="d8cfa08d-f026-4004-a3e2-a0a13df83ddd" />
                      </control>
                      <control
                        code="LBL"
                        id="e2bf93ec-758c-4894-962a-40f1b30386a5"
                        binding="">
                        <placeholder
                          name="Caption"
                          value="calc(TemporaryOrganizationWebURL.PU_URL)" />
                        <placeholder
                          name="Typography"
                          value="text-md-default" />
                      </control>
                    </control>
                    <control
                      code="CLO"
                      id="df410388-ebb9-4b81-9ca3-4f00f3cb559c">
                      <placeholder
                        name="Sentiment"
                        value="critical" />
                      <placeholder
                        name="Description"
                        value="Identified as an organization of potential risk"
                        resid="aba87f5b-87b9-4c1c-84aa-343ca9327e45" />
                      <placeholder
                        name="VisibilityCondition"
                        value="Organization.HasRisk" />
                      <placeholder
                        name="Margin"
                        value="mt-2" />
                    </control>
                  </control>
                </control>
              </control>
              <control
                code="BTN"
                id="52c5c747-6ec0-421b-9cc4-a9ae74a82976"
                binding="">
                <placeholder
                  name="Caption"
                  value="New Temp. Org."
                  resid="65bfe5ab-e142-4199-b051-186a8be0aa7f" />
                <placeholder
                  name="Columns"
                  value="col-xl-3" />
                <placeholder
                  name="Margin"
                  value="mt-4" />
                <placeholder
                  name="Color"
                  value="white" />
                <placeholder
                  name="LeadingIcon"
                  value="mdi-plus-circle-outline" />
                <placeholder
                  name="Transition"
                  value="True" />
                <placeholder
                  name="VisibilityCondition"
                  value="false" />
              </control>
              <control
                code="SRC"
                id="fc207921-ec8f-485c-962f-6ab07a5e9890"
                binding="COP_GS_NKSalesPerson">
                <placeholder
                  name="CaptionOverride"
                  value="Sales person"
                  resid="01881ee8-8279-47f0-9d91-179bbbeffeee" />
                <placeholder
                  name="FieldConfiguration">
                  <xml>
                    <fields xmlns="">
                      <field
                        path="GS_Code"
                        width="300"
                        mode="Default" />
                      <field
                        path="GS_FullName"
                        width="300"
                        mode="Default" />
                      <field
                        path="GS_IsActive"
                        width="300"
                        mode="Default" />
                      <field
                        path="GS_IsDriver"
                        width="300"
                        mode="Optional" />
                      <field
                        path="GS_IsResource"
                        width="300"
                        mode="Optional" />
                      <field
                        path="GS_IsSalesRep"
                        width="300"
                        mode="Default" />
                      <field
                        path="GS_IsSystemAccount"
                        width="300"
                        mode="Optional" />
                      <field
                        path="GS_PER"
                        width="300"
                        mode="Optional" />
                    </fields>
                  </xml>
                </placeholder>
              </control>
              <control
                code="SRC"
                id="32cf5dcf-2b16-4e2d-95eb-9b89d6138aec"
                binding="COP_SalesType">
                <placeholder
                  name="CaptionOverride"
                  value="Sales type"
                  resid="fad988dd-1c52-43cd-85d6-210d0bffe223" />
                <placeholder
                  name="Columns"
                  value="col-lg-6" />
              </control>
              <control
                code="SRC"
                id="6f403423-c6b9-4717-8741-d1d0c6a63964"
                binding="COP_ProductType">
                <placeholder
                  name="Columns"
                  value="col-lg-6" />
                <placeholder
                  name="CaptionOverride"
                  value="Product type"
                  resid="fe88e876-f13c-40a6-a258-28b102d31c8c" />
              </control>
              <control
                code="BOX"
                id="56a9d86c-050b-4967-97e9-cc0e524bb0b1">
                <placeholder
                  name="Layout"
                  value="grid" />
                <control
                  code="DAE"
                  id="cce8dd78-3b61-435e-ba58-b3aaa77e1d4b"
                  binding="COP_EffectiveStartDate">
                  <placeholder
                    name="Columns"
                    value="col-md-6" />
                  <placeholder
                    name="CaptionOverride"
                    value="Effective from"
                    resid="686baa77-66db-4b1e-a9a4-0e0692f28aac" />
                </control>
                <control
                  code="DAE"
                  id="cfdc8294-d856-4000-9226-17987250dbc7"
                  binding="COP_EffectiveEndDate">
                  <placeholder
                    name="Columns"
                    value="col-md-6" />
                  <placeholder
                    name="CaptionOverride"
                    value="Effective to"
                    resid="b45014f8-c101-4435-bd86-7ad452cf7b9c" />
                </control>
              </control>
              <control
                code="RDT"
                id="7e97480d-cf18-418b-a932-e7776a943872"
                binding="CrmOpportunityContacts">
                <placeholder
                  name="AllowAdd"
                  value="True" />
                <placeholder
                  name="CaptionOverride"
                  value="Opportunity contacts"
                  resid="e1a465c8-c295-4c88-86e1-c49e58088eae" />
                <placeholder
                  name="InlineEdit"
                  value="row" />
                <placeholder
                  name="DefaultSortFields">
                  <xml>
                    <ArrayOfFieldSortDefinition xmlns:i="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://schemas.datacontract.org/2004/07/CargoWise.Glow.Infrastructure.Interfaces">
                      <FieldSortDefinition>
                        <FieldName>COC_Primary</FieldName>
                        <IsAscending>false</IsAscending>
                      </FieldSortDefinition>
                    </ArrayOfFieldSortDefinition>
                  </xml>
                </placeholder>
                <placeholder
                  name="AllowAttach"
                  value="False" />
                <placeholder
                  name="AllowDetach"
                  value="False" />
                <placeholder
                  name="ShowItemActions"
                  value="True" />
                <placeholder
                  name="ShowCustomize"
                  value="True" />
                <placeholder
                  name="HideDefaultFooter"
                  value="True" />
                <placeholder
                  name="ItemsPerPage"
                  value="ALL" />
                <placeholder
                  name="ShowToolbar"
                  value="small" />
                <placeholder
                  name="Padding"
                  value="pt-3 pb-3" />
                <placeholder
                  name="ActionMenuItems">
                  <xml>
                    <formFlows xmlns="">
                      <formFlow
                        inDialog="True">d2c6a6d5bac442ed9b7e5bc444b4f07d</formFlow>
                      <formFlow
                        inDialog="True">a69a9b9554d445b0b0390c1ce5b9a079</formFlow>
                    </formFlows>
                  </xml>
                </placeholder>
                <placeholder
                  name="FieldConfiguration">
                  <xml>
                    <fields xmlns="">
                      <field
                        path="COC_OC_Contact"
                        width="250"
                        mode="Default" />
                      <field
                        path="Contact.OC_Title"
                        width="250"
                        mode="Optional"
                        readOnly="true" />
                      <field
                        path="COC_Role"
                        width="40"
                        mode="Default" />
                      <field
                        path="COC_Primary"
                        width="70"
                        mode="Default" />
                      <field
                        path="Email"
                        width="250"
                        mode="Default" />
                      <field
                        path="Mobile"
                        width="250"
                        mode="Default" />
                      <field
                        path="EmailDeliveryStatus"
                        width="250"
                        mode="Default" />
                    </fields>
                  </xml>
                </placeholder>
                <placeholder
                  name="DisabledGridRowActions"
                  value="Documents" />
              </control>
              <control
                code="LBL"
                id="0cdaf850-a615-4139-888c-0dbe5deb29e6"
                binding="">
                <placeholder
                  name="Caption"
                  value="Lead source"
                  resid="f1812694-2808-4cfa-b0b8-97785d3a939d" />
                <placeholder
                  name="Typography"
                  value="title-sm-default" />
              </control>
              <control
                code="SRC"
                id="d5cff479-a543-4246-986c-3dd7b82577e7"
                binding="COP_SourceType">
                <placeholder
                  name="CaptionOverride"
                  value="Source type"
                  resid="22a90eb0-4534-4ebc-b970-c93f2c4f0a62" />
                <placeholder
                  name="Columns"
                  value="col-lg-6" />
              </control>
              <control
                code="TXT"
                id="1c98fa33-224b-4256-9911-1a6ace4ce283"
                binding="COP_SourceDetails">
                <placeholder
                  name="CaptionOverride"
                  value="Source details"
                  resid="cdc6dcfc-4ee6-4147-8c08-7b5a8a6d2fcd" />
                <placeholder
                  name="Columns"
                  value="col-lg-6" />
              </control>
              <control
                code="SRC"
                id="fc985183-34c7-4ef0-86c7-f0b8d931fed1"
                binding="COP_OH_ReferringOrganization">
                <placeholder
                  name="CaptionOverride"
                  value="Referring organization"
                  resid="4c6026cb-5a01-457c-9fa9-4374e464d11f" />
                <placeholder
                  name="Columns"
                  value="col-lg-6" />
              </control>
              <control
                code="SRC"
                id="0ad54857-434f-4a61-9225-16b173ba4ab3"
                binding="COP_OC_ReferringContact">
                <placeholder
                  name="CaptionOverride"
                  value="Referring contact"
                  resid="89e49792-be7e-453c-b3d9-0ca446981a31" />
                <placeholder
                  name="Columns"
                  value="col-lg-6" />
              </control>
            </control>
          </control>
          <control
            code="BOX"
            id="991be68d-71b5-4ed1-a28f-07bf726fc00f"
            binding="">
            <placeholder
              name="Columns"
              value="col-md-6" />
            <placeholder
              name="Layout"
              value="grid" />
            <control
              code="PNL"
              id="f892fed3-884c-4a81-bc78-4afb8e80ad97"
              binding="">
              <placeholder
                name="Caption"
                value="Custom fields"
                resid="e1e41ea7-d45e-47b4-8ab4-5be0568f3f43" />
              <placeholder
                name="Layout"
                value="grid" />
              <placeholder
                name="VisibilityCondition"
                value="HasCustomFields" />
              <control
                code="CUS"
                id="d1e91b5c-92f3-4538-b37f-d932d033bac2"
                binding="">
                <placeholder
                  name="ViewModel"
                  value="CustomFieldsViewModel" />
                <placeholder
                  name="ItemColumns"
                  value="col-md-6" />
              </control>
            </control>
            <control
              code="PNL"
              id="da8fe826-14e8-4aea-b84f-6cc7d428dfe8"
              binding="">
              <placeholder
                name="Caption"
                value="Customer notes"
                resid="b21422a6-f2bd-46c2-b32c-929f26330690" />
              <control
                code="RTF"
                id="4c53387b-3e91-4700-b96d-56784d008753"
                binding="COP_OpportunityNotes">
                <placeholder
                  name="VisibilityCondition"
                  value="true" />
                <placeholder
                  name="IsReadOnly"
                  value="False" />
                <placeholder
                  name="CaptionType"
                  value="none" />
              </control>
            </control>
            <control
              code="PNL"
              id="4f627b2b-dc65-4b0e-bb39-5d73cb1889eb">
              <placeholder
                name="VisibilityCondition"
                value="COP_IsRestricted" />
              <control
                code="RDT"
                id="da9fb447-2eb2-4482-998d-495f42be530f"
                binding="EntityStaffRestrictions">
                <placeholder
                  name="InlineEdit"
                  value="row" />
                <placeholder
                  name="AllowAdd"
                  value="True" />
                <placeholder
                  name="CaptionOverride"
                  value="Users with access"
                  resid="8c0a2922-05bd-458b-a35a-297f63320d91" />
                <placeholder
                  name="HideDefaultFooter"
                  value="True" />
                <placeholder
                  name="AllowAttach"
                  value="False" />
                <placeholder
                  name="ShowToolbar"
                  value="small" />
                <placeholder
                  name="AllowDetach"
                  value="False" />
                <placeholder
                  name="ShowCustomize"
                  value="True" />
                <placeholder
                  name="ItemsPerPage"
                  value="ALL" />
                <placeholder
                  name="Padding"
                  value="pt-3 pb-3" />
                <placeholder
                  name="DisabledGridRowActions"
                  value="Documents" />
              </control>
            </control>
          </control>
        </control>
      </control>
      <control
        code="TAB"
        id="942b79aa-a3e6-4aba-9b1d-40aa1a59458d">
        <placeholder
          name="Caption"
          value="Sales activity"
          resid="024c50b9-c0ad-47c4-968e-f98ae38ac3b9" />
      </control>
      <control
        code="TAI"
        id="0526cb9a-55be-4d3c-9765-134904420d40">
        <placeholder
          name="Padding"
          value="pa-2" />
        <control
          code="BOX"
          id="184e1261-6b1d-456f-843e-f7e02cbbf4dc"
          binding="">
          <control
            code="PNL"
            id="9d175b2b-c822-42aa-a195-d3fb4e250946"
            binding="">
            <placeholder
              name="Caption"
              value="-- To be implemented --"
              resid="08fab8e0-10a0-4933-a2e7-4bf21fd3470e" />
          </control>
        </control>
      </control>
      <control
        code="TAB"
        id="81e11a2e-578e-4d87-9c8d-198c0765831c">
        <placeholder
          name="Caption"
          value="Communications"
          resid="4fe772d8-2933-4fae-93af-460a7d2376c1" />
      </control>
      <control
        code="TAI"
        id="2d50f810-2c03-4556-8994-01cd0f4ad802">
        <placeholder
          name="Padding"
          value="pa-2" />
        <control
          code="BOX"
          id="30296603-eb50-4818-ad25-08c68835f57e"
          binding="">
          <placeholder
            name="Layout"
            value="grid" />
          <control
            code="BOX"
            id="4284d937-0819-4938-a956-7178ecffecd4"
            binding="">
            <placeholder
              name="Columns"
              value="col-lg-4" />
            <control
              code="PNL"
              id="65603951-5d82-4da7-ba12-80dd2bdec663"
              binding="">
              <control
                code="RDT"
                id="8df2e446-f244-4393-b04e-baf19bcc01f4"
                binding="SalesCalls">
                <placeholder
                  name="FieldConfiguration">
                  <xml>
                    <fields xmlns="">
                      <field
                        path="OQ_CallDate"
                        width="50"
                        mode="Mandatory" />
                      <field
                        path="OQ_CallSummary"
                        width="150"
                        mode="Mandatory" />
                      <field
                        path="OQ_Status"
                        width="100"
                        mode="Mandatory" />
                      <field
                        path="OQ_SystemCreateUser"
                        width="200"
                        mode="Optional" />
                      <field
                        path="OQ_SystemLastEditUser"
                        width="200"
                        mode="Optional" />
                      <field
                        path="OQ_SystemCreateTimeUtc"
                        width="200"
                        mode="Optional" />
                      <field
                        path="OQ_SystemLastEditTimeUtc"
                        width="200"
                        mode="Optional" />
                    </fields>
                  </xml>
                </placeholder>
                <placeholder
                  name="DefaultSortFields">
                  <xml>
                    <ArrayOfFieldSortDefinition xmlns:i="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://schemas.datacontract.org/2004/07/CargoWise.Glow.Infrastructure.Interfaces">
                      <FieldSortDefinition>
                        <FieldName>OQ_CallDate</FieldName>
                        <IsAscending>false</IsAscending>
                      </FieldSortDefinition>
                    </ArrayOfFieldSortDefinition>
                  </xml>
                </placeholder>
                <placeholder
                  name="CaptionOverride"
                  value="Communications"
                  resid="b806975f-561a-4b08-b979-4f179de974e0" />
                <placeholder
                  name="HideDefaultFooter"
                  value="True" />
                <placeholder
                  name="ItemsPerPage"
                  value="ALL" />
                <placeholder
                  name="AllowAdd"
                  value="True" />
                <placeholder
                  name="AllowAttach"
                  value="False" />
                <placeholder
                  name="ShowToolbar"
                  value="large" />
                <placeholder
                  name="ShowFilters"
                  value="True" />
                <placeholder
                  name="ShowCustomize"
                  value="True" />
                <placeholder
                  name="DisabledGridRowActions"
                  value="Documents" />
              </control>
            </control>
          </control>
          <control
            code="BOX"
            id="f6d4b745-c9b9-4298-9b73-f696dff99186"
            binding="">
            <placeholder
              name="Layout"
              value="grid" />
            <placeholder
              name="Columns"
              value="col-lg-8" />
            <placeholder
              name="VisibilityCondition"
              value="SalesCalls.Any()" />
            <control
              code="PNL"
              id="3141e9f0-be6a-4e4f-a11a-9a1f23b97f44"
              binding="">
              <placeholder
                name="Layout"
                value="grid" />
              <placeholder
                name="Columns"
                value="col-lg-6" />
              <placeholder
                name="Height"
                value="100%" />
              <control
                code="BOX"
                id="3a31712d-624a-433c-a334-4f55bba3f996"
                binding="">
                <placeholder
                  name="Layout"
                  value="flex" />
                <placeholder
                  name="FlexJustify"
                  value="justify-space-between" />
                <placeholder
                  name="FlexAlign"
                  value="align-center" />
                <control
                  code="LBL"
                  id="ac8e1d30-56ed-433e-9e6d-2b1ee6891ba4"
                  binding="">
                  <placeholder
                    name="Caption"
                    value="Communication details"
                    resid="c7da7268-01d9-4b34-88a1-f95e0c4ed636" />
                  <placeholder
                    name="Typography"
                    value="title-sm-default" />
                </control>
              </control>
              <control
                code="TXT"
                id="273b4422-f730-4758-b67a-3ed5b1b534eb"
                binding="SalesCalls/OQ_CallSummary">
                <placeholder
                  name="CaptionOverride"
                  value="Subject"
                  resid="5951cfe4-e554-4435-baf1-5b2c0793f83b" />
              </control>
              <control
                code="SRC"
                id="f6e85d18-330a-480f-b734-78d30e123d62"
                binding="SalesCalls/OQ_Status">
                <placeholder
                  name="Columns"
                  value="col-lg-6" />
                <placeholder
                  name="CaptionOverride"
                  value="Status"
                  resid="e7ee3155-2989-4e53-973b-e8ede5618be4" />
              </control>
              <control
                code="BOX"
                id="a1a5fdcd-7a35-41ee-9f77-451061d5bf40">
                <placeholder
                  name="Layout"
                  value="grid" />
                <placeholder
                  name="Columns"
                  value="col-lg-6" />
                <control
                  code="DTE"
                  id="fc31e74f-b5dd-4a1e-84fa-d723ed05308a"
                  binding="SalesCalls/OQ_NextCall">
                  <placeholder
                    name="Columns"
                    value="col-6" />
                  <placeholder
                    name="CaptionOverride"
                    value="Activity date"
                    resid="04b6fc2c-7e81-4584-9597-3f3389095a0b" />
                </control>
                <control
                  code="DTE"
                  id="b8909e9b-2d4d-4c09-8d7b-96beb6f80189"
                  binding="SalesCalls/OQ_CallDate">
                  <placeholder
                    name="Columns"
                    value="col-6" />
                  <placeholder
                    name="CaptionOverride"
                    value="Closed date"
                    resid="4f9b4085-53e2-46e3-9f11-83238389aeaf" />
                </control>
              </control>
              <control
                code="SRC"
                id="d08b6d29-f346-485e-96cd-ffbeb2a981f0"
                binding="SalesCalls/OQ_TypeOfCall">
                <placeholder
                  name="Columns"
                  value="col-lg-6" />
              </control>
              <control
                code="SRC"
                id="763fe29c-f0b6-4bb8-bdf6-8f4063a43f5c"
                binding="SalesCalls/OQ_Category">
                <placeholder
                  name="Columns"
                  value="col-lg-6" />
              </control>
              <control
                code="TXT"
                id="2150dbcc-33ca-4760-a9d0-9f382b7d3f5d"
                binding="SalesCalls/OQ_LocationText">
                <placeholder
                  name="CaptionOverride"
                  value="Location"
                  resid="d1786aca-980d-428b-ab22-69d521863899" />
              </control>
              <control
                code="SRC"
                id="9c5cecbb-5263-475a-835b-df3e737591b9"
                binding="SalesCalls/OQ_GS_NKSalesRep" />
            </control>
            <control
              code="PNL"
              id="5b33096e-b5a0-48c0-ad64-c93b3d040b4f"
              binding="">
              <placeholder
                name="Columns"
                value="col-lg-6" />
              <placeholder
                name="Height"
                value="100%" />
              <control
                code="TBS"
                id="78a3cf7b-ba4e-4085-8035-39127012a16b">
                <control
                  code="TAB"
                  id="6fadc680-d305-4140-aa75-667d1ddad790">
                  <placeholder
                    name="Caption"
                    value="Communication notes"
                    resid="534c9d81-d483-4eff-944b-a5e56dc797ba" />
                </control>
                <control
                  code="TAI"
                  id="1b5bb0d5-4d12-4273-955d-9c37c8f645a7">
                  <control
                    code="RTF"
                    id="6f01e2c6-238e-4809-84ff-fa118f931eb0"
                    binding="SalesCalls/OQ_SalesCallNotes">
                    <placeholder
                      name="CaptionType"
                      value="none" />
                  </control>
                </control>
                <control
                  code="TAB"
                  id="c52b07fe-bb14-4f36-b7d0-d3f32546900a">
                  <placeholder
                    name="Caption"
                    value="Internal notes"
                    resid="742f5b88-849d-43bc-b5eb-8d09addd70a3" />
                </control>
                <control
                  code="TAI"
                  id="a28eba04-2198-4379-9472-dd08daf17261">
                  <control
                    code="RTF"
                    id="7844c7bf-db44-467a-88ce-782941d81145"
                    binding="SalesCalls/OQ_FollowupNotes">
                    <placeholder
                      name="CaptionType"
                      value="none" />
                  </control>
                </control>
              </control>
            </control>
            <control
              code="PNL"
              id="f8a72dd0-a734-4e12-a377-bf329acc3207"
              binding="">
              <control
                code="RDT"
                id="003b72f6-10fb-47fc-8745-64a79a6e914d"
                binding="SalesCalls/OrgSalesCallAdditionalAttendees">
                <placeholder
                  name="AllowAdd"
                  value="True" />
                <placeholder
                  name="InlineEdit"
                  value="row" />
                <placeholder
                  name="FieldConfiguration">
                  <xml>
                    <fields xmlns="">
                      <field
                        path="O6_ReceiverReminder"
                        width="100"
                        mode="Optional" />
                      <field
                        path="Type"
                        width="120"
                        mode="Default" />
                      <field
                        path="O6_AttendeeID"
                        width="300"
                        mode="Default" />
                      <field
                        path="O6_AttendeeName"
                        width="300"
                        mode="Default" />
                      <field
                        path="O6_EmailAddress"
                        width="300"
                        mode="Default" />
                      <field
                        path="EmailDeliveryStatus"
                        width="200"
                        mode="Default" />
                      <field
                        path="O6_Phone"
                        width="210"
                        mode="Default" />
                    </fields>
                  </xml>
                </placeholder>
                <placeholder
                  name="ItemsPerPage"
                  value="ALL" />
                <placeholder
                  name="HideDefaultFooter"
                  value="True" />
                <placeholder
                  name="CaptionOverride"
                  value="Attendees"
                  resid="7f286d74-fb90-4263-824f-bd083b023a3c" />
                <placeholder
                  name="ShowToolbar"
                  value="small" />
                <placeholder
                  name="DisabledGridRowActions"
                  value="Documents" />
              </control>
            </control>
          </control>
        </control>
      </control>
    </control>
  </form>
