#transformationVersion: 70.0
#
VZ_PK: ad71ebcdcfbd4b3898a16946cebae91a
VZ_ConfigurationKey: ad71ebcd-cfbd-4b38-98a1-6946cebae91a
VZ_FormID: ETL - VDV3 - Destination Depot - Edit Consol
VZ_Caption:
  resKey: VZ_Caption|ad71ebcd-cfbd-4b38-98a1-6946cebae91a
  text: Edit Consol
VZ_FormFactor: DSK
VZ_EntityType: IJobConsol
VZ_FormType: FRM
VZ_RequiresLogin: true
VZ_Dependencies: >-
  <dependencies xmlns="http://cargowise.com/glow/2014/11/14/dependencies.xsd">
    <expandPath
      path="SendingForwarderAddress.OrgHeader" />
    <expandPath
      path="SendingForwarderAddress" />
    <expandPath
      path="LoadPort" />
    <expandPath
      path="DischargePort" />
    <expandPath
      path="ReceivingForwarderAddress.OrgHeader" />
    <expandPath
      path="ReceivingForwarderAddress" />
    <expandPath
      path="ShippingLineAddress.OrgHeader" />
    <expandPath
      path="ShippingLineAddress" />
    <expandPath
      path="TransportLastLeg" />
    <expandPath
      path="CreditorAddress.OrgHeader" />
    <expandPath
      path="CreditorAddress" />
    <expandPath
      path="TransportFirstLeg.LoadPort" />
    <expandPath
      path="TransportFirstLeg" />
    <expandPath
      path="TransportLastLeg.DiscPort" />
    <expandPath
      path="ArrivalCTOAddress.OrgHeader" />
    <expandPath
      path="ArrivalCTOAddress" />
    <expandPath
      path="UnpackDepotAddress.OrgHeader" />
    <expandPath
      path="UnpackDepotAddress" />
    <expandPath
      path="ContainerYardEmptyReturnAddress.OrgHeader" />
    <expandPath
      path="ContainerYardEmptyReturnAddress" />
    <expandPath
      path="PortOfFirstArrival" />
    <expandPath
      path="JobShipments" />
    <calculatedProperty
      path="TransportLastLeg" />
    <calculatedProperty
      path="TransportFirstLeg" />
    <calculatedProperty
      path="CountAll" />
    <calculatedProperty
      path="CountHeld" />
    <calculatedProperty
      path="CountCleared" />
    <calculatedProperty
      path="CountScannedAll" />
    <calculatedProperty
      path="CountScannedHeld" />
    <calculatedProperty
      path="CountScannedCleared" />
    <calculatedProperty
      path="JobShipments" />
    <datagrid
      path="JobShipments">
      <expandPath
        path="BookedShippingLineAddress" />
      <expandPath
        path="HandledOnBehalfOfForwarder" />
      <expandPath
        path="CreatedByStaff" />
      <expandPath
        path="DeliveryAgent" />
      <expandPath
        path="ImportReleaseDepot" />
      <expandPath
        path="Destination" />
      <expandPath
        path="DischargePort" />
      <expandPath
        path="ExportBroker" />
      <expandPath
        path="FreightRateDestination" />
      <expandPath
        path="FreightRateOrigin" />
      <expandPath
        path="GatewayFreightSellRateCurrency" />
      <expandPath
        path="GoodsValueCurr" />
      <expandPath
        path="HouseBillIssuePlace" />
      <expandPath
        path="ImportBroker" />
      <expandPath
        path="TotalCountPackType" />
      <expandPath
        path="InsuranceCurrency" />
      <expandPath
        path="LastEditedByStaff" />
      <expandPath
        path="LoadPort" />
      <expandPath
        path="WhsLocation" />
      <expandPath
        path="ColoadMasterShipment" />
      <expandPath
        path="FreightCostRateCurrency" />
      <expandPath
        path="Origin" />
      <expandPath
        path="PackType" />
      <expandPath
        path="ExportReceivingDepot" />
      <expandPath
        path="PlaceOfDischarge" />
      <expandPath
        path="PlaceOfReceipt" />
      <expandPath
        path="JobSailing" />
      <expandPath
        path="ServiceLevel" />
      <expandPath
        path="SplitSwitchShipment" />
      <expandPath
        path="FrtRateCurrency" />
      <expandPath
        path="TranshipAgent" />
    </datagrid>
  </dependencies>
VZ_FormData: >-
  <form
    id="7a17402d-1a00-443b-b3df-b21b06abffc0" xmlns="http://wisetechglobal.com/glow/2017/05/26/form.xsd">
    <placeholder
      name="BackgroundImagePk"
      value="" />
    <placeholder
      name="BackgroundSize"
      value="Auto" />
    <placeholder
      name="BackgroundImageOverride"
      value="" />
    <placeholder
      name="FormLayout"
      value="Splitview" />
    <placeholder
      name="ExpandPaths"
      value="" />
    <placeholder
      name="ScanInputHandling"
      value="NON" />
    <placeholder
      name="RemovePadding"
      value="false" />
    <placeholder
      name="TurnOnPortableOneToOneDesignMode"
      value="false" />
    <placeholder
      name="ContainsDefaultButton"
      value="False" />
    <control
      code="GRP"
      id="f33bb716-7067-4726-89c9-c74cbd4f6cc3"
      top="1"
      width="29"
      height="20">
      <placeholder
        name="CanBeHidden"
        value="True" />
      <placeholder
        name="VisibilityCondition"
        value="" />
      <placeholder
        name="Header"
        value="New Section"
        resid="9a28ef6e-1fae-49c4-8ba3-f4f6aaaecdb9" />
      <placeholder
        name="HeaderAlignment"
        value="Left" />
      <placeholder
        name="HeaderDisplayMode"
        value="Hidden" />
      <placeholder
        name="BackgroundImagePk"
        value="" />
      <placeholder
        name="BackgroundImageOverride"
        value="" />
      <control
        code="FLB"
        id="12bce9a1-debf-4837-8c88-bb4ca2fceccf"
        left="0"
        top="2"
        width="4"
        height="1">
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="LabelText"
          value="Consol Details"
          resid="1b2a245e-b7b2-488f-a9f0-a60fdd76e7b3" />
        <placeholder
          name="LabelType"
          value="NormalLabel" />
        <placeholder
          name="LabelNotificationType"
          value="Information" />
        <placeholder
          name="ForegroundThemeColor"
          value="DefaultButton" />
        <placeholder
          name="TextSize"
          value="Small" />
        <placeholder
          name="FontWeight"
          value="Bold" />
        <placeholder
          name="LabelAlignment"
          value="CenterLeft" />
        <placeholder
          name="BackgroundColor"
          value="" />
        <placeholder
          name="Padding"
          value="None" />
      </control>
      <control
        code="FLB"
        id="31ea7998-fdc0-490a-ad92-6c7d00f2626a"
        left="15"
        top="2"
        width="4"
        height="1">
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="LabelText"
          value="Organizations"
          resid="797bb5fa-25b6-46b1-9255-7dff29a5ba76" />
        <placeholder
          name="LabelType"
          value="NormalLabel" />
        <placeholder
          name="LabelNotificationType"
          value="Information" />
        <placeholder
          name="ForegroundThemeColor"
          value="DefaultButton" />
        <placeholder
          name="TextSize"
          value="Small" />
        <placeholder
          name="FontWeight"
          value="Bold" />
        <placeholder
          name="LabelAlignment"
          value="CenterLeft" />
        <placeholder
          name="BackgroundColor"
          value="" />
        <placeholder
          name="Padding"
          value="None" />
      </control>
      <control
        code="CMB"
        id="035abc45-d967-4aaa-bb46-2998c6948982"
        left="0"
        top="3"
        width="4"
        height="1"
        binding="JK_AgentType">
        <placeholder
          name="IsReadOnly"
          value="False" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="DisplayMode"
          value="Unspecified" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="CMB"
        id="8bda38fe-d965-4ec1-bd93-d283e5977cca"
        left="4"
        top="3"
        width="4"
        height="1"
        binding="JK_TransportMode">
        <placeholder
          name="IsReadOnly"
          value="False" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="DisplayMode"
          value="Unspecified" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="CMB"
        id="376ae887-e15f-4892-bc69-b95602ef8812"
        left="8"
        top="3"
        width="4"
        height="1"
        binding="JK_ConsolMode">
        <placeholder
          name="IsReadOnly"
          value="False" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="DisplayMode"
          value="Unspecified" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="SRC"
        id="741fa89d-361c-47ff-a8ab-83bdcccb4829"
        left="15"
        top="3"
        width="4"
        height="1"
        binding="SendingForwarderAddress.OA_OH">
        <placeholder
          name="IsReadOnly"
          value="True" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="Sending Agent"
          resid="9597c3b4-4ac1-40ac-ac33-af3de2b954bd" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="DisplayMode"
          value="CodeOnly" />
        <placeholder
          name="FieldConfiguration"
          value="" />
        <placeholder
          name="DefaultFilter"
          value="" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="TXT"
        id="2c4064bc-0043-4bd9-b6a9-da7956df332b"
        left="19"
        top="3"
        width="5"
        height="1"
        binding="SendingForwarderAddress.OrgHeader.OH_FullName">
        <placeholder
          name="IsReadOnly"
          value="True" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="Sending Agent Name"
          resid="7ce2e4f7-2a67-4368-b479-aa6a771da30a" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="OnSubmit"
          value="" />
        <placeholder
          name="MultiLine"
          value="False" />
        <placeholder
          name="EnableAutoComplete"
          value="False" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="SRC"
        id="830a7576-8657-423d-a747-b90496d8ea4f"
        left="24"
        top="3"
        width="5"
        height="1"
        binding="JK_OA_SendingForwarderAddress">
        <placeholder
          name="IsReadOnly"
          value="False" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="Sending Agent Address"
          resid="5b67e51b-acfc-4086-a79d-04ebb6ab8e70" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="DisplayMode"
          value="CodeOnly" />
        <placeholder
          name="FieldConfiguration"
          value="" />
        <placeholder
          name="DefaultFilter"
          value="" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="SRC"
        id="39cddca6-6e18-44ba-8417-f2ef24b402c2"
        left="0"
        top="4"
        width="4"
        height="1"
        binding="JK_RL_NKLoadPort">
        <placeholder
          name="IsReadOnly"
          value="False" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="DisplayMode"
          value="CodeOnly" />
        <placeholder
          name="FieldConfiguration"
          value="" />
        <placeholder
          name="DefaultFilter"
          value="" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="SRC"
        id="bc1a0c19-1645-4a54-830c-aee8db42d39a"
        left="4"
        top="4"
        width="4"
        height="1"
        binding="JK_RL_NKDischargePort">
        <placeholder
          name="IsReadOnly"
          value="False" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="DisplayMode"
          value="CodeOnly" />
        <placeholder
          name="FieldConfiguration"
          value="" />
        <placeholder
          name="DefaultFilter"
          value="" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="TXT"
        id="4cf63812-e77d-42e9-8059-de737f24957f"
        left="8"
        top="4"
        width="4"
        height="1"
        binding="JK_Phase">
        <placeholder
          name="IsReadOnly"
          value="False" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="OnSubmit"
          value="" />
        <placeholder
          name="MultiLine"
          value="False" />
        <placeholder
          name="EnableAutoComplete"
          value="False" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="SRC"
        id="ff4204d0-3b02-4274-a00c-12639a1610b5"
        left="15"
        top="4"
        width="4"
        height="1"
        binding="ReceivingForwarderAddress.OA_OH">
        <placeholder
          name="IsReadOnly"
          value="True" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="Receiving Agent"
          resid="aa90fa6e-d100-4312-98d6-3b9e3ae71008" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="DisplayMode"
          value="CodeOnly" />
        <placeholder
          name="FieldConfiguration"
          value="" />
        <placeholder
          name="DefaultFilter"
          value="" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="TXT"
        id="9b1f47c3-41b9-4a0d-bd9b-6dfa879031ff"
        left="19"
        top="4"
        width="5"
        height="1"
        binding="ReceivingForwarderAddress.OrgHeader.OH_FullName">
        <placeholder
          name="IsReadOnly"
          value="True" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="Receiving Agent Name"
          resid="ed31d44f-6a88-4f7f-82a1-cd03b7564552" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="OnSubmit"
          value="" />
        <placeholder
          name="MultiLine"
          value="False" />
        <placeholder
          name="EnableAutoComplete"
          value="False" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="SRC"
        id="5d9626d2-2c07-4408-ba09-eb639e672e48"
        left="24"
        top="4"
        width="5"
        height="1"
        binding="JK_OA_ReceivingForwarderAddress">
        <placeholder
          name="IsReadOnly"
          value="False" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="Receiving Agent Address"
          resid="5a8bdef6-49b2-4554-ba2c-2499b72c38de" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="DisplayMode"
          value="CodeOnly" />
        <placeholder
          name="FieldConfiguration"
          value="" />
        <placeholder
          name="DefaultFilter"
          value="" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="FLB"
        id="7cfe3ea1-eeef-4988-9929-f56c7d203441"
        left="0"
        top="5"
        width="4"
        height="1">
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="LabelText"
          value="Flight/Voyage"
          resid="ec06aad3-bd80-435a-aaba-3e8ef0c79179" />
        <placeholder
          name="LabelType"
          value="NormalLabel" />
        <placeholder
          name="LabelNotificationType"
          value="Information" />
        <placeholder
          name="ForegroundThemeColor"
          value="DefaultButton" />
        <placeholder
          name="TextSize"
          value="Small" />
        <placeholder
          name="FontWeight"
          value="Bold" />
        <placeholder
          name="LabelAlignment"
          value="CenterLeft" />
        <placeholder
          name="BackgroundColor"
          value="" />
        <placeholder
          name="Padding"
          value="None" />
      </control>
      <control
        code="SRC"
        id="2a99185b-25dd-4d78-ac54-c26ca083028d"
        left="15"
        top="5"
        width="4"
        height="1"
        binding="ShippingLineAddress.OA_OH">
        <placeholder
          name="IsReadOnly"
          value="True" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="Carrier"
          resid="9737f61c-27be-40d7-a485-5c333227dff7" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="DisplayMode"
          value="CodeOnly" />
        <placeholder
          name="FieldConfiguration"
          value="" />
        <placeholder
          name="DefaultFilter"
          value="" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="TXT"
        id="d43d4eae-5234-4786-b930-f0ccd8e39ac2"
        left="19"
        top="5"
        width="5"
        height="1"
        binding="ShippingLineAddress.OrgHeader.OH_FullName">
        <placeholder
          name="IsReadOnly"
          value="True" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="Carrier Name"
          resid="cff84733-a0f4-449a-bcbf-10066a8bfef6" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="OnSubmit"
          value="" />
        <placeholder
          name="MultiLine"
          value="False" />
        <placeholder
          name="EnableAutoComplete"
          value="False" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="SRC"
        id="cdbb2dc1-36e0-4525-b52c-68a17c82884b"
        left="24"
        top="5"
        width="5"
        height="1"
        binding="JK_OA_ShippingLineAddress">
        <placeholder
          name="IsReadOnly"
          value="False" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="Carrier Address"
          resid="16073988-0be3-4375-924e-655f703c37b4" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="DisplayMode"
          value="CodeOnly" />
        <placeholder
          name="FieldConfiguration"
          value="" />
        <placeholder
          name="DefaultFilter"
          value="" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="TXT"
        id="f2bc4c02-e94d-4d78-97b9-a24720074ec4"
        left="0"
        top="6"
        width="4"
        height="1"
        binding="JK_MasterBillNum">
        <placeholder
          name="IsReadOnly"
          value="False" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="OnSubmit"
          value="" />
        <placeholder
          name="MultiLine"
          value="False" />
        <placeholder
          name="EnableAutoComplete"
          value="False" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="TXT"
        id="77687eb1-f112-4376-b68a-99d57844df58"
        left="4"
        top="6"
        width="4"
        height="1"
        binding="TransportLastLeg.JW_VoyageFlight">
        <placeholder
          name="IsReadOnly"
          value="False" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="OnSubmit"
          value="" />
        <placeholder
          name="MultiLine"
          value="False" />
        <placeholder
          name="EnableAutoComplete"
          value="False" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="SRC"
        id="fa945865-f153-4865-9f9d-4e4e27bad768"
        left="15"
        top="6"
        width="4"
        height="1"
        binding="CreditorAddress.OA_OH">
        <placeholder
          name="IsReadOnly"
          value="True" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="Creditor"
          resid="be5e5968-591a-4ae0-b0d8-846f434b3573" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="DisplayMode"
          value="CodeOnly" />
        <placeholder
          name="FieldConfiguration"
          value="" />
        <placeholder
          name="DefaultFilter"
          value="" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="TXT"
        id="abadd6de-44e3-42c5-8d13-803455387d37"
        left="19"
        top="6"
        width="5"
        height="1"
        binding="CreditorAddress.OrgHeader.OH_FullName">
        <placeholder
          name="IsReadOnly"
          value="True" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="Creditor Name"
          resid="4d18477a-b15b-442e-b05d-8b37790b8286" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="OnSubmit"
          value="" />
        <placeholder
          name="MultiLine"
          value="False" />
        <placeholder
          name="EnableAutoComplete"
          value="False" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="SRC"
        id="475b0b25-43de-469d-ad42-f2a414dabbda"
        left="24"
        top="6"
        width="5"
        height="1"
        binding="JK_OA_CreditorAddress">
        <placeholder
          name="IsReadOnly"
          value="False" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="Creditor Address"
          resid="4e8bbed2-7eef-4c51-a5dc-1fbbd54ea25f" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="DisplayMode"
          value="CodeOnly" />
        <placeholder
          name="FieldConfiguration"
          value="" />
        <placeholder
          name="DefaultFilter"
          value="" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="SRC"
        id="b165e1bc-b965-4568-8a95-712a90d485b2"
        left="0"
        top="7"
        width="4"
        height="1"
        binding="TransportFirstLeg.JW_RL_NKLoadPort">
        <placeholder
          name="IsReadOnly"
          value="False" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="DisplayMode"
          value="CodeOnly" />
        <placeholder
          name="FieldConfiguration"
          value="" />
        <placeholder
          name="DefaultFilter"
          value="" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="DTE"
        id="f7d36025-c2d7-4701-b622-aa0afb61f02b"
        left="4"
        top="7"
        width="4"
        height="1"
        binding="TransportFirstLeg.JW_ETD">
        <placeholder
          name="IsReadOnly"
          value="False" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="DefaultValueExpression"
          value="" />
        <placeholder
          name="IncludeTime"
          value="True" />
        <placeholder
          name="ShowNowButton"
          value="True" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="DTE"
        id="31e4587c-0b75-44a5-8e5f-9f9f4fdab6c7"
        left="8"
        top="7"
        width="4"
        height="1"
        binding="TransportFirstLeg.JW_ATD">
        <placeholder
          name="IsReadOnly"
          value="False" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="DefaultValueExpression"
          value="" />
        <placeholder
          name="IncludeTime"
          value="True" />
        <placeholder
          name="ShowNowButton"
          value="True" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="FLB"
        id="669a80c4-5b15-4660-97c5-88a8297e06dd"
        left="15"
        top="7"
        width="4"
        height="1">
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="LabelText"
          value="Arrival"
          resid="df4104e8-0415-499c-8e63-7f153bc34261" />
        <placeholder
          name="LabelType"
          value="NormalLabel" />
        <placeholder
          name="LabelNotificationType"
          value="Information" />
        <placeholder
          name="ForegroundThemeColor"
          value="DefaultButton" />
        <placeholder
          name="TextSize"
          value="Small" />
        <placeholder
          name="FontWeight"
          value="Bold" />
        <placeholder
          name="LabelAlignment"
          value="CenterLeft" />
        <placeholder
          name="BackgroundColor"
          value="" />
        <placeholder
          name="Padding"
          value="None" />
      </control>
      <control
        code="SRC"
        id="95b1edc6-f858-43b1-900b-0b9cfc3b7cd2"
        left="0"
        top="8"
        width="4"
        height="1"
        binding="TransportLastLeg.JW_RL_NKDiscPort">
        <placeholder
          name="IsReadOnly"
          value="False" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="DisplayMode"
          value="CodeOnly" />
        <placeholder
          name="FieldConfiguration"
          value="" />
        <placeholder
          name="DefaultFilter"
          value="" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="DTE"
        id="737c9086-8a35-4d60-98c1-b2b211038271"
        left="4"
        top="8"
        width="4"
        height="1"
        binding="TransportLastLeg.JW_ETA">
        <placeholder
          name="IsReadOnly"
          value="False" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="DefaultValueExpression"
          value="" />
        <placeholder
          name="IncludeTime"
          value="True" />
        <placeholder
          name="ShowNowButton"
          value="True" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="DTE"
        id="93ae9bb3-42e0-4c62-868b-bc15cacba230"
        left="8"
        top="8"
        width="4"
        height="1"
        binding="TransportLastLeg.JW_ATA">
        <placeholder
          name="IsReadOnly"
          value="False" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="DefaultValueExpression"
          value="" />
        <placeholder
          name="IncludeTime"
          value="True" />
        <placeholder
          name="ShowNowButton"
          value="True" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="SRC"
        id="45e267a9-268c-423f-a617-b7c79a1d1b24"
        left="15"
        top="8"
        width="4"
        height="1"
        binding="ArrivalCTOAddress.OA_OH">
        <placeholder
          name="IsReadOnly"
          value="True" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="CTO Address"
          resid="7b4e8f9b-2617-4e41-ad89-db7a5cb4825f" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="DisplayMode"
          value="CodeOnly" />
        <placeholder
          name="FieldConfiguration"
          value="" />
        <placeholder
          name="DefaultFilter"
          value="" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="TXT"
        id="9dbfa3f3-4f25-4108-8b93-b2ee677879ba"
        left="19"
        top="8"
        width="5"
        height="1"
        binding="ArrivalCTOAddress.OrgHeader.OH_FullName">
        <placeholder
          name="IsReadOnly"
          value="True" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="CTO Name"
          resid="6de8df3e-6124-435b-8a45-c58088622ea8" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="OnSubmit"
          value="" />
        <placeholder
          name="MultiLine"
          value="False" />
        <placeholder
          name="EnableAutoComplete"
          value="False" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="SRC"
        id="9830a215-0d96-4cc4-8b08-bb480d10b51c"
        left="24"
        top="8"
        width="5"
        height="1"
        binding="JK_OA_ArrivalCTOAddress">
        <placeholder
          name="IsReadOnly"
          value="False" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="DisplayMode"
          value="CodeOnly" />
        <placeholder
          name="FieldConfiguration"
          value="" />
        <placeholder
          name="DefaultFilter"
          value="" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="FLB"
        id="3ad16c47-ed1b-4354-a776-60887b758dad"
        left="0"
        top="9"
        width="4"
        height="1">
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="LabelText"
          value="HVL Counts"
          resid="cbe0edea-2cb6-4d75-9aeb-6617878e1857" />
        <placeholder
          name="LabelType"
          value="NormalLabel" />
        <placeholder
          name="LabelNotificationType"
          value="Information" />
        <placeholder
          name="ForegroundThemeColor"
          value="DefaultButton" />
        <placeholder
          name="TextSize"
          value="Small" />
        <placeholder
          name="FontWeight"
          value="Bold" />
        <placeholder
          name="LabelAlignment"
          value="CenterLeft" />
        <placeholder
          name="BackgroundColor"
          value="" />
        <placeholder
          name="Padding"
          value="None" />
      </control>
      <control
        code="SRC"
        id="9e19740a-6c79-4d15-be26-311803d45416"
        left="15"
        top="9"
        width="4"
        height="1"
        binding="UnpackDepotAddress.OA_OH">
        <placeholder
          name="IsReadOnly"
          value="True" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="CFS Address"
          resid="eeb0e43b-89c4-4a0a-bdb1-d6a3d0fe4d96" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="DisplayMode"
          value="CodeOnly" />
        <placeholder
          name="FieldConfiguration"
          value="" />
        <placeholder
          name="DefaultFilter"
          value="" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="TXT"
        id="e49e5575-c197-4cb1-ab7e-0126fafc101e"
        left="19"
        top="9"
        width="5"
        height="1"
        binding="UnpackDepotAddress.OrgHeader.OH_FullName">
        <placeholder
          name="IsReadOnly"
          value="True" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="CFS Name"
          resid="ed13b6a8-37af-418f-8874-cba7d0859699" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="OnSubmit"
          value="" />
        <placeholder
          name="MultiLine"
          value="False" />
        <placeholder
          name="EnableAutoComplete"
          value="False" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="SRC"
        id="c54b7924-df05-42cc-8c00-8f872b6963b7"
        left="24"
        top="9"
        width="5"
        height="1"
        binding="JK_OA_UnpackDepotAddress">
        <placeholder
          name="IsReadOnly"
          value="False" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="DisplayMode"
          value="CodeOnly" />
        <placeholder
          name="FieldConfiguration"
          value="" />
        <placeholder
          name="DefaultFilter"
          value="" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="NUM"
        id="952d20ce-0631-4499-9aa7-09654918f3c6"
        left="0"
        top="10"
        width="4"
        height="1"
        binding="CountAll">
        <placeholder
          name="IsReadOnly"
          value="True" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="Total Items"
          resid="7c4574e2-ab2d-4913-b0d0-f6f3cded079f" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="NumericFieldLeftAlignment"
          value="False" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="NUM"
        id="9c668b68-2aab-471f-ad92-72a47c821d23"
        left="4"
        top="10"
        width="4"
        height="1"
        binding="CountHeld">
        <placeholder
          name="IsReadOnly"
          value="True" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="Held Items"
          resid="426aae59-7f5e-43e5-be4b-ad5c0d727f33" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="NumericFieldLeftAlignment"
          value="False" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="NUM"
        id="4bed30f4-6b81-4883-b045-04924a0c73f3"
        left="8"
        top="10"
        width="4"
        height="1"
        binding="CountCleared">
        <placeholder
          name="IsReadOnly"
          value="True" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="Cleared Items"
          resid="f18f657e-a299-4355-8591-fad97bed2c6e" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="NumericFieldLeftAlignment"
          value="False" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="SRC"
        id="85c571fd-3f22-465c-b91e-e796ec015464"
        left="15"
        top="10"
        width="4"
        height="1"
        binding="ContainerYardEmptyReturnAddress.OA_OH">
        <placeholder
          name="IsReadOnly"
          value="True" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="Container Yard"
          resid="c3f8606a-e25c-4137-9f2e-aab9633ac61d" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="DisplayMode"
          value="CodeOnly" />
        <placeholder
          name="FieldConfiguration"
          value="" />
        <placeholder
          name="DefaultFilter"
          value="" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="TXT"
        id="28d339dd-9b01-427f-95c2-b75fe0034bcd"
        left="19"
        top="10"
        width="5"
        height="1"
        binding="ContainerYardEmptyReturnAddress.OrgHeader.OH_FullName">
        <placeholder
          name="IsReadOnly"
          value="True" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="Container Yard Name"
          resid="e8a3123b-387a-4475-b131-dcbfb71e0ed2" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="OnSubmit"
          value="" />
        <placeholder
          name="MultiLine"
          value="False" />
        <placeholder
          name="EnableAutoComplete"
          value="False" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="SRC"
        id="318dc8af-6a78-4c34-9968-9126e0e2bb12"
        left="24"
        top="10"
        width="5"
        height="1"
        binding="JK_OA_ContainerYardEmptyReturnAddress">
        <placeholder
          name="IsReadOnly"
          value="False" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="DisplayMode"
          value="CodeOnly" />
        <placeholder
          name="FieldConfiguration"
          value="" />
        <placeholder
          name="DefaultFilter"
          value="" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="NUM"
        id="5b267107-7287-4206-aa7d-11c9aa4c776a"
        left="0"
        top="11"
        width="4"
        height="1"
        binding="CountScannedAll">
        <placeholder
          name="IsReadOnly"
          value="True" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="Scanned Items"
          resid="26ff3c60-0eac-4ec5-9a27-ef3a99ea2be1" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="NumericFieldLeftAlignment"
          value="False" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="NUM"
        id="d4112839-571c-4302-b870-46c237e85951"
        left="4"
        top="11"
        width="4"
        height="1"
        binding="CountScannedHeld">
        <placeholder
          name="IsReadOnly"
          value="True" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="Scanned Held"
          resid="db8b9c0c-996c-4dcd-b39c-ee1ebe7d86a3" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="NumericFieldLeftAlignment"
          value="False" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="NUM"
        id="7c7b3f39-a4f4-41f4-8de5-cc76fe37a7d0"
        left="8"
        top="11"
        width="4"
        height="1"
        binding="CountScannedCleared">
        <placeholder
          name="IsReadOnly"
          value="True" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="Scanned Clear"
          resid="16aec79d-ccb0-4162-a34c-9e76bc8a788a" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="NumericFieldLeftAlignment"
          value="False" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="SRC"
        id="64a5bd8d-1269-4912-9ae4-f71654b18040"
        left="15"
        top="11"
        width="4"
        height="1"
        binding="JK_RL_NKPortOfFirstArrival">
        <placeholder
          name="IsReadOnly"
          value="False" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="DisplayMode"
          value="CodeOnly" />
        <placeholder
          name="FieldConfiguration"
          value="" />
        <placeholder
          name="DefaultFilter"
          value="" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="DTE"
        id="8967aa40-28b4-4368-bdd4-da2468315971"
        left="19"
        top="11"
        width="5"
        height="1"
        binding="JK_DatePortOfFirstArrival">
        <placeholder
          name="IsReadOnly"
          value="False" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="DefaultValueExpression"
          value="" />
        <placeholder
          name="IncludeTime"
          value="True" />
        <placeholder
          name="ShowNowButton"
          value="True" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="GRD"
        id="264b2a15-d49b-4eb4-849d-4d954695d144"
        left="0"
        top="12"
        width="29"
        height="7"
        binding="JobShipments">
        <placeholder
          name="IsReadOnly"
          value="False" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="CaptionOverride"
          value="Shipments"
          resid="6ad2bfa2-f6cd-4b7c-889c-2adfca685eab" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="HeaderAlignment"
          value="Left" />
        <placeholder
          name="HeaderDisplayMode"
          value="Visible" />
        <placeholder
          name="AllowAdd"
          value="False" />
        <placeholder
          name="AllowAttach"
          value="True" />
        <placeholder
          name="AllowDetach"
          value="True" />
        <placeholder
          name="AllowInlineEdit"
          value="True" />
        <placeholder
          name="FieldConfiguration">
          <xml>
            <fields xmlns="">
              <field
                path="JS_ShipmentType"
                width="150"
                mode="Optional" />
              <field
                path="JS_UniqueConsignRef"
                width="150"
                mode="Optional" />
              <field
                path="ConsignorName"
                width="175"
                mode="Optional" />
              <field
                path="JS_HouseBill"
                width="150"
                mode="Optional" />
              <field
                path="JS_OuterPacks"
                width="70"
                mode="Optional" />
              <field
                path="JS_ActualWeightMeasure"
                width="130"
                mode="Optional" />
              <field
                path="JS_ActualVolumeMeasure"
                width="130"
                mode="Optional" />
              <field
                path="JS_ActualChargeable"
                width="100"
                mode="Optional" />
              <field
                path="ItemCount"
                width="100"
                mode="Optional" />
              <field
                path="HeldCount"
                width="100"
                mode="Optional" />
              <field
                path="ClearCount"
                width="100"
                mode="Optional" />
              <field
                path="SurplusCount"
                width="99"
                mode="Optional" />
              <field
                path="JS_AdditionalTerms"
                width="250"
                mode="Optional" />
              <field
                path="InternationalArrivalConsol"
                width="140"
                mode="Optional" />
              <field
                path="JS_A_BKD"
                width="180"
                mode="Optional" />
              <field
                path="JS_IsBooking"
                width="70"
                mode="Optional" />
              <field
                path="BookingParty"
                width="130"
                mode="Optional" />
              <field
                path="JS_CFSReference"
                width="200"
                mode="Optional" />
              <field
                path="CargoReportSent"
                width="170"
                mode="Optional" />
              <field
                path="JS_OA_BookedShippingLineAddress"
                width="250"
                mode="Optional" />
              <field
                path="JS_ManifestedLoadingMeters"
                width="120"
                mode="Optional" />
              <field
                path="JS_ManifestedVolumeMeasure"
                width="130"
                mode="Optional" />
              <field
                path="JS_ManifestedWeightMeasure"
                width="130"
                mode="Optional" />
              <field
                path="JS_ManifestedChargeable"
                width="100"
                mode="Optional" />
              <field
                path="JS_PL_NKCarrierServiceLevel"
                width="210"
                mode="Optional" />
              <field
                path="JS_HBLAWBChargesDisplay"
                width="70"
                mode="Optional" />
              <field
                path="JS_OH_HandledOnBehalfOfForwarder"
                width="250"
                mode="Optional" />
              <field
                path="JS_DocumentedChargeable"
                width="100"
                mode="Optional" />
              <field
                path="JS_DocumentedLoadingMeters"
                width="120"
                mode="Optional" />
              <field
                path="JS_DocumentedVolumeMeasure"
                width="130"
                mode="Optional" />
              <field
                path="JS_DocumentedWeightMeasure"
                width="130"
                mode="Optional" />
              <field
                path="JS_ClientRequestedETA"
                width="180"
                mode="Optional" />
              <field
                path="JS_IsForwardRegistered"
                width="90"
                mode="Optional" />
              <field
                path="ConsigneeDeliveryAddress"
                width="250"
                mode="Optional" />
              <field
                path="ConsigneeDocumentaryAddress"
                width="170"
                mode="Optional" />
              <field
                path="ConsignorDocumentaryAddress"
                width="170"
                mode="Optional" />
              <field
                path="ConsignorPickupAddress"
                width="240"
                mode="Optional" />
              <field
                path="JS_ConsolReference"
                width="200"
                mode="Optional" />
              <field
                path="JS_PackingMode"
                width="50"
                mode="Optional" />
              <field
                path="CountAll"
                width="90"
                mode="Optional" />
              <field
                path="CountCleared"
                width="130"
                mode="Optional" />
              <field
                path="CountHeld"
                width="100"
                mode="Optional" />
              <field
                path="CountNon"
                width="90"
                mode="Optional" />
              <field
                path="CountScannedAll"
                width="170"
                mode="Optional" />
              <field
                path="CountScannedCleared"
                width="210"
                mode="Optional" />
              <field
                path="CountScannedHeld"
                width="180"
                mode="Optional" />
              <field
                path="CountScannedNon"
                width="170"
                mode="Optional" />
              <field
                path="CountShortItem"
                width="160"
                mode="Optional" />
              <field
                path="CountSurplusItem"
                width="180"
                mode="Optional" />
              <field
                path="JS_SystemCreateUser"
                width="160"
                mode="Optional" />
              <field
                path="CurrentBranchJobHeader"
                width="250"
                mode="Optional" />
              <field
                path="JS_OH_DeliveryAgent"
                width="250"
                mode="Optional" />
              <field
                path="JS_OA_ImportReleaseDepot"
                width="250"
                mode="Optional" />
              <field
                path="JS_RL_NKDestination"
                width="50"
                mode="Optional" />
              <field
                path="JS_IsDirectBooking"
                width="70"
                mode="Optional" />
              <field
                path="JS_RL_NKDischargePort"
                width="140"
                mode="Optional" />
              <field
                path="DocsAndCartageDetail"
                width="230"
                mode="Optional" />
              <field
                path="DomesticPaymentTerms"
                width="250"
                mode="Optional" />
              <field
                path="JS_E_ARV"
                width="180"
                mode="Optional" />
              <field
                path="JS_E_DEP"
                width="180"
                mode="Optional" />
              <field
                path="JS_OH_ExportBroker"
                width="250"
                mode="Optional" />
              <field
                path="JS_ExportReceivingDepotDispatchRequested"
                width="250"
                mode="Optional" />
              <field
                path="JS_ExportReceivingDepotReceiptRequested"
                width="250"
                mode="Optional" />
              <field
                path="FreightLabelURL"
                width="250"
                mode="Optional" />
              <field
                path="JS_RL_NKFreightRateDestination"
                width="240"
                mode="Optional" />
              <field
                path="JS_RL_NKFreightRateOrigin"
                width="190"
                mode="Optional" />
              <field
                path="JS_TranshipToOtherCFS"
                width="180"
                mode="Optional" />
              <field
                path="JS_GatewayFreightSellRate"
                width="200"
                mode="Optional" />
              <field
                path="JS_FreightGatewaySellRateAutoratingMode"
                width="40"
                mode="Optional" />
              <field
                path="JS_RX_NKGatewayFreightSellRateCurrency"
                width="50"
                mode="Optional" />
              <field
                path="JS_GoodsDescription"
                width="250"
                mode="Optional" />
              <field
                path="JS_GoodsValue"
                width="200"
                mode="Optional" />
              <field
                path="JS_RX_NKGoodsValueCurr"
                width="50"
                mode="Optional" />
              <field
                path="JS_NoCopyBills"
                width="70"
                mode="Optional" />
              <field
                path="JS_HBLContainerPackModeOverride"
                width="130"
                mode="Optional" />
              <field
                path="JS_RL_NKHouseBillIssuePlace"
                width="140"
                mode="Optional" />
              <field
                path="HouseBillURL"
                width="250"
                mode="Optional" />
              <field
                path="JS_HouseBillOfLadingType"
                width="80"
                mode="Optional" />
              <field
                path="JS_INCO"
                width="40"
                mode="Optional" />
              <field
                path="JS_OH_ImportBroker"
                width="250"
                mode="Optional" />
              <field
                path="JS_ImportReleaseDepotDispatchRequested"
                width="250"
                mode="Optional" />
              <field
                path="JS_ImportReleaseDepotReceiptRequested"
                width="250"
                mode="Optional" />
              <field
                path="JS_TotalPackageCount"
                width="70"
                mode="Optional" />
              <field
                path="JS_F3_NKTotalCountPackType"
                width="40"
                mode="Optional" />
              <field
                path="JS_InsuranceValue"
                width="200"
                mode="Optional" />
              <field
                path="JS_RX_NKInsuranceCurrency"
                width="50"
                mode="Optional" />
              <field
                path="JS_InterimReceipt"
                width="250"
                mode="Optional" />
              <field
                path="JS_A_RCV"
                width="180"
                mode="Optional" />
              <field
                path="JS_IsCFSRegistered"
                width="170"
                mode="Optional" />
              <field
                path="JS_IsCancelled"
                width="120"
                mode="Optional" />
              <field
                path="IsDomesticShipment"
                width="200"
                mode="Optional" />
              <field
                path="JS_IsHighRisk"
                width="120"
                mode="Optional" />
              <field
                path="JS_IsNeutralMaster"
                width="170"
                mode="Optional" />
              <field
                path="JS_IsShipping"
                width="110"
                mode="Optional" />
              <field
                path="JS_IsSplitShipment"
                width="170"
                mode="Optional" />
              <field
                path="JS_HouseBillIssueDate"
                width="180"
                mode="Optional" />
              <field
                path="JS_SystemLastEditUser"
                width="200"
                mode="Optional" />
              <field
                path="LastLoginOrganization"
                width="250"
                mode="Optional" />
              <field
                path="LastOutturnDate"
                width="170"
                mode="Optional" />
              <field
                path="LastScanEvent"
                width="150"
                mode="Optional" />
              <field
                path="JS_Legacy_Support_Columns_Start"
                width="250"
                mode="Optional" />
              <field
                path="JS_RL_NKLoadPort"
                width="90"
                mode="Optional" />
              <field
                path="JS_LoadingMeters"
                width="100"
                mode="Optional" />
              <field
                path="JS_WL"
                width="250"
                mode="Optional" />
              <field
                path="JS_JS_ColoadMasterShipment"
                width="250"
                mode="Optional" />
              <field
                path="MilestonesTextSummary"
                width="250"
                mode="Optional" />
              <field
                path="JS_FreightCostRate"
                width="200"
                mode="Optional" />
              <field
                path="JS_FreightCostRateAutoratingMode"
                width="40"
                mode="Optional" />
              <field
                path="JS_RX_NKFreightCostRateCurrency"
                width="50"
                mode="Optional" />
              <field
                path="NotifyPartyAddress"
                width="200"
                mode="Optional" />
              <field
                path="JS_ShippedOnBoardDate"
                width="180"
                mode="Optional" />
              <field
                path="OrderCount"
                width="110"
                mode="Optional" />
              <field
                path="JS_RL_NKOrigin"
                width="60"
                mode="Optional" />
              <field
                path="JS_NoOriginalBills"
                width="90"
                mode="Optional" />
              <field
                path="JS_F3_NKPackType"
                width="40"
                mode="Optional" />
              <field
                path="JS_OverrideWaybillDefaults"
                width="250"
                mode="Optional" />
              <field
                path="JS_PackingOrder"
                width="130"
                mode="Optional" />
              <field
                path="JS_PaymentTermAutoratingOverride"
                width="250"
                mode="Optional" />
              <field
                path="JS_Phase"
                width="50"
                mode="Optional" />
              <field
                path="JS_OA_ExportReceivingDepot"
                width="250"
                mode="Optional" />
              <field
                path="JS_RL_NKPlaceOfDischarge"
                width="180"
                mode="Optional" />
              <field
                path="JS_RL_NKPlaceOfReceipt"
                width="160"
                mode="Optional" />
              <field
                path="JS_CartageWaybill"
                width="200"
                mode="Optional" />
              <field
                path="JS_SystemCreateTimeUtc"
                width="180"
                mode="Optional" />
              <field
                path="JS_ReleaseType"
                width="70"
                mode="Optional" />
              <field
                path="JS_JX"
                width="250"
                mode="Optional" />
              <field
                path="JS_ScreeningStatus"
                width="50"
                mode="Optional" />
              <field
                path="JS_RS_NKServiceLevel"
                width="90"
                mode="Optional" />
              <field
                path="ShipmentAndPacksPackageCountMatches"
                width="250"
                mode="Optional" />
              <field
                path="ShipmentAndPacksVolumeMatches"
                width="250"
                mode="Optional" />
              <field
                path="ShipmentAndPacksWeightMatches"
                width="250"
                mode="Optional" />
              <field
                path="JS_ShippedOnBoard"
                width="80"
                mode="Optional" />
              <field
                path="JS_ShipperCODAmount"
                width="200"
                mode="Optional" />
              <field
                path="JS_ShipperCODPayMethod"
                width="40"
                mode="Optional" />
              <field
                path="JS_BookingReference"
                width="200"
                mode="Optional" />
              <field
                path="ShippingPaymentTerms"
                width="250"
                mode="Optional" />
              <field
                path="JS_JS_SplitSwitchShipment"
                width="250"
                mode="Optional" />
              <field
                path="JS_UnitFreightRate"
                width="200"
                mode="Optional" />
              <field
                path="JS_FreightSpotRateAutoratingMode"
                width="40"
                mode="Optional" />
              <field
                path="JS_RX_NKFrtRateCurrency"
                width="50"
                mode="Optional" />
              <field
                path="JS_ShipmentStatus"
                width="60"
                mode="Optional" />
              <field
                path="JS_SystemLastEditTimeUtc"
                width="250"
                mode="Optional" />
              <field
                path="TotalNumberofOuterPacks"
                width="250"
                mode="Optional" />
              <field
                path="TotalVolumeofOuterPacks"
                width="250"
                mode="Optional" />
              <field
                path="TotalWeightofOuterPacks"
                width="250"
                mode="Optional" />
              <field
                path="JS_OH_TranshipAgent"
                width="250"
                mode="Optional" />
              <field
                path="JS_TransportMode"
                width="60"
                mode="Optional" />
              <field
                path="JS_WarehouseLocation"
                width="100"
                mode="Optional" />
              <field
                path="JS_EFreightStatus"
                width="160"
                mode="Optional" />
              <field
                path="InternationalArrivalConsol.JK_MasterBillNum"
                width="100"
                mode="Default" />
              <field
                path="InternationalArrivalConsol.TransportLastLeg.JW_RL_NKLoadPort"
                width="100"
                mode="Default" />
              <field
                path="InternationalArrivalConsol.TransportLastLeg.JW_RL_NKDiscPort"
                width="100"
                mode="Default" />
              <field
                path="ConsigneeDocumentaryAddress.EffectiveCompanyName"
                width="250"
                mode="Optional" />
              <field
                path="InternationalArrivalConsol.TransportFirstLeg.JW_ETD"
                width="120"
                mode="Default" />
              <field
                path="InternationalArrivalConsol.TransportLastLeg.JW_ETA"
                width="120"
                mode="Default" />
              <field
                path="InternationalArrivalConsol.TransportFirstLeg.JW_ATD"
                width="120"
                mode="Default" />
              <field
                path="InternationalArrivalConsol.TransportLastLeg.JW_ATA"
                width="120"
                mode="Default" />
              <field
                path="InternationalArrivalConsol.TransportLastLeg.JW_VoyageFlight"
                width="150"
                mode="Default" />
              <field
                path="InternationalArrivalConsol.ShippingLineAddress.CompanyName"
                width="250"
                mode="Default" />
              <field
                path="InternationalArrivalConsol.ArrivalCTOAddress.CompanyName"
                width="250"
                mode="Default" />
              <field
                path="InternationalArrivalConsol.TransportLastLeg.JobSailing.JobVoyDestination.JB_AvailabilityDate"
                width="120"
                mode="Default" />
              <field
                path="InternationalArrivalConsol.UnpackDepotAddress.CompanyName"
                width="250"
                mode="Default" />
              <field
                path="DocsAndCartageDetail.JP_LCLAvailable"
                width="120"
                mode="Default" />
              <field
                path="JS_AttachedOrderXMLUpdateCutOffDateUtc"
                width="100"
                mode="Optional" />
              <field
                path="JS_CommunityTransitStatus"
                width="100"
                mode="Optional" />
              <field
                path="JS_VisibleTabs"
                width="100"
                mode="Optional" />
              <field
                path="ContactHasWebBookingViewRight"
                width="250"
                mode="Optional" />
              <field
                path="ContactHasWebCFSShipmentViewRight"
                width="250"
                mode="Optional" />
              <field
                path="HLRCargoReporting"
                width="190"
                mode="Optional" />
              <field
                path="HLROutturnEvent"
                width="170"
                mode="Optional" />
              <field
                path="MasterHouseUnderbondCount"
                width="250"
                mode="Optional" />
              <field
                path="CountShortHeld"
                width="160"
                mode="Optional" />
            </fields>
          </xml>
        </placeholder>
        <placeholder
          name="DefaultSortFields"
          value="" />
        <placeholder
          name="ShowDocumentsIcon"
          value="False" />
        <placeholder
          name="RowActionsMode"
          value="None" />
        <placeholder
          name="HideGridActions"
          value="False" />
        <placeholder
          name="HideFilters"
          value="False" />
        <placeholder
          name="ActionMenuItems"
          value="" />
        <placeholder
          name="EditFormFlow"
          value="" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
    </control>
  </form>
