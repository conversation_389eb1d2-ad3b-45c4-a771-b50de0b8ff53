#transformationVersion: 70.0
#
VZ_PK: 5d9c49176d64422ca8f5e73e87554f4b
VZ_ConfigurationKey: 5d9c4917-6d64-422c-a8f5-e73e87554f4b
VZ_FormID: HRM - Staff Profile
VZ_Caption:
  resKey: VZ_Caption|5d9c4917-6d64-422c-a8f5-e73e87554f4b
  text: Staff Profile
VZ_FormFactor: DSK
VZ_EntityType: IGlbStaff
VZ_FormType: FRM
VZ_RequiresLogin: true
VZ_MaterialDesign: true
VZ_Dependencies: <dependencies xmlns="http://cargowise.com/glow/2014/11/14/dependencies.xsd" />
VZ_FormData: >-
  <form
    id="a8ae2269-2924-43dc-9547-b3cef13569a4" xmlns="http://wisetechglobal.com/glow/2017/05/26/form.xsd">
    <control
      code="TBS"
      id="10bcd7f2-552a-46f2-bb9a-2c53145bd82f"
      binding="">
      <placeholder
        name="Application"
        value="True" />
      <control
        code="TAB"
        id="9bd0f476-16ff-4fd5-8328-7cf1c921d682">
        <placeholder
          name="Caption"
          value="Personal Details"
          resid="82169a48-4482-4783-b3bc-a15b92ad673a" />
      </control>
      <control
        code="TAI"
        id="bdd38d47-6bfd-4f9e-91df-3574f49adbb1">
        <control
          code="BOX"
          id="cb25de64-5967-48c2-b42f-e23fec7bdfaf"
          binding="">
          <placeholder
            name="Layout"
            value="grid" />
          <placeholder
            name="FlexWrap"
            value="flex-nowrap" />
          <control
            code="BOX"
            id="f9358209-6cae-428e-bc17-0bc84e1bdd47">
            <placeholder
              name="Columns"
              value="col-sm-6 col-md-4 col-lg-3" />
            <control
              code="PNL"
              id="9e588b06-6b0e-4653-a17c-769c1c671b02"
              binding="">
              <placeholder
                name="Caption"
                value="Details"
                resid="92bdccd5-31dd-4772-8a32-57ea26c81c02" />
              <control
                code="BOX"
                id="94e7e06d-925f-4fea-8320-d40f8468b0e4">
                <placeholder
                  name="Layout"
                  value="grid" />
                <control
                  code="BOX"
                  id="e6e8895b-d20b-4930-b1dc-86c81cff4020">
                  <placeholder
                    name="Layout"
                    value="grid" />
                  <control
                    code="TXT"
                    id="d27b13d9-1a78-427c-8592-44fc34a3d2f5"
                    binding="GS_Code">
                    <placeholder
                      name="Columns"
                      value="col-6" />
                  </control>
                </control>
                <control
                  code="TXT"
                  id="e5604909-a695-443b-826b-e57016a98e77"
                  binding="GS_GivenName" />
                <control
                  code="TXT"
                  id="957171b3-2f0c-4679-a1d8-befc9ed777cc"
                  binding="GS_MiddleName" />
                <control
                  code="TXT"
                  id="7db35375-4522-43ee-bab3-3b191354366b"
                  binding="GS_Surname" />
                <control
                  code="TXT"
                  id="be93b377-ca60-41ce-832c-f5d853482aad"
                  binding="GS_FriendlyName">
                  <placeholder
                    name="Margin"
                    value="mt-3" />
                </control>
                <control
                  code="TXT"
                  id="76ebfe98-8d5c-4279-93a5-0054259d4f07"
                  binding="GS_PreferredSurname" />
                <control
                  code="TXT"
                  id="11cff468-823c-4712-87c5-dc9705ce7617"
                  binding="GS_FullName" />
                <control
                  code="TXT"
                  id="d703b07c-0bf9-4bb2-9a98-0e59b7a4f98e"
                  binding="GS_FullNameInMotherLanguage" />
                <control
                  code="TXT"
                  id="ed94de2a-3210-4169-8001-ced51a8ca913"
                  binding="GS_LoginName">
                  <placeholder
                    name="Margin"
                    value="mt-3" />
                </control>
                <control
                  code="SRC"
                  id="01f0e597-eb69-43cc-98c6-1dbdb872c347"
                  binding="GS_DomainName" />
                <control
                  code="BOX"
                  id="9113fa41-a448-4070-b3cd-3c28166cfcb8">
                  <placeholder
                    name="Layout"
                    value="grid" />
                  <control
                    code="DAE"
                    id="b7c51ade-30af-4324-9be5-3abb85f850d9"
                    binding="GS_Birthdate">
                    <placeholder
                      name="Columns"
                      value="col-6" />
                  </control>
                  <control
                    code="SRC"
                    id="900cd291-6929-43ea-acc1-f9c085a3ab95"
                    binding="GS_Gender">
                    <placeholder
                      name="Columns"
                      value="col-6" />
                  </control>
                  <control
                    code="TXT"
                    id="ab34c2b2-398d-468c-82b6-d23beabe2552"
                    binding="GS_GenderCustomTerm">
                    <placeholder
                      name="VisibilityCondition"
                      value="GS_Gender == &quot;C&quot;" />
                    <placeholder
                      name="CaptionOverride"
                      value="Gender"
                      resid="70ed38f3-7d6a-4cf7-94b6-2215e34b79fc" />
                  </control>
                </control>
                <control
                  code="BOX"
                  id="a3dd2e8f-01b0-451f-8355-778ad71ebc7c"
                  binding="">
                  <placeholder
                    name="Layout"
                    value="flex" />
                  <placeholder
                    name="FlexJustify"
                    value="justify-space-around" />
                  <placeholder
                    name="Margin"
                    value="mt-2" />
                  <control
                    code="IMS"
                    id="573c11c9-06c7-4064-a5a4-1b84dd4f6416"
                    binding="GS_ProfilePhoto" />
                  <control
                    code="IMS"
                    id="74e4fe43-41a9-4581-981a-259d406e1d64"
                    binding="GS_UserSignature" />
                </control>
                <control
                  code="BOX"
                  id="92199d11-8d12-4049-b611-1125b5cc675b">
                  <placeholder
                    name="Layout"
                    value="flex" />
                  <placeholder
                    name="FlexJustify"
                    value="justify-space-around" />
                  <control
                    code="LBL"
                    id="03fb1911-5f72-46a3-acd6-7cfd07e4a148">
                    <placeholder
                      name="Caption"
                      value="Profile Photo"
                      resid="882de0f0-5e35-4757-8671-225cf3f1d752" />
                  </control>
                  <control
                    code="LBL"
                    id="dd1d93ee-4966-40ce-ab88-c677fd403763">
                    <placeholder
                      name="Caption"
                      value="Signature"
                      resid="39bed588-5e1f-4948-b54b-52ebb913a5a1" />
                  </control>
                </control>
              </control>
            </control>
          </control>
          <control
            code="BOX"
            id="eab25fe5-ca47-4371-94d0-e40cea23b2d6"
            binding="">
            <placeholder
              name="Layout"
              value="grid" />
            <placeholder
              name="Columns"
              value="col-sm-6 col-md-4 col-lg-3" />
            <control
              code="PNL"
              id="50761fdf-8d47-414a-9490-6b35b5ec45eb"
              binding="">
              <placeholder
                name="Caption"
                value="Contact"
                resid="ec27b1d6-1145-49dd-b2b7-9e2ba549e69c" />
              <control
                code="BOX"
                id="a5ceac71-7bc9-45a1-98be-fde566db61de">
                <placeholder
                  name="Layout"
                  value="grid" />
                <control
                  code="TXT"
                  id="64de167b-1e89-4184-b84b-c1b9e5f5d3f9"
                  binding="GS_EmailAddress">
                  <placeholder
                    name="CaptionOverride"
                    value="Email Address (Work)"
                    resid="29290fe3-5257-4bac-a1ef-a1fe60fb1a8f" />
                </control>
                <control
                  code="TXT"
                  id="8dbbf55d-6c98-4459-ac87-56862e70d5f0"
                  binding="GS_WorkPhone" />
                <control
                  code="TXT"
                  id="ed455497-76e4-4908-9037-5547fdcc9928"
                  binding="GS_MobilePhone" />
                <control
                  code="TXT"
                  id="c0b5fa4a-8906-47c7-9c4b-f207c39afc19"
                  binding="GS_HomePhone" />
                <control
                  code="OPT"
                  id="9c5e89d8-09e6-472d-8d16-a4909336b693"
                  binding="GS_PublishMobilePhone">
                  <placeholder
                    name="Margin"
                    value="mt-0" />
                </control>
                <control
                  code="OPT"
                  id="973bbf96-b798-4ecb-b109-27180c54eb6f"
                  binding="GS_PublishHomePhone">
                  <placeholder
                    name="Margin"
                    value="mt-0" />
                </control>
              </control>
            </control>
            <control
              code="PNL"
              id="49178794-eead-49fb-9e73-8077efa06086"
              binding="">
              <placeholder
                name="Caption"
                value="Next of kin"
                resid="7a59f845-013b-4ca6-b499-1ebdd0f8b99c" />
              <control
                code="BOX"
                id="dd3c720e-178b-4c1a-a6b6-c72a3a658a05">
                <placeholder
                  name="Layout"
                  value="grid" />
                <control
                  code="TXT"
                  id="a9a10b05-604c-4dad-8b4a-2fc28e643e1e"
                  binding="GS_NextOfKin">
                  <placeholder
                    name="CaptionOverride"
                    value="Name"
                    resid="81969f71-0fd9-4e9f-ba5c-0c3712a9045a" />
                </control>
                <control
                  code="TXT"
                  id="56691274-3d56-44f5-bacb-90a42993b782"
                  binding="GS_NextOfKinHomePhone">
                  <placeholder
                    name="CaptionOverride"
                    value="Phone Number"
                    resid="ca7cc53e-e4b0-4d68-b187-1e6ab7827a77" />
                </control>
                <control
                  code="TXT"
                  id="08bb66ca-f00d-412f-90a2-f40698289a5d"
                  binding="GS_NextOfKinEmail">
                  <placeholder
                    name="CaptionOverride"
                    value="Email Address"
                    resid="655dd129-a709-4eb4-b4d7-28035862dcc0" />
                </control>
                <control
                  code="SRC"
                  id="453ebbc2-e435-4c24-b265-784f46cd7a58"
                  binding="GS_NextOfKinRelationship">
                  <placeholder
                    name="CaptionOverride"
                    value="Relationship"
                    resid="32ceb67c-abba-4d2a-8bbd-80f4edbdecdd" />
                </control>
              </control>
            </control>
            <control
              code="PNL"
              id="500e29a0-bab8-479f-a9be-2e166db014fd"
              binding="">
              <placeholder
                name="Caption"
                value="Emergency contact"
                resid="4e6a6d2b-709e-4035-9ee5-b08026507481" />
              <control
                code="BOX"
                id="ba17b74a-4a21-4dfd-8e22-ce62fe4591b2">
                <placeholder
                  name="Layout"
                  value="grid" />
                <control
                  code="TXT"
                  id="88ee1037-04ca-4bc7-aeb5-513dd9b50ea7"
                  binding="GS_EmergencyContactName">
                  <placeholder
                    name="CaptionOverride"
                    value="Name"
                    resid="2712bca4-2d8b-4c0a-8a45-6e47b3df9a9d" />
                </control>
                <control
                  code="TXT"
                  id="153128ef-24c1-47d6-a004-7dd88c98cac7"
                  binding="GS_EmergencyHomePhone">
                  <placeholder
                    name="CaptionOverride"
                    value="Phone Number"
                    resid="9befe63b-a531-4ce3-a27e-782af8662c66" />
                </control>
                <control
                  code="TXT"
                  id="cedf6b96-8de1-4d6b-b3c3-988dd44d2675"
                  binding="GS_EmergencyContactEmail">
                  <placeholder
                    name="CaptionOverride"
                    value="Email Address"
                    resid="00eb8fb6-9c86-45d7-b18f-ecb4af4bc5a9" />
                </control>
                <control
                  code="SRC"
                  id="e12c1228-c065-4fb5-bec9-6e86a67e92ab"
                  binding="GS_EmergencyContactRelationship">
                  <placeholder
                    name="CaptionOverride"
                    value="Relationship"
                    resid="61a5b999-32ac-4174-9d60-f32666d4cc73" />
                </control>
              </control>
            </control>
          </control>
          <control
            code="BOX"
            id="2a84b280-89dd-48a0-92cc-44981c3722ab"
            binding="">
            <placeholder
              name="Columns"
              value="col-sm-6 col-md-4 col-lg-3" />
            <placeholder
              name="Layout"
              value="grid" />
            <control
              code="PNL"
              id="027d84e0-8d85-4035-8b8d-97b7aca853a5"
              binding="">
              <placeholder
                name="Caption"
                value="Residency"
                resid="c0a237fb-9b62-4297-b7ce-e1823671b467" />
              <control
                code="BOX"
                id="9fe7fb98-2dd9-446c-bac2-aacc8f00df1d">
                <placeholder
                  name="Layout"
                  value="grid" />
                <control
                  code="SRC"
                  id="c2201abb-328d-4179-971c-a2ed0cbf7f41"
                  binding="GS_ResidencyStatus" />
                <control
                  code="DAE"
                  id="5fbbb6c6-7a05-46ea-8adc-3efc284c8e10"
                  binding="GS_ResidencyExpiry">
                  <placeholder
                    name="CaptionOverride"
                    value="Residency Expiry"
                    resid="3d81b2a9-e988-4998-a84f-bc29e06e9039" />
                </control>
                <control
                  code="SRC"
                  id="1d1b9655-65da-4174-a720-d2f61e47a536"
                  binding="GS_RN_NKCountryCode">
                  <placeholder
                    name="CaptionOverride"
                    value="Country / Region"
                    resid="724547bb-9149-46e6-a14c-eaa44d437d2e" />
                </control>
                <control
                  code="TXA"
                  id="38683382-9d6b-4663-9091-1eaba73ee92a"
                  binding="GS_UserAddress1" />
                <control
                  code="TXA"
                  id="4eed2afe-2344-4eee-9357-5f3f8a575e30"
                  binding="GS_UserAddress2" />
                <control
                  code="SRC"
                  id="fdf5f819-626e-401d-b9e6-b839b3755e9f"
                  binding="GS_City">
                  <placeholder
                    name="CaptionOverride"
                    value="City"
                    resid="75b42767-02b9-4cbf-83bf-a3f6640630de" />
                </control>
                <control
                  code="SRC"
                  id="0124a294-b4b7-46f6-8417-695707114f22"
                  binding="GS_Postcode">
                  <placeholder
                    name="CaptionOverride"
                    value="Postcode"
                    resid="717a0c7c-a0e5-4d58-8de7-bfa79fcd3d7a" />
                </control>
                <control
                  code="SRC"
                  id="91a997dd-dc34-4bb7-ade5-6f99a44c72e6"
                  binding="GS_State">
                  <placeholder
                    name="CaptionOverride"
                    value="State"
                    resid="fc55601f-5be9-4510-b2e9-5dd23040b038" />
                </control>
              </control>
            </control>
          </control>
          <control
            code="BOX"
            id="820906ea-d0ed-4bcf-9860-7c58b3d86131"
            binding="">
            <placeholder
              name="Columns"
              value="col-sm-6 col-md-4 col-lg-3" />
            <placeholder
              name="Layout"
              value="grid" />
            <control
              code="PNL"
              id="543389d1-0108-4f40-a1af-1b708831b255">
              <placeholder
                name="Caption"
                value="Demographics"
                resid="7243c3fd-a3a6-4686-8c4a-14c9b19e4d6e" />
              <control
                code="BOX"
                id="61a01c44-29ae-4566-9ebd-0c18bbe07a58">
                <placeholder
                  name="Layout"
                  value="grid" />
                <control
                  code="SRC"
                  id="42ef5782-debe-499d-967d-b86f58c41f06"
                  binding="GS_RN_NKNationalityCode" />
                <control
                  code="SRC"
                  id="81574fb9-028a-401c-a17d-4a74ed59314b"
                  binding="GS_WorkingLanguage">
                  <placeholder
                    name="CaptionOverride"
                    value="System Language"
                    resid="f4424d75-2b47-4932-8d5f-06a546fa0ba1" />
                </control>
              </control>
            </control>
            <control
              code="PNL"
              id="51f49ede-d03d-4d94-b0ac-6e46a359f118">
              <placeholder
                name="Caption"
                value="Access"
                resid="ed8b8e1f-aa21-4030-9eb7-4bc0b21bd8f1" />
              <control
                code="CRD"
                id="bc1238ab-d546-45f7-89ce-c09f7098ab4a">
                <placeholder
                  name="Padding"
                  value="pa-3" />
                <control
                  code="SRC"
                  id="da963099-5b9f-4f11-9142-e17f11c060ad"
                  binding="GS_GB_HomeBranch" />
                <control
                  code="SRC"
                  id="1a19f68b-6683-496c-849b-850dcdafd48c"
                  binding="GS_GE_HomeDepartment" />
              </control>
              <control
                code="BOX"
                id="02bcd167-664a-4d2a-b91d-6d55fa6548a5">
                <control
                  code="OPT"
                  id="b21b37e9-bb2b-4fa8-9400-e50c7d7bbb40"
                  binding="GS_CanLogin" />
                <control
                  code="OPT"
                  id="a26d504b-0418-4203-8ce4-c6f8e9dcc2dd"
                  binding="GS_IsActive" />
                <control
                  code="OPT"
                  id="0546c293-f9c8-476c-926d-68fddb6b0059"
                  binding="GS_IsController" />
                <control
                  code="OPT"
                  id="bd6c9af0-59a4-4722-affc-65203975ba46"
                  binding="GS_IsOperational" />
                <control
                  code="OPT"
                  id="e40ee281-b65e-4bec-9bd8-3450896abc05"
                  binding="IsADLinked" />
              </control>
            </control>
            <control
              code="PNL"
              id="115dc7cf-41a2-4378-b273-68011c4da40e"
              binding="">
              <placeholder
                name="Caption"
                value="Custom fields"
                resid="6c0c24f5-d151-464a-b2c6-09eb092e6bb7" />
              <placeholder
                name="VisibilityCondition"
                value="HasCustomFields" />
              <control
                code="CUS"
                id="9ba73e2c-debb-4543-bb78-98722d27ad60"
                binding="">
                <placeholder
                  name="Margin"
                  value="mt-1" />
                <placeholder
                  name="ViewModel"
                  value="CustomFieldsViewModel" />
              </control>
            </control>
          </control>
        </control>
      </control>
      <control
        code="TAB"
        id="0e3a8cf2-c4ac-40cd-9503-69203ee7dc56">
        <placeholder
          name="Caption"
          value="Position"
          resid="7506c0e6-f4eb-4150-8246-485dfec1d0c7" />
      </control>
      <control
        code="TAI"
        id="bf94c01a-561a-49d3-85c7-e1a1313c49f6">
        <control
          code="BOX"
          id="b03013a0-8471-490b-94bf-80227cc2480c">
          <placeholder
            name="FlexWrap"
            value="flex-nowrap" />
          <placeholder
            name="Layout"
            value="grid" />
          <control
            code="BOX"
            id="8556b49d-501d-4ade-96db-e9801789b0c5">
            <placeholder
              name="Columns"
              value="col-md-3 col-xl-2" />
            <placeholder
              name="Layout"
              value="grid" />
            <control
              code="PNL"
              id="9bec7ecd-0c2d-4308-be94-cb8963a3aa41"
              binding="">
              <placeholder
                name="Layout"
                value="grid" />
              <control
                code="LBL"
                id="41f660bd-50e9-4bc1-b4a3-9e73ed421706">
                <placeholder
                  name="Typography"
                  value="h6" />
                <placeholder
                  name="Caption"
                  value="Key dates"
                  resid="44ec25a3-3508-42a5-88cb-b8fd4640b6b2" />
              </control>
              <control
                code="DAE"
                id="5713f585-f30d-48b1-ab0a-29d34d688e06"
                binding="GS_EmploymentDate">
                <placeholder
                  name="CaptionOverride"
                  value="Start date"
                  resid="499fde67-5bef-4641-8801-e30302181e13" />
              </control>
              <control
                code="DAE"
                id="e115955e-9034-48fa-bf19-6ae0695fa234"
                binding="GS_DepartureDate">
                <placeholder
                  name="IsReadOnly"
                  value="True" />
              </control>
            </control>
            <control
              code="PNL"
              id="a3124877-2a03-4003-b83d-bdd99093f04a"
              binding="">
              <placeholder
                name="Layout"
                value="grid" />
              <control
                code="LBL"
                id="73ba800a-cc84-4936-ac04-9da24c5c9015">
                <placeholder
                  name="Caption"
                  value="Employing entity"
                  resid="062e2736-4221-42ce-a63b-38e269c0cf9a" />
                <placeholder
                  name="Typography"
                  value="h6" />
              </control>
              <control
                code="DAE"
                id="81ae8943-c48b-4dac-ae87-8ab5e30be2bc"
                binding="CurrentEmployingEntity.EffectiveDate">
                <placeholder
                  name="IsReadOnly"
                  value="True" />
              </control>
              <control
                code="SRC"
                id="e23f596d-ab83-4665-8821-1bf55ddb1498"
                binding="CurrentEmployingEntity.GHB_GB_Branch">
                <placeholder
                  name="IsReadOnly"
                  value="True" />
              </control>
              <control
                code="BOX"
                id="11a3f0ec-6c47-47c3-91ae-19a666390a8d">
                <placeholder
                  name="Layout"
                  value="flex" />
                <placeholder
                  name="FlexJustify"
                  value="justify-end" />
                <control
                  code="BTN"
                  id="31dfaaac-d30d-480f-b3d9-7c8a38017c20">
                  <placeholder
                    name="Caption"
                    value="View / edit employing entity"
                    resid="cd1dfcb7-83b3-4922-9108-ddddcc897536" />
                  <placeholder
                    name="Variant"
                    value="default" />
                  <placeholder
                    name="Transition"
                    value="True" />
                  <placeholder
                    name="Margin"
                    value="mt-2" />
                </control>
              </control>
            </control>
            <control
              code="PNL"
              id="6a8e7d87-712d-43fa-bb6a-ac92d40d7413"
              binding="">
              <placeholder
                name="Layout"
                value="grid" />
              <control
                code="LBL"
                id="da2b55ba-1ede-43f8-a96d-f7070ed957b3">
                <placeholder
                  name="Typography"
                  value="h6" />
                <placeholder
                  name="Caption"
                  value="Beneficiary entity"
                  resid="0cb649ea-c331-489c-ad5e-e92f9f187e20" />
              </control>
              <control
                code="DAE"
                id="ad7502c8-5725-4fbb-a1d5-bc883a340b73"
                binding="CurrentBeneficiaryEntity.EffectiveDate">
                <placeholder
                  name="IsReadOnly"
                  value="True" />
              </control>
              <control
                code="SRC"
                id="5049469b-7f8b-4cc1-9825-6e467be46bb1"
                binding="CurrentBeneficiaryEntity.GBB_GB_Branch">
                <placeholder
                  name="IsReadOnly"
                  value="True" />
              </control>
              <control
                code="BOX"
                id="50bd6d1a-487f-4039-8a7a-61b41c442f6b">
                <placeholder
                  name="Layout"
                  value="flex" />
                <placeholder
                  name="FlexJustify"
                  value="justify-end" />
                <control
                  code="BTN"
                  id="a06be1a6-aaef-4c34-85f4-2a11b5934f94">
                  <placeholder
                    name="Caption"
                    value="View / edit beneficiary entity"
                    resid="9feabb58-baaa-4808-ab59-ce7c4652d11b" />
                  <placeholder
                    name="Variant"
                    value="default" />
                  <placeholder
                    name="Transition"
                    value="True" />
                  <placeholder
                    name="Margin"
                    value="mt-2" />
                </control>
              </control>
            </control>
          </control>
          <control
            code="BOX"
            id="66535c4e-73cd-464a-a3cf-f32874896194">
            <placeholder
              name="Columns"
              value="col-md-9 col-xl-10" />
            <placeholder
              name="Layout"
              value="grid" />
            <control
              code="BOX"
              id="aaf03780-0f3e-4841-a90a-51315307eb52">
              <placeholder
                name="Columns"
                value="col-md-6" />
              <placeholder
                name="Layout"
                value="grid" />
              <control
                code="PNL"
                id="43107061-542d-4fcf-bbe7-40ea131d2ae1"
                binding="">
                <control
                  code="BOX"
                  id="e7208ad0-a9a5-4883-9ed0-c637082ecefa">
                  <placeholder
                    name="Layout"
                    value="flex" />
                  <placeholder
                    name="FlexJustify"
                    value="justify-end" />
                  <placeholder
                    name="FlexWrap"
                    value="flex-wrap" />
                  <control
                    code="BOX"
                    id="30bd3d28-378c-4575-8095-7f9b2f6e859e">
                    <placeholder
                      name="Layout"
                      value="grid" />
                    <placeholder
                      name="FlexJustify"
                      value="justify-end" />
                    <placeholder
                      name="FlexAlign"
                      value="align-end" />
                    <placeholder
                      name="Margin"
                      value="mr-3" />
                    <placeholder
                      name="Padding"
                      value="pb-3" />
                    <control
                      code="LBL"
                      id="920c9059-6b59-487d-8387-1cbc3d28a2c6">
                      <placeholder
                        name="Caption"
                        value="Current role"
                        resid="3abc600c-0d22-433a-8b43-abc584bb942d" />
                      <placeholder
                        name="Typography"
                        value="h6" />
                    </control>
                    <control
                      code="DVR"
                      id="bbaf5b5c-2056-4116-9950-e70cf4e4433c" />
                  </control>
                  <control
                    code="BOX"
                    id="83762ce6-88bd-4fa0-8f15-25403218c669">
                    <placeholder
                      name="Padding"
                      value="pb-3" />
                    <control
                      code="BTN"
                      id="a098b7d7-46df-47c9-9285-3a403bd2c6b6"
                      binding="">
                      <placeholder
                        name="Caption"
                        value="View / edit role history"
                        resid="3a6c5e9d-2523-417e-b63f-9975c30cfd85" />
                      <placeholder
                        name="Variant"
                        value="default" />
                      <placeholder
                        name="Transition"
                        value="True" />
                    </control>
                  </control>
                </control>
                <control
                  code="BOX"
                  id="b621593c-d3e3-47c7-a6ac-dd490efa5ead">
                  <placeholder
                    name="Layout"
                    value="grid" />
                  <placeholder
                    name="Margin"
                    value="mt-n2" />
                  <control
                    code="DAE"
                    id="a1d018ec-8549-48fd-86a5-204bb1a17842"
                    binding="CurrentRole.EffectiveDate">
                    <placeholder
                      name="Columns"
                      value="col-sm-4" />
                    <placeholder
                      name="CaptionOverride"
                      value="Effective date"
                      resid="24f29beb-3732-47dd-9b70-f540553b15cb" />
                    <placeholder
                      name="IsReadOnly"
                      value="True" />
                  </control>
                  <control
                    code="TXT"
                    id="16962f6b-c24a-4550-8da7-653f8c49a723"
                    binding="CurrentRole.GEH_JobTitle">
                    <placeholder
                      name="Columns"
                      value="col-sm-8 col-xl-4" />
                    <placeholder
                      name="CaptionOverride"
                      value="Job title"
                      resid="8d5b75eb-065d-480b-a6f9-f307f76d5178" />
                    <placeholder
                      name="IsReadOnly"
                      value="True" />
                  </control>
                  <control
                    code="CMB"
                    id="478cb3fb-6614-40a2-a567-c42a3ee64113"
                    binding="CurrentRole.GEH_JobFamily">
                    <placeholder
                      name="CaptionOverride"
                      value="Job family"
                      resid="051a017f-e96f-48a1-8b12-8af8e98422bf" />
                    <placeholder
                      name="Columns"
                      value="col-xl-4" />
                    <placeholder
                      name="IsReadOnly"
                      value="True" />
                  </control>
                </control>
              </control>
              <control
                code="PNL"
                id="a91b6cf8-d476-4d34-ac31-a28347f68b18"
                binding="">
                <control
                  code="BOX"
                  id="c40cedd7-d1f3-40f0-8a2a-b05109ea9fa7">
                  <placeholder
                    name="Layout"
                    value="flex" />
                  <placeholder
                    name="FlexJustify"
                    value="justify-end" />
                  <placeholder
                    name="FlexWrap"
                    value="flex-wrap" />
                  <control
                    code="BOX"
                    id="8440c122-3144-48a5-855b-da67990c39d2">
                    <placeholder
                      name="FlexJustify"
                      value="justify-end" />
                    <placeholder
                      name="FlexAlign"
                      value="align-end" />
                    <placeholder
                      name="Layout"
                      value="grid" />
                    <placeholder
                      name="Padding"
                      value="pb-3" />
                    <placeholder
                      name="Margin"
                      value="mr-3" />
                    <control
                      code="LBL"
                      id="04398eda-4801-4c61-858b-75cbc6f5b86a">
                      <placeholder
                        name="Caption"
                        value="Current team"
                        resid="466c17dc-b0bf-49b7-a2c8-8e96f3972ef6" />
                      <placeholder
                        name="Typography"
                        value="h6" />
                    </control>
                    <control
                      code="DVR"
                      id="bd053126-8cfb-498e-8e66-13a2376ab0d0" />
                  </control>
                  <control
                    code="BOX"
                    id="73daf682-cd5e-4e50-8f60-484da05e1bf2">
                    <placeholder
                      name="Padding"
                      value="pb-3" />
                    <control
                      code="BTN"
                      id="6ac747a5-4520-4714-8b4f-1da04659c69d">
                      <placeholder
                        name="Caption"
                        value="View / edit team history"
                        resid="27c37aad-d2b2-4f55-829a-2647653d3407" />
                      <placeholder
                        name="Variant"
                        value="default" />
                      <placeholder
                        name="Transition"
                        value="True" />
                    </control>
                  </control>
                </control>
                <control
                  code="BOX"
                  id="8b601a27-ceaf-4fca-9b08-6186927ec5ee">
                  <placeholder
                    name="Layout"
                    value="grid" />
                  <placeholder
                    name="Margin"
                    value="mt-n2" />
                  <control
                    code="DAE"
                    id="0ce9256d-16d9-48b9-9909-484fd08aa82b"
                    binding="CurrentTeam.EffectiveDate">
                    <placeholder
                      name="Columns"
                      value="col-sm-4" />
                    <placeholder
                      name="CaptionOverride"
                      value="Effective date"
                      resid="a1b6c6e7-0823-4f07-a12c-d05344ee4bb8" />
                    <placeholder
                      name="IsReadOnly"
                      value="True" />
                  </control>
                  <control
                    code="SRC"
                    id="49e845ab-e19b-4691-8020-f2b437657050"
                    binding="CurrentTeam.GET_GST_NKTeamCode">
                    <placeholder
                      name="Columns"
                      value="col-sm-8 col-xl-4" />
                    <placeholder
                      name="CaptionOverride"
                      value="Team"
                      resid="91785711-1b58-411c-b7a8-0c97943eafce" />
                    <placeholder
                      name="IsReadOnly"
                      value="True" />
                  </control>
                  <control
                    code="TXT"
                    id="045e23d3-6498-44c1-80de-dc1abba7dd41"
                    binding="CurrentTeam.ParentTeam">
                    <placeholder
                      name="CaptionOverride"
                      value="Parent team"
                      resid="5a30c1fd-064a-424a-b0d7-c0e5ad966e1e" />
                    <placeholder
                      name="Columns"
                      value="col-xl-4" />
                  </control>
                </control>
              </control>
              <control
                code="PNL"
                id="64ebaa05-a274-4cc3-a87f-08786b311bcb"
                binding="">
                <control
                  code="BOX"
                  id="ff13ad66-da57-4ac0-bd61-0c561d590455">
                  <placeholder
                    name="Layout"
                    value="grid" />
                  <control
                    code="BOX"
                    id="1732f709-ba53-4490-ad8f-baa11d8b4789">
                    <placeholder
                      name="Layout"
                      value="flex" />
                    <placeholder
                      name="FlexWrap"
                      value="flex-wrap" />
                    <placeholder
                      name="FlexJustify"
                      value="justify-end" />
                    <control
                      code="BOX"
                      id="d41e75c0-31fc-4a73-8b92-a23a8dc5a396">
                      <placeholder
                        name="Padding"
                        value="pb-3" />
                      <placeholder
                        name="Margin"
                        value="mr-3" />
                      <placeholder
                        name="Layout"
                        value="grid" />
                      <control
                        code="LBL"
                        id="20f05934-892b-48e7-9573-79d956d9305e">
                        <placeholder
                          name="Typography"
                          value="h6" />
                        <placeholder
                          name="Caption"
                          value="Current working location"
                          resid="6daedea1-7783-4595-aea9-bae3167ef097" />
                      </control>
                      <control
                        code="DVR"
                        id="f281701e-e757-4aab-b027-a1ebce466e4d" />
                    </control>
                    <control
                      code="BOX"
                      id="85ccf2f2-eaec-430d-b587-4806ffe6fd38">
                      <placeholder
                        name="Padding"
                        value="pb-3" />
                      <placeholder
                        name="Layout"
                        value="flex" />
                      <placeholder
                        name="FlexJustify"
                        value="justify-end" />
                      <control
                        code="BTN"
                        id="f0a458d5-1e9c-4707-9071-b32df84f7b9d"
                        binding="">
                        <placeholder
                          name="Caption"
                          value="View / edit working location history"
                          resid="29886c79-2c4f-4f81-a4d4-89a2366fe1ee" />
                        <placeholder
                          name="Variant"
                          value="default" />
                        <placeholder
                          name="Transition"
                          value="True" />
                      </control>
                    </control>
                  </control>
                </control>
                <control
                  code="BOX"
                  id="e65d4426-3bbc-4056-a720-6ea9e9f30a79">
                  <placeholder
                    name="Layout"
                    value="grid" />
                  <placeholder
                    name="Margin"
                    value="mt-n2" />
                  <control
                    code="DAE"
                    id="3352c8a6-4a4a-41f3-a832-96c87b1a5304"
                    binding="CurrentPhysicalWorkAddress.EffectiveDate">
                    <placeholder
                      name="Columns"
                      value="col-sm-4" />
                    <placeholder
                      name="CaptionOverride"
                      value="Effective date"
                      resid="fb2e7782-3cbf-486d-997d-d4ae7f0dfd16" />
                    <placeholder
                      name="IsReadOnly"
                      value="True" />
                  </control>
                  <control
                    code="CMB"
                    id="d71bfc5a-f3e9-40d5-889a-203572bb45d0"
                    binding="CurrentPhysicalWorkAddress.GEL_LocationSource">
                    <placeholder
                      name="Columns"
                      value="col-sm-4" />
                    <placeholder
                      name="CaptionOverride"
                      value="Location source"
                      resid="8783ed66-5681-467e-a9ca-c901a78e6b48" />
                    <placeholder
                      name="IsReadOnly"
                      value="True" />
                  </control>
                  <control
                    code="SRC"
                    id="43e25aac-ef9b-452b-afad-02c6e84aee8e"
                    binding="CurrentPhysicalWorkAddress.GEL_GB_SourceBranch">
                    <placeholder
                      name="Columns"
                      value="col-sm-4" />
                    <placeholder
                      name="CaptionOverride"
                      value="Source branch"
                      resid="4c3e21c2-4326-4eac-bddf-b3302aeeea5d" />
                    <placeholder
                      name="IsReadOnly"
                      value="True" />
                  </control>
                  <control
                    code="ADD"
                    id="194600a0-2d80-4f54-9d3a-3a7a70f6c09f"
                    binding="CurrentPhysicalWorkAddress.GEL_OA_SourceOrgAddress">
                    <placeholder
                      name="IsReadOnly"
                      value="True" />
                  </control>
                  <control
                    code="TXT"
                    id="d3aaf374-5164-4041-8f3a-b169b0ae0f8a"
                    binding="CurrentPhysicalWorkAddress.GEL_Address1">
                    <placeholder
                      name="CaptionOverride"
                      value="Address 1"
                      resid="7c6bdc02-c983-4ef7-9ad4-f482c61014fa" />
                    <placeholder
                      name="Columns"
                      value="col-sm-6" />
                    <placeholder
                      name="IsReadOnly"
                      value="True" />
                  </control>
                  <control
                    code="TXT"
                    id="020da52a-1803-4145-b079-3180aea9930e"
                    binding="CurrentPhysicalWorkAddress.GEL_Address2">
                    <placeholder
                      name="CaptionOverride"
                      value="Address 2"
                      resid="1fe7371c-8b25-47d6-b58f-bea3b45c5167" />
                    <placeholder
                      name="Columns"
                      value="col-sm-6" />
                    <placeholder
                      name="IsReadOnly"
                      value="True" />
                  </control>
                  <control
                    code="TXT"
                    id="0fbd9bd0-56a8-4d9e-aaeb-79f35084cbe2"
                    binding="CurrentPhysicalWorkAddress.GEL_City">
                    <placeholder
                      name="Columns"
                      value="col-sm-6" />
                    <placeholder
                      name="CaptionOverride"
                      value="City"
                      resid="4e8ff9f9-6743-4697-8c58-927f66f27bc2" />
                    <placeholder
                      name="IsReadOnly"
                      value="True" />
                  </control>
                  <control
                    code="TXT"
                    id="f43b2bd4-8774-417e-b99a-19d563887adb"
                    binding="CurrentPhysicalWorkAddress.GEL_State">
                    <placeholder
                      name="Columns"
                      value="col-sm-6" />
                    <placeholder
                      name="CaptionOverride"
                      value="State"
                      resid="8fc05225-f3c9-4e6d-8a2f-88f89c918f65" />
                    <placeholder
                      name="IsReadOnly"
                      value="True" />
                  </control>
                  <control
                    code="TXT"
                    id="2cce70f7-bc74-446b-83a6-52447dcd2bba"
                    binding="CurrentPhysicalWorkAddress.GEL_PostCode">
                    <placeholder
                      name="Columns"
                      value="col-sm-6" />
                    <placeholder
                      name="CaptionOverride"
                      value="Postcode"
                      resid="772ed41d-970b-4e41-996e-c02d6e4465a0" />
                    <placeholder
                      name="IsReadOnly"
                      value="True" />
                  </control>
                  <control
                    code="SRC"
                    id="87b7116f-2d31-46e7-81cc-42f60bef365d"
                    binding="CurrentPhysicalWorkAddress.GEL_RN_NKCountryCode">
                    <placeholder
                      name="Columns"
                      value="col-sm-6" />
                    <placeholder
                      name="CaptionOverride"
                      value="Country / region"
                      resid="eec90bc0-0a52-464c-a05c-4c043c27f224" />
                    <placeholder
                      name="IsReadOnly"
                      value="True" />
                  </control>
                </control>
              </control>
              <control
                code="PNL"
                id="c721634f-b723-484d-98d9-1cb16ebb358c"
                binding="">
                <control
                  code="BOX"
                  id="5ed78c5b-6d72-47f1-8d21-33f6689c8565"
                  binding="">
                  <placeholder
                    name="Layout"
                    value="flex" />
                  <placeholder
                    name="FlexJustify"
                    value="justify-end" />
                  <placeholder
                    name="FlexWrap"
                    value="flex-wrap" />
                  <control
                    code="BOX"
                    id="3721b0ef-d75a-40c6-a3be-838d031b746e">
                    <placeholder
                      name="Layout"
                      value="grid" />
                    <placeholder
                      name="Margin"
                      value="mr-3" />
                    <placeholder
                      name="Padding"
                      value="pb-3" />
                    <control
                      code="LBL"
                      id="29eb08e6-bd09-43d4-bf1d-f8b65c41a73c">
                      <placeholder
                        name="Typography"
                        value="h6" />
                      <placeholder
                        name="Caption"
                        value="Other contract details"
                        resid="7c313414-2059-4fdc-88c2-36796dcab49d" />
                    </control>
                    <control
                      code="DVR"
                      id="c9795f48-d1a6-4a98-a0ca-c5243836aa9a" />
                  </control>
                  <control
                    code="BOX"
                    id="54735819-9be5-46bb-a4a0-e7e7b5fd9181">
                    <placeholder
                      name="Padding"
                      value="pb-3" />
                    <control
                      code="BTN"
                      id="fe757f71-3451-446e-a63b-cfa46ac83d70"
                      binding="">
                      <placeholder
                        name="Caption"
                        value="View / edit departure details"
                        resid="1be5fa9c-9450-4c70-977e-15b383fb0e3e" />
                      <placeholder
                        name="Transition"
                        value="True" />
                      <placeholder
                        name="Variant"
                        value="default" />
                    </control>
                  </control>
                </control>
                <control
                  code="BOX"
                  id="7e63ca19-524e-4de9-bb2a-d9c3a730359c">
                  <placeholder
                    name="Layout"
                    value="grid" />
                  <placeholder
                    name="Margin"
                    value="mt-n2" />
                  <control
                    code="DAE"
                    id="a1452b67-1f62-467a-84b7-fdec0efd4025"
                    binding="GS_ProbationEndDate">
                    <placeholder
                      name="Columns"
                      value="col-sm-4" />
                  </control>
                  <control
                    code="DAE"
                    id="8001ba70-2e88-49c5-a498-9a4ab2870fb9"
                    binding="GS_LastDayOfWork">
                    <placeholder
                      name="Columns"
                      value="col-sm-4" />
                    <placeholder
                      name="IsReadOnly"
                      value="True" />
                  </control>
                  <control
                    code="DAE"
                    id="67306b43-b92d-4d77-ac56-da2c031ed527"
                    binding="GS_DepartureDate">
                    <placeholder
                      name="Columns"
                      value="col-sm-4" />
                    <placeholder
                      name="IsReadOnly"
                      value="True" />
                  </control>
                  <control
                    code="CMB"
                    id="56284936-bdd4-4d45-8ac4-5e44d1e53e8d"
                    binding="CurrentRole.GEH_DepartureReason">
                    <placeholder
                      name="Columns"
                      value="col-sm-4" />
                    <placeholder
                      name="IsReadOnly"
                      value="True" />
                  </control>
                  <control
                    code="TXT"
                    id="ca6dad5b-e51c-408e-af14-2ab85b25eeb8"
                    binding="CurrentRole.GEH_DepartureComments">
                    <placeholder
                      name="Columns"
                      value="col-sm-8" />
                    <placeholder
                      name="IsReadOnly"
                      value="True" />
                  </control>
                </control>
              </control>
            </control>
            <control
              code="BOX"
              id="8181eb26-3d17-4368-bc59-df5094aa77ce"
              binding="">
              <placeholder
                name="Columns"
                value="col-md-6" />
              <placeholder
                name="Layout"
                value="grid" />
              <control
                code="PNL"
                id="efc95b24-bf02-4339-8451-7848b78f4335"
                binding="">
                <control
                  code="BOX"
                  id="62aafa14-b516-4687-a468-e96497a6ad7d">
                  <placeholder
                    name="Layout"
                    value="flex" />
                  <placeholder
                    name="FlexJustify"
                    value="justify-end" />
                  <placeholder
                    name="FlexWrap"
                    value="flex-wrap" />
                  <control
                    code="BOX"
                    id="e3a3c8c4-77ab-4300-b373-bd60e590f650">
                    <placeholder
                      name="Layout"
                      value="grid" />
                    <placeholder
                      name="FlexJustify"
                      value="justify-end" />
                    <placeholder
                      name="FlexAlign"
                      value="align-end" />
                    <placeholder
                      name="Padding"
                      value="pb-3" />
                    <placeholder
                      name="Margin"
                      value="mr-3" />
                    <control
                      code="LBL"
                      id="ea279578-31b3-40d9-8f35-be87efd62fed"
                      binding="">
                      <placeholder
                        name="Caption"
                        value="Current manager"
                        resid="4fe62ca1-7524-4925-b2f3-b6e4f963d6f6" />
                      <placeholder
                        name="Typography"
                        value="h6" />
                    </control>
                    <control
                      code="DVR"
                      id="0d177b65-d0a0-48ac-aa26-897363f25fa7" />
                  </control>
                  <control
                    code="BOX"
                    id="ebe1a1a0-8324-460c-9cba-7be92f3447f3">
                    <placeholder
                      name="Padding"
                      value="pb-3" />
                    <control
                      code="BTN"
                      id="534c5230-4c94-44f3-9289-6e9ce7fbdf76"
                      binding="">
                      <placeholder
                        name="Caption"
                        value="View / edit manager history"
                        resid="51c69095-64f2-4f05-b333-ba2ad2013c0a" />
                      <placeholder
                        name="Transition"
                        value="True" />
                      <placeholder
                        name="Variant"
                        value="default" />
                    </control>
                  </control>
                </control>
                <control
                  code="BOX"
                  id="4f620d60-61a0-4cbc-a4c2-f0b59e230509">
                  <placeholder
                    name="Layout"
                    value="grid" />
                  <placeholder
                    name="Margin"
                    value="mt-n2" />
                  <control
                    code="DAE"
                    id="82b5a54d-46e1-4e14-9a54-3d423ba57555"
                    binding="CurrentPeopleLeader.GSM_EffectiveDate">
                    <placeholder
                      name="Columns"
                      value="col-sm-4" />
                    <placeholder
                      name="CaptionOverride"
                      value="Effective date"
                      resid="88858050-5dc6-4bcc-965c-b82464ca96e2" />
                    <placeholder
                      name="IsReadOnly"
                      value="True" />
                  </control>
                  <control
                    code="SRC"
                    id="39a0c08e-6826-4ec6-a50e-5c2fd8d7da67"
                    binding="CurrentPeopleLeader.GSM_GS_Manager">
                    <placeholder
                      name="Columns"
                      value="col-sm-8 col-xl-4" />
                    <placeholder
                      name="IsReadOnly"
                      value="True" />
                  </control>
                  <control
                    code="TXT"
                    id="a210e44d-b2b4-4c15-a2af-adf81f3dffcf"
                    binding="CurrentPeopleLeader.Manager.CurrentJobTitle">
                    <placeholder
                      name="IsReadOnly"
                      value="True" />
                    <placeholder
                      name="CaptionOverride"
                      value="Manager job title"
                      resid="34241926-8607-41aa-a363-ad5c1e500223" />
                    <placeholder
                      name="Columns"
                      value="col-xl-4" />
                  </control>
                </control>
              </control>
              <control
                code="PNL"
                id="83681203-03fa-4789-871f-a4a1c60f598f"
                binding="">
                <placeholder
                  name="Layout"
                  value="grid" />
                <control
                  code="BOX"
                  id="b5b9be87-c078-4351-92cb-a327f33b14bc">
                  <placeholder
                    name="Layout"
                    value="flex" />
                  <placeholder
                    name="FlexJustify"
                    value="justify-end" />
                  <placeholder
                    name="FlexWrap"
                    value="flex-wrap" />
                  <control
                    code="BOX"
                    id="9a900e83-5c8f-41e0-9503-555e163e27e0">
                    <placeholder
                      name="Layout"
                      value="grid" />
                    <placeholder
                      name="FlexJustify"
                      value="justify-end" />
                    <placeholder
                      name="FlexAlign"
                      value="align-end" />
                    <placeholder
                      name="Padding"
                      value="pb-3" />
                    <placeholder
                      name="Margin"
                      value="mr-3" />
                    <control
                      code="LBL"
                      id="b2b8c3dd-a279-4247-bce8-20a0718e3f09"
                      binding="">
                      <placeholder
                        name="Caption"
                        value="Current direct reports"
                        resid="10e7baea-d424-4185-9b59-e771c5e084ea" />
                      <placeholder
                        name="Typography"
                        value="h6" />
                    </control>
                    <control
                      code="DVR"
                      id="5fec1c8d-f4b7-440e-86b1-b3b3422b3af1" />
                  </control>
                  <control
                    code="BOX"
                    id="0cb45048-8005-492c-8d20-789c0f8a78d1">
                    <placeholder
                      name="Padding"
                      value="pb-3" />
                    <control
                      code="BTN"
                      id="a4edbd4d-c639-4b9f-8b30-9dfc2083a72d"
                      binding="">
                      <placeholder
                        name="Caption"
                        value="View / edit direct reports history"
                        resid="93307176-58ab-4bb9-8485-70c389351e00" />
                      <placeholder
                        name="Transition"
                        value="True" />
                      <placeholder
                        name="Variant"
                        value="default" />
                    </control>
                  </control>
                </control>
                <control
                  code="BOX"
                  id="a8fcbacb-fff6-46de-b23b-da911c75cae6">
                  <placeholder
                    name="Margin"
                    value="mt-n2" />
                  <control
                    code="RDT"
                    id="cbf0ee11-8615-4c9f-9a46-acb7df472f0f"
                    binding="CurrentDirectReports">
                    <placeholder
                      name="AllowAttach"
                      value="False" />
                    <placeholder
                      name="AllowDetach"
                      value="False" />
                    <placeholder
                      name="CaptionType"
                      value="none" />
                    <placeholder
                      name="HideActions"
                      value="True" />
                    <placeholder
                      name="HideCaption"
                      value="True" />
                    <placeholder
                      name="InlineEdit"
                      value="none" />
                    <placeholder
                      name="IsReadOnly"
                      value="True" />
                    <placeholder
                      name="ItemsPerPage"
                      value="10" />
                    <placeholder
                      name="ShowFilters"
                      value="False" />
                    <placeholder
                      name="DefaultFilter">
                      <xml>
                        <ArrayOfFilterGroup xmlns:a="http://schemas.microsoft.com/2003/10/Serialization/Arrays" xmlns:i="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://cargowise.com/glow/2014/07/16/filters.xsd">
                          <FilterGroup>
                            <Filters>
                              <Filter>
                                <FilterType>SimpleLookupFilter</FilterType>
                                <Operation>StartsWith</Operation>
                                <PropertyPath>GSM_ManagerType</PropertyPath>
                                <Values />
                              </Filter>
                            </Filters>
                            <IsImplicit>false</IsImplicit>
                          </FilterGroup>
                          <FilterGroup>
                            <Filters>
                              <Filter>
                                <FilterType>SimpleLookupFilter</FilterType>
                                <Operation>StartsWith</Operation>
                                <PropertyPath>GSM_ManagerType</PropertyPath>
                                <Values>
                                  <a:string>DRM</a:string>
                                </Values>
                              </Filter>
                            </Filters>
                            <IsImplicit>true</IsImplicit>
                          </FilterGroup>
                        </ArrayOfFilterGroup>
                      </xml>
                    </placeholder>
                    <placeholder
                      name="VisibilityCondition"
                      value="CurrentDirectReports.Count() &gt; 0" />
                    <placeholder
                      name="ShowCustomize"
                      value="False" />
                    <placeholder
                      name="FieldConfiguration">
                      <xml>
                        <fields xmlns="">
                          <field
                            path="GSM_EffectiveDate"
                            width="300"
                            mode="Default" />
                          <field
                            path="GSM_GS_Staff"
                            width="300"
                            mode="Default" />
                          <field
                            path="GSM_EndDate"
                            width="120"
                            mode="Optional" />
                          <field
                            path="GSM_ManagerType"
                            width="200"
                            mode="Optional" />
                          <field
                            path="GSM_GS_Manager"
                            width="200"
                            mode="Optional" />
                          <field
                            path="IGlbStaffManager_CurrentOn"
                            width="80"
                            mode="FilterOnly" />
                          <field
                            path="Manager.GS_Code"
                            width="140"
                            mode="Optional" />
                          <field
                            path="Manager.GS_FullName"
                            width="250"
                            mode="Optional" />
                          <field
                            path="Staff.GS_Code"
                            width="120"
                            mode="Optional" />
                          <field
                            path="Staff.GS_FullName"
                            width="250"
                            mode="Optional" />
                          <field
                            path="Manager.GS_GB_HomeBranch"
                            width="200"
                            mode="FilterOnly" />
                          <field
                            path="Manager.GS_GE_HomeDepartment"
                            width="220"
                            mode="FilterOnly" />
                          <field
                            path="Manager.HomeBranch.GB_GC"
                            width="200"
                            mode="FilterOnly" />
                          <field
                            path="Manager.CurrentTeam.TeamNameDescription"
                            width="200"
                            mode="Optional" />
                          <field
                            path="Manager.GS_EmailAddress"
                            width="200"
                            mode="Optional" />
                          <field
                            path="Staff.GS_GB_HomeBranch"
                            width="200"
                            mode="FilterOnly" />
                          <field
                            path="Staff.GS_GE_HomeDepartment"
                            width="200"
                            mode="FilterOnly" />
                          <field
                            path="Staff.HomeBranch.GB_GC"
                            width="250"
                            mode="FilterOnly" />
                          <field
                            path="Staff.CurrentTeam.TeamNameDescription"
                            width="200"
                            mode="Optional" />
                          <field
                            path="Staff.GS_EmailAddress"
                            width="200"
                            mode="Optional" />
                          <field
                            path="Staff.HomeBranch.GB_BranchName"
                            width="250"
                            mode="Optional" />
                          <field
                            path="Staff.HomeBranch.GB_Code"
                            width="120"
                            mode="Optional" />
                          <field
                            path="Staff.HomeBranch.GlbCompany.GC_Code"
                            width="120"
                            mode="Optional" />
                          <field
                            path="Staff.HomeBranch.GlbCompany.GC_Name"
                            width="250"
                            mode="Optional" />
                          <field
                            path="Staff.HomeDepartment.GE_Code"
                            width="120"
                            mode="Optional" />
                          <field
                            path="Staff.HomeDepartment.GE_Desc"
                            width="250"
                            mode="Optional" />
                          <field
                            path="Staff.CurrentEmployingEntity.Branch.GB_BranchName"
                            width="250"
                            mode="Optional" />
                          <field
                            path="Staff.CurrentEmployingEntity.Branch.GB_Code"
                            width="120"
                            mode="Optional" />
                          <field
                            path="Staff.CurrentEmployingEntity.Branch.GlbCompany.GC_Code"
                            width="120"
                            mode="Optional" />
                          <field
                            path="Staff.CurrentEmployingEntity.Branch.GlbCompany.GC_Name"
                            width="250"
                            mode="Optional" />
                          <field
                            path="Staff.CurrentEmployingEntity.Department.GE_Code"
                            width="120"
                            mode="Optional" />
                          <field
                            path="Staff.CurrentEmployingEntity.Department.GE_Desc"
                            width="250"
                            mode="Optional" />
                          <field
                            path="Manager.HomeBranch.GB_BranchName"
                            width="250"
                            mode="Optional" />
                          <field
                            path="Manager.HomeBranch.GB_Code"
                            width="120"
                            mode="Optional" />
                          <field
                            path="Manager.HomeBranch.GlbCompany.GC_Code"
                            width="120"
                            mode="Optional" />
                          <field
                            path="Manager.HomeBranch.GlbCompany.GC_Name"
                            width="250"
                            mode="Optional" />
                          <field
                            path="Manager.HomeDepartment.GE_Code"
                            width="120"
                            mode="Optional" />
                          <field
                            path="Manager.HomeDepartment.GE_Desc"
                            width="250"
                            mode="Optional" />
                          <field
                            path="Manager.CurrentEmployingEntity.Branch.GB_BranchName"
                            width="250"
                            mode="Optional" />
                          <field
                            path="Manager.CurrentEmployingEntity.Branch.GB_Code"
                            width="120"
                            mode="Optional" />
                          <field
                            path="Manager.CurrentEmployingEntity.Branch.GlbCompany.GC_Code"
                            width="120"
                            mode="Optional" />
                          <field
                            path="Manager.CurrentEmployingEntity.Branch.GlbCompany.GC_Name"
                            width="250"
                            mode="Optional" />
                          <field
                            path="Manager.CurrentEmployingEntity.Department.GE_Code"
                            width="120"
                            mode="Optional" />
                          <field
                            path="Manager.CurrentEmployingEntity.Department.GE_Desc"
                            width="250"
                            mode="Optional" />
                          <field
                            path="Manager.CurrentBeneficiaryEntity.GBB_GB_Branch"
                            width="200"
                            mode="Optional" />
                          <field
                            path="Manager.CurrentBeneficiaryEntity.GBB_GE_Department"
                            width="220"
                            mode="Optional" />
                          <field
                            path="Manager.CurrentBeneficiaryEntity.Branch.GB_GC"
                            width="200"
                            mode="Optional" />
                          <field
                            path="Staff.CurrentBeneficiaryEntity.GBB_GB_Branch"
                            width="200"
                            mode="Optional" />
                          <field
                            path="Staff.CurrentBeneficiaryEntity.GBB_GE_Department"
                            width="200"
                            mode="Optional" />
                          <field
                            path="Staff.CurrentBeneficiaryEntity.Branch.GB_GC"
                            width="250"
                            mode="Optional" />
                          <field
                            path="Staff.CurrentBeneficiaryEntity.Branch.GB_BranchName"
                            width="250"
                            mode="Optional" />
                          <field
                            path="Staff.CurrentBeneficiaryEntity.Branch.GB_Code"
                            width="120"
                            mode="Optional" />
                          <field
                            path="Staff.CurrentBeneficiaryEntity.Branch.GlbCompany.GC_Code"
                            width="120"
                            mode="Optional" />
                          <field
                            path="Staff.CurrentBeneficiaryEntity.Branch.GlbCompany.GC_Name"
                            width="250"
                            mode="Optional" />
                          <field
                            path="Staff.CurrentBeneficiaryEntity.Department.GE_Code"
                            width="120"
                            mode="Optional" />
                          <field
                            path="Staff.CurrentBeneficiaryEntity.Department.GE_Desc"
                            width="250"
                            mode="Optional" />
                          <field
                            path="Manager.CurrentBeneficiaryEntity.Branch.GB_BranchName"
                            width="250"
                            mode="Optional" />
                          <field
                            path="Manager.CurrentBeneficiaryEntity.Branch.GB_Code"
                            width="120"
                            mode="Optional" />
                          <field
                            path="Manager.CurrentBeneficiaryEntity.Branch.GlbCompany.GC_Code"
                            width="120"
                            mode="Optional" />
                          <field
                            path="Manager.CurrentBeneficiaryEntity.Branch.GlbCompany.GC_Name"
                            width="250"
                            mode="Optional" />
                          <field
                            path="Manager.CurrentBeneficiaryEntity.Department.GE_Code"
                            width="120"
                            mode="Optional" />
                          <field
                            path="Manager.CurrentBeneficiaryEntity.Department.GE_Desc"
                            width="250"
                            mode="Optional" />
                          <field
                            path="Staff.GS_EmploymentBasis"
                            width="250"
                            mode="Optional" />
                          <field
                            path="Staff.CurrentJobTitle"
                            width="250"
                            mode="Optional" />
                          <field
                            path="Staff.CurrentTeam.Team.GST_Code"
                            width="250"
                            mode="Optional" />
                          <field
                            path="Staff.CurrentRemuneration.Country.RN_Desc"
                            width="250"
                            mode="Optional" />
                          <field
                            path="Staff.CurrentRemuneration.Country.RN_Code"
                            width="200"
                            mode="Optional" />
                          <field
                            path="Staff.CurrentRemuneration.Currency.RX_Code"
                            width="200"
                            mode="Optional" />
                          <field
                            path="Staff.CurrentRemuneration.Currency.RX_Desc"
                            width="250"
                            mode="Optional" />
                          <field
                            path="Manager.GS_EmploymentBasis"
                            width="250"
                            mode="Optional" />
                          <field
                            path="Manager.CurrentJobTitle"
                            width="250"
                            mode="Optional" />
                          <field
                            path="Manager.CurrentTeam.Team.GST_Code"
                            width="250"
                            mode="Optional" />
                          <field
                            path="Manager.CurrentRemuneration.Country.RN_Desc"
                            width="250"
                            mode="Optional" />
                          <field
                            path="Manager.CurrentRemuneration.Country.RN_Code"
                            width="200"
                            mode="Optional" />
                          <field
                            path="Manager.CurrentRemuneration.Currency.RX_Code"
                            width="200"
                            mode="Optional" />
                          <field
                            path="Manager.CurrentRemuneration.Currency.RX_Desc"
                            width="250"
                            mode="Optional" />
                          <field
                            path="Manager.CurrentRole.GEH_JobFamily"
                            width="250"
                            mode="Optional" />
                          <field
                            path="Staff.CurrentRole.GEH_JobFamily"
                            width="250"
                            mode="Optional" />
                          <field
                            path="Staff.GS_IsActive"
                            width="70"
                            mode="FilterOnly" />
                          <field
                            path="Manager.GS_IsActive"
                            width="70"
                            mode="FilterOnly" />
                          <field
                            path="Staff.StaffName"
                            width="250"
                            mode="FilterOnly" />
                          <field
                            path="GSM_IsApproved"
                            width="110"
                            mode="FilterOnly" />
                          <field
                            path="Staff.GS_LoginName"
                            width="250"
                            mode="Optional" />
                          <field
                            path="Staff.CurrentEmployingEntity.Branch.Country.RN_Desc"
                            width="250"
                            mode="Optional" />
                          <field
                            path="Staff.CurrentEmployingEntity.Branch.Country.RN_Code"
                            width="40"
                            mode="Optional" />
                        </fields>
                      </xml>
                    </placeholder>
                    <placeholder
                      name="HideItemActions"
                      value="True" />
                  </control>
                  <control
                    code="LBL"
                    id="ae6d827d-c92e-465d-a5a3-cb5c647fbe89"
                    binding="">
                    <placeholder
                      name="Caption"
                      value="This person does not have any current direct reports"
                      resid="b3529a44-2722-4312-ac39-55dcb353b910" />
                    <placeholder
                      name="Typography"
                      value="body" />
                    <placeholder
                      name="VisibilityCondition"
                      value="DirectReports.Count() == 0" />
                  </control>
                </control>
              </control>
              <control
                code="PNL"
                id="fe65a970-1435-4c51-9173-632ac0f03bc5"
                binding="">
                <control
                  code="BOX"
                  id="afedba5f-23b4-43da-99c9-4aa76fa60a86">
                  <placeholder
                    name="Layout"
                    value="flex" />
                  <placeholder
                    name="FlexWrap"
                    value="flex-wrap" />
                  <placeholder
                    name="FlexJustify"
                    value="justify-end" />
                  <control
                    code="BOX"
                    id="6b6cb962-8e93-4b20-bd36-5e3e50e4eaf3"
                    binding="">
                    <placeholder
                      name="Layout"
                      value="grid" />
                    <placeholder
                      name="Margin"
                      value="mr-3" />
                    <placeholder
                      name="Padding"
                      value="pb-3" />
                    <control
                      code="LBL"
                      id="801d7603-46cc-4858-8f0a-944c8c743adc">
                      <placeholder
                        name="Caption"
                        value="Cost centers"
                        resid="c4fe693a-18c5-41b1-8957-19f5d143d42c" />
                      <placeholder
                        name="Typography"
                        value="h6" />
                    </control>
                    <control
                      code="DVR"
                      id="66398b51-34cd-458f-8562-b64213d741a2" />
                  </control>
                  <control
                    code="BOX"
                    id="8b8da1d7-3d06-4f04-ba58-171ba7149197">
                    <placeholder
                      name="Padding"
                      value="pb-3" />
                    <control
                      code="BTN"
                      id="9407cb14-096a-43e4-89cd-6d58dcb1966f"
                      binding="">
                      <placeholder
                        name="Caption"
                        value="View / edit cost centers history"
                        resid="bfe7affb-3b23-48fe-815b-475e761ab41b" />
                      <placeholder
                        name="Transition"
                        value="True" />
                      <placeholder
                        name="Variant"
                        value="default" />
                    </control>
                  </control>
                </control>
                <control
                  code="BOX"
                  id="ea3190e8-74ae-4f35-87fe-b0fb58430ff0">
                  <placeholder
                    name="Margin"
                    value="mt-n2" />
                  <control
                    code="RDT"
                    id="ae714263-069e-422d-ad41-82b7a8ca3288"
                    binding="GlbStaffCostCentres">
                    <placeholder
                      name="AllowAdd"
                      value="False" />
                    <placeholder
                      name="CaptionOverride"
                      value="Cost Centers"
                      resid="b5927be9-5694-4380-a08b-8bccad25192c" />
                    <placeholder
                      name="FieldConfiguration">
                      <xml>
                        <fields xmlns="">
                          <field
                            path="GSK_StartDate"
                            width="160"
                            mode="Mandatory" />
                          <field
                            path="GSK_GE_Department"
                            width="200"
                            mode="Mandatory" />
                          <field
                            path="GSK_GB_Branch"
                            width="200"
                            mode="Mandatory" />
                          <field
                            path="GSK_EndDate"
                            width="120"
                            mode="Mandatory" />
                          <field
                            path="Branch.GB_AccountingGroupCode"
                            width="250"
                            mode="Default" />
                          <field
                            path="BranchManagementCodeDescription"
                            width="250"
                            mode="Default" />
                          <field
                            path="IGlbStaffCostCentre_CurrentOn"
                            width="80"
                            mode="FilterOnly" />
                          <field
                            path="Department.GE_Code"
                            width="120"
                            mode="Default" />
                          <field
                            path="Department.GE_Desc"
                            width="200"
                            mode="Default" />
                          <field
                            path="Branch.GB_GC"
                            width="200"
                            mode="Optional" />
                          <field
                            path="Branch.GB_BranchName"
                            width="250"
                            mode="Optional" />
                          <field
                            path="Branch.GB_Code"
                            width="120"
                            mode="Optional" />
                          <field
                            path="Branch.GlbCompany.GC_Code"
                            width="120"
                            mode="Optional" />
                          <field
                            path="Branch.GlbCompany.GC_Name"
                            width="250"
                            mode="Optional" />
                        </fields>
                      </xml>
                    </placeholder>
                    <placeholder
                      name="IsReadOnly"
                      value="True" />
                    <placeholder
                      name="HideItemActions"
                      value="True" />
                    <placeholder
                      name="ShowSelect"
                      value="False" />
                    <placeholder
                      name="ShowCustomize"
                      value="False" />
                    <placeholder
                      name="ShowToolbar"
                      value="none" />
                    <placeholder
                      name="ItemsPerPage"
                      value="5" />
                    <placeholder
                      name="HideDefaultFooter"
                      value="True" />
                    <placeholder
                      name="VisibilityCondition"
                      value="GlbStaffCostCentres.Count() &gt; 0" />
                  </control>
                  <control
                    code="LBL"
                    id="ec238e64-7ead-4d6d-93dc-5976d81a82a5"
                    binding="">
                    <placeholder
                      name="Typography"
                      value="body" />
                    <placeholder
                      name="Caption"
                      value="This person does not have any cost centers"
                      resid="9ed69b11-1d23-4682-bcb3-cf24b11b0b14" />
                    <placeholder
                      name="VisibilityCondition"
                      value="GlbStaffCostCentres.Count() == 0" />
                  </control>
                </control>
              </control>
              <control
                code="PNL"
                id="d85b2601-a73b-4851-b1b4-432c8e9f08a5"
                binding="">
                <control
                  code="BOX"
                  id="e3910fea-5f54-4231-b50b-45ab6f5f76b8">
                  <placeholder
                    name="Layout"
                    value="flex" />
                  <placeholder
                    name="FlexWrap"
                    value="flex-wrap" />
                  <placeholder
                    name="FlexJustify"
                    value="justify-end" />
                  <control
                    code="BOX"
                    id="d4408243-eae0-49df-bf84-df8a0d41991e">
                    <placeholder
                      name="Layout"
                      value="grid" />
                    <placeholder
                      name="Margin"
                      value="mr-3" />
                    <placeholder
                      name="Padding"
                      value="pb-3" />
                    <control
                      code="LBL"
                      id="b4fcb6ae-4861-4c6a-95ab-7b3bdf8d0b63">
                      <placeholder
                        name="Typography"
                        value="h6" />
                      <placeholder
                        name="Caption"
                        value="Timezone"
                        resid="17278787-98f0-4a29-ba18-d9802c00cd3c" />
                    </control>
                    <control
                      code="DVR"
                      id="6633c4c6-bb0e-4de6-a748-bdc1cf63940d" />
                  </control>
                  <control
                    code="BOX"
                    id="f829dc0e-a520-4b70-bde1-3acca8104454">
                    <control
                      code="BTN"
                      id="12c6d3e2-82ff-4c51-a266-6c2be21c280a"
                      binding="">
                      <placeholder
                        name="Variant"
                        value="default" />
                      <placeholder
                        name="Transition"
                        value="True" />
                      <placeholder
                        name="Caption"
                        value="View / edit timezone"
                        resid="fb6f54f5-bcd3-455c-b387-152734b31b9e" />
                    </control>
                  </control>
                </control>
                <control
                  code="BOX"
                  id="571148a4-b59a-495b-8564-f4d11fd766f8">
                  <placeholder
                    name="Layout"
                    value="grid" />
                  <placeholder
                    name="Margin"
                    value="mt-n2" />
                  <control
                    code="DAE"
                    id="f694e9ea-d063-403a-8ddb-b66d7e7646c1"
                    binding="CurrentTimezone.GSZ_StartDate">
                    <placeholder
                      name="Columns"
                      value="col-sm-4" />
                    <placeholder
                      name="IsReadOnly"
                      value="True" />
                  </control>
                  <control
                    code="SRC"
                    id="62128571-df33-4b71-b005-c2bb2aee10f2"
                    binding="CurrentTimezone.GSZ_R3_NKTimeZoneSetName">
                    <placeholder
                      name="Columns"
                      value="col-sm-8" />
                    <placeholder
                      name="IsReadOnly"
                      value="True" />
                  </control>
                </control>
              </control>
              <control
                code="PNL"
                id="4881b3ce-85e3-4261-b46c-210f3a923d44"
                binding="">
                <control
                  code="BOX"
                  id="421c62b3-e572-41e3-8ea3-86ac141b94a1">
                  <placeholder
                    name="Layout"
                    value="flex" />
                  <placeholder
                    name="FlexJustify"
                    value="justify-end" />
                  <placeholder
                    name="FlexWrap"
                    value="flex-wrap" />
                  <control
                    code="BOX"
                    id="c6d52bbc-4340-47b7-8bbb-a000eea530b5">
                    <placeholder
                      name="Layout"
                      value="grid" />
                    <placeholder
                      name="Margin"
                      value="mr-3" />
                    <placeholder
                      name="Padding"
                      value="pb-3" />
                    <control
                      code="LBL"
                      id="d39c4e90-1455-4fb7-8bab-2b64bf2241f1">
                      <placeholder
                        name="Typography"
                        value="h6" />
                      <placeholder
                        name="Caption"
                        value="Public holidays"
                        resid="76ce81df-f0c3-4126-89ec-723d949ff191" />
                    </control>
                    <control
                      code="DVR"
                      id="cff8a532-f89c-4ba2-b3b2-393344c4db18" />
                  </control>
                  <control
                    code="BOX"
                    id="e45ed5ff-21c3-47f7-a0bb-6ddb2c957475">
                    <placeholder
                      name="Padding"
                      value="pb-3" />
                    <control
                      code="BTN"
                      id="150d70d8-6e68-47e6-abeb-277c39391192">
                      <placeholder
                        name="Caption"
                        value="View / edit public holidays"
                        resid="f316b462-0456-4d8d-bc79-9e96a560fdc7" />
                      <placeholder
                        name="Variant"
                        value="default" />
                      <placeholder
                        name="Transition"
                        value="True" />
                    </control>
                  </control>
                </control>
                <control
                  code="BOX"
                  id="b157ec06-3d5f-42fe-b678-1acd33bf87e5">
                  <placeholder
                    name="Layout"
                    value="grid" />
                  <placeholder
                    name="Margin"
                    value="mt-n2" />
                  <control
                    code="LBL"
                    id="855d5d61-cd77-4397-950d-467850ac80e8"
                    binding="">
                    <placeholder
                      name="Caption"
                      value="Public holidays are being inherited from the home branch."
                      resid="e55a013b-a1c2-4f58-b615-035189f5689c" />
                    <placeholder
                      name="Typography"
                      value="body" />
                    <placeholder
                      name="Align"
                      value="left" />
                    <placeholder
                      name="VisibilityCondition"
                      value="CurrentPublicHolidayList == null" />
                  </control>
                  <control
                    code="DAE"
                    id="3340ba13-ba76-479a-9a9e-52a46d98776f"
                    binding="CurrentPublicHolidayList.EffectiveDate">
                    <placeholder
                      name="Columns"
                      value="col-sm-4" />
                    <placeholder
                      name="IsReadOnly"
                      value="True" />
                  </control>
                  <control
                    code="SRC"
                    id="df843dab-e7fe-4b72-b7ce-5afd415f298b"
                    binding="CurrentPublicHolidayList.GHH_GHS_HolidaySource">
                    <placeholder
                      name="Columns"
                      value="col-sm-8" />
                    <placeholder
                      name="IsReadOnly"
                      value="True" />
                  </control>
                </control>
              </control>
            </control>
          </control>
        </control>
      </control>
      <control
        code="TAB"
        id="572fbd2f-4af1-438b-8e41-04362591f908">
        <placeholder
          name="Caption"
          value="Work Pattern"
          resid="e0e5ae45-b51e-4616-a405-8d0ad5944438" />
      </control>
      <control
        code="TAI"
        id="4c9537a7-8d4d-4a32-92ba-0b539e0091ec">
        <control
          code="TBS"
          id="fda78f36-19fd-4dff-9038-3401e55b4890">
          <placeholder
            name="Style"
            value="max-width: 1000px" />
          <placeholder
            name="Margin"
            value="ml-auto mr-auto" />
          <control
            code="TAB"
            id="fdf1f221-2e19-4189-950d-eff8b3800ca6">
            <placeholder
              name="Caption"
              value="Current"
              resid="eafeafc4-eb7e-421b-a0e0-4ddbb9e15f5b" />
          </control>
          <control
            code="TAB"
            id="d29bfbea-c193-481b-b932-4bc1eb30401a">
            <placeholder
              name="Caption"
              value="All"
              resid="7f46acaf-852d-49d6-9e8e-ee431d8fdf4f" />
          </control>
          <control
            code="TAI"
            id="33f48e8a-c2d0-4a99-8e48-29b9db5fe488">
            <control
              code="BOX"
              id="8e1f9809-7b65-4d60-b2bf-54f3d64a9b19">
              <placeholder
                name="Layout"
                value="grid" />
              <control
                code="CRD"
                id="bfdfe489-91df-425a-84d7-e6d6da262691">
                <placeholder
                  name="VisibilityCondition"
                  value="CurrentWorkPattern != null" />
                <placeholder
                  name="Margin"
                  value="mt-2" />
                <control
                  code="BOX"
                  id="4cb39c8e-d5a5-417f-b7a4-de8c40523161">
                  <placeholder
                    name="Layout"
                    value="grid" />
                  <placeholder
                    name="Margin"
                    value="ma-8" />
                  <control
                    code="DSF"
                    id="91132d8e-4e79-488c-9bd7-802246c244f3"
                    binding="CurrentWorkPattern.EffectiveDate">
                    <placeholder
                      name="Columns"
                      value="col-3 col-md-2" />
                    <placeholder
                      name="ValueType"
                      value="date" />
                  </control>
                  <control
                    code="DSF"
                    id="00ae892d-daba-4b50-8cdf-c9dd4a4f44e2"
                    binding="CurrentWorkPattern.GWP_StandardDurationAsTimeSpan">
                    <placeholder
                      name="Columns"
                      value="col-4 col-md-3" />
                    <placeholder
                      name="ValueType"
                      value="duration" />
                  </control>
                  <control
                    code="DSF"
                    id="781c25f5-654e-4756-8e21-0b36fa707439"
                    binding="CurrentWorkPattern.TotalDuration">
                    <placeholder
                      name="Columns"
                      value="col-3 col-md-2" />
                    <placeholder
                      name="ValueType"
                      value="duration" />
                  </control>
                  <control
                    code="DSF"
                    id="6300cf00-988b-427f-af94-b9fec28338b9"
                    binding="CurrentWorkPattern.FTE">
                    <placeholder
                      name="Columns"
                      value="col-2 col-md-1" />
                    <placeholder
                      name="ValueType"
                      value="number" />
                  </control>
                  <control
                    code="CMB"
                    id="0b6bf514-c56b-4e21-a4e1-8687b298c2d4"
                    binding="GS_EmploymentBasis">
                    <placeholder
                      name="Columns"
                      value="col-md-4" />
                    <placeholder
                      name="IsReadOnly"
                      value="True" />
                  </control>
                  <control
                    code="DSF"
                    id="f8420039-81e4-4528-9db7-a0722ef3e78e"
                    binding="CurrentWorkPattern.GWP_Comment">
                    <placeholder
                      name="VisibilityCondition"
                      value="CurrentWorkPattern != null &amp;&amp; CurrentWorkPattern.GWP_Comment != &quot;&quot;" />
                  </control>
                  <control
                    code="SCH"
                    id="479ff044-e35b-4106-89a2-36173dcd494b">
                    <placeholder
                      name="ViewModel"
                      value="WeekScheduleViewModel" />
                    <placeholder
                      name="ViewModelDataItem"
                      value="CurrentWorkPattern" />
                    <placeholder
                      name="IsReadOnly"
                      value="true" />
                    <placeholder
                      name="ShowTicks"
                      value="true" />
                    <placeholder
                      name="ShowInactiveDays"
                      value="true" />
                  </control>
                </control>
              </control>
              <control
                code="CRD"
                id="cbe4d7a8-97ce-4f10-b69c-2737a720b72b">
                <placeholder
                  name="Padding"
                  value="pa-8" />
                <placeholder
                  name="VisibilityCondition"
                  value="CurrentWorkPattern == null" />
                <control
                  code="BOX"
                  id="5564f322-a913-44c5-93cb-00373dd26a0e">
                  <placeholder
                    name="Class"
                    value="d-flex" />
                  <control
                    code="LBL"
                    id="f7608173-9677-4f5e-918e-ad51635ba310">
                    <placeholder
                      name="Caption"
                      value="No current work pattern"
                      resid="653c75fd-019a-432c-8acd-d510346cd260" />
                  </control>
                </control>
              </control>
            </control>
          </control>
          <control
            code="TAI"
            id="52af8cef-41e7-45e9-9755-deba8b820175">
            <control
              code="BOX"
              id="7f769320-ee92-477c-8ef8-49edc4398e5d"
              binding="">
              <placeholder
                name="Margin"
                value="mt-4" />
              <placeholder
                name="Padding"
                value="pa-1" />
              <control
                code="REP"
                id="48129966-294f-4fbb-afd7-7a24aff05944"
                binding="GlbWorkPatterns">
                <placeholder
                  name="Multiple"
                  value="True" />
                <placeholder
                  name="HeaderContentTemplateID"
                  value="168d0d0660594ea299cd24ec5df1d66e" />
                <placeholder
                  name="ContentTemplateID"
                  value="cb44546b14f1429db07d25e507872032" />
                <placeholder
                  name="SortDefinitions"
                  value="{&quot;GWP_EffectiveDate&quot;:{&quot;Caption&quot;:&quot;Effective Date&quot;,&quot;Type&quot;:&quot;String&quot;,&quot;Direction&quot;:&quot;descending&quot;}}" />
                <placeholder
                  name="ItemSubtitle"
                  value="calc(GWP_Comment)" />
                <placeholder
                  name="NewFormFlow"
                  value="da71cf14c8654924bb32c3937d1ef2dc" />
                <placeholder
                  name="ItemsPerPage"
                  value="-1" />
                <placeholder
                  name="HideDefaultFooter"
                  value="True" />
                <placeholder
                  name="VisibilityCondition"
                  value="%.CurrentUserCheckpoints.Contains(&quot;CanAddNewWorkPattern&quot;) " />
                <placeholder
                  name="ShowAdd"
                  value="True" />
              </control>
              <control
                code="REP"
                id="a142c274-69ec-4d51-b590-1a982a5bfd65"
                binding="GlbWorkPatterns">
                <placeholder
                  name="Multiple"
                  value="True" />
                <placeholder
                  name="HeaderContentTemplateID"
                  value="168d0d0660594ea299cd24ec5df1d66e" />
                <placeholder
                  name="ContentTemplateID"
                  value="cb44546b14f1429db07d25e507872032" />
                <placeholder
                  name="SortDefinitions"
                  value="{&quot;GWP_EffectiveDate&quot;:{&quot;Caption&quot;:&quot;Effective Date&quot;,&quot;Type&quot;:&quot;String&quot;,&quot;Direction&quot;:&quot;descending&quot;}}" />
                <placeholder
                  name="ItemSubtitle"
                  value="calc(GWP_Comment)" />
                <placeholder
                  name="NewFormFlow"
                  value="da71cf14c8654924bb32c3937d1ef2dc" />
                <placeholder
                  name="ItemsPerPage"
                  value="-1" />
                <placeholder
                  name="HideDefaultFooter"
                  value="True" />
                <placeholder
                  name="VisibilityCondition"
                  value="!%.CurrentUserCheckpoints.Contains(&quot;CanAddNewWorkPattern&quot;) &amp;&amp; IsSelf" />
                <placeholder
                  name="ShowAdd"
                  value="False" />
              </control>
            </control>
          </control>
        </control>
      </control>
      <control
        code="TAB"
        id="aea6754f-e28e-4e66-b2ae-4c409e03761b">
        <placeholder
          name="Caption"
          value="Remuneration"
          resid="f89055e0-221a-4c1e-aa79-fdc769ff6aed" />
      </control>
      <control
        code="TAI"
        id="f72efe97-fa28-44eb-a061-6aeec7f6ccca">
        <control
          code="BOX"
          id="3545c25b-b35d-4ee2-9707-ed04422e7496"
          binding="">
          <placeholder
            name="Padding"
            value="py-0" />
          <placeholder
            name="Layout"
            value="flow" />
          <control
            code="BOX"
            id="bf01a6e6-bc7e-4689-a3c1-24cce8efedc7"
            binding="">
            <placeholder
              name="Layout"
              value="grid" />
            <control
              code="PNL"
              id="71c06d11-c40d-405f-9aed-85342b24ceba"
              binding="">
              <placeholder
                name="Layout"
                value="grid" />
              <control
                code="LBL"
                id="d806e89d-5565-4863-b928-e4a95a7bf64a">
                <placeholder
                  name="Caption"
                  value="Remuneration History"
                  resid="a89f2fb7-fded-45db-9e41-a72ef15e6f28" />
                <placeholder
                  name="Typography"
                  value="h6" />
                <placeholder
                  name="Margin"
                  value="mb-n4" />
              </control>
              <control
                code="RDT"
                id="8df51395-e170-4495-aa5f-e084872e14ff"
                binding="HRMRemHistories">
                <placeholder
                  name="ShowCustomize"
                  value="True" />
                <placeholder
                  name="AllowAdd"
                  value="False" />
                <placeholder
                  name="InlineEdit"
                  value="none" />
                <placeholder
                  name="IsReadOnly"
                  value="False" />
                <placeholder
                  name="CaptionOverride"
                  value="Select a row from the grid to see the details"
                  resid="97b9f6fa-0d5b-4a87-a94d-f71c5d8ea99e" />
                <placeholder
                  name="CaptionTypography"
                  value="body" />
                <placeholder
                  name="ItemsPerPage"
                  value="5" />
              </control>
            </control>
            <control
              code="BOX"
              id="99485e22-a8d3-40f8-9c7c-6983dcbd9a78">
              <placeholder
                name="Columns"
                value="col-md-5" />
              <control
                code="PNL"
                id="41311c5d-a8c5-49fc-a25b-3487178878c2"
                binding="">
                <placeholder
                  name="Layout"
                  value="grid" />
                <control
                  code="LBL"
                  id="f1ec4e3d-80ab-4301-9c55-aa4e7e986ce6"
                  binding="">
                  <placeholder
                    name="Caption"
                    value="Selected Remuneration Details"
                    resid="8108766d-5580-4145-9d59-6503a100154f" />
                  <placeholder
                    name="Typography"
                    value="h6" />
                </control>
                <control
                  code="PNL"
                  id="be144522-e370-4d84-bdda-ff8ac1cdaa6f"
                  binding="">
                  <control
                    code="BOX"
                    id="8aa7a405-7a3e-49bf-87e8-cb960204f0ff"
                    binding="">
                    <placeholder
                      name="Layout"
                      value="flex" />
                    <placeholder
                      name="FlexJustify"
                      value="justify-end" />
                    <placeholder
                      name="FlexWrap"
                      value="flex-wrap" />
                    <control
                      code="BOX"
                      id="9270e5d0-303b-49d2-800b-3d1b351d765b">
                      <placeholder
                        name="Layout"
                        value="grid" />
                      <placeholder
                        name="Margin"
                        value="mr-3" />
                      <placeholder
                        name="Padding"
                        value="pb-3" />
                      <control
                        code="LBL"
                        id="84613156-9675-4f4d-8c1e-7418a0be5c61"
                        binding="">
                        <placeholder
                          name="Caption"
                          value="Classification"
                          resid="53920f1f-3eb7-48ca-8d45-e237478e6042" />
                        <placeholder
                          name="Typography"
                          value="h6" />
                      </control>
                      <control
                        code="DVR"
                        id="53f934e2-989b-4eb3-8555-f139b400a7bc" />
                    </control>
                    <control
                      code="BOX"
                      id="622494c8-6a0d-4b97-8061-261168c9921e">
                      <placeholder
                        name="Padding"
                        value="pb-3" />
                      <control
                        code="BTN"
                        id="0ed205d5-59cc-4c4c-a2b4-d7d43b97b7ab"
                        binding="">
                        <placeholder
                          name="Caption"
                          value="Create/Edit Entries"
                          resid="9c91c8f8-1fce-4e15-a85e-563d4b136de5" />
                        <placeholder
                          name="Transition"
                          value="True" />
                        <placeholder
                          name="Outlined"
                          value="True" />
                        <placeholder
                          name="Variant"
                          value="default" />
                      </control>
                    </control>
                  </control>
                  <control
                    code="BOX"
                    id="6f1bc0e2-1030-4ec3-954d-8177fad730fb"
                    binding="">
                    <placeholder
                      name="Layout"
                      value="grid" />
                    <placeholder
                      name="Margin"
                      value="mt-n2" />
                    <control
                      code="DAE"
                      id="5ef0569b-7cb6-4586-bab8-6e831c7def87"
                      binding="HRMRemHistories/GlbStaffClassification.EffectiveDate">
                      <placeholder
                        name="Columns"
                        value="col-sm-4" />
                    </control>
                    <control
                      code="CMB"
                      id="3d72ce47-689a-4514-96d9-803e3bf988f2"
                      binding="HRMRemHistories/GlbStaffClassification.GSL_Classification">
                      <placeholder
                        name="Columns"
                        value="col-sm-8" />
                    </control>
                  </control>
                </control>
                <control
                  code="PNL"
                  id="910047de-2957-4e2d-8312-853ac925ed4d"
                  binding="">
                  <control
                    code="BOX"
                    id="e8c134fc-60ec-4644-832f-d9d1637447b5">
                    <placeholder
                      name="Layout"
                      value="flex" />
                    <placeholder
                      name="FlexJustify"
                      value="justify-end" />
                    <placeholder
                      name="FlexWrap"
                      value="flex-wrap" />
                    <control
                      code="BOX"
                      id="1fc40419-b602-4ffe-b47d-c68282666d88">
                      <placeholder
                        name="Layout"
                        value="grid" />
                      <placeholder
                        name="Margin"
                        value="mr-3" />
                      <placeholder
                        name="Padding"
                        value="pb-3" />
                      <control
                        code="LBL"
                        id="a4660e89-1d29-4c22-8a36-fc99e4a2e592"
                        binding="">
                        <placeholder
                          name="Caption"
                          value="Working Basis"
                          resid="37a555ba-b081-432a-bffe-75dde7145bf3" />
                        <placeholder
                          name="Typography"
                          value="h6" />
                      </control>
                      <control
                        code="DVR"
                        id="0522229e-75fd-4703-8d05-53ffa14712d0" />
                    </control>
                    <control
                      code="BOX"
                      id="d87bdffb-34df-41c8-b0d5-81d7eb6e62bd">
                      <placeholder
                        name="Padding"
                        value="pb-3" />
                      <control
                        code="BTN"
                        id="c9e87edd-dc16-44dc-a8b0-b6116c2fd923"
                        binding="">
                        <placeholder
                          name="Caption"
                          value="Create/Edit Entries"
                          resid="5f3980be-f651-42a2-8e75-ae18edc63324" />
                        <placeholder
                          name="Transition"
                          value="True" />
                        <placeholder
                          name="Outlined"
                          value="True" />
                        <placeholder
                          name="Variant"
                          value="default" />
                      </control>
                    </control>
                  </control>
                  <control
                    code="BOX"
                    id="7b5b8f40-0a33-49e6-8ad1-dd9263c6d385">
                    <placeholder
                      name="Layout"
                      value="grid" />
                    <placeholder
                      name="Margin"
                      value="mt-n2" />
                    <control
                      code="DAE"
                      id="16af5308-1f55-4c1d-9d1f-8adfe882f0e9"
                      binding="HRMRemHistories/GlbStaffWorkingBasis.EffectiveDate">
                      <placeholder
                        name="Columns"
                        value="col-sm-4" />
                    </control>
                    <control
                      code="CMB"
                      id="67ba1348-1dfc-4fd2-9307-35fc15fd7181"
                      binding="HRMRemHistories/GlbStaffWorkingBasis.GSW_WorkingBasis">
                      <placeholder
                        name="Columns"
                        value="col-sm-8" />
                    </control>
                  </control>
                </control>
                <control
                  code="PNL"
                  id="c4931a3c-5746-43a7-9672-728d87176803"
                  binding="">
                  <control
                    code="BOX"
                    id="3ab1e298-3340-488f-b781-587c8edf8f8a">
                    <placeholder
                      name="Layout"
                      value="flex" />
                    <placeholder
                      name="FlexJustify"
                      value="justify-end" />
                    <placeholder
                      name="FlexWrap"
                      value="flex-wrap" />
                    <control
                      code="BOX"
                      id="130a297b-b08b-46c4-a74c-6ef6626f37f4">
                      <placeholder
                        name="Layout"
                        value="grid" />
                      <placeholder
                        name="Margin"
                        value="mr-3" />
                      <placeholder
                        name="Padding"
                        value="pb-3" />
                      <control
                        code="LBL"
                        id="b86fc096-45db-4303-b4a0-cb2a176114ba"
                        binding="">
                        <placeholder
                          name="Caption"
                          value="Performance Review"
                          resid="3ab53350-36d1-45a8-ba95-ec5d3482174e" />
                        <placeholder
                          name="Typography"
                          value="h6" />
                      </control>
                      <control
                        code="DVR"
                        id="675d7022-7958-48e8-a4b5-1e553a813b6d" />
                    </control>
                    <control
                      code="BOX"
                      id="9d363e44-5392-4d32-8951-b8d68402cd43">
                      <placeholder
                        name="Padding"
                        value="pb-3" />
                      <control
                        code="BTN"
                        id="75289302-00f8-448e-a73a-c4def6972fcb"
                        binding="">
                        <placeholder
                          name="Caption"
                          value="Create/Edit Entries"
                          resid="e8d8d291-442a-4d2c-898f-59b42f3da4c6" />
                        <placeholder
                          name="Transition"
                          value="True" />
                        <placeholder
                          name="Outlined"
                          value="True" />
                        <placeholder
                          name="Variant"
                          value="default" />
                      </control>
                    </control>
                  </control>
                  <control
                    code="BOX"
                    id="5e4a3233-5d1d-4c79-a437-fa96457487c2">
                    <placeholder
                      name="Layout"
                      value="grid" />
                    <placeholder
                      name="Margin"
                      value="mt-n2" />
                    <control
                      code="BOX"
                      id="06e8c7be-ef99-4b78-b81e-b7438113c4fe"
                      binding="">
                      <placeholder
                        name="Layout"
                        value="grid" />
                      <control
                        code="DAE"
                        id="eec74fa6-a520-4f55-9270-9ea0964a897a"
                        binding="HRMRemHistories/GlbStaffReview.EffectiveDate">
                        <placeholder
                          name="Columns"
                          value="col-sm-4" />
                      </control>
                      <control
                        code="SRC"
                        id="93b0f0b5-d4e4-467d-90f5-3e8e51e605b2"
                        binding="HRMRemHistories/GlbStaffReview.GSV_GS_NKReviewer">
                        <placeholder
                          name="Columns"
                          value="col-sm-8" />
                      </control>
                    </control>
                    <control
                      code="SRC"
                      id="0ae98805-a77b-4468-a8f8-924f132c05d1"
                      binding="HRMRemHistories/GlbStaffReview.PerformanceRating">
                      <placeholder
                        name="CaptionType"
                        value="description" />
                    </control>
                    <control
                      code="TXT"
                      id="d8848bac-dfa1-4bb2-89a7-ba10ea9cb745"
                      binding="HRMRemHistories/GlbStaffReview.GSV_Comments" />
                  </control>
                </control>
              </control>
            </control>
            <control
              code="BOX"
              id="07226069-84a9-482c-9283-e5c50947da73">
              <placeholder
                name="Layout"
                value="grid" />
              <placeholder
                name="Columns"
                value="col-md-7" />
              <control
                code="PNL"
                id="d02ca05c-3ba9-4df7-8320-bb2c698a1b00"
                binding="">
                <control
                  code="BOX"
                  id="5e4a3233-5d1d-4c79-a437-fa96457487c3">
                  <control
                    code="BOX"
                    id="fd2fe390-33e6-4978-b68f-03a84845c91f">
                    <placeholder
                      name="Layout"
                      value="flex" />
                    <placeholder
                      name="FlexAlign"
                      value="align-end" />
                    <control
                      code="BOX"
                      id="ae0952a4-fe53-4451-afd9-e4caead3baf3">
                      <placeholder
                        name="Layout"
                        value="grid" />
                      <placeholder
                        name="Margin"
                        value="mr-3" />
                      <placeholder
                        name="Padding"
                        value="pb-3" />
                      <control
                        code="LBL"
                        id="b86fc096-45db-4303-b4a0-cb2a176114bb"
                        binding="">
                        <placeholder
                          name="Caption"
                          value="Remuneration Package Details"
                          resid="4c1918dd-72aa-4ccc-bb13-db5c541a30b6" />
                        <placeholder
                          name="Typography"
                          value="h6" />
                      </control>
                      <control
                        code="DVR"
                        id="7b817e24-6dbe-4eda-b70d-1c381f18ee57" />
                    </control>
                    <control
                      code="BOX"
                      id="dfcd317e-2391-4b67-9e72-7cdd28982048">
                      <placeholder
                        name="Padding"
                        value="pb-3" />
                      <control
                        code="BTN"
                        id="f612f77d-8b28-4030-a5cc-4492a09fe01c"
                        binding="">
                        <placeholder
                          name="Caption"
                          value="Create/Edit Entries"
                          resid="e5f253ca-c5c7-4cf8-8329-be95abbc2329" />
                        <placeholder
                          name="Transition"
                          value="True" />
                        <placeholder
                          name="Outlined"
                          value="True" />
                        <placeholder
                          name="Variant"
                          value="default" />
                      </control>
                    </control>
                  </control>
                  <control
                    code="BOX"
                    id="06e8c7be-ef99-4b78-b81e-b7438113c4ff"
                    binding="">
                    <placeholder
                      name="Layout"
                      value="grid" />
                    <control
                      code="DAE"
                      id="7ae3bf5c-0ee1-424b-97e8-75fbb20430ba"
                      binding="HRMRemHistories/GlbStaffRemuneration.EffectiveDate">
                      <placeholder
                        name="Columns"
                        value="col-6 col-md-3" />
                      <placeholder
                        name="CaptionOverride"
                        value="Package Date"
                        resid="4fbb90bc-2b7a-42b0-9d97-1f367824cd6e" />
                    </control>
                    <control
                      code="NUM"
                      id="7b07b32e-ed95-467e-a927-b2cd28948fe5"
                      binding="HRMRemHistories/GlbStaffRemuneration.GSR_FullTimeEquivalent">
                      <placeholder
                        name="Columns"
                        value="col-6 col-md-3" />
                    </control>
                    <control
                      code="SRC"
                      id="14e34cc4-27a2-48e3-bd07-8f4ba6f7eee7"
                      binding="HRMRemHistories/GlbStaffRemuneration.GSR_RN_NKCountry">
                      <placeholder
                        name="Columns"
                        value="col-6 col-md-3" />
                    </control>
                    <control
                      code="SRC"
                      id="b9fab55d-fdaf-4d64-8a27-5eb43c40d4fd"
                      binding="HRMRemHistories/GlbStaffRemuneration.GSR_RX_NKCurrency">
                      <placeholder
                        name="Columns"
                        value="col-6 col-md-3" />
                    </control>
                  </control>
                </control>
                <control
                  code="RDT"
                  id="0a5acd69-39f8-43f1-84a5-7c22a0ca4899"
                  binding="HRMRemHistories/GlbStaffRemuneration.GlbStaffEntitlements">
                  <placeholder
                    name="AllowAdd"
                    value="True" />
                  <placeholder
                    name="InlineEdit"
                    value="row" />
                  <placeholder
                    name="ShowItemActions"
                    value="True" />
                  <placeholder
                    name="CaptionOverride"
                    value="Entitlements"
                    resid="dbe88d69-7ef0-4976-857d-447fb94a200e" />
                  <placeholder
                    name="ShowCustomize"
                    value="True" />
                  <placeholder
                    name="Margin"
                    value="ml-1 mr-1" />
                  <placeholder
                    name="CaptionTypography"
                    value="b1" />
                </control>
                <control
                  code="BOX"
                  id="435aa175-72cd-4bfc-b722-8033b73b337b">
                  <control
                    code="LBL"
                    id="4eb2ec7d-cdb9-4941-b5cb-3b07b1e29742"
                    binding="">
                    <placeholder
                      name="Caption"
                      value="Entitlement Breakdown for Selected Group"
                      resid="3ac1fd6e-6986-47e4-af37-ba9155baf76b" />
                    <placeholder
                      name="Typography"
                      value="h6" />
                    <placeholder
                      name="Columns"
                      value="col-7" />
                  </control>
                  <control
                    code="CMP"
                    id="48e2ba36-b714-41cc-abe5-a23d0124adf0"
                    binding="">
                    <placeholder
                      name="Component"
                      value="cargoWiseOne.productHrm.components.EntitlementBreakdown" />
                    <placeholder
                      name="Margin"
                      value="mt-2" />
                  </control>
                </control>
              </control>
              <control
                code="PNL"
                id="14a2be44-412c-4021-94f9-b8a3449ef704"
                binding="">
                <control
                  code="RDT"
                  id="6f6994ad-441e-4632-af8a-8d683696ce12"
                  binding="GlbStaffOneOffEntitlements">
                  <placeholder
                    name="AllowAdd"
                    value="True" />
                  <placeholder
                    name="InlineEdit"
                    value="row" />
                  <placeholder
                    name="ShowCustomize"
                    value="True" />
                  <placeholder
                    name="CaptionOverride"
                    value="One-off Entitlements"
                    resid="fca69cfd-2d7b-4358-b6f4-69712b8aa644" />
                  <placeholder
                    name="Margin"
                    value="mt-n1" />
                </control>
              </control>
            </control>
          </control>
        </control>
      </control>
      <control
        code="TAB"
        id="86d677bc-a372-4686-9870-0c365d9c3dbd"
        binding="">
        <placeholder
          name="Caption"
          value="Leave"
          resid="f85c966b-a39d-485a-a7cb-d8153227aeff" />
      </control>
      <control
        code="TAI"
        id="512b50bc-ed38-4b4d-a202-eb249ffc3098">
        <control
          code="BOX"
          id="ccf88b76-8291-488b-8b22-916146164a53"
          binding="">
          <placeholder
            name="FlexDirection"
            value="flex-column" />
          <placeholder
            name="FlexJustify"
            value="justify-space-around" />
          <placeholder
            name="Layout"
            value="grid" />
          <control
            code="BOX"
            id="ac4f08c2-eaa9-4d9a-9892-b92a1a14937a">
            <placeholder
              name="Columns"
              value="col-sm-4 col-md-3" />
            <placeholder
              name="Layout"
              value="grid" />
            <placeholder
              name="VisibilityCondition"
              value="%.CurrentUserCheckpoints.Contains(&quot;CanViewAllLeaveRequests&quot;)" />
            <control
              code="PNL"
              id="ff949ccf-8353-46de-b0f3-6da26c450e77"
              binding="">
              <placeholder
                name="Caption"
                value="Leave Details"
                resid="18238c48-1e37-4050-80d4-293293fefa71" />
              <control
                code="BOX"
                id="0d6beb03-f5de-4536-8234-8e20ea274c4e"
                binding="">
                <placeholder
                  name="Layout"
                  value="flex" />
                <placeholder
                  name="FlexDirection"
                  value="flex-column" />
                <control
                  code="BOX"
                  id="2757e25e-a2ac-4bed-bad4-21a53cb8a803">
                  <placeholder
                    name="Layout"
                    value="grid" />
                  <placeholder
                    name="FlexDirection"
                    value="flex-column" />
                  <placeholder
                    name="FlexJustify"
                    value="justify-center" />
                  <control
                    code="DSF"
                    id="598a464c-e9cf-4c8a-9b3b-3b2b3a692dfd"
                    binding="LeaveBenefit">
                    <placeholder
                      name="CaptionType"
                      value="short" />
                  </control>
                </control>
                <control
                  code="BTN"
                  id="f0f3e9f4-b799-4076-8afd-a33adbe3d878"
                  binding="">
                  <placeholder
                    name="Caption"
                    value="Leave Benefits"
                    resid="b8b70c32-01ad-4256-b4d9-7c8065de9e74" />
                  <placeholder
                    name="Color"
                    value="grey lighten-2" />
                  <placeholder
                    name="Transition"
                    value="True" />
                  <placeholder
                    name="Margin"
                    value="mt-2 mb-2" />
                </control>
                <control
                  code="BTN"
                  id="f925db7c-b1ea-4919-8103-efa634a8c9e3"
                  binding="">
                  <placeholder
                    name="Caption"
                    value="Make a Manual Adjustment"
                    resid="27a1f5fc-323b-40eb-b431-4aa7ea08d456" />
                  <placeholder
                    name="Transition"
                    value="True" />
                  <placeholder
                    name="Color"
                    value="grey lighten-2" />
                </control>
              </control>
            </control>
          </control>
          <control
            code="BOX"
            id="403a66c9-a94d-4014-85ae-1230a887f90b">
            <placeholder
              name="Layout"
              value="grid" />
            <placeholder
              name="Columns"
              value="col-sm-8 col-md-9" />
            <control
              code="PNL"
              id="7ad5e09a-be8d-42a5-9dbc-22cb050900c9"
              binding="">
              <placeholder
                name="Layout"
                value="grid" />
              <placeholder
                name="VisibilityCondition"
                value="%.CurrentUserCheckpoints.Contains(&quot;CanViewAllLeaveRequests&quot;)" />
              <control
                code="DAE"
                id="61fae6e9-71ff-43a6-969d-8b8c59e8e347"
                binding="LeaveProjectionSelectedDate">
                <placeholder
                  name="CaptionOverride"
                  value="Leave Balances as at:"
                  resid="f315c07a-9dd3-4f60-b085-44e0e4606db8" />
                <placeholder
                  name="CaptionType"
                  value="short" />
              </control>
              <control
                code="CMP"
                id="6fdc9899-f8dc-4d7a-b6eb-f54d7d5554c2"
                binding="">
                <placeholder
                  name="Component"
                  value="cargoWiseOne.productHrm.components.ProcessedBalancesBucket" />
              </control>
            </control>
            <control
              code="PNL"
              id="2eed224e-e088-44bd-8d7e-43e8fa52753a"
              binding="">
              <control
                code="TBS"
                id="de1a6808-a5ef-4858-a352-68c4025a6912">
                <placeholder
                  name="Margin"
                  value="mt-n1" />
                <control
                  code="TAB"
                  id="47b7c9fb-6d69-42a0-9bcf-861c42bb040b">
                  <placeholder
                    name="Caption"
                    value="Leave Requests"
                    resid="f3cf2281-ab1d-4089-8730-d38d65831d49" />
                </control>
                <control
                  code="TAI"
                  id="ae5ddb81-598f-4c8a-9b9c-1e7c0815ea14">
                  <placeholder
                    name="Padding"
                    value="pa-2" />
                  <control
                    code="RDT"
                    id="2331bd37-d5a8-4da1-b842-808e25662811"
                    binding="GlbStaffHolidays">
                    <placeholder
                      name="FieldConfiguration">
                      <xml>
                        <fields xmlns="">
                          <field
                            path="GA_WorkHolidayType"
                            width="200"
                            mode="Default" />
                          <field
                            path="LeaveTypeCode"
                            width="80"
                            mode="Default" />
                          <field
                            path="LeaveTypeName"
                            width="120"
                            mode="Default" />
                          <field
                            path="GA_StartTime"
                            width="180"
                            mode="Default" />
                          <field
                            path="GA_EndTime"
                            width="180"
                            mode="Default" />
                          <field
                            path="ApprovalManagerName"
                            width="100"
                            mode="Default" />
                          <field
                            path="LastApprovalManager"
                            width="300"
                            mode="Optional" />
                          <field
                            path="GA_ApprovalStatus"
                            width="100"
                            mode="Default" />
                          <field
                            path="ApprovalTime"
                            width="180"
                            mode="Default" />
                          <field
                            path="ProcessingStatus"
                            width="100"
                            mode="Default" />
                          <field
                            path="ProcessingDate"
                            width="180"
                            mode="Default" />
                          <field
                            path="ProcessingBy"
                            width="100"
                            mode="Default" />
                          <field
                            path="LastUpdatedBy"
                            width="300"
                            mode="Optional" />
                          <field
                            path="GA_LeaveComment"
                            width="250"
                            mode="Optional" />
                          <field
                            path="LatestStatusChangeAt"
                            width="230"
                            mode="Optional" />
                          <field
                            path="GA_DaysLeaveTaken"
                            width="90"
                            mode="Optional" />
                          <field
                            path="GA_OverrideLeaveTaken"
                            width="90"
                            mode="Optional" />
                        </fields>
                      </xml>
                    </placeholder>
                    <placeholder
                      name="CaptionTypography"
                      value="title" />
                    <placeholder
                      name="ShowFilters"
                      value="True" />
                    <placeholder
                      name="CaptionOverride"
                      value="Leave Requests"
                      resid="*************-4c0d-9f71-0db4a157b325" />
                    <placeholder
                      name="ShowCustomize"
                      value="True" />
                    <placeholder
                      name="AllowAttach"
                      value="False" />
                    <placeholder
                      name="NewFormFlowConfiguration">
                      <xml>
                        <formFlows xmlns="">
                          <formFlow
                            inDialog="True">41d58bba67164604bc83b7fe4617c2f4</formFlow>
                        </formFlows>
                      </xml>
                    </placeholder>
                    <placeholder
                      name="AllowAdd"
                      value="True" />
                    <placeholder
                      name="FullPage"
                      value="True" />
                    <placeholder
                      name="NewCaption"
                      value="New Leave Request" />
                    <placeholder
                      name="VisibilityCondition"
                      value="%.CurrentUserCheckpoints.Contains(&quot;CanViewAllLeaveRequests&quot;)" />
                  </control>
                  <control
                    code="RDT"
                    id="946693c5-4668-4393-b44b-9db2fda9c032"
                    binding="GlbStaffHolidays">
                    <placeholder
                      name="CaptionOverride"
                      value="Leave Requests"
                      resid="86b77b24-ad95-47db-8565-d10f4a717cb2" />
                    <placeholder
                      name="CaptionTypography"
                      value="title" />
                    <placeholder
                      name="ShowFilters"
                      value="True" />
                    <placeholder
                      name="FieldConfiguration">
                      <xml>
                        <fields xmlns="">
                          <field
                            path="GA_WorkHolidayType"
                            width="300"
                            mode="Mandatory" />
                          <field
                            path="LeaveTypeCode"
                            width="300"
                            mode="Default" />
                          <field
                            path="LeaveTypeName"
                            width="300"
                            mode="Default" />
                          <field
                            path="GA_StartTime"
                            width="300"
                            mode="Default" />
                          <field
                            path="GA_EndTime"
                            width="300"
                            mode="Default" />
                          <field
                            path="ApprovalManagerName"
                            width="300"
                            mode="Default" />
                          <field
                            path="LastApprovalManager"
                            width="300"
                            mode="Optional" />
                          <field
                            path="GA_ApprovalStatus"
                            width="300"
                            mode="Default" />
                          <field
                            path="ApprovalTime"
                            width="300"
                            mode="Default" />
                          <field
                            path="ProcessingStatus"
                            width="300"
                            mode="Default" />
                          <field
                            path="ProcessingDate"
                            width="300"
                            mode="Optional" />
                          <field
                            path="ProcessingBy"
                            width="300"
                            mode="Default" />
                          <field
                            path="LastUpdatedBy"
                            width="300"
                            mode="Default" />
                          <field
                            path="GA_LeaveComment"
                            width="300"
                            mode="Optional" />
                          <field
                            path="LatestStatusChangeAt"
                            width="300"
                            mode="Optional" />
                          <field
                            path="GA_DaysLeaveTaken"
                            width="300"
                            mode="Optional" />
                          <field
                            path="GA_OverrideLeaveTaken"
                            width="300"
                            mode="Optional" />
                        </fields>
                      </xml>
                    </placeholder>
                    <placeholder
                      name="FullPage"
                      value="True" />
                    <placeholder
                      name="ShowAddActions"
                      value="False" />
                    <placeholder
                      name="VisibilityCondition"
                      value="!%.CurrentUserCheckpoints.Contains(&quot;CanViewAllLeaveRequests&quot;)" />
                    <placeholder
                      name="AllowAdd"
                      value="False" />
                    <placeholder
                      name="AllowAttach"
                      value="False" />
                    <placeholder
                      name="AllowDetach"
                      value="False" />
                    <placeholder
                      name="HideItemActions"
                      value="True" />
                  </control>
                </control>
                <control
                  code="TAB"
                  id="43503225-4ed4-4fe9-a59e-4f22b7e59f53">
                  <placeholder
                    name="Caption"
                    value="Benefits On Demand"
                    resid="7203cb48-c028-4c45-b436-7bd3eee12c7d" />
                  <placeholder
                    name="VisibilityCondition"
                    value="%.CurrentUserCheckpoints.Contains(&quot;CanViewAllLeaveRequests&quot;)" />
                </control>
                <control
                  code="TAI"
                  id="8f1bc555-9c60-4f0e-b218-825783db419a">
                  <control
                    code="RDT"
                    id="987b91ca-aee4-43b4-b7e4-bdaea398464d"
                    binding="HrlBenefitOnDemands">
                    <placeholder
                      name="CaptionOverride"
                      value="Benefits On Demand"
                      resid="e6a8b77d-c6e4-44e9-b5f4-2e1141fa2725" />
                    <placeholder
                      name="CaptionTypography"
                      value="title" />
                    <placeholder
                      name="HideActions"
                      value="False" />
                    <placeholder
                      name="HideItemActions"
                      value="True" />
                    <placeholder
                      name="InlineEdit"
                      value="table" />
                  </control>
                </control>
                <control
                  code="TAB"
                  id="b2f18afe-071c-452f-93b0-5e5e88f850a9">
                  <placeholder
                    name="Caption"
                    value="Processing History"
                    resid="3ea7f894-f658-4fa4-aeb5-09fc868af48e" />
                  <placeholder
                    name="VisibilityCondition"
                    value="%.CurrentUserCheckpoints.Contains(&quot;CanViewAllLeaveRequests&quot;)" />
                </control>
                <control
                  code="TAI"
                  id="e36c66d4-4464-47bd-a269-449ed832e456">
                  <control
                    code="RDT"
                    id="9feb9fc9-5131-45fa-9011-f431b8440ad6"
                    binding="HrlBalanceTransactions">
                    <placeholder
                      name="CaptionOverride"
                      value="Processing History"
                      resid="4801691d-dfd4-472a-88bd-658601ebb9bf" />
                    <placeholder
                      name="CaptionTypography"
                      value="title" />
                    <placeholder
                      name="DefaultFilter">
                      <xml>
                        <ArrayOfFilterGroup xmlns:a="http://schemas.microsoft.com/2003/10/Serialization/Arrays" xmlns:i="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://cargowise.com/glow/2014/07/16/filters.xsd">
                          <FilterGroup>
                            <Filters>
                              <Filter>
                                <FilterType>StringFilter</FilterType>
                                <Operation>IsNot</Operation>
                                <PropertyPath>LLT_TransactionType</PropertyPath>
                                <Values>
                                  <a:string>FPR</a:string>
                                </Values>
                              </Filter>
                              <Filter>
                                <FilterType>StringFilter</FilterType>
                                <Operation>IsNot</Operation>
                                <PropertyPath>LLT_TransactionType</PropertyPath>
                                <Values>
                                  <a:string>FRR</a:string>
                                </Values>
                              </Filter>
                            </Filters>
                            <IsImplicit>true</IsImplicit>
                          </FilterGroup>
                        </ArrayOfFilterGroup>
                      </xml>
                    </placeholder>
                    <placeholder
                      name="ShowFilters"
                      value="True" />
                    <placeholder
                      name="DefaultSortFields">
                      <xml>
                        <ArrayOfFieldSortDefinition xmlns:i="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://schemas.datacontract.org/2004/07/CargoWise.Glow.Infrastructure.Interfaces">
                          <FieldSortDefinition>
                            <FieldName>Accrual</FieldName>
                            <IsAscending>false</IsAscending>
                          </FieldSortDefinition>
                        </ArrayOfFieldSortDefinition>
                      </xml>
                    </placeholder>
                    <placeholder
                      name="FieldConfiguration">
                      <xml>
                        <fields xmlns="">
                          <field
                            path="LLT_LeaveType"
                            width="100"
                            mode="Mandatory" />
                          <field
                            path="LeaveTypeCode"
                            width="300"
                            mode="Optional" />
                          <field
                            path="LeaveTypeName"
                            width="300"
                            mode="Optional" />
                          <field
                            path="LLT_DeltaValueHours"
                            width="170"
                            mode="Mandatory" />
                          <field
                            path="LLT_Forfeiture"
                            width="170"
                            mode="Mandatory" />
                          <field
                            path="LLT_Accrual"
                            width="70"
                            mode="Mandatory" />
                          <field
                            path="LLT_SystemCreateTimeUtc"
                            width="220"
                            mode="Optional" />
                          <field
                            path="LLT_Comment"
                            width="250"
                            mode="Optional" />
                          <field
                            path="Accrual"
                            width="300"
                            mode="Optional" />
                          <field
                            path="LastProcessedBalance"
                            width="250"
                            mode="Optional" />
                          <field
                            path="ProcessedLeaveBalance"
                            width="230"
                            mode="Optional" />
                        </fields>
                      </xml>
                    </placeholder>
                  </control>
                </control>
              </control>
            </control>
          </control>
        </control>
      </control>
    </control>
  </form>
