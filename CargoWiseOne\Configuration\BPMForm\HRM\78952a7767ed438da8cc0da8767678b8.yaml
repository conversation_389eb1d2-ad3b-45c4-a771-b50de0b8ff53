#transformationVersion: 70.0
#
VZ_PK: 78952a7767ed438da8cc0da8767678b8
VZ_ConfigurationKey: 78952a77-67ed-438d-a8cc-0da8767678b8
VZ_FormID: HRM - Remuneration - Classifications
VZ_Caption:
  resKey: VZ_Caption|78952a77-67ed-438d-a8cc-0da8767678b8
  text: Classifications
VZ_FormFactor: DSK
VZ_EntityType: IGlbStaff
VZ_FormType: FRM
VZ_RequiresLogin: true
VZ_MaterialDesign: true
VZ_Dependencies: <dependencies xmlns="http://cargowise.com/glow/2014/11/14/dependencies.xsd" />
VZ_FormData: >-
  <form
    id="75380502-88ab-4df4-8c16-e67928d180a6" xmlns="http://wisetechglobal.com/glow/2017/05/26/form.xsd">
    <placeholder
      name="Layout"
      value="grid" />
    <control
      code="RDT"
      id="5c142beb-c535-4588-88cb-933f51ba0714"
      binding="GlbStaffClassifications">
      <placeholder
        name="AllowAdd"
        value="True" />
      <placeholder
        name="CaptionOverride"
        value="Classifications"
        resid="0ade48ca-a123-43ec-966c-2badb337b7da" />
      <placeholder
        name="InlineEdit"
        value="row" />
      <placeholder
        name="ShowCustomize"
        value="True" />
      <placeholder
        name="ShowItemActions"
        value="True" />
      <placeholder
        name="ShowSelect"
        value="True" />
      <placeholder
        name="DefaultSortFields">
        <xml>
          <ArrayOfFieldSortDefinition xmlns:i="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://schemas.datacontract.org/2004/07/CargoWise.Glow.Infrastructure.Interfaces">
            <FieldSortDefinition>
              <FieldName>EffectiveDate</FieldName>
              <IsAscending>false</IsAscending>
            </FieldSortDefinition>
          </ArrayOfFieldSortDefinition>
        </xml>
      </placeholder>
      <placeholder
        name="AllowAttach"
        value="false" />
    </control>
  </form>
