#transformationVersion: 70.0
#
VZ_PK: 744237eff65b4c3ba8a3e284b2c62b13
VZ_ConfigurationKey: 744237ef-f65b-4c3b-a8a3-e284b2c62b13
VZ_FormID: CCA - Allocate Consolidations
VZ_Caption:
  resKey: VZ_Caption|744237eff65b4c3ba8a3e284b2c62b13
  text: Allocate Consolidations
VZ_FormFactor: DSK
VZ_EntityType: IRatingContract
VZ_FormType: FRM
VZ_RequiresLogin: true
VZ_MaterialDesign: true
VZ_ExtenderFunc: CargoWise.Glow.Business.Forwarding.Contracts.AllocateConsolidationsFormExtenderG2.Extend
VZ_Dependencies: <dependencies xmlns="http://cargowise.com/glow/2014/11/14/dependencies.xsd" />
VZ_FormData: >-
  <form
    id="3239fa03-faa4-443a-bd80-407108117084" xmlns="http://wisetechglobal.com/glow/2017/05/26/form.xsd">
    <placeholder
      name="FlexDirection"
      value="flex-column" />
    <placeholder
      name="Layout"
      value="flex" />
    <placeholder
      name="Padding"
      value="pb-3" />
    <placeholder
      name="FitToHeight"
      value="True" />
    <control
      code="SDT"
      id="045c32c8-1019-4997-a281-9b9e9c33da22"
      binding="">
      <placeholder
        name="CaptionOverride"
        value="Search Consolidations with/without Containers for Allocation"
        resid="bf57b49f-12a5-4937-a1d9-3a0366004214" />
      <placeholder
        name="EntityType"
        value="IJobConsolAndContainer" />
      <placeholder
        name="DefaultFilter">
        <xml>
          <ArrayOfFilterGroup xmlns:a="http://schemas.microsoft.com/2003/10/Serialization/Arrays" xmlns:i="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://cargowise.com/glow/2014/07/16/filters.xsd">
            <FilterGroup>
              <Filters>
                <Filter>
                  <FilterType>StringFilter</FilterType>
                  <Operation>Is</Operation>
                  <PropertyPath>JCK_TransportMode</PropertyPath>
                  <Values>
                    <a:string>&lt;RCT_TransportMode&gt;</a:string>
                  </Values>
                </Filter>
                <Filter>
                  <FilterType>DateTimeFilter</FilterType>
                  <Operation>IsInTheDateRange</Operation>
                  <PropertyPath>JobConsol.TransportRoutings.JW_ETD</PropertyPath>
                  <Values>
                    <a:string>&lt;RCT_StartDate&gt;</a:string>
                    <a:string>&lt;RCT_EndDate&gt;</a:string>
                  </Values>
                </Filter>
                <Filter>
                  <FilterType>AdvancedGuidLookupFilter</FilterType>
                  <Operation>Is</Operation>
                  <PropertyPath>JobConsol.ShippingLineAddress.OA_OH</PropertyPath>
                  <Values>
                    <a:string>&lt;RCT_OH&gt;</a:string>
                  </Values>
                </Filter>
                <Filter>
                  <FilterType>StringFilter</FilterType>
                  <Operation>IsBlank</Operation>
                  <PropertyPath>JCK_ContractNumber</PropertyPath>
                  <Values />
                </Filter>
              </Filters>
              <IsImplicit>false</IsImplicit>
            </FilterGroup>
          </ArrayOfFilterGroup>
        </xml>
      </placeholder>
      <placeholder
        name="FieldConfiguration">
        <xml>
          <fields xmlns="">
            <field
              path="JCK_ConsolNumber"
              width="140"
              mode="Default" />
            <field
              path="JCK_MasterBillNumber"
              width="140"
              mode="Default" />
            <field
              path="JCK_AgentType"
              width="100"
              mode="Default" />
            <field
              path="JCK_TransportMode"
              width="140"
              mode="Default" />
            <field
              path="JCK_ConsolMode"
              width="110"
              mode="Default" />
            <field
              path="JCK_LoadPort"
              width="90"
              mode="Default" />
            <field
              path="JCK_DischargePort"
              width="110"
              mode="Default" />
            <field
              path="SendingForwarderAddress.OrgHeader.OH_Code"
              width="120"
              mode="Default" />
            <field
              path="ReceivingForwarderAddress.OrgHeader.OH_Code"
              width="120"
              mode="Default" />
            <field
              path="JCK_ContainerNumber"
              width="150"
              mode="Default" />
            <field
              path="JCK_ContainerCount"
              width="150"
              mode="Default" />
            <field
              path="JCK_ContainerMode"
              width="140"
              mode="Default" />
            <field
              path="JCK_CommodityCode"
              width="140"
              mode="Default" />
            <field
              path="JCK_ContractNumber"
              width="250"
              mode="Optional" />
            <field
              path="JobContainer.JC_RC"
              width="250"
              mode="Default" />
            <field
              path="JobContainer.RefContainer.RC_StorageClass"
              width="250"
              mode="Default" />
            <field
              path="JobContainer.RefContainer.RC_FreightRateClass"
              width="250"
              mode="Default" />
            <field
              path="JobContainer.RefContainer.RC_TEU"
              width="60"
              mode="Default" />
            <field
              path="ConsolAllocationLine.RCA_AllocationLineID"
              width="250"
              mode="Optional"
              isFilterable="%.CCAIsAllocationsVisible"
              isVisible="%.CCAIsAllocationsVisible" />
            <field
              path="ContainerAllocationLine.RCA_AllocationLineID"
              width="250"
              mode="Optional"
              isFilterable="%.CCAIsAllocationsVisible"
              isVisible="%.CCAIsAllocationsVisible" />
            <field
              path="JobConsol.ShippingLineAddress.OA_OH"
              width="250"
              mode="Default" />
            <field
              path="Vessel"
              width="250"
              mode="Default" />
            <field
              path="ETD"
              width="250"
              mode="Default" />
            <field
              path="VoyageFlight"
              width="250"
              mode="Default" />
            <field
              path="JobConsol.TransportRoutings.JW_Vessel"
              width="250"
              mode="FilterOnly" />
            <field
              path="JobConsol.TransportRoutings.JW_ETD"
              width="180"
              mode="FilterOnly" />
            <field
              path="JobConsol.TransportRoutings.JW_VoyageFlight"
              width="250"
              mode="FilterOnly" />
            <field
              path="JobConsol.JK_RL_NKCarrierBookingOffice"
              width="300"
              mode="Optional" />
            <field
              path="ConsolLatestBookingStatusAndDate.JKS_LatestBookingStatus"
              width="300"
              caption="Ocean Booking Status"
              resid="4e7956f0-9304-4c5f-982e-4e1f9eaf3169"
              mode="Optional" />
            <field
              path="ConsolLatestBookingStatusAndDate.JKS_LatestBookingDate"
              width="300"
              caption="Ocean Booking Status Date"
              resid="4ab9efc4-2177-4e53-b163-7b17c6835b77"
              mode="Optional" />
            <field
              path="JobConsol.TransportRoutings"
              width="300"
              caption="Related Transport Legs"
              resid="9a7f9cd5-5bc8-4bd4-90d3-3dcc666d2bfc"
              mode="FilterOnly" />
          </fields>
        </xml>
      </placeholder>
      <placeholder
        name="FitToHeight"
        value="True" />
      <placeholder
        name="ActionMenuItems">
        <xml>
          <formFlows xmlns="">
            <formFlow
              inDialog="True">e2418038dc184dc28d450c0064eb8fe4</formFlow>
          </formFlows>
        </xml>
      </placeholder>
      <placeholder
        name="HideDefaultFooter"
        value="False" />
      <placeholder
        name="ItemsPerPage"
        value="100" />
      <placeholder
        name="HideItemActions"
        value="False" />
      <placeholder
        name="ShowSelect"
        value="True" />
    </control>
    <control
      code="BOX"
      id="c3c07eb4-6bee-45ac-ad6b-e7bbac198afe"
      binding="">
      <placeholder
        name="FlexJustify"
        value="justify-end" />
      <placeholder
        name="Layout"
        value="flex" />
      <placeholder
        name="Margin"
        value="mt-5" />
      <control
        code="BTN"
        id="74ce755d-ebeb-4b6e-abfa-7fc8b659d13d"
        binding="">
        <placeholder
          name="Caption"
          value="Allocate Consol(s) to Contract"
          resid="686df56d-b6da-4ca8-9705-33672e2b3a2f" />
        <placeholder
          name="Transition"
          value="True" />
        <placeholder
          name="VisibilityCondition"
          value="!%.CCAEnforceConsolAllocationAtRouteLevel" />
      </control>
    </control>
  </form>
