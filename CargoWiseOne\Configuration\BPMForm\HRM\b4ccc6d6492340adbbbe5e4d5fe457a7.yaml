#transformationVersion: 70.0
#
VZ_PK: b4ccc6d6492340adbbbe5e4d5fe457a7
VZ_ConfigurationKey: b4ccc6d6-4923-40ad-bbbe-5e4d5fe457a7
VZ_FormID: HRM - Hiring Request - My Approvals
VZ_Caption:
  resKey: VZ_Caption|b4ccc6d6-4923-40ad-bbbe-5e4d5fe457a7
  text: My Hiring Request Approvals
VZ_FormFactor: DSK
VZ_FormType: PAG
VZ_RequiresLogin: true
VZ_MaterialDesign: true
VZ_Dependencies: <dependencies xmlns="http://cargowise.com/glow/2014/11/14/dependencies.xsd" />
VZ_FormData: >-
  <form
    id="8706a81f-406b-47e0-8c32-46a85bf716eb" xmlns="http://wisetechglobal.com/glow/2017/05/26/form.xsd">
    <placeholder
      name="Layout"
      value="fill" />
    <control
      code="SDT"
      id="cda58667-ec11-4fa4-94ac-7e46765e234c"
      binding="">
      <placeholder
        name="CaptionOverride"
        value="Approvals"
        resid="dd7064c7-4e31-4044-a9c7-04ec363b3262" />
      <placeholder
        name="EntityType"
        value="IWorkflowTask[[IHRHiringRequest]]" />
      <placeholder
        name="DefaultFilter">
        <xml>
          <ArrayOfFilterGroup xmlns:a="http://schemas.microsoft.com/2003/10/Serialization/Arrays" xmlns:i="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://cargowise.com/glow/2014/07/16/filters.xsd">
            <FilterGroup>
              <Filters>
                <Filter>
                  <FilterType>BoolFilter</FilterType>
                  <Operation>Is</Operation>
                  <PropertyPath>IsApprovalTask</PropertyPath>
                  <Values>
                    <a:string>true</a:string>
                  </Values>
                </Filter>
                <Filter>
                  <FilterType>StringFilter</FilterType>
                  <Operation>Is</Operation>
                  <PropertyPath>AssignedStaffMember.GS_Code</PropertyPath>
                  <Values>
                    <a:string>&lt;%.CurrentStaffCode&gt;</a:string>
                  </Values>
                </Filter>
                <Filter>
                  <FilterType>SimpleLookupFilter</FilterType>
                  <Operation>Is</Operation>
                  <PropertyPath>P9_Status</PropertyPath>
                  <Values>
                    <a:string>ASN</a:string>
                  </Values>
                </Filter>
              </Filters>
              <IsImplicit>true</IsImplicit>
            </FilterGroup>
          </ArrayOfFilterGroup>
        </xml>
      </placeholder>
      <placeholder
        name="EditFormFlowConfiguration">
        <xml>
          <formFlows xmlns="">
            <formFlow>6d3b9d4ac41d4eed9d100cbdb2915776</formFlow>
          </formFlows>
        </xml>
      </placeholder>
      <placeholder
        name="FullPage"
        value="True" />
      <placeholder
        name="SearchMode"
        value="Entity" />
      <placeholder
        name="Columns"
        value="col-6" />
      <placeholder
        name="DefaultSortFields">
        <xml>
          <ArrayOfFieldSortDefinition xmlns:i="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://schemas.datacontract.org/2004/07/CargoWise.Glow.Infrastructure.Interfaces" />
        </xml>
      </placeholder>
      <placeholder
        name="FieldConfiguration">
        <xml>
          <fields xmlns="">
            <field
              path="ApprovalStatus"
              width="300"
              mode="Optional" />
            <field
              path="P9_GS_NKAssignedStaffMember"
              width="300"
              mode="Optional" />
            <field
              path="P9_Description"
              width="300"
              mode="Optional" />
            <field
              path="P9_ActualDate"
              width="180"
              mode="Optional" />
            <field
              path="P9_ActualDateUtc"
              width="180"
              mode="Optional" />
            <field
              path="P9_ActualDuration"
              width="150"
              mode="Optional" />
            <field
              path="P9_Sequence"
              width="80"
              mode="Optional" />
            <field
              path="P9_Type"
              width="40"
              mode="Optional" />
            <field
              path="P9_CompletedTimeUtc"
              width="180"
              mode="Optional" />
            <field
              path="P9_EstDuration"
              width="120"
              mode="Optional" />
            <field
              path="P9_EstimateVariationFactor"
              width="250"
              mode="Optional" />
            <field
              path="IsApprovalTask"
              width="150"
              mode="Optional" />
            <field
              path="ApprovalType"
              width="250"
              mode="Optional" />
            <field
              path="Parent.HRR_HA_JobApplicant"
              width="250"
              mode="Default" />
            <field
              path="P9_Status"
              width="60"
              mode="Optional" />
          </fields>
        </xml>
      </placeholder>
    </control>
  </form>
