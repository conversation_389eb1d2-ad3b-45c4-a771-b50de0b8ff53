#transformationVersion: 70.0
#
VZ_PK: f483762fc7d34060b8dc57d9bd3b6922
VZ_ConfigurationKey: f483762f-c7d3-4060-b8dc-57d9bd3b6922
VZ_FormID: CCX - Allocate Consolidations
VZ_Caption:
  resKey: VZ_Caption|f483762f-c7d3-4060-b8dc-57d9bd3b6922
  text: Allocate Consolidations
VZ_FormFactor: DSK
VZ_EntityType: IRatingContract
VZ_FormType: FRM
VZ_RequiresLogin: true
VZ_ExtenderFunc: CargoWise.Glow.Business.Forwarding.Contracts.AllocateConsolidationsFormExtender.AllocateConsolidations
VZ_Dependencies: >-
  <dependencies
    xmlns="http://cargowise.com/glow/2014/11/14/dependencies.xsd" />
VZ_FormData: >-
  <form
    id="03c80229-0886-4c98-8aa8-0b4331944842"
    xmlns="http://wisetechglobal.com/glow/2017/05/26/form.xsd">
    <placeholder
      name="BackgroundImagePk"
      value="" />
    <placeholder
      name="BackgroundSize"
      value="Auto" />
    <placeholder
      name="BackgroundImageOverride"
      value="" />
    <placeholder
      name="FormLayout"
      value="Splitview" />
    <placeholder
      name="ExpandPaths"
      value="" />
    <placeholder
      name="ScanInputHandling"
      value="NON" />
    <placeholder
      name="RemovePadding"
      value="false" />
    <placeholder
      name="TurnOnPortableOneToOneDesignMode"
      value="false" />
    <placeholder
      name="ContainsDefaultButton"
      value="False" />
    <control
      code="SRL"
      id="e209bdb3-a50f-2884-4196-b855beac57e8"
      left="0"
      top="0"
      right="0"
      bottom="1">
      <placeholder
        name="CanBeHidden"
        value="True" />
      <placeholder
        name="IsInitialFocus"
        value="False" />
      <placeholder
        name="CaptionOverride"
        value="Search Consolidations with/without Containers for Allocation"
        resid="07293035-7024-4f64-a8cb-db2e2e0887d8" />
      <placeholder
        name="VisibilityCondition"
        value="" />
      <placeholder
        name="HeaderAlignment"
        value="Left" />
      <placeholder
        name="HeaderDisplayMode"
        value="Visible" />
      <placeholder
        name="FilterType"
        value="Entity" />
      <placeholder
        name="EntityType"
        value="IJobConsolAndContainer" />
      <placeholder
        name="ItemDisplayPath"
        value="" />
      <placeholder
        name="DisabledGridRowActions"
        value="" />
      <placeholder
        name="DefaultFilter">
        <xml>
          <ArrayOfFilterGroup
            xmlns:a="http://schemas.microsoft.com/2003/10/Serialization/Arrays"
            xmlns:i="http://www.w3.org/2001/XMLSchema-instance"
            xmlns="http://cargowise.com/glow/2014/07/16/filters.xsd">
            <FilterGroup>
              <Filters>
                <Filter>
                  <FilterType>StringFilter</FilterType>
                  <Operation>Is</Operation>
                  <PropertyPath>JCK_TransportMode</PropertyPath>
                  <Values>
                    <a:string>&lt;RCT_TransportMode&gt;</a:string>
                  </Values>
                </Filter>
                <Filter>
                  <FilterType>DateTimeFilter</FilterType>
                  <Operation>IsInTheDateRange</Operation>
                  <PropertyPath>JobConsol.TransportRoutings.JW_ETD</PropertyPath>
                  <Values>
                    <a:string>&lt;RCT_StartDate&gt;</a:string>
                    <a:string>&lt;RCT_EndDate&gt;</a:string>
                  </Values>
                </Filter>
                <Filter>
                  <FilterType>AdvancedGuidLookupFilter</FilterType>
                  <Operation>Is</Operation>
                  <PropertyPath>JobConsol.ShippingLineAddress.OA_OH</PropertyPath>
                  <Values>
                    <a:string>&lt;RCT_OH&gt;</a:string>
                  </Values>
                </Filter>
                <Filter>
                  <FilterType>StringFilter</FilterType>
                  <Operation>IsBlank</Operation>
                  <PropertyPath>JCK_ContractNumber</PropertyPath>
                  <Values />
                </Filter>
              </Filters>
              <IsImplicit>false</IsImplicit>
            </FilterGroup>
          </ArrayOfFilterGroup>
        </xml>
      </placeholder>
      <placeholder
        name="DefaultFilterPropertyPath"
        value="" />
      <placeholder
        name="FieldConfiguration"
        value="" />
      <placeholder
        name="DefaultSortFields"
        value="" />
      <placeholder
        name="DefaultGroupFields"
        value="" />
      <placeholder
        name="EnableGrouping"
        value="False" />
      <placeholder
        name="AutoRefreshInterval"
        value="0" />
      <placeholder
        name="AllowMultiSelect"
        value="True" />
      <placeholder
        name="ShowWorkflowLabel"
        value="True" />
      <placeholder
        name="RowActionsMode"
        value="None" />
      <placeholder
        name="HideGridActions"
        value="False" />
      <placeholder
        name="MinimizeFilters"
        value="False" />
      <placeholder
        name="WorkflowOpenInNewWindow"
        value="False" />
      <placeholder
        name="SearchOnInit"
        value="False" />
      <placeholder
        name="ShowQuickSearch"
        value="False" />
      <placeholder
        name="QuickSearchCaption"
        value="" />
      <placeholder
        name="ValueField"
        value="" />
      <placeholder
        name="Category"
        value="" />
      <placeholder
        name="ActionMenuItems"
        value="" />
      <placeholder
        name="EditFormFlow"
        value="" />
      <placeholder
        name="ResetFilterOnInit"
        value="True" />
      <placeholder
        name="FilterDisplayMode"
        value="InlineAndPopup" />
    </control>
    <control
      code="TBT"
      id="e05ef6af-34e8-4237-a5e2-e18613a33fc1"
      width="5"
      height="1"
      right="0"
      bottom="0">
      <placeholder
        name="CanBeHidden"
        value="True" />
      <placeholder
        name="IsInitialFocus"
        value="False" />
      <placeholder
        name="VisibilityCondition"
        value="" />
      <placeholder
        name="Content"
        value="Allocate Consol(s) to Contract"
        resid="5396dff1-145c-44fc-82e7-6b26ac40ae21" />
      <placeholder
        name="IsDefault"
        value="False" />
    </control>
  </form>
