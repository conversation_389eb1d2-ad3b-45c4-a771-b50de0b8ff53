#transformationVersion: 70.0
#
VZ_PK: 3a2b4456eca14fa7b29ccd5c0ea5a717
VZ_ConfigurationKey: 3a2b4456-eca1-4fa7-b29c-cd5c0ea5a717
VZ_FormID: CCX - Contract Details
VZ_Caption:
  resKey: VZ_Caption|3a2b4456-eca1-4fa7-b29c-cd5c0ea5a717
  text: Contract Entry
VZ_FormFactor: DSK
VZ_EntityType: IRatingContract
VZ_FormType: FRM
VZ_RequiresLogin: true
VZ_ExtenderFunc: CargoWise.Glow.Business.Forwarding.Contracts.ContractsEditFormExtender.extendContractsEditForm
VZ_Dependencies: >-
  <dependencies xmlns="http://cargowise.com/glow/2014/11/14/dependencies.xsd">
    <expandPath
      path="RatingContractNamedAccountPivots" />
    <expandPath
      path="OrgHeader" />
    <expandPath
      path="ContractOwner" />
    <expandPath
      path="RatingContractAllocationLines/ConsolContainers" />
    <expandPath
      path="RatingContractContainerDetentions" />
    <expandPath
      path="Consolidations" />
    <expandPath
      path="Bookings" />
    <expandPath
      path="RatingContractAllocationLines" />
    <expandPath
      path="ChargeGroupContractRatingDates" />
    <expandPath
      path="RatingContractAllocationLines/RatingContractNamedAccountPivots" />
    <expandPath
      path="RatingContractAllocationLines/JobConsols" />
    <expandPath
      path="RatingContractAllocationLines/JobContainers" />
    <expandPath
      path="RatingContractAllocationLines/RatingContractNamedAccountPivots" />
    <expandPath
      path="RatingContractAllocationLines/Bookings" />
    <calculatedProperty
      path="QuantityCN" />
    <calculatedProperty
      path="CapacityWithVarianceCN" />
    <calculatedProperty
      path="UtilizationCN" />
    <calculatedProperty
      path="OutstandingCommittedCN" />
    <calculatedProperty
      path="OutstandingWithVarianceCN" />
    <calculatedProperty
      path="QuantityTEU" />
    <calculatedProperty
      path="CapacityWithVarianceTEU" />
    <calculatedProperty
      path="UtilizationTEU" />
    <calculatedProperty
      path="OutstandingCommittedTEU" />
    <calculatedProperty
      path="OutstandingWithVarianceTEU" />
    <calculatedProperty
      path="DetailsSelectedTab" />
    <calculatedProperty
      path="RatingContractAllocationLines/ConsolContainers" />
    <calculatedProperty
      path="Consolidations" />
    <calculatedProperty
      path="Bookings" />
    <calculatedProperty
      path="RoutesSelectedTab" />
    <calculatedProperty
      path="RatingContractAllocationLines/Bookings" />
    <datagrid
      path="RatingContractContainerDetentions">
      <expandPath
        path="Client" />
    </datagrid>
    <datagrid
      path="RatingContractAllocationLines/ConsolContainers">
      <expandPath
        path="ContainerCommodity" />
      <expandPath
        path="CreatedByStaff" />
      <expandPath
        path="GatewaySellSpotRateCurrency" />
      <expandPath
        path="GoodsCurrency" />
      <expandPath
        path="LastEditedByStaff" />
      <expandPath
        path="CostSpotRateCurrency" />
      <expandPath
        path="PackType" />
      <expandPath
        path="SellSpotRateCurrency" />
      <expandPath
        path="RefContainer" />
      <expandPath
        path="JobConsol" />
      <expandPath
        path="JobConsol.ShippingLineAddress.OrgHeader" />
    </datagrid>
    <datagrid
      path="Consolidations">
      <expandPath
        path="ArrivalCTOAddress.OrgHeader" />
      <expandPath
        path="DepartureCTOAddress.OrgHeader" />
      <expandPath
        path="UnpackDepotAddress.OrgHeader" />
      <expandPath
        path="ShippingLineAddress.OrgHeader" />
      <expandPath
        path="LoadPort" />
      <expandPath
        path="DischargePort" />
      <expandPath
        path="ArrivalCTOAddress" />
      <expandPath
        path="ArrivalUnpackCFSTransportAddress" />
      <expandPath
        path="CoLoadAddress" />
      <expandPath
        path="ContainerYardEmptyPickupAddress" />
      <expandPath
        path="ContainerYardEmptyReturnAddress" />
      <expandPath
        path="CreditorAddress" />
      <expandPath
        path="DepartureCTOAddress" />
      <expandPath
        path="DeparturePackCFSTransportAddress" />
      <expandPath
        path="PackDepotAddress" />
      <expandPath
        path="ReceivingForwarderAddress" />
      <expandPath
        path="SendingForwarderAddress" />
      <expandPath
        path="ShippingLineAddress" />
      <expandPath
        path="UnpackDepotAddress" />
      <expandPath
        path="ReceivingForwarderContact" />
      <expandPath
        path="SendingForwarderContact" />
      <expandPath
        path="ArrivalUnpackCFSTransport" />
      <expandPath
        path="Creditor" />
      <expandPath
        path="DeparturePackCFSTransport" />
      <expandPath
        path="FirstForeignPort" />
      <expandPath
        path="LastForeignPort" />
      <expandPath
        path="MasterBillIssuePlace" />
      <expandPath
        path="PortOfFirstArrival" />
      <expandPath
        path="CreatedByStaff" />
      <expandPath
        path="LastEditedByStaff" />
    </datagrid>
    <datagrid
      path="Bookings">
      <expandPath
        path="Booking" />
      <expandPath
        path="Booking.OneTimeQuote" />
      <expandPath
        path="Booking.GoodsValueCurr" />
      <expandPath
        path="RatingHeader" />
    </datagrid>
    <datagrid
      path="RatingContractAllocationLines">
      <expandPath
        path="ContainerType" />
      <expandPath
        path="SailingSchedule" />
      <expandPath
        path="SailingSchedule.JobVoyOrigin" />
      <expandPath
        path="SailingSchedule.JobVoyDestination" />
    </datagrid>
    <datagrid
      path="RatingContractAllocationLines/JobConsols">
      <expandPath
        path="ArrivalCTOAddress.OrgHeader" />
      <expandPath
        path="DepartureCTOAddress.OrgHeader" />
      <expandPath
        path="UnpackDepotAddress.OrgHeader" />
      <expandPath
        path="ShippingLineAddress.OrgHeader" />
      <expandPath
        path="LoadPort" />
      <expandPath
        path="DischargePort" />
      <expandPath
        path="FirstForeignPort" />
      <expandPath
        path="LastForeignPort" />
      <expandPath
        path="MasterBillIssuePlace" />
      <expandPath
        path="PortOfFirstArrival" />
      <expandPath
        path="CreatedByStaff" />
      <expandPath
        path="LastEditedByStaff" />
    </datagrid>
    <datagrid
      path="RatingContractAllocationLines/ConsolContainers">
      <expandPath
        path="ContainerCommodity" />
      <expandPath
        path="CreatedByStaff" />
      <expandPath
        path="GatewaySellSpotRateCurrency" />
      <expandPath
        path="GoodsCurrency" />
      <expandPath
        path="LastEditedByStaff" />
      <expandPath
        path="CostSpotRateCurrency" />
      <expandPath
        path="PackType" />
      <expandPath
        path="SellSpotRateCurrency" />
      <expandPath
        path="RefContainer" />
      <expandPath
        path="JobConsol" />
      <expandPath
        path="JobConsol.ShippingLineAddress.OrgHeader" />
    </datagrid>
    <datagrid
      path="RatingContractAllocationLines/Bookings">
      <expandPath
        path="RatingHeader" />
      <expandPath
        path="Booking" />
      <expandPath
        path="Booking.OneTimeQuote" />
      <expandPath
        path="Booking.GoodsValueCurr" />
    </datagrid>
    <datagrid
      path="RatingContractAllocationLines/JobContainers">
      <expandPath
        path="ContainerCommodity" />
      <expandPath
        path="RefContainer" />
      <expandPath
        path="CFSClient" />
      <expandPath
        path="CreatedByStaff" />
      <expandPath
        path="DepartureContainerYardAddress" />
      <expandPath
        path="ArrivalContainerYardAddress" />
      <expandPath
        path="FCLBookingOnlyLink" />
      <expandPath
        path="GatewaySellSpotRateCurrency" />
      <expandPath
        path="GoodsCurrency" />
      <expandPath
        path="LastEditedByStaff" />
      <expandPath
        path="JobConsol" />
      <expandPath
        path="CostSpotRateCurrency" />
      <expandPath
        path="PackType" />
      <expandPath
        path="JobSailing" />
      <expandPath
        path="ShippingLine" />
      <expandPath
        path="SellSpotRateCurrency" />
      <expandPath
        path="JobConsol.ShippingLineAddress.OrgHeader" />
      <expandPath
        path="JobConsol.ShippingLineAddress" />
    </datagrid>
  </dependencies>
VZ_FormData: >-
  <form
    id="8cf8e945-6c83-4437-b7ee-c87441bb31cf" xmlns="http://wisetechglobal.com/glow/2017/05/26/form.xsd">
    <placeholder
      name="BackgroundImagePk"
      value="" />
    <placeholder
      name="BackgroundSize"
      value="Auto" />
    <placeholder
      name="BackgroundImageOverride"
      value="" />
    <placeholder
      name="FormLayout"
      value="Splitview" />
    <placeholder
      name="ExpandPaths"
      value="" />
    <placeholder
      name="ScanInputHandling"
      value="NON" />
    <placeholder
      name="RemovePadding"
      value="false" />
    <placeholder
      name="TurnOnPortableOneToOneDesignMode"
      value="false" />
    <placeholder
      name="ContainsDefaultButton"
      value="False" />
    <control
      code="GRP"
      id="d1531f92-fb87-4936-b035-12d4fef4280b"
      left="0"
      top="0"
      height="9"
      right="0">
      <placeholder
        name="CanBeHidden"
        value="True" />
      <placeholder
        name="VisibilityCondition"
        value="" />
      <placeholder
        name="Header"
        value="Top Section"
        resid="7722fc70-a7c3-4530-a5b9-b0214ed21a89" />
      <placeholder
        name="HeaderAlignment"
        value="Left" />
      <placeholder
        name="HeaderDisplayMode"
        value="Hidden" />
      <placeholder
        name="BackgroundImagePk"
        value="" />
      <placeholder
        name="BackgroundImageOverride"
        value="" />
      <control
        code="FLB"
        id="18c86b4f-81fd-4b53-a576-a2c051520978"
        left="1"
        top="0"
        width="4"
        height="1">
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="LabelText"
          value="Contract Information"
          resid="1386ac81-105b-4788-a564-0e74e8d234f3" />
        <placeholder
          name="LabelType"
          value="NormalLabel" />
        <placeholder
          name="LabelNotificationType"
          value="Information" />
        <placeholder
          name="ForegroundThemeColor"
          value="DefaultButton" />
        <placeholder
          name="TextSize"
          value="Medium" />
        <placeholder
          name="FontWeight"
          value="Bold" />
        <placeholder
          name="LabelAlignment"
          value="CenterLeft" />
        <placeholder
          name="BackgroundColor"
          value="" />
        <placeholder
          name="Padding"
          value="None" />
      </control>
      <control
        code="GRD"
        id="0306e9d6-848d-4281-a305-ba4a9f9c765e"
        left="23"
        top="0"
        height="8"
        right="1"
        binding="RatingContractNamedAccountPivots">
        <placeholder
          name="IsReadOnly"
          value="False" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="CaptionOverride"
          value="Named Account Clients"
          resid="06d8a92a-9a0b-48a9-aff3-fa2d94dcdf47" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="HeaderAlignment"
          value="Left" />
        <placeholder
          name="HeaderDisplayMode"
          value="Visible" />
        <placeholder
          name="AllowAdd"
          value="True" />
        <placeholder
          name="AllowAttach"
          value="True" />
        <placeholder
          name="AllowDetach"
          value="True" />
        <placeholder
          name="AllowInlineEdit"
          value="True" />
        <placeholder
          name="FieldConfiguration"
          value="" />
        <placeholder
          name="DefaultSortFields"
          value="" />
        <placeholder
          name="ShowDocumentsIcon"
          value="True" />
        <placeholder
          name="RowActionsMode"
          value="RowAndMenu" />
        <placeholder
          name="HideGridActions"
          value="False" />
        <placeholder
          name="HideFilters"
          value="False" />
        <placeholder
          name="ActionMenuItems"
          value="" />
        <placeholder
          name="EditFormFlow"
          value="" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="TXT"
        id="8352c2da-8b9e-493c-a656-1bfcb98909b2"
        left="1"
        top="1"
        width="4"
        height="1"
        binding="RCT_ContractNumber">
        <placeholder
          name="IsReadOnly"
          value="False" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="OnSubmit"
          value="" />
        <placeholder
          name="MultiLine"
          value="False" />
        <placeholder
          name="EnableAutoComplete"
          value="False" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="TXT"
        id="45e477f6-da8b-42b4-b626-d12e082afe18"
        left="6"
        top="1"
        width="4"
        height="1"
        binding="RCT_TransportMode">
        <placeholder
          name="IsReadOnly"
          value="True" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="OnSubmit"
          value="" />
        <placeholder
          name="MultiLine"
          value="False" />
        <placeholder
          name="EnableAutoComplete"
          value="False" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="DTE"
        id="23facb95-13b2-412f-a09c-d42fea5fedaf"
        left="11"
        top="1"
        width="3"
        height="1"
        binding="RCT_StartDate">
        <placeholder
          name="IsReadOnly"
          value="False" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="DefaultValueExpression"
          value="" />
        <placeholder
          name="IncludeTime"
          value="True" />
        <placeholder
          name="ShowNowButton"
          value="True" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="CMB"
        id="10da158f-793c-48a4-85cd-0d159af97dd8"
        left="15"
        top="1"
        width="3"
        height="1"
        binding="RCT_ContainerType">
        <placeholder
          name="IsReadOnly"
          value="False" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="" />
        <placeholder
          name="VisibilityCondition"
          value="0a565d52-3583-4bb0-a3b2-af2883f3885b" />
        <placeholder
          name="DisplayMode"
          value="Unspecified" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="OPT"
        id="477c675a-32ca-4fac-bac2-f2d9645cbfce"
        left="18"
        top="1"
        width="4"
        height="1"
        binding="RCT_AllowHazardousCommodities">
        <placeholder
          name="IsReadOnly"
          value="False" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="" />
        <placeholder
          name="VisibilityCondition"
          value="8da08eec-90b8-417e-a190-4c328dfefa1d" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="TXT"
        id="2e9ec3a0-69ed-4689-8996-cce998540005"
        left="1"
        top="2"
        width="4"
        height="1"
        binding="RCT_Description">
        <placeholder
          name="IsReadOnly"
          value="False" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="OnSubmit"
          value="" />
        <placeholder
          name="MultiLine"
          value="False" />
        <placeholder
          name="EnableAutoComplete"
          value="False" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="SRC"
        id="31f40054-1995-4588-8424-7884460b8353"
        left="6"
        top="2"
        width="4"
        height="1"
        binding="RCT_OH">
        <placeholder
          name="IsReadOnly"
          value="False" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="DisplayMode"
          value="Unspecified" />
        <placeholder
          name="FieldConfiguration"
          value="" />
        <placeholder
          name="DefaultFilter"
          value="" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="DTE"
        id="d5b58ad6-7fd4-462c-b5da-fbf4eddaa0c9"
        left="11"
        top="2"
        width="3"
        height="1"
        binding="RCT_EndDate">
        <placeholder
          name="IsReadOnly"
          value="False" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="DefaultValueExpression"
          value="" />
        <placeholder
          name="IncludeTime"
          value="True" />
        <placeholder
          name="ShowNowButton"
          value="True" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="SRC"
        id="fbfc5dd5-7be0-4c92-9224-f97b18b5103d"
        left="15"
        top="2"
        width="7"
        height="1"
        binding="RCT_GS_NKContractOwner">
        <placeholder
          name="IsReadOnly"
          value="False" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="DisplayMode"
          value="Unspecified" />
        <placeholder
          name="FieldConfiguration"
          value="" />
        <placeholder
          name="DefaultFilter"
          value="" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="FLB"
        id="6d8b1967-5b30-57ac-476e-a8ade445e374"
        left="1"
        top="3.5"
        width="4"
        height="1">
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="VisibilityCondition"
          value="0a565d52-3583-4bb0-a3b2-af2883f3885b" />
        <placeholder
          name="LabelText"
          value="Total Allocation"
          resid="34d6fdde-bdfe-4928-bac6-0499f530cecb" />
        <placeholder
          name="LabelType"
          value="NormalLabel" />
        <placeholder
          name="LabelNotificationType"
          value="Information" />
        <placeholder
          name="ForegroundThemeColor"
          value="DefaultButton" />
        <placeholder
          name="TextSize"
          value="Medium" />
        <placeholder
          name="FontWeight"
          value="Bold" />
        <placeholder
          name="LabelAlignment"
          value="CenterLeft" />
        <placeholder
          name="BackgroundColor"
          value="" />
        <placeholder
          name="Padding"
          value="None" />
      </control>
      <control
        code="NUM"
        id="3f7a7c89-5165-4ff4-a506-396a7a5b83c9"
        left="1"
        top="4.5"
        width="3"
        height="1"
        binding="QuantityCN">
        <placeholder
          name="IsReadOnly"
          value="True" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="" />
        <placeholder
          name="VisibilityCondition"
          value="0a565d52-3583-4bb0-a3b2-af2883f3885b" />
        <placeholder
          name="NumericFieldLeftAlignment"
          value="False" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="NUM"
        id="f1164c64-d582-4d0e-99fd-060b9fc040b8"
        left="4"
        top="4.5"
        width="4"
        height="1"
        binding="CapacityWithVarianceCN">
        <placeholder
          name="IsReadOnly"
          value="True" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="" />
        <placeholder
          name="VisibilityCondition"
          value="0a565d52-3583-4bb0-a3b2-af2883f3885b" />
        <placeholder
          name="NumericFieldLeftAlignment"
          value="False" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="NUM"
        id="1be5e886-ff73-4f00-a20a-dfe5098cfdd0"
        left="8"
        top="4.5"
        width="3"
        height="1"
        binding="UtilizationCN">
        <placeholder
          name="IsReadOnly"
          value="True" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="" />
        <placeholder
          name="VisibilityCondition"
          value="0a565d52-3583-4bb0-a3b2-af2883f3885b" />
        <placeholder
          name="NumericFieldLeftAlignment"
          value="False" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="NUM"
        id="1e3a6d53-dc9e-496a-9e7a-8b699299bb26"
        left="11"
        top="4.5"
        width="4"
        height="1"
        binding="OutstandingCommittedCN">
        <placeholder
          name="IsReadOnly"
          value="True" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="" />
        <placeholder
          name="VisibilityCondition"
          value="0a565d52-3583-4bb0-a3b2-af2883f3885b" />
        <placeholder
          name="NumericFieldLeftAlignment"
          value="False" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="NUM"
        id="60bfef4b-b2c3-4a14-a19c-99bbfda50aef"
        left="15"
        top="4.5"
        width="4"
        height="1"
        binding="OutstandingWithVarianceCN">
        <placeholder
          name="IsReadOnly"
          value="True" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="" />
        <placeholder
          name="VisibilityCondition"
          value="0a565d52-3583-4bb0-a3b2-af2883f3885b" />
        <placeholder
          name="NumericFieldLeftAlignment"
          value="False" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="NUM"
        id="07a020be-7725-41ad-8a11-c2a1e381dbd0"
        left="1"
        top="5.5"
        width="3"
        height="1"
        binding="QuantityTEU">
        <placeholder
          name="IsReadOnly"
          value="True" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="" />
        <placeholder
          name="VisibilityCondition"
          value="0a565d52-3583-4bb0-a3b2-af2883f3885b" />
        <placeholder
          name="NumericFieldLeftAlignment"
          value="False" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="NUM"
        id="aad16e2a-14d3-4f3e-9970-f17812f63929"
        left="4"
        top="5.5"
        width="4"
        height="1"
        binding="CapacityWithVarianceTEU">
        <placeholder
          name="IsReadOnly"
          value="True" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="" />
        <placeholder
          name="VisibilityCondition"
          value="0a565d52-3583-4bb0-a3b2-af2883f3885b" />
        <placeholder
          name="NumericFieldLeftAlignment"
          value="False" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="NUM"
        id="1cb26181-2668-42cd-bd36-054cf501ee88"
        left="8"
        top="5.5"
        width="3"
        height="1"
        binding="UtilizationTEU">
        <placeholder
          name="IsReadOnly"
          value="True" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="" />
        <placeholder
          name="VisibilityCondition"
          value="0a565d52-3583-4bb0-a3b2-af2883f3885b" />
        <placeholder
          name="NumericFieldLeftAlignment"
          value="False" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="NUM"
        id="665e51c6-3966-46f3-8a8f-9e627b5c7f79"
        left="11"
        top="5.5"
        width="4"
        height="1"
        binding="OutstandingCommittedTEU">
        <placeholder
          name="IsReadOnly"
          value="True" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="" />
        <placeholder
          name="VisibilityCondition"
          value="0a565d52-3583-4bb0-a3b2-af2883f3885b" />
        <placeholder
          name="NumericFieldLeftAlignment"
          value="False" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="NUM"
        id="0dff4844-be52-4375-a511-b3a4f6db19ca"
        left="15"
        top="5.5"
        width="4"
        height="1"
        binding="OutstandingWithVarianceTEU">
        <placeholder
          name="IsReadOnly"
          value="True" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="" />
        <placeholder
          name="VisibilityCondition"
          value="0a565d52-3583-4bb0-a3b2-af2883f3885b" />
        <placeholder
          name="NumericFieldLeftAlignment"
          value="False" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="TBT"
        id="30006b04-c494-4ebe-9afc-5d07207a6371"
        left="15"
        top="6.8"
        width="5"
        height="1">
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="VisibilityCondition"
          value="908c76a1-ef57-428b-ae4b-7974552868cd" />
        <placeholder
          name="Content"
          value="Allocation Route Wizard"
          resid="ca54ef6f-15f4-4773-a04f-cc09280a89a1" />
        <placeholder
          name="IsDefault"
          value="False" />
      </control>
      <control
        code="OBG"
        id="7bfaf006-4531-4309-b395-beb520b644eb"
        left="1"
        top="8"
        width="21"
        height="1"
        binding="DetailsSelectedTab">
        <placeholder
          name="IsReadOnly"
          value="False" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="GridColumns"
          value="0" />
        <placeholder
          name="ButtonDisplayMode"
          value="DescOnly" />
        <placeholder
          name="HeaderDisplayMode"
          value="Hidden" />
        <placeholder
          name="IsIconVisible"
          value="False" />
        <placeholder
          name="IconPosition"
          value="Left" />
        <placeholder
          name="IconSize"
          value="Default" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
    </control>
    <control
      code="GRP"
      id="e6a0a13e-2ac3-4e7c-9a70-36f34578435d"
      left="0"
      top="9"
      height="10"
      right="0">
      <placeholder
        name="CanBeHidden"
        value="True" />
      <placeholder
        name="VisibilityCondition"
        value="9db014c5-918f-4752-934f-7cf80de0da5d" />
      <placeholder
        name="Header"
        value="Container Penalties Section"
        resid="c23e86db-48d3-4159-bc32-e92d48f45429" />
      <placeholder
        name="HeaderAlignment"
        value="Left" />
      <placeholder
        name="HeaderDisplayMode"
        value="Hidden" />
      <placeholder
        name="BackgroundImagePk"
        value="" />
      <placeholder
        name="BackgroundImageOverride"
        value="" />
      <control
        code="GRD"
        id="1f5cecc2-a972-4ae4-b384-fbfa46593a93"
        left="1"
        top="0"
        height="9"
        right="1"
        binding="RatingContractContainerDetentions">
        <placeholder
          name="IsReadOnly"
          value="False" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="CaptionOverride"
          value="Container Penalties"
          resid="cea6d7e7-a97d-40e7-b925-0257d5c9e957" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="HeaderAlignment"
          value="Left" />
        <placeholder
          name="HeaderDisplayMode"
          value="Visible" />
        <placeholder
          name="AllowAdd"
          value="True" />
        <placeholder
          name="AllowAttach"
          value="True" />
        <placeholder
          name="AllowDetach"
          value="True" />
        <placeholder
          name="AllowInlineEdit"
          value="True" />
        <placeholder
          name="FieldConfiguration">
          <xml>
            <fields xmlns="">
              <field
                path="RCD_Direction"
                width="90"
                mode="Default" />
              <field
                path="RCD_PenaltyType"
                width="120"
                mode="Default" />
              <field
                path="RCD_DetentionPortOrCountry"
                width="250"
                mode="Default" />
              <field
                path="RCD_OriginPortOrCountry"
                width="220"
                mode="Default" />
              <field
                path="RCD_ContainerType"
                width="140"
                mode="Default" />
              <field
                path="RCD_FreeDays"
                width="90"
                mode="Default" />
              <field
                path="RCD_OH_Client"
                width="250"
                mode="Default" />
              <field
                path="RCD_StartDateOverride"
                width="190"
                mode="Default" />
              <field
                path="RCD_EndDateOverride"
                width="170"
                mode="Default" />
              <field
                path="FirstFreeDay"
                width="250"
                mode="Default" />
              <field
                path="LastFreeDay"
                width="250"
                mode="Default" />
            </fields>
          </xml>
        </placeholder>
        <placeholder
          name="DefaultSortFields"
          value="" />
        <placeholder
          name="ShowDocumentsIcon"
          value="True" />
        <placeholder
          name="RowActionsMode"
          value="" />
        <placeholder
          name="HideGridActions"
          value="False" />
        <placeholder
          name="HideFilters"
          value="False" />
        <placeholder
          name="ActionMenuItems"
          value="" />
        <placeholder
          name="EditFormFlow"
          value="" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
    </control>
    <control
      code="GRP"
      id="e82767e4-4744-4ec3-9d60-058fba005e3a"
      left="0"
      top="9"
      right="0"
      bottom="0">
      <placeholder
        name="CanBeHidden"
        value="True" />
      <placeholder
        name="VisibilityCondition"
        value="5c4ac2d0-5ba8-4347-88cf-af4a0a2818b2" />
      <placeholder
        name="Header"
        value="Allocated Consolidations"
        resid="dd2add6e-5437-4e6e-b000-67c768c7dfb6" />
      <placeholder
        name="HeaderAlignment"
        value="Left" />
      <placeholder
        name="HeaderDisplayMode"
        value="Hidden" />
      <placeholder
        name="BackgroundImagePk"
        value="" />
      <placeholder
        name="BackgroundImageOverride"
        value="" />
      <control
        code="GRD"
        id="121156b4-9a76-4daa-a55d-8b3e02d5a348"
        left="1"
        top="0"
        right="1"
        bottom="2"
        binding="Consolidations">
        <placeholder
          name="IsReadOnly"
          value="False" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="CaptionOverride"
          value="Allocated Consolidations"
          resid="3dec4b1d-0a1e-4a1b-9faf-268030f99e28" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="HeaderAlignment"
          value="Left" />
        <placeholder
          name="HeaderDisplayMode"
          value="Visible" />
        <placeholder
          name="AllowAdd"
          value="False" />
        <placeholder
          name="AllowAttach"
          value="True" />
        <placeholder
          name="AllowDetach"
          value="True" />
        <placeholder
          name="AllowInlineEdit"
          value="False" />
        <placeholder
          name="FieldConfiguration">
          <xml>
            <fields xmlns="">
              <field
                path="JK_UniqueConsignRef"
                width="200"
                mode="Mandatory" />
              <field
                path="JK_MasterBillNum"
                width="200"
                mode="Default" />
              <field
                path="JK_TransportMode"
                width="200"
                mode="Default" />
              <field
                path="TransportFirstLeg.JW_VoyageFlight"
                width="250"
                mode="Optional" />
              <field
                path="TransportLastLeg.JW_VoyageFlight"
                width="250"
                mode="Optional" />
              <field
                path="ArrivalCTOAddress.OrgHeader.OH_FullName"
                width="250"
                mode="Default" />
              <field
                path="DepartureCTOAddress.OrgHeader.OH_FullName"
                width="250"
                mode="Default" />
              <field
                path="UnpackDepotAddress.OrgHeader.OH_FullName"
                width="250"
                mode="Default" />
              <field
                path="ShippingLineAddress.OrgHeader.OH_FullName"
                width="250"
                mode="Default" />
              <field
                path="JK_IsCancelled"
                width="120"
                mode="Optional" />
              <field
                path="TransportFirstLeg.JW_ATD"
                width="120"
                mode="Default" />
              <field
                path="TransportFirstLeg.JW_ETD"
                width="120"
                mode="Default" />
              <field
                path="TransportLastLeg.JW_ATA"
                width="120"
                mode="Default" />
              <field
                path="TransportLastLeg.JW_ETA"
                width="120"
                mode="Default" />
              <field
                path="JK_RL_NKLoadPort"
                width="80"
                mode="Default" />
              <field
                path="JK_RL_NKDischargePort"
                width="90"
                mode="Default" />
              <field
                path="JK_AgentsReference"
                width="100"
                mode="Optional" />
              <field
                path="JK_AgentType"
                width="100"
                mode="Optional" />
              <field
                path="JK_AWBServiceLevel"
                width="100"
                mode="Optional" />
              <field
                path="JK_BookingReference"
                width="100"
                mode="Optional" />
              <field
                path="JK_CoLoadBookingReference"
                width="100"
                mode="Optional" />
              <field
                path="JK_CoLoadMasterBill"
                width="100"
                mode="Optional" />
              <field
                path="JK_ConsolChargeable"
                width="100"
                mode="Optional" />
              <field
                path="JK_ConsolChargeableRate"
                width="100"
                mode="Optional" />
              <field
                path="JK_ConsolCutOffDate"
                width="100"
                mode="Optional" />
              <field
                path="JK_ConsolMode"
                width="100"
                mode="Optional" />
              <field
                path="JK_ConsolStatus"
                width="100"
                mode="Optional" />
              <field
                path="JK_CorrectedConsolVolume"
                width="100"
                mode="Optional" />
              <field
                path="JK_CorrectedConsolVolumeUnit"
                width="100"
                mode="Optional" />
              <field
                path="JK_CorrectedConsolWeight"
                width="100"
                mode="Optional" />
              <field
                path="JK_CorrectedConsolWeightUnit"
                width="100"
                mode="Optional" />
              <field
                path="JK_CustomDate1"
                width="100"
                mode="Optional" />
              <field
                path="JK_CustomDate2"
                width="100"
                mode="Optional" />
              <field
                path="JK_CustomFlag1"
                width="100"
                mode="Optional" />
              <field
                path="JK_CustomFlag2"
                width="100"
                mode="Optional" />
              <field
                path="JK_CustomsReference"
                width="100"
                mode="Optional" />
              <field
                path="JK_DateFirstForeignPort"
                width="100"
                mode="Optional" />
              <field
                path="JK_DateLastForeignPort"
                width="100"
                mode="Optional" />
              <field
                path="JK_DatePortOfFirstArrival"
                width="100"
                mode="Optional" />
              <field
                path="JK_IsCFS"
                width="100"
                mode="Optional" />
              <field
                path="JK_IsForwarding"
                width="100"
                mode="Optional" />
              <field
                path="JK_IsHazardous"
                width="100"
                mode="Optional" />
              <field
                path="JK_IsNeutralMaster"
                width="100"
                mode="Optional" />
              <field
                path="JK_MasterBillIssueDate"
                width="100"
                mode="Optional" />
              <field
                path="JK_MaximumAllowablePackageHeight"
                width="100"
                mode="Optional" />
              <field
                path="JK_MaximumAllowablePackageLength"
                width="100"
                mode="Optional" />
              <field
                path="JK_MaximumAllowablePackageUnit"
                width="100"
                mode="Optional" />
              <field
                path="JK_MaximumAllowablePackageWidth"
                width="100"
                mode="Optional" />
              <field
                path="JK_MBLAWBChargesDisplay"
                width="100"
                mode="Optional" />
              <field
                path="JK_NoCopyBills"
                width="100"
                mode="Optional" />
              <field
                path="JK_NoOriginalBills"
                width="100"
                mode="Optional" />
              <field
                path="JK_OA_ArrivalCTOAddress"
                width="100"
                mode="Optional" />
              <field
                path="JK_OA_ArrivalUnpackCFSTransportAddress"
                width="100"
                mode="Optional" />
              <field
                path="JK_OA_CoLoadAddress"
                width="100"
                mode="Optional" />
              <field
                path="JK_OA_ContainerYardEmptyPickupAddress"
                width="100"
                mode="Optional" />
              <field
                path="JK_OA_ContainerYardEmptyReturnAddress"
                width="100"
                mode="Optional" />
              <field
                path="JK_OA_CreditorAddress"
                width="100"
                mode="Optional" />
              <field
                path="JK_OA_DepartureCTOAddress"
                width="100"
                mode="Optional" />
              <field
                path="JK_OA_DeparturePackCFSTransportAddress"
                width="100"
                mode="Optional" />
              <field
                path="JK_OA_PackDepotAddress"
                width="100"
                mode="Optional" />
              <field
                path="JK_OA_ReceivingForwarderAddress"
                width="100"
                mode="Optional" />
              <field
                path="JK_OA_SendingForwarderAddress"
                width="100"
                mode="Optional" />
              <field
                path="JK_OA_ShippingLineAddress"
                width="100"
                mode="Optional" />
              <field
                path="JK_OA_UnpackDepotAddress"
                width="100"
                mode="Optional" />
              <field
                path="JK_OC_ReceivingForwarderContact"
                width="100"
                mode="Optional" />
              <field
                path="JK_OC_SendingForwarderContact"
                width="100"
                mode="Optional" />
              <field
                path="JK_OH_ArrivalUnpackCFSTransport"
                width="100"
                mode="Optional" />
              <field
                path="JK_OH_Creditor"
                width="100"
                mode="Optional" />
              <field
                path="JK_OH_DeparturePackCFSTransport"
                width="100"
                mode="Optional" />
              <field
                path="JK_OverrideConsolChargeable"
                width="100"
                mode="Optional" />
              <field
                path="JK_OverrideWaybillDefaults"
                width="100"
                mode="Optional" />
              <field
                path="JK_PackDepotDispatchRequested"
                width="100"
                mode="Optional" />
              <field
                path="JK_PackDepotReceiptRequested"
                width="100"
                mode="Optional" />
              <field
                path="JK_Phase"
                width="100"
                mode="Optional" />
              <field
                path="JK_PrepaidCollect"
                width="100"
                mode="Optional" />
              <field
                path="JK_PrintOptionForColoadsOnManifest"
                width="100"
                mode="Optional" />
              <field
                path="JK_PrintOptionForColoadsOnOtherDocs"
                width="100"
                mode="Optional" />
              <field
                path="JK_PrintOptionForPackagesOnAWB"
                width="100"
                mode="Optional" />
              <field
                path="JK_ReceivingForwarderHandlingType"
                width="100"
                mode="Optional" />
              <field
                path="JK_ReleaseType"
                width="100"
                mode="Optional" />
              <field
                path="JK_RequiredTemperatureMaximum"
                width="100"
                mode="Optional" />
              <field
                path="JK_RequiredTemperatureMinimum"
                width="100"
                mode="Optional" />
              <field
                path="JK_RequiredTemperatureUnit"
                width="100"
                mode="Optional" />
              <field
                path="JK_RequiresTemperatureControl"
                width="100"
                mode="Optional" />
              <field
                path="JK_RL_NKFirstForeignPort"
                width="100"
                mode="Optional" />
              <field
                path="JK_RL_NKLastForeignPort"
                width="100"
                mode="Optional" />
              <field
                path="JK_RL_NKMasterBillIssuePlace"
                width="100"
                mode="Optional" />
              <field
                path="JK_RL_NKPortOfFirstArrival"
                width="100"
                mode="Optional" />
              <field
                path="JK_ScreeningStatus"
                width="100"
                mode="Optional" />
              <field
                path="JK_SendingForwarderHandlingType"
                width="100"
                mode="Optional" />
              <field
                path="JK_ShippedOnBoardDate"
                width="100"
                mode="Optional" />
              <field
                path="JK_SystemCreateTimeUtc"
                width="100"
                mode="Optional" />
              <field
                path="JK_SystemCreateUser"
                width="100"
                mode="Optional" />
              <field
                path="JK_SystemLastEditTimeUtc"
                width="100"
                mode="Optional" />
              <field
                path="JK_SystemLastEditUser"
                width="100"
                mode="Optional" />
              <field
                path="JK_TotalShipmentActOtherUnit"
                width="100"
                mode="Optional" />
              <field
                path="JK_TotalShipmentActVolumeCheck"
                width="100"
                mode="Optional" />
              <field
                path="JK_TotalShipmentActWeightCheck"
                width="100"
                mode="Optional" />
              <field
                path="JK_TotalShipmentChargableCheck"
                width="100"
                mode="Optional" />
              <field
                path="JK_TotalShipmentChargeableUnit"
                width="100"
                mode="Optional" />
              <field
                path="JK_TotalShipmentCountCheck"
                width="100"
                mode="Optional" />
              <field
                path="JK_UnpackDepotDispatchRequested"
                width="100"
                mode="Optional" />
              <field
                path="JK_UnpackDepotReceiptRequested"
                width="100"
                mode="Optional" />
              <field
                path="ContainerCount"
                width="80"
                mode="Optional" />
              <field
                path="LoadListInstructionsNote"
                width="250"
                mode="Optional" />
              <field
                path="ContainerCountPerType"
                width="250"
                mode="Optional" />
              <field
                path="CTOCutOff"
                width="250"
                mode="Optional" />
              <field
                path="JK_CarrierContractNumber"
                width="250"
                mode="Optional" />
              <field
                path="TransportFirstLeg.JW_Vessel"
                width="250"
                mode="Optional" />
              <field
                path="AllocationLevel"
                width="250"
                mode="Optional" />
              <field
                path="ContainerCountNumber"
                width="190"
                mode="Optional" />
              <field
                path="TEU"
                width="70"
                mode="Optional" />
              <field
                path="AllocatedCN"
                width="120"
                mode="Optional" />
              <field
                path="AllocatedTEU"
                width="130"
                mode="Optional" />
              <field
                path="AllocationRoutes"
                width="250"
                mode="Optional" />
              <field
                path="ContainerTypesSummary"
                width="250"
                mode="Default" />
            </fields>
          </xml>
        </placeholder>
        <placeholder
          name="DefaultSortFields"
          value="" />
        <placeholder
          name="ShowDocumentsIcon"
          value="False" />
        <placeholder
          name="RowActionsMode"
          value="MenuItems" />
        <placeholder
          name="HideGridActions"
          value="False" />
        <placeholder
          name="HideFilters"
          value="False" />
        <placeholder
          name="ActionMenuItems"
          value="" />
        <placeholder
          name="EditFormFlow"
          value="" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="TBT"
        id="4be2b4bc-6e78-7290-484a-169f2e6b7f3a"
        left="1"
        width="5"
        height="1"
        bottom="0.5">
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="Content"
          value="Allocate Consolidations"
          resid="3cf38626-60b8-31ab-4206-7ad148043f23" />
        <placeholder
          name="IsDefault"
          value="False" />
      </control>
    </control>
    <control
      code="GRP"
      id="8704f403-c445-4fae-ba1c-22364249f15d"
      left="0"
      top="9"
      right="0"
      bottom="0">
      <placeholder
        name="CanBeHidden"
        value="True" />
      <placeholder
        name="VisibilityCondition"
        value="f547ffac-763c-4b6b-ab54-62e134001175" />
      <placeholder
        name="Header"
        value="Allocated Bookings"
        resid="f6bfee6c-fc3a-4697-bf47-4743f04281a9" />
      <placeholder
        name="HeaderAlignment"
        value="Left" />
      <placeholder
        name="HeaderDisplayMode"
        value="Hidden" />
      <placeholder
        name="BackgroundImagePk"
        value="" />
      <placeholder
        name="BackgroundImageOverride"
        value="" />
      <control
        code="GRD"
        id="ef25ef30-4aba-4da3-9286-b10b05e7a9f5"
        left="1"
        top="0"
        right="1"
        bottom="2"
        binding="Bookings">
        <placeholder
          name="IsReadOnly"
          value="False" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="CaptionOverride"
          value="Allocated Bookings"
          resid="d161a8d9-035a-4153-8a66-68f98ce85ab2" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="HeaderAlignment"
          value="Left" />
        <placeholder
          name="HeaderDisplayMode"
          value="Visible" />
        <placeholder
          name="AllowAdd"
          value="False" />
        <placeholder
          name="AllowAttach"
          value="True" />
        <placeholder
          name="AllowDetach"
          value="True" />
        <placeholder
          name="AllowInlineEdit"
          value="False" />
        <placeholder
          name="FieldConfiguration">
          <xml>
            <fields xmlns="">
              <field
                name="Quote"
                path="VB_QuoteNumber"
                width="200"
                mode="Mandatory" />
              <field
                path="Booking.JS_UniqueConsignRef"
                width="200"
                mode="Default" />
              <field
                path="Booking.ConsignorDocumentaryAddress.OrganizationCode"
                width="250"
                mode="Default" />
              <field
                path="Booking.ConsignorName"
                width="250"
                mode="Default" />
              <field
                path="Booking.JS_TransportMode"
                width="240"
                mode="Default" />
              <field
                path="Booking.OneTimeQuote.TH_IsLocked"
                width="250"
                mode="Default" />
              <field
                path="Booking.OneTimeQuote.TH_IsOneOffQuoteConsumed"
                width="250"
                mode="Default" />
              <field
                path="Booking.JS_GoodsValue"
                width="200"
                mode="Default" />
              <field
                path="Booking.JS_CFSReference"
                width="200"
                mode="Default" />
              <field
                path="Booking.GoodsValueCurr.RX_Code"
                width="40"
                mode="Default" />
              <field
                path="RatingHeader.OneOffShipment.TT_QuoteKPI"
                width="250"
                mode="Default" />
              <field
                path="RatingHeader.OneOffShipment.TT_Status"
                width="250"
                mode="Default" />
              <field
                path="RatingHeader.OneOffShipment.TT_QuoteSource"
                width="250"
                mode="Default" />
              <field
                path="RatingHeader.OneOffShipment.TT_RevisionReason"
                width="250"
                mode="Default" />
            </fields>
          </xml>
        </placeholder>
        <placeholder
          name="DefaultSortFields"
          value="" />
        <placeholder
          name="ShowDocumentsIcon"
          value="False" />
        <placeholder
          name="RowActionsMode"
          value="MenuItems" />
        <placeholder
          name="HideGridActions"
          value="False" />
        <placeholder
          name="HideFilters"
          value="False" />
        <placeholder
          name="ActionMenuItems"
          value="" />
        <placeholder
          name="EditFormFlow"
          value="" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
    </control>
    <control
      code="GRP"
      id="aa7dc872-a0c9-48ad-8671-12455cd7cde6"
      left="0"
      top="9"
      right="0"
      bottom="0">
      <placeholder
        name="CanBeHidden"
        value="True" />
      <placeholder
        name="VisibilityCondition"
        value="274a8a5b-cf68-4ec5-ad2e-ebccf6ed50de" />
      <placeholder
        name="Header"
        value="Allocation Routes Section"
        resid="83e9bd6a-9637-47c2-b8bd-119eeefd39e4" />
      <placeholder
        name="HeaderAlignment"
        value="Left" />
      <placeholder
        name="HeaderDisplayMode"
        value="Hidden" />
      <placeholder
        name="BackgroundImagePk"
        value="" />
      <placeholder
        name="BackgroundImageOverride"
        value="" />
      <control
        code="GRD"
        id="5574b506-88f4-49e1-9e07-3843ac46dca1"
        left="1"
        top="0"
        height="9"
        right="1"
        binding="RatingContractAllocationLines">
        <placeholder
          name="IsReadOnly"
          value="False" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="CaptionOverride"
          value="Allocation Routes"
          resid="b936e35b-0e70-40b2-89e8-1dbe692531c5" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="HeaderAlignment"
          value="Left" />
        <placeholder
          name="HeaderDisplayMode"
          value="Visible" />
        <placeholder
          name="AllowAdd"
          value="True" />
        <placeholder
          name="AllowAttach"
          value="True" />
        <placeholder
          name="AllowDetach"
          value="True" />
        <placeholder
          name="AllowInlineEdit"
          value="True" />
        <placeholder
          name="FieldConfiguration">
          <xml>
            <fields xmlns="">
              <field
                path="RCA_AllocationLineID"
                width="120"
                mode="Default" />
              <field
                path="RCA_StartDate"
                width="120"
                mode="Default" />
              <field
                path="RCA_ExpiryDate"
                width="120"
                mode="Default" />
              <field
                path="LoadLocationWithProxy"
                width="110"
                mode="Default" />
              <field
                path="DischargeLocationWithProxy"
                width="110"
                mode="Default" />
              <field
                path="VoyageWithProxy"
                width="110"
                mode="Default" />
              <field
                path="VesselWithProxy"
                width="120"
                mode="Default" />
              <field
                path="RCA_RC_ContainerType"
                width="100"
                mode="Default" />
              <field
                path="RCA_StorageOrFreightRateClass"
                width="200"
                mode="Default" />
              <field
                path="AllocatedQuantity"
                width="80"
                mode="Default" />
              <field
                path="RCA_AllocatedUQ"
                width="80"
                mode="Default" />
              <field
                path="RCA_AllowRelatedPorts"
                width="170"
                mode="Default" />
              <field
                path="ServiceStringWithProxy"
                width="250"
                mode="Default" />
              <field
                path="HasBookingLimit"
                width="100"
                mode="Default" />
              <field
                path="BookingVariance"
                width="150"
                mode="Default" />
              <field
                path="Utilization"
                width="100"
                mode="Optional" />
              <field
                path="OutstandingCommitted"
                width="150"
                mode="Default" />
              <field
                path="OutstandingWithVariance"
                width="160"
                mode="Default" />
              <field
                path="CapacityWithVariance"
                width="250"
                mode="Optional" />
              <field
                path="SailingSchedule.JX_UniqueReference"
                width="130"
                mode="Optional"
                readOnly="true" />
              <field
                path="SailingSchedule.JobVoyOrigin.JA_E_DEP"
                width="130"
                mode="Optional"
                readOnly="true" />
              <field
                path="LinkedScheduleETDUpdated"
                width="200"
                mode="Optional"
                readOnly="true" />
              <field
                path="SailingSchedule.JobVoyOrigin.JA_S_DEP"
                width="130"
                mode="Optional"
                readOnly="true" />
              <field
                path="SailingSchedule.JobVoyDestination.JB_E_ARV"
                width="130"
                mode="Optional"
                readOnly="true" />
              <field
                path="SailingSchedule.JobVoyDestination.JB_S_ARV"
                width="130"
                mode="Optional"
                readOnly="true" />
            </fields>
          </xml>
        </placeholder>
        <placeholder
          name="DefaultSortFields"
          value="" />
        <placeholder
          name="ShowDocumentsIcon"
          value="True" />
        <placeholder
          name="RowActionsMode"
          value="MenuItems" />
        <placeholder
          name="HideGridActions"
          value="False" />
        <placeholder
          name="HideFilters"
          value="False" />
        <placeholder
          name="ActionMenuItems"
          value="" />
        <placeholder
          name="EditFormFlow"
          value="" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="OBG"
        id="42f23d42-d839-b784-4a5f-b957f421b5f3"
        left="1"
        top="10"
        width="14"
        height="1"
        binding="RoutesSelectedTab">
        <placeholder
          name="IsReadOnly"
          value="False" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="GridColumns"
          value="3" />
        <placeholder
          name="ButtonDisplayMode"
          value="DescOnly" />
        <placeholder
          name="HeaderDisplayMode"
          value="Hidden" />
        <placeholder
          name="IsIconVisible"
          value="False" />
        <placeholder
          name="IconPosition"
          value="Left" />
        <placeholder
          name="IconSize"
          value="Default" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
    </control>
    <control
      code="GRP"
      id="b60466f4-ce63-4cb4-a112-0242b5a787ba"
      left="1"
      top="9"
      width="23"
      height="15">
      <placeholder
        name="CanBeHidden"
        value="True" />
      <placeholder
        name="VisibilityCondition"
        value="34f144d0-abc8-44d9-b780-7baaf644cbf5" />
      <placeholder
        name="Header"
        value="Tariffs &amp; Rates"
        resid="29b193a1-d369-4f3a-b0a7-1c252b3b8279" />
      <placeholder
        name="HeaderAlignment"
        value="Left" />
      <placeholder
        name="HeaderDisplayMode"
        value="Visible" />
      <placeholder
        name="BackgroundImagePk"
        value="" />
      <placeholder
        name="BackgroundImageOverride"
        value="" />
      <control
        code="CMB"
        id="57134cbe-6abf-4976-aeb2-3d5e2f1a977c"
        left="0"
        top="0"
        width="4"
        height="1"
        binding="RCT_AutoratingDateFiltering">
        <placeholder
          name="IsReadOnly"
          value="False" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="DisplayMode"
          value="CodeDesc" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="GRD"
        id="f6da73a2-bf8a-4bf6-a5d6-feeac50cd85f"
        left="0"
        top="1"
        width="23"
        height="12"
        binding="ChargeGroupContractRatingDates">
        <placeholder
          name="IsReadOnly"
          value="False" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="CaptionOverride"
          value="" />
        <placeholder
          name="VisibilityCondition"
          value="04ce2346-1c11-46f3-acf5-5f8cb8355a09" />
        <placeholder
          name="HeaderAlignment"
          value="Left" />
        <placeholder
          name="HeaderDisplayMode"
          value="Visible" />
        <placeholder
          name="AllowAdd"
          value="True" />
        <placeholder
          name="AllowAttach"
          value="True" />
        <placeholder
          name="AllowDetach"
          value="True" />
        <placeholder
          name="AllowInlineEdit"
          value="True" />
        <placeholder
          name="FieldConfiguration"
          value="" />
        <placeholder
          name="DefaultSortFields"
          value="" />
        <placeholder
          name="ShowDocumentsIcon"
          value="True" />
        <placeholder
          name="RowActionsMode"
          value="RowAndMenu" />
        <placeholder
          name="HideGridActions"
          value="False" />
        <placeholder
          name="HideFilters"
          value="False" />
        <placeholder
          name="ActionMenuItems"
          value="" />
        <placeholder
          name="EditFormFlow"
          value="" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
    </control>
    <control
      code="GRP"
      id="ee2cdd51-274b-c2a3-4d20-d1decb3d7d33"
      left="0"
      top="20"
      right="0"
      bottom="0">
      <placeholder
        name="CanBeHidden"
        value="True" />
      <placeholder
        name="VisibilityCondition"
        value="4dde622b-1de7-4388-8a45-c981995d5aee" />
      <placeholder
        name="Header"
        value="Named Account Clients"
        resid="ff3a76dc-7a09-78bf-4e05-06e1415397b1" />
      <placeholder
        name="HeaderAlignment"
        value="Left" />
      <placeholder
        name="HeaderDisplayMode"
        value="Hidden" />
      <placeholder
        name="BackgroundImagePk"
        value="" />
      <placeholder
        name="BackgroundImageOverride"
        value="" />
      <control
        code="GRD"
        id="754c8344-bbce-4319-b6d2-015e2fb055f7"
        left="1"
        top="0"
        right="1"
        bottom="1"
        binding="RatingContractAllocationLines/RatingContractNamedAccountPivots">
        <placeholder
          name="IsReadOnly"
          value="False" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="CaptionOverride"
          value="Named Account Clients of Route"
          resid="39ef4063-1c9d-479d-8aa2-9c51ef4c50d6" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="HeaderAlignment"
          value="Left" />
        <placeholder
          name="HeaderDisplayMode"
          value="Visible" />
        <placeholder
          name="AllowAdd"
          value="True" />
        <placeholder
          name="AllowAttach"
          value="True" />
        <placeholder
          name="AllowDetach"
          value="True" />
        <placeholder
          name="AllowInlineEdit"
          value="True" />
        <placeholder
          name="FieldConfiguration"
          value="" />
        <placeholder
          name="DefaultSortFields"
          value="" />
        <placeholder
          name="ShowDocumentsIcon"
          value="True" />
        <placeholder
          name="RowActionsMode"
          value="RowAndMenu" />
        <placeholder
          name="HideGridActions"
          value="False" />
        <placeholder
          name="HideFilters"
          value="False" />
        <placeholder
          name="ActionMenuItems"
          value="" />
        <placeholder
          name="EditFormFlow"
          value="" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
    </control>
    <control
      code="GRP"
      id="ba5fce83-0b17-5a87-4ec2-ec6e9bc80cd9"
      left="0"
      top="20"
      right="0"
      bottom="0">
      <placeholder
        name="CanBeHidden"
        value="True" />
      <placeholder
        name="VisibilityCondition"
        value="f250b537-69a3-4a8d-a8bc-eaab62bdf707" />
      <placeholder
        name="Header"
        value="Consolidations and Containers"
        resid="743f5557-d1ed-ddb4-474b-8c4123ec5c72" />
      <placeholder
        name="HeaderAlignment"
        value="Left" />
      <placeholder
        name="HeaderDisplayMode"
        value="Hidden" />
      <placeholder
        name="BackgroundImagePk"
        value="" />
      <placeholder
        name="BackgroundImageOverride"
        value="" />
      <control
        code="GRD"
        id="58f97025-b475-4295-b1b6-63f1858369e1"
        left="1"
        top="0"
        width="21"
        bottom="1"
        binding="RatingContractAllocationLines/JobConsols">
        <placeholder
          name="IsReadOnly"
          value="True" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="CaptionOverride"
          value="Allocated Consolidations of Route"
          resid="9a778736-d640-4e2b-b38d-0719f7a45972" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="HeaderAlignment"
          value="Left" />
        <placeholder
          name="HeaderDisplayMode"
          value="Visible" />
        <placeholder
          name="AllowAdd"
          value="False" />
        <placeholder
          name="AllowAttach"
          value="True" />
        <placeholder
          name="AllowDetach"
          value="True" />
        <placeholder
          name="AllowInlineEdit"
          value="False" />
        <placeholder
          name="FieldConfiguration"
          value="" />
        <placeholder
          name="DefaultSortFields"
          value="" />
        <placeholder
          name="ShowDocumentsIcon"
          value="False" />
        <placeholder
          name="RowActionsMode"
          value="MenuItems" />
        <placeholder
          name="HideGridActions"
          value="False" />
        <placeholder
          name="HideFilters"
          value="False" />
        <placeholder
          name="ActionMenuItems"
          value="" />
        <placeholder
          name="EditFormFlow"
          value="" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="GRD"
        id="59e4e746-d057-4b63-a194-a7b32b0211e6"
        left="23"
        top="0"
        right="1"
        bottom="1"
        binding="RatingContractAllocationLines/ConsolContainers">
        <placeholder
          name="IsReadOnly"
          value="True" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="CaptionOverride"
          value="Allocated Consolidation Containers of Route"
          resid="367993f9-2f0d-4cef-943c-0505730bdb7f" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="HeaderAlignment"
          value="Left" />
        <placeholder
          name="HeaderDisplayMode"
          value="Visible" />
        <placeholder
          name="AllowAdd"
          value="False" />
        <placeholder
          name="AllowAttach"
          value="True" />
        <placeholder
          name="AllowDetach"
          value="True" />
        <placeholder
          name="AllowInlineEdit"
          value="False" />
        <placeholder
          name="FieldConfiguration"
          value="" />
        <placeholder
          name="DefaultSortFields"
          value="" />
        <placeholder
          name="ShowDocumentsIcon"
          value="False" />
        <placeholder
          name="RowActionsMode"
          value="MenuItems" />
        <placeholder
          name="HideGridActions"
          value="False" />
        <placeholder
          name="HideFilters"
          value="False" />
        <placeholder
          name="ActionMenuItems"
          value="" />
        <placeholder
          name="EditFormFlow"
          value="" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
    </control>
    <control
      code="GRP"
      id="315e18fa-00e1-4f35-aff7-e0a297656b47"
      left="0"
      top="20"
      right="0"
      bottom="0">
      <placeholder
        name="CanBeHidden"
        value="True" />
      <placeholder
        name="VisibilityCondition"
        value="34ed5e69-6948-4eb2-9e2b-dc8462c5be91" />
      <placeholder
        name="Header"
        value="Bookings and Containers"
        resid="95b11465-be0a-40e2-b04f-5d8f61365653" />
      <placeholder
        name="HeaderAlignment"
        value="Left" />
      <placeholder
        name="HeaderDisplayMode"
        value="Hidden" />
      <placeholder
        name="BackgroundImagePk"
        value="" />
      <placeholder
        name="BackgroundImageOverride"
        value="" />
      <control
        code="GRD"
        id="aa1ec948-4778-45bc-93f3-ace5fefbd5d7"
        left="1"
        top="0"
        width="21"
        bottom="1"
        binding="RatingContractAllocationLines/Bookings">
        <placeholder
          name="IsReadOnly"
          value="True" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="CaptionOverride"
          value="Allocated Bookings of Route"
          resid="069d417d-c78f-414f-b4cb-9351010735b2" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="HeaderAlignment"
          value="Left" />
        <placeholder
          name="HeaderDisplayMode"
          value="Visible" />
        <placeholder
          name="AllowAdd"
          value="False" />
        <placeholder
          name="AllowAttach"
          value="True" />
        <placeholder
          name="AllowDetach"
          value="True" />
        <placeholder
          name="AllowInlineEdit"
          value="False" />
        <placeholder
          name="FieldConfiguration"
          value="" />
        <placeholder
          name="DefaultSortFields"
          value="" />
        <placeholder
          name="ShowDocumentsIcon"
          value="False" />
        <placeholder
          name="RowActionsMode"
          value="MenuItems" />
        <placeholder
          name="HideGridActions"
          value="False" />
        <placeholder
          name="HideFilters"
          value="False" />
        <placeholder
          name="ActionMenuItems"
          value="" />
        <placeholder
          name="EditFormFlow"
          value="" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="GRD"
        id="32b8b547-8904-4c44-a0a2-a5b48e31f83d"
        left="23"
        top="0"
        right="1"
        bottom="1"
        binding="RatingContractAllocationLines/JobContainers">
        <placeholder
          name="IsReadOnly"
          value="True" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="CaptionOverride"
          value="Allocated Booking Containers of Route"
          resid="de0b0cb8-8480-42dd-964d-b8851b67a496" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="HeaderAlignment"
          value="Left" />
        <placeholder
          name="HeaderDisplayMode"
          value="Visible" />
        <placeholder
          name="AllowAdd"
          value="False" />
        <placeholder
          name="AllowAttach"
          value="True" />
        <placeholder
          name="AllowDetach"
          value="True" />
        <placeholder
          name="AllowInlineEdit"
          value="False" />
        <placeholder
          name="FieldConfiguration">
          <xml>
            <fields xmlns="">
              <field
                path="JC_ContainerNum"
                width="200"
                mode="Default" />
              <field
                path="JC_FCLUnloadFromVessel"
                width="180"
                mode="Default" />
              <field
                path="JC_FCLAvailable"
                width="180"
                mode="Default" />
              <field
                path="JC_ArrivalCTOStorageStartDate"
                width="180"
                mode="Default" />
              <field
                path="JC_FCLWharfGateOut"
                width="180"
                mode="Default" />
              <field
                path="AllocationLevel"
                width="250"
                mode="Default" />
              <field
                path="JC_ArrivalSlotReference"
                width="200"
                mode="Default" />
              <field
                path="JC_RH_NKContainerCommodityCode"
                width="90"
                mode="Default" />
              <field
                path="JC_ContainerQuality"
                width="70"
                mode="Default" />
              <field
                path="JC_ContainerStatus"
                width="60"
                mode="Default" />
              <field
                path="RefContainer.RC_ShippingMode"
                width="40"
                mode="Default" />
              <field
                path="JC_ContainerYardEmptyReturnGateIn"
                width="180"
                mode="Default" />
              <field
                path="JC_IsDamaged"
                width="100"
                mode="Default" />
              <field
                path="JC_DeliverySequence"
                width="80"
                mode="Default" />
              <field
                path="JC_DepartureSlotReference"
                width="200"
                mode="Default" />
              <field
                path="JC_IsEmptyContainer"
                width="70"
                mode="Default" />
              <field
                path="JC_ReleaseNum"
                width="200"
                mode="Default" />
              <field
                path="JC_ContainerYardEmptyPickupGateOut"
                width="220"
                mode="Default" />
              <field
                path="JC_EmptyReturnReference"
                width="200"
                mode="Default" />
              <field
                path="JC_FCLOnBoardVessel"
                width="180"
                mode="Default" />
              <field
                path="JC_FCLWharfGateIn"
                width="180"
                mode="Default" />
              <field
                path="JC_SealNum"
                width="200"
                mode="Optional" />
              <field
                path="JC_RC"
                width="250"
                mode="Optional" />
              <field
                path="JC_ArrivalCartageComplete"
                width="200"
                mode="Optional" />
              <field
                path="JC_DepartureCartageComplete"
                width="180"
                mode="Optional" />
              <field
                path="JC_AirVentFlow"
                width="160"
                mode="Optional" />
              <field
                path="JC_ArrivalDeliveryRequiredBy"
                width="250"
                mode="Optional" />
              <field
                path="ArrivalPort"
                width="250"
                mode="Optional" />
              <field
                path="JC_LCLAvailable"
                width="180"
                mode="Optional" />
              <field
                path="JC_LCLStorageCommences"
                width="180"
                mode="Optional" />
              <field
                path="Chiller"
                width="70"
                mode="Optional" />
              <field
                path="ChillerOrFreezer"
                width="250"
                mode="Optional" />
              <field
                path="JC_OH_CFSClient"
                width="250"
                mode="Optional" />
              <field
                path="JC_RefrigGeneratorID"
                width="170"
                mode="Optional" />
              <field
                path="ContactHasWebContainerViewRight"
                width="250"
                mode="Optional" />
              <field
                path="JC_ContainerCount"
                width="70"
                mode="Optional" />
              <field
                path="JC_ContainerImportDORelease"
                width="240"
                mode="Optional" />
              <field
                path="JC_ContainerJobID"
                width="200"
                mode="Optional" />
              <field
                path="JC_ContainerMode"
                width="40"
                mode="Optional" />
              <field
                path="JC_IsControlledAtmosphere"
                width="100"
                mode="Optional" />
              <field
                path="JC_SystemCreateUser"
                width="160"
                mode="Optional" />
              <field
                path="JC_DeliveryMode"
                width="130"
                mode="Optional" />
              <field
                path="JC_DepartureSlotDateTime"
                width="180"
                mode="Optional" />
              <field
                path="JC_DepartureDockReceipt"
                width="200"
                mode="Optional" />
              <field
                path="JC_DunnageWeightMeasure"
                width="130"
                mode="Optional" />
              <field
                path="JC_OA_DepartureContainerYardAddress"
                width="250"
                mode="Optional" />
              <field
                path="JC_EmptyReadyForReturn"
                width="180"
                mode="Optional" />
              <field
                path="JC_EmptyRequired"
                width="180"
                mode="Optional" />
              <field
                path="JC_EmptyReturnedBy"
                width="180"
                mode="Optional" />
              <field
                path="JC_OA_ArrivalContainerYardAddress"
                width="250"
                mode="Optional" />
              <field
                path="JC_ArrivalEstimatedDelivery"
                width="230"
                mode="Optional" />
              <field
                path="JC_DepartureEstimatedPickup"
                width="180"
                mode="Optional" />
              <field
                path="JC_ExportDepotCustomsReference"
                width="200"
                mode="Optional" />
              <field
                path="JC_JS_FCLBookingOnlyLink"
                width="250"
                mode="Optional" />
              <field
                path="JC_FCLStorageArrivedUnderbond"
                width="210"
                mode="Optional" />
              <field
                path="JC_FCLStorageUnderbondCleared"
                width="250"
                mode="Optional" />
              <field
                path="JC_AirVentFlowRateUnit"
                width="40"
                mode="Optional" />
              <field
                path="Freezer"
                width="70"
                mode="Optional" />
              <field
                path="JC_GatewaySellSpotRate"
                width="200"
                mode="Optional" />
              <field
                path="JC_GatewaySellSpotRateMode"
                width="40"
                mode="Optional" />
              <field
                path="JC_RX_NKGatewaySellSpotRateCurrency"
                width="50"
                mode="Optional" />
              <field
                path="JC_RX_NKGoodsCurrency"
                width="80"
                mode="Optional" />
              <field
                path="JC_GoodsValue"
                width="200"
                mode="Optional" />
              <field
                path="JC_GrossWeightMeasure"
                width="130"
                mode="Optional" />
              <field
                path="JC_GrossWeightVerificationType"
                width="250"
                mode="Optional" />
              <field
                path="GrossWeightVerifiedBy"
                width="240"
                mode="Optional" />
              <field
                path="JC_HarmonisedCode"
                width="150"
                mode="Optional" />
              <field
                path="HasConsolidation"
                width="170"
                mode="Optional" />
              <field
                path="HasUSBasedLoadPort"
                width="220"
                mode="Optional" />
              <field
                path="JC_TotalHeightMeasure"
                width="130"
                mode="Optional" />
              <field
                path="JC_FCLHeldInTransitStaging"
                width="250"
                mode="Optional" />
              <field
                path="JC_HumidityPercent"
                width="100"
                mode="Optional" />
              <field
                path="JC_ImportDepotCustomsReference"
                width="250"
                mode="Optional" />
              <field
                path="JC_DepartureDeliveryByRail"
                width="250"
                mode="Optional" />
              <field
                path="JC_IsCFSRegistered"
                width="170"
                mode="Optional" />
              <field
                path="JC_IsSealOk"
                width="100"
                mode="Optional" />
              <field
                path="JC_IsShipperOwned"
                width="100"
                mode="Optional" />
              <field
                path="JC_SystemLastEditUser"
                width="200"
                mode="Optional" />
              <field
                path="JC_TotalLengthMeasure"
                width="130"
                mode="Optional" />
              <field
                path="JC_JK"
                width="250"
                mode="Optional" />
              <field
                path="LoadPort"
                width="250"
                mode="Optional" />
              <field
                path="JC_FCLStorageModuleOnlyMaster"
                width="200"
                mode="Optional" />
              <field
                path="MostRecentVGMLog"
                width="190"
                mode="Optional" />
              <field
                path="JC_Purpose"
                width="70"
                mode="Optional" />
              <field
                path="JC_CostSpotRate"
                width="200"
                mode="Optional" />
              <field
                path="JC_CostSpotRateMode"
                width="40"
                mode="Optional" />
              <field
                path="JC_RX_NKCostSpotRateCurrency"
                width="50"
                mode="Optional" />
              <field
                path="JC_OverhangBack"
                width="100"
                mode="Optional" />
              <field
                path="JC_OverhangRight"
                width="100"
                mode="Optional" />
              <field
                path="JC_OverrideFCLAvailableStorage"
                width="80"
                mode="Optional" />
              <field
                path="JC_OverrideLCLAvailableStorage"
                width="90"
                mode="Optional" />
              <field
                path="JC_PackDate"
                width="180"
                mode="Optional" />
              <field
                path="JC_F3_NKPackType"
                width="90"
                mode="Optional" />
              <field
                path="JC_ArrivalPickupByRail"
                width="140"
                mode="Optional" />
              <field
                path="PortOfArrival"
                width="250"
                mode="Optional" />
              <field
                path="PortOfLoading"
                width="250"
                mode="Optional" />
              <field
                path="JC_DepartureCartageAdvised"
                width="250"
                mode="Optional" />
              <field
                path="JC_ArrivalCartageAdvised"
                width="180"
                mode="Optional" />
              <field
                path="JC_ArrivalCartageRef"
                width="200"
                mode="Optional" />
              <field
                path="JC_DepartureCartageRef"
                width="200"
                mode="Optional" />
              <field
                path="JC_ContainerRating"
                width="60"
                mode="Optional" />
              <field
                path="JC_JX"
                width="250"
                mode="Optional" />
              <field
                path="JC_SealParty"
                width="90"
                mode="Optional" />
              <field
                path="JC_AdditionalSealNum"
                width="200"
                mode="Optional" />
              <field
                path="JC_AdditionalSealParty"
                width="130"
                mode="Optional" />
              <field
                path="JC_OH_ShippingLine"
                width="250"
                mode="Optional" />
              <field
                path="JC_ArrivalSlotDateTime"
                width="180"
                mode="Optional" />
              <field
                path="SpecialInstructionsNoteText"
                width="250"
                mode="Optional" />
              <field
                path="JC_SellSpotRate"
                width="200"
                mode="Optional" />
              <field
                path="JC_SellSpotRateMode"
                width="40"
                mode="Optional" />
              <field
                path="JC_RX_NKSellSpotRateCurrency"
                width="50"
                mode="Optional" />
              <field
                path="JC_ContainerStorageLocation"
                width="100"
                mode="Optional" />
              <field
                path="JC_StowagePosition"
                width="250"
                mode="Optional" />
              <field
                path="JC_SystemCreateTimeUtc"
                width="220"
                mode="Optional" />
              <field
                path="JC_SystemLastEditTimeUtc"
                width="250"
                mode="Optional" />
              <field
                path="JC_TareWeightMeasure"
                width="130"
                mode="Optional" />
              <field
                path="JC_TempRecorderSerialNo"
                width="200"
                mode="Optional" />
              <field
                path="JC_SetPointTempMeasure"
                width="110"
                mode="Optional" />
              <field
                path="JC_Additional2SealNum"
                width="200"
                mode="Optional" />
              <field
                path="JC_Additional2SealParty"
                width="130"
                mode="Optional" />
              <field
                path="TotalPackages"
                width="140"
                mode="Optional" />
              <field
                path="JC_TrainWagonNumber"
                width="130"
                mode="Optional" />
              <field
                path="JC_LCLUnpack"
                width="180"
                mode="Optional" />
              <field
                path="JC_UnpackGang"
                width="110"
                mode="Optional" />
              <field
                path="JC_UnpackShed"
                width="110"
                mode="Optional" />
              <field
                path="JC_GrossWeightVerificationStatus"
                width="100"
                mode="Optional" />
              <field
                path="JC_GrossWeightVerificationDateTime"
                width="180"
                mode="Optional" />
              <field
                path="JC_VehicleColor"
                width="250"
                mode="Optional" />
              <field
                path="JC_VehicleMake"
                width="250"
                mode="Optional" />
              <field
                path="JC_VehicleModel"
                width="250"
                mode="Optional" />
              <field
                path="JC_VehicleNumberOfDoors"
                width="70"
                mode="Optional" />
              <field
                path="JC_VehicleTransmission"
                width="120"
                mode="Optional" />
              <field
                path="JC_VehicleYear"
                width="70"
                mode="Optional" />
              <field
                path="VerifiedByAddressCompanyName"
                width="250"
                mode="Optional" />
              <field
                path="VerifiedByAddressContactName"
                width="250"
                mode="Optional" />
              <field
                path="VerifiedByAddressEmail"
                width="250"
                mode="Optional" />
              <field
                path="VerifiedByAddressPhone"
                width="250"
                mode="Optional" />
              <field
                path="JC_VolumeCapacityMeasure"
                width="130"
                mode="Optional" />
              <field
                path="JC_GrossVolume"
                width="100"
                mode="Optional" />
              <field
                path="JC_GrossVolumeUQ"
                width="40"
                mode="Optional" />
              <field
                path="JC_WeightCapacityMeasure"
                width="130"
                mode="Optional" />
              <field
                path="JC_TotalWidthMeasure"
                width="130"
                mode="Optional" />
              <field
                path="ContainerNumberWithFallback"
                width="200"
                mode="Optional" />
              <field
                path="RefContainer.RC_ContainerType"
                width="80"
                mode="Optional" />
              <field
                path="JobConsol.JK_MasterBillNum"
                width="200"
                mode="Optional" />
              <field
                path="JobConsol.JK_BookingReference"
                width="200"
                mode="Optional" />
              <field
                path="JobConsol.ShippingLineAddress.OrgHeader.OH_FullName"
                width="200"
                mode="Optional" />
              <field
                path="JobConsol.JK_AgentType"
                width="40"
                mode="Optional" />
              <field
                path="JobConsol.JK_TransportMode"
                width="60"
                mode="Optional" />
              <field
                path="JobConsol.JK_ConsolMode"
                width="50"
                mode="Optional" />
              <field
                path="JobConsol.TransportFirstLeg.JW_ETD"
                width="180"
                mode="Optional" />
              <field
                path="JobConsol.TransportFirstLeg.JW_VoyageFlight"
                width="250"
                mode="Optional" />
              <field
                path="RefContainer.RC_TEU"
                width="60"
                mode="Optional" />
              <field
                path="RefContainer.RC_FreightRateClass"
                width="250"
                mode="Optional" />
              <field
                path="RefContainer.RC_StorageClass"
                width="250"
                mode="Optional" />
              <field
                path="JobConsol.TransportFirstLeg.JW_Vessel"
                width="250"
                mode="Optional" />
              <field
                path="JobConsol.JK_OA_ShippingLineAddress"
                width="250"
                mode="Optional" />
            </fields>
          </xml>
        </placeholder>
        <placeholder
          name="DefaultSortFields"
          value="" />
        <placeholder
          name="ShowDocumentsIcon"
          value="False" />
        <placeholder
          name="RowActionsMode"
          value="MenuItems" />
        <placeholder
          name="HideGridActions"
          value="False" />
        <placeholder
          name="HideFilters"
          value="False" />
        <placeholder
          name="ActionMenuItems"
          value="" />
        <placeholder
          name="EditFormFlow"
          value="" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
    </control>
  </form>
