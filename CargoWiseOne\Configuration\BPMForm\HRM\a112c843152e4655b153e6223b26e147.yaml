#transformationVersion: 70.0
#
VZ_PK: a112c843152e4655b153e6223b26e147
VZ_ConfigurationKey: a112c843-152e-4655-b153-e6223b26e147
VZ_FormID: HRM - Position - Employing Entities
VZ_Caption:
  resKey: VZ_Caption|a112c843-152e-4655-b153-e6223b26e147
  text: Employing Entities
VZ_FormFactor: DSK
VZ_EntityType: IGlbStaff
VZ_FormType: FRM
VZ_RequiresLogin: true
VZ_MaterialDesign: true
VZ_Dependencies: >-
  <dependencies xmlns="http://cargowise.com/glow/2014/11/14/dependencies.xsd">
    <expandPath
      path="GlbEmployingBranchDepartments" />
    <datagrid
      path="GlbEmployingBranchDepartments">
      <expandPath
        path="Branch" />
      <expandPath
        path="Branch.GlbCompany" />
      <expandPath
        path="Department" />
      <expandPath
        path="CreatedByStaff" />
      <expandPath
        path="LastEditedByStaff" />
    </datagrid>
  </dependencies>
VZ_FormData: >-
  <form
    id="7baaf7f7-7e6a-4563-a3f2-cf8c62e4c9bc" xmlns="http://wisetechglobal.com/glow/2017/05/26/form.xsd">
    <placeholder
      name="Fluid"
      value="True" />
    <placeholder
      name="MinWidth"
      value="1500" />
    <control
      code="RDT"
      id="08110e24-3cab-4604-aace-c4a737ea0444"
      binding="GlbEmployingBranchDepartments">
      <placeholder
        name="AllowAdd"
        value="True" />
      <placeholder
        name="InlineEdit"
        value="table" />
      <placeholder
        name="IsReadOnly"
        value="False" />
      <placeholder
        name="CaptionOverride"
        value="Employing Entities"
        resid="50382041-b5b7-479e-aae9-44fb50d71d05" />
      <placeholder
        name="FieldConfiguration">
        <xml>
          <fields xmlns="">
            <field
              path="EffectiveDate"
              width="200"
              mode="Default" />
            <field
              path="GHB_SystemCreateTimeUtc"
              width="100"
              mode="Optional" />
            <field
              path="GHB_SystemLastEditTimeUtc"
              width="100"
              mode="Optional" />
            <field
              path="GHB_EffectiveDate"
              width="120"
              mode="Optional" />
            <field
              path="GHB_AutoEffectiveEndDate"
              width="120"
              mode="Optional" />
            <field
              path="Branch.GB_BranchName"
              width="250"
              mode="Optional" />
            <field
              path="Branch.GB_Code"
              width="120"
              mode="Optional" />
            <field
              path="Branch.GlbCompany.GC_Code"
              width="120"
              mode="Optional" />
            <field
              path="Branch.GlbCompany.GC_Name"
              width="250"
              mode="Optional" />
            <field
              path="CreatedByStaff.GS_Code"
              width="120"
              mode="Optional" />
            <field
              path="CreatedByStaff.GS_FullName"
              width="250"
              mode="Optional" />
            <field
              path="LastEditedByStaff.GS_Code"
              width="120"
              mode="Optional" />
            <field
              path="LastEditedByStaff.GS_FullName"
              width="250"
              mode="Optional" />
            <field
              path="GHB_GB_Branch"
              width="300"
              mode="Default" />
            <field
              path="Branch.GB_GC"
              width="300"
              mode="Default" />
            <field
              path="GHB_SystemCreateUser"
              width="100"
              mode="FilterOnly" />
            <field
              path="GHB_SystemLastEditUser"
              width="100"
              mode="FilterOnly" />
            <field
              path="IGlbEmployingBranchDepartment_Filter_CurrentOn"
              width="100"
              mode="FilterOnly" />
            <field
              path="IGlbEmployingBranchDepartment_Filter_CurrentOn_IgnoreTimezone"
              width="250"
              mode="FilterOnly" />
          </fields>
        </xml>
      </placeholder>
      <placeholder
        name="DefaultSortFields">
        <xml>
          <ArrayOfFieldSortDefinition xmlns:i="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://schemas.datacontract.org/2004/07/CargoWise.Glow.Infrastructure.Interfaces">
            <FieldSortDefinition>
              <FieldName>EffectiveDate</FieldName>
              <IsAscending>false</IsAscending>
            </FieldSortDefinition>
          </ArrayOfFieldSortDefinition>
        </xml>
      </placeholder>
      <placeholder
        name="ShowCustomize"
        value="True" />
      <placeholder
        name="ShowItemActions"
        value="True" />
      <placeholder
        name="ShowSelect"
        value="True" />
      <placeholder
        name="AllowAttach"
        value="false" />
    </control>
  </form>
