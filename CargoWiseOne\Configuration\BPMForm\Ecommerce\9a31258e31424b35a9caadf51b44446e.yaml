#transformationVersion: 70.0
#
VZ_PK: 9a31258e31424b35a9caadf51b44446e
VZ_ConfigurationKey: 9a31258e-3142-4b35-a9ca-adf51b44446e
VZ_FormID: ETL - VDV3 - Destination Depot - View Surplus Items on Consol
VZ_Caption:
  resKey: VZ_Caption|9a31258e-3142-4b35-a9ca-adf51b44446e
  text: View Surplus Items on Consol
VZ_FormFactor: DSK
VZ_EntityType: IJobConsol
VZ_FormType: FRM
VZ_RequiresLogin: true
VZ_Dependencies: <dependencies xmlns="http://cargowise.com/glow/2014/11/14/dependencies.xsd" />
VZ_FormData: >-
  <form
    id="9570ba96-bcac-4441-8a90-a0028f0c4331" xmlns="http://wisetechglobal.com/glow/2017/05/26/form.xsd">
    <placeholder
      name="BackgroundImagePk"
      value="" />
    <placeholder
      name="BackgroundSize"
      value="Auto" />
    <placeholder
      name="BackgroundImageOverride"
      value="" />
    <placeholder
      name="FormLayout"
      value="Splitview" />
    <placeholder
      name="ExpandPaths"
      value="" />
    <placeholder
      name="ScanInputHandling"
      value="NON" />
    <placeholder
      name="RemovePadding"
      value="false" />
    <placeholder
      name="TurnOnPortableOneToOneDesignMode"
      value="false" />
    <placeholder
      name="ContainsDefaultButton"
      value="False" />
    <control
      code="SRL"
      id="b134e3af-cb55-43c4-9bcf-65e57d36ca12"
      left="0"
      top="0"
      right="0"
      bottom="0">
      <placeholder
        name="CanBeHidden"
        value="True" />
      <placeholder
        name="IsInitialFocus"
        value="False" />
      <placeholder
        name="CaptionOverride"
        value="Consol - Surplus Items on Consol"
        resid="9a058863-628e-4b19-98e8-3920a0c6aec7" />
      <placeholder
        name="VisibilityCondition"
        value="" />
      <placeholder
        name="HeaderAlignment"
        value="Left" />
      <placeholder
        name="HeaderDisplayMode"
        value="Visible" />
      <placeholder
        name="FilterType"
        value="Entity" />
      <placeholder
        name="EntityType"
        value="IHVLVItem" />
      <placeholder
        name="ItemDisplayPath"
        value="" />
      <placeholder
        name="DisabledGridRowActions"
        value="" />
      <placeholder
        name="DefaultFilter">
        <xml>
          <ArrayOfFilterGroup xmlns:a="http://schemas.microsoft.com/2003/10/Serialization/Arrays" xmlns:i="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://cargowise.com/glow/2014/07/16/filters.xsd">
            <FilterGroup>
              <Filters>
                <Filter>
                  <FilterType>AdvancedGuidLookupFilter</FilterType>
                  <Operation>Is</Operation>
                  <PropertyPath>LoadedOnShipment.JobConShipLinks.JN_JK</PropertyPath>
                  <Values>
                    <a:string>&lt;JK_PK&gt;</a:string>
                  </Values>
                </Filter>
                <Filter>
                  <FilterType>SimpleLookupFilter</FilterType>
                  <Operation>Is</Operation>
                  <PropertyPath>HVI_Status</PropertyPath>
                  <Values>
                    <a:string>SUD</a:string>
                  </Values>
                </Filter>
              </Filters>
              <IsImplicit>true</IsImplicit>
            </FilterGroup>
            <FilterGroup>
              <Filters>
                <Filter>
                  <FilterType>StringFilter</FilterType>
                  <Operation>StartsWith</Operation>
                  <PropertyPath>HVI_CurrentBarcode</PropertyPath>
                  <Values />
                </Filter>
                <Filter>
                  <FilterType>StringFilter</FilterType>
                  <Operation>StartsWith</Operation>
                  <PropertyPath>HVI_ShipperReference</PropertyPath>
                  <Values />
                </Filter>
                <Filter>
                  <FilterType>IsActiveFilter</FilterType>
                  <Operation>Is</Operation>
                  <PropertyPath>HVI_IsActive</PropertyPath>
                  <Values>
                    <a:string>True</a:string>
                  </Values>
                </Filter>
              </Filters>
              <IsImplicit>false</IsImplicit>
            </FilterGroup>
          </ArrayOfFilterGroup>
        </xml>
      </placeholder>
      <placeholder
        name="DefaultFilterPropertyPath"
        value="" />
      <placeholder
        name="FieldConfiguration">
        <xml>
          <fields xmlns="">
            <field
              path="BookingHeader.HVH_BookingReference"
              width="200"
              mode="Optional" />
            <field
              path="HVI_ItemId"
              width="250"
              mode="Default" />
            <field
              path="HVI_CurrentBarcode"
              width="250"
              mode="Default" />
            <field
              path="HVI_ShipperReference"
              width="250"
              mode="Default" />
            <field
              path="HVI_Status"
              width="250"
              mode="Default" />
            <field
              path="Consignment.HVC_ImportCustomsClearanceStatus"
              width="250"
              mode="Default" />
            <field
              path="Consignment.HVC_ExportCustomsClearanceStatus"
              width="250"
              mode="Default" />
            <field
              path="Consignment.HVC_ImportReleaseStatus"
              width="250"
              mode="Default" />
            <field
              path="HVI_JS_LoadedOnShipment"
              width="150"
              mode="Default" />
            <field
              path="HVI_GoodsDescription"
              width="250"
              mode="Default" />
            <field
              path="Consignment.HVC_GoodsValue"
              width="250"
              mode="Default" />
            <field
              path="Consignment.HVC_RX_NKGoodsValueCurrency"
              width="250"
              mode="Default" />
            <field
              path="HVI_ManifestedWeight"
              width="150"
              mode="Default" />
            <field
              path="HVI_ManifestedVolume"
              width="150"
              mode="Default" />
            <field
              path="HVI_ActualWeight"
              width="150"
              mode="Default" />
            <field
              path="HVI_ActualVolume"
              width="150"
              mode="Default" />
            <field
              path="HVI_F3_NKPackType"
              width="100"
              mode="Default" />
            <field
              path="HVI_IsDamaged"
              width="80"
              mode="Default" />
            <field
              path="HVI_IsPillaged"
              width="80"
              mode="Default" />
            <field
              path="HVI_IsUllaged"
              width="80"
              mode="Default" />
            <field
              path="HVI_Height"
              width="100"
              mode="Optional" />
            <field
              path="HVI_Width"
              width="100"
              mode="Optional" />
            <field
              path="HVI_Length"
              width="100"
              mode="Optional" />
            <field
              path="HVI_UnitOfDimension"
              width="40"
              mode="Optional" />
            <field
              path="HVI_HVL_LoadList"
              width="250"
              mode="Optional" />
            <field
              path="HVI_HVO_OuterPackage"
              width="250"
              mode="Optional" />
            <field
              path="HVI_KM_LastMileTransportBooking"
              width="250"
              mode="Optional" />
            <field
              path="HVI_HVC_Consignment"
              width="250"
              mode="Optional" />
            <field
              path="HVI_ContainerNumber"
              width="200"
              mode="Optional" />
            <field
              path="HVI_IsActive"
              width="90"
              mode="Optional" />
            <field
              path="HVI_IsValidatedForUniqueness"
              width="200"
              mode="Optional" />
            <field
              path="Consignment.HVC_ConsignmentId"
              width="250"
              mode="Optional" />
            <field
              path="EventListHasAnyWithInterceptEvent"
              width="250"
              mode="Optional" />
            <field
              path="EventListHasAnyWithInterceptTaskCompletedEvent"
              width="250"
              mode="Optional" />
            <field
              path="EventListHasAnyWithScannedEvent"
              width="250"
              mode="Optional" />
            <field
              path="Consignment.HVC_ShipperName"
              width="250"
              mode="Optional" />
            <field
              path="Consignment.HVC_ConsigneeName"
              width="250"
              mode="Optional" />
            <field
              path="Consignment.HVC_OA_ConsigneeAddress"
              width="250"
              mode="Optional" />
            <field
              path="Consignment.HVC_OA_ShipperAddress"
              width="250"
              mode="Optional" />
            <field
              path="Consignment.HVC_RN_NKConsigneeCountryCode"
              width="250"
              mode="Optional" />
            <field
              path="Consignment.HVC_JE_ImportDeclaration"
              width="250"
              mode="Optional" />
            <field
              path="Consignment.HVC_JE_ExportDeclaration"
              width="250"
              mode="Optional" />
            <field
              path="Consignment.HVC_OH_LastMileCarrierBookingAgent"
              width="250"
              mode="Optional" />
            <field
              path="Consignment.Header.HCH_JS_Shipment"
              width="250"
              mode="Optional" />
            <field
              path="Consignment.HVC_RN_NKShipperCountryCode"
              width="250"
              mode="Optional" />
            <field
              path="Consignment.HVC_CarrierAccountNumber"
              width="250"
              mode="Optional" />
            <field
              path="Consignment.CarrierServiceCode"
              width="250"
              mode="Optional" />
            <field
              path="Consignment.HVC_ConsigneeAddress1"
              width="250"
              mode="Optional" />
            <field
              path="Consignment.HVC_ConsigneeAddress2"
              width="250"
              mode="Optional" />
            <field
              path="Consignment.HVC_ConsigneeAddressValidationStatus"
              width="250"
              mode="Optional" />
            <field
              path="Consignment.HVC_ConsigneeCity"
              width="250"
              mode="Optional" />
            <field
              path="Consignment.HVC_ConsigneeContact"
              width="250"
              mode="Optional" />
            <field
              path="Consignment.HVC_ConsigneeEmail"
              width="250"
              mode="Optional" />
            <field
              path="Consignment.HVC_ConsigneeFax"
              width="250"
              mode="Optional" />
            <field
              path="Consignment.HVC_ConsigneeInstructions"
              width="250"
              mode="Optional" />
            <field
              path="Consignment.HVC_ConsigneeMobile"
              width="250"
              mode="Optional" />
            <field
              path="Consignment.HVC_ConsigneePhone"
              width="250"
              mode="Optional" />
            <field
              path="Consignment.HVC_ConsigneePostcode"
              width="250"
              mode="Optional" />
            <field
              path="Consignment.HVC_ConsigneeState"
              width="250"
              mode="Optional" />
            <field
              path="Consignment.HVC_GoodsDescription"
              width="250"
              mode="Optional" />
            <field
              path="Consignment.HVC_INCO"
              width="180"
              mode="Optional" />
            <field
              path="Consignment.HVC_PL_NKLastMileCarrierServiceLevel"
              width="250"
              mode="Optional" />
            <field
              path="Consignment.HVC_PreScreeningStatus"
              width="250"
              mode="Optional" />
            <field
              path="Consignment.HVC_ShipperAddress1"
              width="250"
              mode="Optional" />
            <field
              path="Consignment.HVC_ShipperAddress2"
              width="250"
              mode="Optional" />
            <field
              path="Consignment.HVC_ShipperCity"
              width="250"
              mode="Optional" />
            <field
              path="Consignment.HVC_ShipperEmail"
              width="250"
              mode="Optional" />
            <field
              path="Consignment.HVC_ShipperFax"
              width="250"
              mode="Optional" />
            <field
              path="Consignment.HVC_ShipperMobile"
              width="250"
              mode="Optional" />
            <field
              path="Consignment.HVC_ShipperContact"
              width="250"
              mode="Optional" />
            <field
              path="Consignment.HVC_ShipperPhone"
              width="250"
              mode="Optional" />
            <field
              path="Consignment.HVC_ShipperPostcode"
              width="250"
              mode="Optional" />
            <field
              path="Consignment.HVC_ShipperReference"
              width="250"
              mode="Optional" />
            <field
              path="Consignment.HVC_ShipperState"
              width="250"
              mode="Optional" />
            <field
              path="Consignment.HVC_Status"
              width="200"
              mode="Optional" />
            <field
              path="Consignment.HVC_UndgClass"
              width="240"
              mode="Optional" />
            <field
              path="Consignment.HVC_VendorIdentifier"
              width="250"
              mode="Optional" />
            <field
              path="Consignment.HVC_WaybillNumber"
              width="250"
              mode="Optional" />
            <field
              path="Consignment.HVC_AuthorityToLeave"
              width="250"
              mode="Optional" />
            <field
              path="Consignment.HVC_IsHazardous"
              width="230"
              mode="Optional" />
            <field
              path="Consignment.HVC_IsActive"
              width="230"
              mode="Optional" />
            <field
              path="Consignment.HVC_IsSignatureRequired"
              width="250"
              mode="Optional" />
            <field
              path="Consignment.HVC_IsTaxPrePaid"
              width="250"
              mode="Optional" />
            <field
              path="Consignment.IsTranshipment"
              width="250"
              mode="Optional" />
            <field
              path="Consignment.HVC_IsPerishable"
              width="240"
              mode="Optional" />
            <field
              path="Consignment.HVC_IsPersonalEffects"
              width="250"
              mode="Optional" />
            <field
              path="Consignment.HVC_IsValidatedForUniqueness"
              width="250"
              mode="Optional" />
            <field
              path="Consignment.HVC_RequiresFumigation"
              width="250"
              mode="Optional" />
            <field
              path="Consignment.HVC_IsSelfBooked"
              width="250"
              mode="Optional" />
            <field
              path="Consignment.HVC_IsTimber"
              width="200"
              mode="Optional" />
            <field
              path="BookingHeader"
              width="140"
              mode="Optional" />
            <field
              path="DestinationDepot"
              width="250"
              mode="Optional" />
            <field
              path="OriginDepot"
              width="250"
              mode="Optional" />
            <field
              path="CargoWiseRef"
              width="250"
              mode="Optional" />
            <field
              path="CartageURL"
              width="250"
              mode="Optional" />
            <field
              path="GateInAtOriginDepot"
              width="230"
              mode="Optional" />
            <field
              path="HVI_IsUnmanifestedAtDestination"
              width="250"
              mode="Optional" />
            <field
              path="ItemHasBeenScanned"
              width="210"
              mode="Optional" />
            <field
              path="LastInterceptEventReference"
              width="250"
              mode="Optional" />
            <field
              path="URL"
              width="250"
              mode="Optional" />
            <field
              path="HVI_IsScannedAtDestination"
              width="100"
              mode="Optional" />
            <field
              path="HVI_ImportReleaseStatus"
              width="100"
              mode="Optional" />
            <field
              path="Consignment.HVC_OH_LastMileCarrier"
              width="250"
              mode="Optional" />
            <field
              path="Consignment.HVC_OA_DestinationDepot"
              width="250"
              mode="Optional" />
            <field
              path="Consignment.HVC_SystemCreateTimeUtc"
              width="250"
              mode="Optional" />
            <field
              path="Consignment.HVC_SystemLastEditTimeUtc"
              width="250"
              mode="Optional" />
            <field
              path="Consignment.HVC_ActualVolumeMeasure"
              width="130"
              mode="Optional" />
            <field
              path="Consignment.HVC_ActualWeightMeasure"
              width="130"
              mode="Optional" />
            <field
              path="Consignment.HVC_ManifestedVolumeMeasure"
              width="130"
              mode="Optional" />
            <field
              path="Consignment.HVC_ManifestedWeightMeasure"
              width="130"
              mode="Optional" />
            <field
              path="Consignment.HVC_ItemCount"
              width="250"
              mode="Optional" />
            <field
              path="Consignment.HVC_HVH_BookingHeader"
              width="250"
              mode="Optional" />
            <field
              path="Consignment.HVC_SystemLastEditUser"
              width="250"
              mode="Optional" />
            <field
              path="ShipmentETailer"
              width="250"
              mode="Default" />
            <field
              path="HVI_CarrierBookingStatus"
              width="220"
              mode="Optional" />
            <field
              path="VolumeWeight"
              width="250"
              mode="Optional" />
            <field
              path="Chargeable"
              width="250"
              mode="Optional" />
            <field
              path="DensityFactor"
              width="250"
              mode="Optional" />
          </fields>
        </xml>
      </placeholder>
      <placeholder
        name="DefaultSortFields">
        <xml>
          <ArrayOfFieldSortDefinition xmlns:i="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://schemas.datacontract.org/2004/07/CargoWise.Glow.Infrastructure.Interfaces">
            <FieldSortDefinition>
              <FieldName>HVI_Status</FieldName>
              <IsAscending>true</IsAscending>
            </FieldSortDefinition>
            <FieldSortDefinition>
              <FieldName>HVI_ItemId</FieldName>
              <IsAscending>true</IsAscending>
            </FieldSortDefinition>
          </ArrayOfFieldSortDefinition>
        </xml>
      </placeholder>
      <placeholder
        name="DefaultGroupFields"
        value="" />
      <placeholder
        name="EnableGrouping"
        value="False" />
      <placeholder
        name="AutoRefreshInterval"
        value="0" />
      <placeholder
        name="AllowMultiSelect"
        value="True" />
      <placeholder
        name="ShowWorkflowLabel"
        value="True" />
      <placeholder
        name="RowActionsMode"
        value="RowAndMenu" />
      <placeholder
        name="HideGridActions"
        value="False" />
      <placeholder
        name="MinimizeFilters"
        value="True" />
      <placeholder
        name="WorkflowOpenInNewWindow"
        value="False" />
      <placeholder
        name="SearchOnInit"
        value="True" />
      <placeholder
        name="ShowQuickSearch"
        value="False" />
      <placeholder
        name="QuickSearchCaption"
        value="" />
      <placeholder
        name="ValueField"
        value="" />
      <placeholder
        name="Category"
        value="" />
      <placeholder
        name="ActionMenuItems"
        value="" />
      <placeholder
        name="EditFormFlow"
        value="27842edb7c6b406ab0f8a0de282d7c91" />
      <placeholder
        name="ResetFilterOnInit"
        value="False" />
      <placeholder
        name="FilterDisplayMode"
        value="InlineAndPopup" />
    </control>
  </form>
