#transformationVersion: 70.0
#
VZ_PK: ae28dbacc0f140399bcdf3782c10b04b
VZ_ConfigurationKey: ae28dbac-c0f1-4039-9bcd-f3782c10b04b
VZ_FormID: ETL - VDV3 - Destination Depot - Outer Packages
VZ_Caption:
  resKey: VZ_Caption|ae28dbac-c0f1-4039-9bcd-f3782c10b04b
  text: Destination Depot - Outer Packages
VZ_FormFactor: DSK
VZ_FormType: PAG
VZ_RequiresLogin: true
VZ_Dependencies: <dependencies xmlns="http://cargowise.com/glow/2014/11/14/dependencies.xsd" />
VZ_FormData: >-
  <form
    id="cc0da138-0797-4cd2-b813-d721a35753e5" xmlns="http://wisetechglobal.com/glow/2017/05/26/form.xsd">
    <placeholder
      name="BackgroundImagePk"
      value="" />
    <placeholder
      name="BackgroundSize"
      value="Auto" />
    <placeholder
      name="BackgroundImageOverride"
      value="" />
    <placeholder
      name="FormLayout"
      value="Splitview" />
    <placeholder
      name="ExpandPaths"
      value="" />
    <placeholder
      name="ScanInputHandling"
      value="NON" />
    <placeholder
      name="RemovePadding"
      value="false" />
    <placeholder
      name="TurnOnPortableOneToOneDesignMode"
      value="false" />
    <placeholder
      name="ContainsDefaultButton"
      value="False" />
    <control
      code="GRP"
      id="448f065b-f325-4d3e-8c58-43127297d4f4"
      left="0"
      top="0"
      width="6"
      height="4">
      <placeholder
        name="CanBeHidden"
        value="True" />
      <placeholder
        name="VisibilityCondition"
        value="" />
      <placeholder
        name="Header"
        value="New Section"
        resid="1892303f-3ec4-4e24-8ed5-f36072591f0f" />
      <placeholder
        name="HeaderAlignment"
        value="Left" />
      <placeholder
        name="HeaderDisplayMode"
        value="Hidden" />
      <placeholder
        name="BackgroundImagePk"
        value="" />
      <placeholder
        name="BackgroundImageOverride"
        value="" />
      <control
        code="TIL"
        id="e8542b5d-748a-45f2-bdfe-0eebb4a00d05"
        left="0"
        top="0"
        width="2"
        height="2">
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="Size"
          value="Standard" />
        <placeholder
          name="Color"
          value="Default" />
        <placeholder
          name="DisplayMode"
          value="TextAndIcon" />
        <placeholder
          name="Text"
          value="Shipments"
          resid="4e988998-018f-4d16-be24-b2815c0ff469" />
        <placeholder
          name="Image"
          value="d4c02b187e9d4e37be59b173a7caa29a" />
        <placeholder
          name="ImageOverride"
          value="" />
        <placeholder
          name="Description"
          value="" />
        <placeholder
          name="Behaviour"
          value="Page" />
        <placeholder
          name="PagePK"
          value="6304763e5b644c069fb98fe353716da5" />
        <placeholder
          name="Url"
          value="" />
        <placeholder
          name="FormFlow"
          value="" />
      </control>
      <control
        code="TIL"
        id="a8ee33e9-b91f-46de-875f-e06e3d239f13"
        left="2"
        top="0"
        width="2"
        height="2">
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="Size"
          value="Standard" />
        <placeholder
          name="Color"
          value="Default" />
        <placeholder
          name="DisplayMode"
          value="TextAndIcon" />
        <placeholder
          name="Text"
          value="Consignments"
          resid="6cf4e813-d8ba-49e0-92dc-d60338cc2c7c" />
        <placeholder
          name="Image"
          value="2c348de953fe4e8db9967317a1a942c4" />
        <placeholder
          name="ImageOverride"
          value="" />
        <placeholder
          name="Description"
          value="" />
        <placeholder
          name="Behaviour"
          value="Page" />
        <placeholder
          name="PagePK"
          value="8a85c607db774a4da7ce0622d9c51236" />
        <placeholder
          name="Url"
          value="" />
        <placeholder
          name="FormFlow"
          value="" />
      </control>
      <control
        code="TIL"
        id="08845d9f-55a0-4d76-9577-47b4404758fe"
        left="4"
        top="0"
        width="2"
        height="2">
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="Size"
          value="Standard" />
        <placeholder
          name="Color"
          value="Default" />
        <placeholder
          name="DisplayMode"
          value="TextAndIcon" />
        <placeholder
          name="Text"
          value="Headers"
          resid="66e7b649-fe47-4c4f-9743-6d71415f464a" />
        <placeholder
          name="Image"
          value="05e1a4771ad542aeadfef74283eec66b" />
        <placeholder
          name="ImageOverride"
          value="" />
        <placeholder
          name="Description"
          value="" />
        <placeholder
          name="Behaviour"
          value="Page" />
        <placeholder
          name="PagePK"
          value="44cbb83e146740d3871b38de444582a1" />
        <placeholder
          name="Url"
          value="" />
        <placeholder
          name="FormFlow"
          value="" />
      </control>
      <control
        code="TIL"
        id="46593f9d-2ba9-4acd-93be-863ab62f79bf"
        left="0"
        top="2"
        width="2"
        height="2">
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="Size"
          value="Standard" />
        <placeholder
          name="Color"
          value="Primary" />
        <placeholder
          name="DisplayMode"
          value="TextAndIcon" />
        <placeholder
          name="Text"
          value="Outer Packages"
          resid="5de5dd81-106b-425b-8655-2d3ec547f369" />
        <placeholder
          name="Image"
          value="3e36f6d7c44842d8b2faa22f952210e2" />
        <placeholder
          name="ImageOverride"
          value="" />
        <placeholder
          name="Description"
          value="" />
        <placeholder
          name="Behaviour"
          value="Page" />
        <placeholder
          name="PagePK"
          value="ae28dbacc0f140399bcdf3782c10b04b" />
        <placeholder
          name="Url"
          value="" />
        <placeholder
          name="FormFlow"
          value="" />
      </control>
      <control
        code="TIL"
        id="1439fe9e-1f36-4faf-a8e1-752cd9ac8963"
        left="2"
        top="2"
        width="2"
        height="2">
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="Size"
          value="Standard" />
        <placeholder
          name="Color"
          value="Default" />
        <placeholder
          name="DisplayMode"
          value="TextAndIcon" />
        <placeholder
          name="Text"
          value="Reports"
          resid="a35840b9-4a44-4529-83b4-0bb94fab8c90" />
        <placeholder
          name="Image"
          value="fbcf0fdf52074ddf97ec2ce94d6f47a1" />
        <placeholder
          name="ImageOverride"
          value="" />
        <placeholder
          name="Description"
          value="" />
        <placeholder
          name="Behaviour"
          value="Page" />
        <placeholder
          name="PagePK"
          value="9de1a081d3124bb383082d0cc82f24d8" />
        <placeholder
          name="Url"
          value="" />
        <placeholder
          name="FormFlow"
          value="" />
      </control>
    </control>
    <control
      code="SRL"
      id="716db438-2bd7-4552-b042-6351bb5096da"
      left="7"
      top="0"
      right="0"
      bottom="0">
      <placeholder
        name="CanBeHidden"
        value="True" />
      <placeholder
        name="IsInitialFocus"
        value="False" />
      <placeholder
        name="CaptionOverride"
        value="Outer Packages"
        resid="121cfc52-6726-4261-a1eb-f66d52e61fd5" />
      <placeholder
        name="VisibilityCondition"
        value="" />
      <placeholder
        name="HeaderAlignment"
        value="Left" />
      <placeholder
        name="HeaderDisplayMode"
        value="Visible" />
      <placeholder
        name="FilterType"
        value="Entity" />
      <placeholder
        name="EntityType"
        value="IHVLVOuterPackage" />
      <placeholder
        name="ItemDisplayPath"
        value="" />
      <placeholder
        name="DisabledGridRowActions"
        value="" />
      <placeholder
        name="DefaultFilter">
        <xml>
          <ArrayOfFilterGroup xmlns:a="http://schemas.microsoft.com/2003/10/Serialization/Arrays" xmlns:i="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://cargowise.com/glow/2014/07/16/filters.xsd">
            <FilterGroup>
              <Filters>
                <Filter>
                  <FilterType>StringFilter</FilterType>
                  <Operation>StartsWith</Operation>
                  <PropertyPath>HVO_PackageBarcode</PropertyPath>
                  <Values />
                </Filter>
                <Filter>
                  <FilterType>StringFilter</FilterType>
                  <Operation>StartsWith</Operation>
                  <PropertyPath>HVO_PackageReference</PropertyPath>
                  <Values />
                </Filter>
                <Filter>
                  <FilterType>StringFilter</FilterType>
                  <Operation>StartsWith</Operation>
                  <PropertyPath>HVLVItems.Consignment.HVC_WaybillNumber</PropertyPath>
                  <Values />
                </Filter>
                <Filter>
                  <FilterType>StringFilter</FilterType>
                  <Operation>StartsWith</Operation>
                  <PropertyPath>HVLVItems.Consignment.HVLVItems.HVI_CurrentBarcode</PropertyPath>
                  <Values />
                </Filter>
              </Filters>
              <IsImplicit>false</IsImplicit>
            </FilterGroup>
          </ArrayOfFilterGroup>
        </xml>
      </placeholder>
      <placeholder
        name="DefaultFilterPropertyPath"
        value="" />
      <placeholder
        name="FieldConfiguration">
        <xml>
          <fields xmlns="">
            <field
              path="HVO_PackageBarcode"
              width="250"
              mode="Default" />
            <field
              path="HVO_PackageReference"
              width="250"
              mode="Default" />
            <field
              path="HVO_F3_NKPackageType"
              width="250"
              mode="Default" />
            <field
              path="HVO_Status"
              width="250"
              mode="Default" />
            <field
              path="HVO_OH_LastMileCarrier"
              width="250"
              mode="Default" />
            <field
              path="HVO_HVL_LoadList"
              width="250"
              mode="Default" />
            <field
              path="CountAll"
              width="100"
              mode="Default" />
            <field
              path="LoadList.HVL_MasterBillNumber"
              width="200"
              mode="Default" />
            <field
              path="LoadedOnConsol.JK_UniqueConsignRef"
              width="200"
              mode="Default" />
            <field
              path="LoadList.HVL_E_Dep"
              width="160"
              mode="Default" />
            <field
              path="LoadList.HVL_E_Arv"
              width="160"
              mode="Default" />
            <field
              path="LoadList.HVL_TransportMode"
              width="200"
              mode="Default" />
            <field
              path="LoadList.HVL_OH_Carrier"
              width="250"
              mode="Default" />
            <field
              path="LoadList.HVL_VoyageFlight"
              width="200"
              mode="Default" />
            <field
              path="LoadList.HVL_VesselName"
              width="200"
              mode="Default" />
            <field
              path="HVO_ContainerNumber"
              width="200"
              mode="Default" />
            <field
              path="LoadList.HVL_OA_OriginDepot"
              width="250"
              mode="Default" />
            <field
              path="LoadList.HVL_OA_DestinationDepot"
              width="250"
              mode="Default" />
            <field
              path="HVO_SystemCreateTimeUtc"
              width="180"
              mode="Optional" />
            <field
              path="HVO_SystemLastEditTimeUtc"
              width="180"
              mode="Optional" />
            <field
              path="CountClear"
              width="110"
              mode="Optional" />
            <field
              path="CountHeld"
              width="100"
              mode="Optional" />
            <field
              path="CountNonReported"
              width="180"
              mode="Optional" />
            <field
              path="HVO_Weight"
              width="100"
              mode="Default" />
            <field
              path="HVO_WeightUQ"
              width="90"
              mode="Default" />
            <field
              path="HVO_Volume"
              width="100"
              mode="Default" />
            <field
              path="HVO_VolumeUQ"
              width="90"
              mode="Default" />
            <field
              path="HVO_Length"
              width="100"
              mode="Default" />
            <field
              path="HVO_Width"
              width="100"
              mode="Default" />
            <field
              path="HVO_Height"
              width="100"
              mode="Default" />
            <field
              path="HVO_UnitOfDimension"
              width="170"
              mode="Default" />
            <field
              path="HVO_PL_NKLastMileCarrierServiceLevel"
              width="250"
              mode="Default" />
            <field
              path="HVO_RH_NKCommodityCode"
              width="350"
              mode="Default" />
          </fields>
        </xml>
      </placeholder>
      <placeholder
        name="DefaultSortFields"
        value="" />
      <placeholder
        name="DefaultGroupFields"
        value="" />
      <placeholder
        name="EnableGrouping"
        value="False" />
      <placeholder
        name="AutoRefreshInterval"
        value="0" />
      <placeholder
        name="AllowMultiSelect"
        value="True" />
      <placeholder
        name="ShowWorkflowLabel"
        value="True" />
      <placeholder
        name="RowActionsMode"
        value="RowAndMenu" />
      <placeholder
        name="HideGridActions"
        value="False" />
      <placeholder
        name="MinimizeFilters"
        value="False" />
      <placeholder
        name="WorkflowOpenInNewWindow"
        value="False" />
      <placeholder
        name="SearchOnInit"
        value="True" />
      <placeholder
        name="ShowQuickSearch"
        value="False" />
      <placeholder
        name="QuickSearchCaption"
        value="" />
      <placeholder
        name="ValueField"
        value="" />
      <placeholder
        name="Category"
        value="" />
      <placeholder
        name="ActionMenuItems"
        value="" />
      <placeholder
        name="EditFormFlow"
        value="" />
      <placeholder
        name="ResetFilterOnInit"
        value="False" />
      <placeholder
        name="FilterDisplayMode"
        value="InlineAndPopup" />
    </control>
    <control
      code="GRP"
      id="83d6b0f8-eb72-4611-a27f-e6e71d4da8f7"
      left="0"
      top="4"
      width="6"
      height="5">
      <placeholder
        name="CanBeHidden"
        value="True" />
      <placeholder
        name="VisibilityCondition"
        value="" />
      <placeholder
        name="Header"
        value="Tracking"
        resid="ee4641d3-148e-4b68-86f8-002de5265c2a" />
      <placeholder
        name="HeaderAlignment"
        value="Left" />
      <placeholder
        name="HeaderDisplayMode"
        value="Visible" />
      <placeholder
        name="BackgroundImagePk"
        value="" />
      <placeholder
        name="BackgroundImageOverride"
        value="" />
      <control
        code="TRC"
        id="dfcd740c-8e7f-4a6e-884c-8dd5e4e8df32"
        left="0"
        top="0"
        width="6"
        height="4">
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="VisibilityCondition"
          value="" />
      </control>
    </control>
    <control
      code="FAV"
      id="7d132d0a-d2d4-4fd3-ab8c-a114cad89004"
      left="0"
      top="9"
      width="6"
      bottom="2">
      <placeholder
        name="CanBeHidden"
        value="True" />
      <placeholder
        name="VisibilityCondition"
        value="" />
      <placeholder
        name="EntityType"
        value="" />
      <placeholder
        name="HeaderDisplayMode"
        value="Visible" />
      <placeholder
        name="ShowFavorites"
        value="True" />
      <placeholder
        name="ShowRecents"
        value="True" />
    </control>
  </form>
