#transformationVersion: 70.0
#
VZ_PK: 5a918aeca1e54ed194e47bd19c0e3a0e
VZ_ConfigurationKey: 5a918aec-a1e5-4ed1-94e4-7bd19c0e3a0e
VZ_FormID: AST - Asset Transaction search list
VZ_Caption:
  resKey: VZ_Caption|5a918aeca1e54ed194e47bd19c0e3a0e
  text: Asset Transactions
VZ_FormFactor: DSK
VZ_FormType: PAG
VZ_RequiresLogin: true
VZ_MaterialDesign: true
VZ_Dependencies: <dependencies xmlns="http://cargowise.com/glow/2014/11/14/dependencies.xsd" />
VZ_FormData: >-
  <form
    id="148698d3-d97b-4c7f-ae17-e002439280f4" xmlns="http://wisetechglobal.com/glow/2017/05/26/form.xsd">
    <placeholder
      name="Layout"
      value="fill" />
    <control
      code="BTN"
      id="f4b02afa-f921-42bc-9d00-b82336369549">
      <placeholder
        name="Caption"
        value="Calculate Depreciations"
        resid="c5dc130e-9633-429a-b447-b24485cfe8a7" />
      <placeholder
        name="Width"
        value="200" />
      <placeholder
        name="FormFlowConfiguration">
        <xml>
          <formFlows xmlns="">
            <formFlow
              newSession="True">ab6d813adbe24d15a53da33584cc58dd</formFlow>
          </formFlows>
        </xml>
      </placeholder>
    </control>
    <control
      code="SDT"
      id="9510321d-ac43-48bc-a15f-1fed299c4d13"
      binding="">
      <placeholder
        name="CaptionType"
        value="description" />
      <placeholder
        name="EntityType"
        value="IAccAssetTransactionHeader" />
      <placeholder
        name="FieldConfiguration">
        <xml>
          <fields xmlns="">
            <field
              path="ASH_PostDate"
              width="130"
              mode="Mandatory" />
            <field
              path="ASH_TransactionNumber"
              width="250"
              mode="Mandatory" />
            <field
              path="ASH_AAH_Asset"
              width="250"
              mode="Mandatory" />
            <field
              path="ASH_TransactionType"
              width="160"
              mode="Mandatory" />
            <field
              path="ASH_OSAmount"
              width="200"
              mode="Mandatory" />
            <field
              path="ASH_RX_NKTransactionCurrency"
              width="40"
              mode="Default" />
            <field
              path="ASH_ExchangeRate"
              width="200"
              mode="Default" />
            <field
              path="ASH_LocalAmount"
              width="200"
              mode="Default" />
            <field
              path="ASH_SupplierReferenceDate"
              width="130"
              mode="Default" />
            <field
              path="ASH_SupplierReferenceNumber"
              width="250"
              mode="Default" />
            <field
              path="ASH_GC_Company"
              width="250"
              mode="FilterOnly"
              isFilterable="false" />
            <field
              path="ASH_GB_Branch"
              width="130"
              mode="Mandatory" />
            <field
              path="ASH_GE_Department"
              width="130"
              mode="Mandatory" />
            <field
              path="ASH_IsCancelled"
              width="150"
              mode="Optional" />
            <field
              path="ASH_CancelledDate"
              width="130"
              mode="Optional" />
            <field
              path="ASH_SystemCreateTimeUtc"
              width="130"
              mode="Optional" />
            <field
              path="ASH_SystemCreateUser"
              width="250"
              mode="Optional" />
            <field
              path="ASH_SystemLastEditTimeUtc"
              width="130"
              mode="Optional" />
            <field
              path="ASH_SystemLastEditUser"
              width="250"
              mode="Optional" />
            <field
              path="ASH_Description"
              width="250"
              mode="Optional" />
          </fields>
        </xml>
      </placeholder>
      <placeholder
        name="CaptionOverride"
        value="Asset transactions"
        resid="da33cc17-dd17-45b0-a3f3-81fae32453d2" />
      <placeholder
        name="FullPage"
        value="True" />
      <placeholder
        name="DefaultFilter">
        <xml>
          <ArrayOfFilterGroup xmlns:a="http://schemas.microsoft.com/2003/10/Serialization/Arrays" xmlns:i="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://cargowise.com/glow/2014/07/16/filters.xsd">
            <FilterGroup>
              <Filters>
                <Filter>
                  <FilterType>IsActiveFilter</FilterType>
                  <Operation>Is</Operation>
                  <PropertyPath>ASH_IsCancelled</PropertyPath>
                  <Values>
                    <a:string>false</a:string>
                  </Values>
                </Filter>
              </Filters>
              <IsImplicit>false</IsImplicit>
            </FilterGroup>
            <FilterGroup>
              <Filters>
                <Filter>
                  <FilterType>StringFilter</FilterType>
                  <Operation>Is</Operation>
                  <PropertyPath>Company.GC_Code</PropertyPath>
                  <Values>
                    <a:string>&lt;%.CurrentBranch.GlbCompany.GC_Code&gt;</a:string>
                  </Values>
                </Filter>
              </Filters>
              <IsImplicit>true</IsImplicit>
            </FilterGroup>
          </ArrayOfFilterGroup>
        </xml>
      </placeholder>
      <placeholder
        name="NewFormFlowConfiguration">
        <xml>
          <formFlows xmlns="">
            <formFlow
              newSession="True"
              inDrawer="True">13f5e1a4bc1d448f95bfa4a9a61d9c13</formFlow>
            <formFlow
              newSession="True"
              inDrawer="True">b83d3b7d534a4ba0977dc6499765c710</formFlow>
          </formFlows>
        </xml>
      </placeholder>
      <placeholder
        name="EditFormFlowConfiguration">
        <xml>
          <formFlows xmlns="">
            <formFlow
              newSession="True"
              inDrawer="True">59a96e52434c47cbb05e12df2e028d0a</formFlow>
          </formFlows>
        </xml>
      </placeholder>
      <placeholder
        name="ActionMenuItems">
        <xml>
          <formFlows xmlns="">
            <formFlow
              newSession="True"
              inDrawer="True">59a96e52434c47cbb05e12df2e028d0a</formFlow>
          </formFlows>
        </xml>
      </placeholder>
    </control>
  </form>
