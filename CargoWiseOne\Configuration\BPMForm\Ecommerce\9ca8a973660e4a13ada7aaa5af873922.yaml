#transformationVersion: 70.0
#
VZ_PK: 9ca8a973660e4a13ada7aaa5af873922
VZ_ConfigurationKey: 9ca8a973-660e-4a13-ada7-aaa5af873922
VZ_FormID: ETL - VDV3 - Shipper Portal G1 - Home Page
VZ_Caption:
  resKey: VZ_Caption|9ca8a973-660e-4a13-ada7-aaa5af873922
  text: Ecommerce Shipper Portal G1 Home Page
VZ_FormFactor: DSK
VZ_FormType: PAG
VZ_RequiresLogin: true
VZ_Dependencies: <dependencies xmlns="http://cargowise.com/glow/2014/11/14/dependencies.xsd" />
VZ_FormData: >-
  <form
    id="8c0b8ed3-332b-4cd7-b94b-985f99fde2a6" xmlns="http://wisetechglobal.com/glow/2017/05/26/form.xsd">
    <placeholder
      name="BackgroundImagePk"
      value="" />
    <placeholder
      name="BackgroundSize"
      value="Auto" />
    <placeholder
      name="BackgroundImageOverride"
      value="" />
    <placeholder
      name="FormLayout"
      value="Splitview" />
    <placeholder
      name="ExpandPaths"
      value="" />
    <placeholder
      name="ScanInputHandling"
      value="NON" />
    <placeholder
      name="RemovePadding"
      value="false" />
    <placeholder
      name="TurnOnPortableOneToOneDesignMode"
      value="false" />
    <placeholder
      name="ContainsDefaultButton"
      value="False" />
    <control
      code="GRP"
      id="ea79c5b9-7222-49d1-a63d-a18ec6f8d428"
      width="16"
      height="14">
      <placeholder
        name="CanBeHidden"
        value="True" />
      <placeholder
        name="VisibilityCondition"
        value="" />
      <placeholder
        name="Header"
        value="Shipper Portal G1"
        resid="1b208db9-71fb-4877-8ed2-8696ebaa5e13" />
      <placeholder
        name="HeaderAlignment"
        value="Left" />
      <placeholder
        name="HeaderDisplayMode"
        value="Hidden" />
      <placeholder
        name="BackgroundImagePk"
        value="" />
      <placeholder
        name="BackgroundImageOverride"
        value="" />
      <control
        code="TIL"
        id="2c849541-5370-413f-9f1d-7adc4ecba470"
        left="4"
        top="1"
        width="4"
        height="4">
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="VisibilityCondition"
          value="0b4e8504-aaea-46b2-ae8c-d2633f4969b6" />
        <placeholder
          name="Size"
          value="Large" />
        <placeholder
          name="Color"
          value="Default" />
        <placeholder
          name="DisplayMode"
          value="TextAndIcon" />
        <placeholder
          name="Text"
          value="Create Booking Header"
          resid="4084ba3c-d5e2-450a-8329-bf868e5fb65a" />
        <placeholder
          name="Image"
          value="74a27a164dc145d4b5a8916b1376be68" />
        <placeholder
          name="ImageOverride"
          value="" />
        <placeholder
          name="Description"
          value="Create a new Booking Header record."
          resid="11d62ce0-5e3f-4bce-9fab-e804a8bcd64f" />
        <placeholder
          name="Behaviour"
          value="FormFlow" />
        <placeholder
          name="PagePK"
          value="" />
        <placeholder
          name="Url"
          value="" />
        <placeholder
          name="FormFlow"
          value="f79467e21d38497e89ccebac5d260895" />
      </control>
      <control
        code="TIL"
        id="93740e26-a05f-4c4a-8f4b-e09b0b7a37c2"
        left="4"
        top="1"
        width="4"
        height="4">
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="VisibilityCondition"
          value="23c20a5a-ce5d-4a13-816c-371d715f7666" />
        <placeholder
          name="Size"
          value="Large" />
        <placeholder
          name="Color"
          value="Default" />
        <placeholder
          name="DisplayMode"
          value="TextAndIcon" />
        <placeholder
          name="Text"
          value="Upload Header"
          resid="598fa497-dc3f-4499-a50c-0349bce49af5" />
        <placeholder
          name="Image"
          value="74a27a164dc145d4b5a8916b1376be68" />
        <placeholder
          name="ImageOverride"
          value="" />
        <placeholder
          name="Description"
          value="" />
        <placeholder
          name="Behaviour"
          value="FormFlow" />
        <placeholder
          name="PagePK"
          value="" />
        <placeholder
          name="Url"
          value="" />
        <placeholder
          name="FormFlow"
          value="c1e07308a647496eaffb3013ca68af9d" />
      </control>
      <control
        code="TIL"
        id="bc8cdebc-4327-4c63-8932-04d335da6b17"
        left="8"
        top="1"
        width="4"
        height="4">
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="Size"
          value="Large" />
        <placeholder
          name="Color"
          value="Default" />
        <placeholder
          name="DisplayMode"
          value="TextAndIcon" />
        <placeholder
          name="Text"
          value="Reports"
          resid="22f6dfd1-962b-410d-b1ab-6bb5a55b7968" />
        <placeholder
          name="Image"
          value="fbcf0fdf52074ddf97ec2ce94d6f47a1" />
        <placeholder
          name="ImageOverride"
          value="" />
        <placeholder
          name="Description"
          value="View reports at Item level with pre filtered statuses."
          resid="73ea8474-4a97-4a9f-aee1-78a29ad49ef5" />
        <placeholder
          name="Behaviour"
          value="Page" />
        <placeholder
          name="PagePK"
          value="07990593823a43f0a3f8dcdd9f47da6c" />
        <placeholder
          name="Url"
          value="" />
        <placeholder
          name="FormFlow"
          value="" />
      </control>
      <control
        code="TIL"
        id="a57c9b3c-b20e-4028-9464-8d8db8e0e272"
        left="4"
        top="5"
        width="4"
        height="4">
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="Size"
          value="Large" />
        <placeholder
          name="Color"
          value="Default" />
        <placeholder
          name="DisplayMode"
          value="TextAndIcon" />
        <placeholder
          name="Text"
          value="Carrier Booking"
          resid="da3d368f-3853-47b1-8a6b-b70bb88bc1c3" />
        <placeholder
          name="Image"
          value="5cbc947f13d94e1cbb18076d72d00d7a" />
        <placeholder
          name="ImageOverride"
          value="" />
        <placeholder
          name="Description"
          value="Make a booking with a Last Mile Carrier and print a label"
          resid="c6855c6b-bce0-4160-b56c-06dd2e741b6e" />
        <placeholder
          name="Behaviour"
          value="FormFlow" />
        <placeholder
          name="PagePK"
          value="" />
        <placeholder
          name="Url"
          value="" />
        <placeholder
          name="FormFlow"
          value="32ade3d67a9c4f13b02f83b0c0de06db" />
      </control>
      <control
        code="TIL"
        id="a010b5cd-f185-4c80-9b9e-f929cbfb5517"
        left="8"
        top="5"
        width="4"
        height="4">
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="Size"
          value="Large" />
        <placeholder
          name="Color"
          value="Default" />
        <placeholder
          name="DisplayMode"
          value="TextAndIcon" />
        <placeholder
          name="Text"
          value="Tracking"
          resid="c633b279-6fdf-4797-81e0-303a7a62fc35" />
        <placeholder
          name="Image"
          value="702537aece2b47078b449d342f609b5e" />
        <placeholder
          name="ImageOverride"
          value="" />
        <placeholder
          name="Description"
          value="Track Shipments, Booking Headers, Orders, Items and Invoices."
          resid="dcc3a42e-4e90-44c9-9bfb-38b8ce61dc7c" />
        <placeholder
          name="Behaviour"
          value="Page" />
        <placeholder
          name="PagePK"
          value="79cf3e9f175a4a67af77918cc5984c3d" />
        <placeholder
          name="Url"
          value="" />
        <placeholder
          name="FormFlow"
          value="" />
      </control>
      <control
        code="TRC"
        id="98dec2a5-2755-47cb-bd54-71ecfcabbb3c"
        left="4"
        top="9"
        width="8"
        height="4">
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="VisibilityCondition"
          value="" />
      </control>
    </control>
  </form>
