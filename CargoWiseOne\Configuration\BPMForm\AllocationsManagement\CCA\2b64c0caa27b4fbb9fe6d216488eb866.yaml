#transformationVersion: 70.0
#
VZ_PK: 2b64c0caa27b4fbb9fe6d216488eb866
VZ_ConfigurationKey: 2b64c0ca-a27b-4fbb-9fe6-d216488eb866
VZ_FormID: CCA - View Allocation Routes
VZ_Caption:
  resKey: VZ_Caption|2b64c0ca-a27b-4fbb-9fe6-d216488eb866
  text: View Allocation Routes
VZ_FormFactor: DSK
VZ_EntityType: IRatingContract
VZ_FormType: FRM
VZ_RequiresLogin: true
VZ_MaterialDesign: true
VZ_Dependencies: <dependencies xmlns="http://cargowise.com/glow/2014/11/14/dependencies.xsd" />
VZ_FormData: >-
  <form
    id="23bfa67c-730d-4804-94b1-9bca7eab84a4" xmlns="http://wisetechglobal.com/glow/2017/05/26/form.xsd">
    <placeholder
      name="Layout"
      value="fill" />
    <control
      code="SDT"
      id="3a1d21ad-4c7e-4e5d-afdb-24c6974ba13c"
      binding="">
      <placeholder
        name="EntityType"
        value="IRatingContractAllocationLine" />
      <placeholder
        name="DefaultFilter">
        <xml>
          <ArrayOfFilterGroup xmlns:a="http://schemas.microsoft.com/2003/10/Serialization/Arrays" xmlns:i="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://cargowise.com/glow/2014/07/16/filters.xsd">
            <FilterGroup>
              <Filters>
                <Filter>
                  <FilterType>AdvancedGuidLookupFilter</FilterType>
                  <Operation>Is</Operation>
                  <PropertyPath>RCA_RCT_RatingContract</PropertyPath>
                  <Values>
                    <a:string>&lt;RCT_PK&gt;</a:string>
                  </Values>
                </Filter>
              </Filters>
              <IsImplicit>true</IsImplicit>
            </FilterGroup>
          </ArrayOfFilterGroup>
        </xml>
      </placeholder>
      <placeholder
        name="CaptionOverride"
        value="Allocation Routes"
        resid="83a45c49-bafe-43b2-8688-039877ebf28e" />
      <placeholder
        name="FieldConfiguration">
        <xml>
          <fields xmlns="">
            <field
              path="RCA_AllocationLineID"
              width="300"
              mode="Optional" />
            <field
              path="RCA_StartDate"
              width="300"
              mode="Optional" />
            <field
              path="RCA_ExpiryDate"
              width="300"
              mode="Optional" />
            <field
              path="LoadLocationWithProxy"
              width="130"
              mode="Default" />
            <field
              path="DischargeLocationWithProxy"
              width="130"
              mode="Default" />
            <field
              path="VoyageWithProxy"
              width="130"
              mode="Default" />
            <field
              path="VesselWithProxy"
              width="250"
              mode="Default" />
            <field
              path="RCA_StorageOrFreightRateClass"
              width="300"
              mode="Optional" />
            <field
              path="AllocatedQuantity"
              width="300"
              mode="Optional"
              isFilterable="false" />
            <field
              path="RCA_DemandQuantity"
              width="300"
              mode="Optional" />
            <field
              path="RCA_AllocatedUQ"
              width="300"
              mode="Optional" />
            <field
              path="ServiceStringWithProxy"
              width="250"
              mode="Default" />
            <field
              path="HasBookingLimit"
              width="300"
              mode="Optional"
              isFilterable="false" />
            <field
              path="BookingVariance"
              width="300"
              mode="Optional"
              isFilterable="false" />
            <field
              path="Utilization"
              width="300"
              mode="Optional" />
            <field
              path="OutstandingCommitted"
              width="300"
              mode="Optional" />
            <field
              path="OutstandingWithVariance"
              width="300"
              mode="Optional" />
            <field
              path="RCA_RC_ContainerType"
              width="300"
              mode="Optional" />
            <field
              path="CapacityWithVariance"
              width="300"
              mode="Optional" />
            <field
              path="SailingSchedule.JX_UniqueReference"
              width="130"
              mode="Optional"
              readOnly="true" />
            <field
              path="SailingSchedule.JobVoyOrigin.JA_E_DEP"
              width="130"
              mode="Optional"
              readOnly="true" />
            <field
              path="LinkedScheduleETDUpdated"
              width="200"
              mode="Optional"
              readOnly="true" />
            <field
              path="SailingSchedule.JobVoyOrigin.JA_S_DEP"
              width="130"
              mode="Optional"
              readOnly="true" />
            <field
              path="SailingSchedule.JobVoyDestination.JB_E_ARV"
              width="130"
              mode="Optional"
              readOnly="true" />
            <field
              path="SailingSchedule.JobVoyDestination.JB_S_ARV"
              width="130"
              mode="Optional"
              readOnly="true" />
            <field
              path="RCA_AllocatedQuantity"
              width="180"
              mode="FilterOnly" />
            <field
              path="RCA_AllowRelatedPorts"
              width="170"
              mode="Optional" />
            <field
              path="RCA_HasBookingLimit"
              width="180"
              mode="FilterOnly" />
            <field
              path="RCA_BookingVariance"
              width="180"
              mode="FilterOnly" />
            <field
              path="RCA_ContainerOwner"
              width="300"
              mode="Optional" />
            <field
              path="RCA_AllowGatewayConsolOnly"
              width="300"
              mode="Optional" />
            <field
              path="RCA_AllowGroupageOnly"
              width="300"
              mode="Optional" />
            <field
              path="RCA_AllowFreightSpotRate"
              width="250"
              mode="Optional" />
            <field
              path="RCA_Priority"
              width="300"
              mode="Optional" />
            <field
              path="AllocationRouteAgentPivots"
              width="300"
              caption="Agents"
              resid="d8f4fc2a-99f3-4c44-b56b-2e17b51beb0f"
              mode="FilterOnly" />
            <field
              path="RCA_EJ_TradeLane"
              width="300"
              mode="Optional" />
            <field
              path="TradeLane.EJ_Description"
              width="300"
              mode="Optional"
              isFilterable="false" />
            <field
              path="ContainerWeightLimit"
              width="300"
              mode="Optional" />
            <field
              path="RCA_ContainerWeightLimitType"
              width="300"
              mode="Optional" />
            <field
              path="RCA_ContainerWeightLimitUQ"
              width="300"
              mode="Optional" />
            <field
              path="RatingContractNamedAccountPivots"
              width="250"
              mode="FilterOnly"
              caption="Customers"
              resid="********-468a-49bb-843d-627bd04416bd"
              isFilterable="%.EnableRelatedOrganizationForCustomerSpecificAllocations"
              isVisible="%.EnableRelatedOrganizationForCustomerSpecificAllocations" />
            <field
              path="RCA_PlaceOfDelivery"
              width="300"
              mode="Optional" />
            <field
              path="RCA_PlaceOfReceipt"
              width="300"
              mode="Optional" />
          </fields>
        </xml>
      </placeholder>
      <placeholder
        name="HideItemActions"
        value="True" />
      <placeholder
        name="FullPage"
        value="True" />
    </control>
  </form>
