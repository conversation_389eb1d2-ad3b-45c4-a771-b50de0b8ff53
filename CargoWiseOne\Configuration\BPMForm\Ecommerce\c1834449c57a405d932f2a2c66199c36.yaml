#transformationVersion: 70.0
#
VZ_PK: c1834449c57a405d932f2a2c66199c36
VZ_ConfigurationKey: c1834449-c57a-405d-932f-2a2c66199c36
VZ_FormID: ETL - VDV3 - Shipper/Origin/Destination Depot - Edit Item (Read Only)
VZ_Caption:
  resKey: VZ_Caption|c1834449-c57a-405d-932f-2a2c66199c36
  text: Edit Item
VZ_FormFactor: DSK
VZ_EntityType: IHVLVItem
VZ_FormType: FRM
VZ_RequiresLogin: true
VZ_Dependencies: >-
  <dependencies xmlns="http://cargowise.com/glow/2014/11/14/dependencies.xsd">
    <expandPath
      path="OuterPackage" />
    <expandPath
      path="LastMileTransportBooking" />
    <expandPath
      path="Consignment.LastMileCarrier" />
    <expandPath
      path="Consignment" />
    <expandPath
      path="PackType" />
    <expandPath
      path="HVLVItemLines" />
    <calculatedProperty
      path="LoadListNumber" />
    <calculatedProperty
      path="VolumeWeight" />
    <calculatedProperty
      path="Chargeable" />
    <calculatedProperty
      path="DensityFactor" />
    <datagrid
      path="HVLVItemLines">
      <expandPath
        path="OriginCountry" />
    </datagrid>
  </dependencies>
VZ_FormData: >-
  <form
    id="306379ff-23db-4d1c-a77f-866491f3d169" xmlns="http://wisetechglobal.com/glow/2017/05/26/form.xsd">
    <placeholder
      name="BackgroundImagePk"
      value="" />
    <placeholder
      name="BackgroundSize"
      value="Auto" />
    <placeholder
      name="BackgroundImageOverride"
      value="" />
    <placeholder
      name="FormLayout"
      value="Splitview" />
    <placeholder
      name="ExpandPaths"
      value="" />
    <placeholder
      name="ScanInputHandling"
      value="NON" />
    <placeholder
      name="RemovePadding"
      value="false" />
    <placeholder
      name="TurnOnPortableOneToOneDesignMode"
      value="false" />
    <placeholder
      name="ContainsDefaultButton"
      value="False" />
    <control
      code="GRP"
      id="e5e1e604-94a3-41cf-a4ff-d12c58b9690f"
      left="0.5"
      top="0"
      width="18"
      height="20">
      <placeholder
        name="CanBeHidden"
        value="True" />
      <placeholder
        name="VisibilityCondition"
        value="" />
      <placeholder
        name="Header"
        value="Item Details"
        resid="b7b69774-3ee9-48e9-880c-636b75f32d6d" />
      <placeholder
        name="HeaderAlignment"
        value="Center" />
      <placeholder
        name="HeaderDisplayMode"
        value="Visible" />
      <placeholder
        name="BackgroundImagePk"
        value="" />
      <placeholder
        name="BackgroundImageOverride"
        value="" />
      <control
        code="TXT"
        id="2dafd19f-2179-413f-b246-38bbcb609d3f"
        left="3.3"
        top="0"
        width="6"
        height="1"
        binding="HVI_ItemId">
        <placeholder
          name="IsReadOnly"
          value="True" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="OnSubmit"
          value="" />
        <placeholder
          name="MultiLine"
          value="False" />
        <placeholder
          name="EnableAutoComplete"
          value="False" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="TXT"
        id="aa725919-0bb9-4a21-abc9-dfc5d116f927"
        left="9.3"
        top="0"
        width="6"
        height="1"
        binding="HVI_CurrentBarcode">
        <placeholder
          name="IsReadOnly"
          value="True" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="OnSubmit"
          value="" />
        <placeholder
          name="MultiLine"
          value="False" />
        <placeholder
          name="EnableAutoComplete"
          value="False" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="TXT"
        id="b465bf33-627d-4c7e-aab1-55d3ab565115"
        left="3.3"
        top="1"
        width="6"
        height="1"
        binding="HVI_ShipperReference">
        <placeholder
          name="IsReadOnly"
          value="True" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="OnSubmit"
          value="" />
        <placeholder
          name="MultiLine"
          value="False" />
        <placeholder
          name="EnableAutoComplete"
          value="False" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="CMB"
        id="4df9da2b-74d6-4575-b4b2-697f6787236b"
        left="9.3"
        top="1"
        width="6"
        height="1"
        binding="HVI_Status">
        <placeholder
          name="IsReadOnly"
          value="True" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="DisplayMode"
          value="Unspecified" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="TXT"
        id="a882dd81-6af4-46bd-a21f-e088fc24edb8"
        left="3.3"
        top="2"
        width="6"
        height="1"
        binding="LoadListNumber">
        <placeholder
          name="IsReadOnly"
          value="True" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="OnSubmit"
          value="" />
        <placeholder
          name="MultiLine"
          value="False" />
        <placeholder
          name="EnableAutoComplete"
          value="False" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="TXT"
        id="8a0895f9-a76c-400d-bf74-334cd56c3b58"
        left="9.3"
        top="2"
        width="6"
        height="1"
        binding="HVI_ContainerNumber">
        <placeholder
          name="IsReadOnly"
          value="True" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="OnSubmit"
          value="" />
        <placeholder
          name="MultiLine"
          value="False" />
        <placeholder
          name="EnableAutoComplete"
          value="False" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="TXT"
        id="33b81c08-6983-4a0c-8b1b-26a5e9186cf5"
        left="3.3"
        top="3"
        width="6"
        height="1"
        binding="OuterPackage.HVO_PackageBarcode">
        <placeholder
          name="IsReadOnly"
          value="True" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="Outer Package Barcode"
          resid="4ccee674-99cc-4a2a-a53d-b2c55cc43cfa" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="OnSubmit"
          value="" />
        <placeholder
          name="MultiLine"
          value="False" />
        <placeholder
          name="EnableAutoComplete"
          value="False" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="TXT"
        id="c997ce2f-e8c2-4229-a0d8-06e05294cd7d"
        left="9.3"
        top="3"
        width="6"
        height="1"
        binding="LastMileTransportBooking.KM_JobID">
        <placeholder
          name="IsReadOnly"
          value="True" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="Transport Booking ID"
          resid="73bd8d0b-a649-49c9-b9ee-1ac9b6f7ada1" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="OnSubmit"
          value="" />
        <placeholder
          name="MultiLine"
          value="False" />
        <placeholder
          name="EnableAutoComplete"
          value="False" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="SRC"
        id="a4755638-d6cd-4dd9-b014-0e73222e3bc3"
        left="3.3"
        top="4"
        width="6"
        height="1"
        binding="Consignment.HVC_OH_LastMileCarrier">
        <placeholder
          name="IsReadOnly"
          value="True" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="DisplayMode"
          value="DescOnly" />
        <placeholder
          name="FieldConfiguration"
          value="" />
        <placeholder
          name="DefaultFilter"
          value="" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="CMB"
        id="8783a28d-299c-42bb-a3cf-c60f8b7c7b66"
        left="9.3"
        top="4"
        width="6"
        height="1"
        binding="HVI_CarrierBookingStatus">
        <placeholder
          name="IsReadOnly"
          value="True" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="DisplayMode"
          value="DescOnly" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="NUM"
        id="7595571c-9310-4237-bbee-2390e6f42467"
        left="3.3"
        top="5"
        width="6"
        height="1"
        binding="HVI_ManifestedVolume">
        <placeholder
          name="IsReadOnly"
          value="True" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="Manifested Volume"
          resid="5d29b07e-eba9-47fc-aaf1-97e83a5926a5" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="NumericFieldLeftAlignment"
          value="False" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="NUM"
        id="c67f0344-9ad4-45b2-a587-5f668991a79b"
        left="9.3"
        top="5"
        width="6"
        height="1"
        binding="HVI_ManifestedWeight">
        <placeholder
          name="IsReadOnly"
          value="True" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="Manifested Weight"
          resid="98b2815f-22fa-4c04-8058-3367c363e5b1" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="NumericFieldLeftAlignment"
          value="False" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="NUM"
        id="83957f28-40dd-460a-b810-3619e6ae1383"
        left="3.3"
        top="6"
        width="6"
        height="1"
        binding="HVI_ActualVolume">
        <placeholder
          name="IsReadOnly"
          value="True" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="NumericFieldLeftAlignment"
          value="False" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="NUM"
        id="2e4ff708-ce98-499b-8ba0-5638eb7faf76"
        left="9.3"
        top="6"
        width="6"
        height="1"
        binding="HVI_ActualWeight">
        <placeholder
          name="IsReadOnly"
          value="True" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="NumericFieldLeftAlignment"
          value="False" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="CMB"
        id="780c61ad-705e-4eb6-8c9d-7d7e0c376ca0"
        left="3.3"
        top="7"
        width="6"
        height="1"
        binding="Consignment.HVC_VolumeUQ">
        <placeholder
          name="IsReadOnly"
          value="True" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="DisplayMode"
          value="Unspecified" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="CMB"
        id="4c5b6900-2428-4034-acb8-d042bc2d1f32"
        left="9.3"
        top="7"
        width="6"
        height="1"
        binding="Consignment.HVC_WeightUQ">
        <placeholder
          name="IsReadOnly"
          value="True" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="DisplayMode"
          value="Unspecified" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="NUM"
        id="8297e1ce-c8af-4972-b007-5ce0a256ff6c"
        left="3.3"
        top="8"
        width="6"
        height="1"
        binding="HVI_Length">
        <placeholder
          name="IsReadOnly"
          value="True" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="NumericFieldLeftAlignment"
          value="False" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="NUM"
        id="65ba2acc-9080-4596-a36a-26c4e960a3ac"
        left="9.3"
        top="8"
        width="6"
        height="1"
        binding="HVI_Width">
        <placeholder
          name="IsReadOnly"
          value="True" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="NumericFieldLeftAlignment"
          value="False" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="NUM"
        id="dd672472-8b5a-45c4-bd77-84c4c55b0948"
        left="3.3"
        top="9"
        width="6"
        height="1"
        binding="HVI_Height">
        <placeholder
          name="IsReadOnly"
          value="True" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="NumericFieldLeftAlignment"
          value="False" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="CMB"
        id="23011506-9bad-4a1a-a029-178133082cca"
        left="9.3"
        top="9"
        width="6"
        height="1"
        binding="HVI_UnitOfDimension">
        <placeholder
          name="IsReadOnly"
          value="True" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="DisplayMode"
          value="CodeDesc" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="SRC"
        id="946bdb9a-36cf-45f9-a3c9-1800871d04dc"
        left="3.3"
        top="10"
        width="6"
        height="1"
        binding="HVI_F3_NKPackType">
        <placeholder
          name="IsReadOnly"
          value="True" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="DisplayMode"
          value="CodeDesc" />
        <placeholder
          name="FieldConfiguration"
          value="" />
        <placeholder
          name="DefaultFilter"
          value="" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="TXT"
        id="cdda24b7-fb1b-4742-8db3-1fbb7b5c3f4a"
        left="9.3"
        top="10"
        width="6"
        height="1"
        binding="HVI_GoodsDescription">
        <placeholder
          name="IsReadOnly"
          value="True" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="OnSubmit"
          value="" />
        <placeholder
          name="MultiLine"
          value="False" />
        <placeholder
          name="EnableAutoComplete"
          value="False" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="OPT"
        id="0d480141-16ea-4970-8623-87ac3337d017"
        left="3.3"
        top="11"
        width="6"
        height="1"
        binding="HVI_IsPillaged">
        <placeholder
          name="IsReadOnly"
          value="True" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="OPT"
        id="3d930246-a6c0-4cf2-bfdc-06fb90bcb18c"
        left="9.3"
        top="11"
        width="6"
        height="1"
        binding="HVI_IsDamaged">
        <placeholder
          name="IsReadOnly"
          value="True" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="OPT"
        id="93432066-f35f-4d77-aaef-e1bd4cb8535e"
        left="3.3"
        top="12"
        width="6"
        height="1"
        binding="HVI_IsUllaged">
        <placeholder
          name="IsReadOnly"
          value="True" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="TXT"
        id="9eedb139-6aa2-4010-af2b-155b5b64b3a3"
        left="3.3"
        top="13"
        width="4"
        height="1"
        binding="VolumeWeight">
        <placeholder
          name="IsReadOnly"
          value="True" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="" />
        <placeholder
          name="VisibilityCondition"
          value="81b22d3c-b4bc-458e-9464-99290d1856b5" />
        <placeholder
          name="OnSubmit"
          value="" />
        <placeholder
          name="MultiLine"
          value="False" />
        <placeholder
          name="EnableAutoComplete"
          value="False" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="TXT"
        id="e1476764-bc29-4f4d-a0ba-33a9d7c0c506"
        left="3.3"
        top="13"
        width="4"
        height="1"
        binding="VolumeWeight">
        <placeholder
          name="IsReadOnly"
          value="True" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="Weight Volume"
          resid="bbc82752-9e7a-49f1-885a-8c249c665791" />
        <placeholder
          name="VisibilityCondition"
          value="39e1cb24-aa95-40c8-a6a6-19f8d92ca143" />
        <placeholder
          name="OnSubmit"
          value="" />
        <placeholder
          name="MultiLine"
          value="False" />
        <placeholder
          name="EnableAutoComplete"
          value="False" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="TXT"
        id="e3ba0798-7a12-4423-8091-c10f68515dfe"
        left="7.3"
        top="13"
        width="4"
        height="1"
        binding="Chargeable">
        <placeholder
          name="IsReadOnly"
          value="True" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="OnSubmit"
          value="" />
        <placeholder
          name="MultiLine"
          value="False" />
        <placeholder
          name="EnableAutoComplete"
          value="False" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="TXT"
        id="5926192c-0bb4-49c3-97e2-15c453fee362"
        left="11.3"
        top="13"
        width="4"
        height="1"
        binding="DensityFactor">
        <placeholder
          name="IsReadOnly"
          value="True" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="OnSubmit"
          value="" />
        <placeholder
          name="MultiLine"
          value="False" />
        <placeholder
          name="EnableAutoComplete"
          value="False" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="GRD"
        id="37124f3c-05f3-45a5-82ab-4a331b1470ff"
        left="0"
        top="14"
        width="17.9"
        bottom="0.3"
        binding="HVLVItemLines">
        <placeholder
          name="IsReadOnly"
          value="True" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="CaptionOverride"
          value="Item Lines"
          resid="2841a03c-fee4-43cc-8391-0b916373f32b" />
        <placeholder
          name="VisibilityCondition"
          value="81b22d3c-b4bc-458e-9464-99290d1856b5" />
        <placeholder
          name="HeaderAlignment"
          value="Left" />
        <placeholder
          name="HeaderDisplayMode"
          value="Visible" />
        <placeholder
          name="AllowAdd"
          value="False" />
        <placeholder
          name="AllowAttach"
          value="True" />
        <placeholder
          name="AllowDetach"
          value="True" />
        <placeholder
          name="AllowInlineEdit"
          value="True" />
        <placeholder
          name="FieldConfiguration">
          <xml>
            <fields xmlns="">
              <field
                path="HVS_RN_NKOriginCountryCode"
                width="140"
                mode="Default" />
              <field
                path="HVS_ProductCode"
                width="250"
                mode="Default" />
              <field
                path="HVS_OriginTariff"
                width="150"
                mode="Default" />
              <field
                path="HVS_DestinationTariff"
                width="180"
                mode="Default" />
              <field
                path="HVS_GoodsDescription"
                width="250"
                mode="Default" />
              <field
                path="HVS_OriginGoodsDescription"
                width="250"
                mode="Default" />
              <field
                path="HVS_Quantity"
                width="80"
                mode="Default" />
              <field
                path="HVS_CustomsValue"
                width="200"
                mode="Default" />
              <field
                path="HVS_IntrinsicValue"
                width="200"
                mode="Default" />
              <field
                path="HVS_GrossWeightMeasure"
                width="100"
                mode="Default" />
              <field
                path="HVS_NetWeightMeasure"
                width="100"
                mode="Default" />
              <field
                path="HVS_ItemURL"
                width="250"
                mode="Optional" />
              <field
                path="HVS_ClusterKey"
                width="100"
                mode="Optional" />
              <field
                path="OriginCountry.RN_Code"
                width="100"
                mode="Optional" />
            </fields>
          </xml>
        </placeholder>
        <placeholder
          name="DefaultSortFields"
          value="" />
        <placeholder
          name="ShowDocumentsIcon"
          value="True" />
        <placeholder
          name="RowActionsMode"
          value="RowAndMenu" />
        <placeholder
          name="HideGridActions"
          value="False" />
        <placeholder
          name="HideFilters"
          value="False" />
        <placeholder
          name="ActionMenuItems"
          value="" />
        <placeholder
          name="EditFormFlow"
          value="90806594f4d0494bb42fb1408c0c4235" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
    </control>
  </form>
