#transformationVersion: 70.0
#
VZ_PK: d4146e3f460f4911a5e119bc187252a0
VZ_ConfigurationKey: d4146e3f-460f-4911-a5e1-19bc187252a0
VZ_FormID: CYP Edit Pick up Instruction
VZ_Caption:
  resKey: VZ_Caption|d4146e3f-460f-4911-a5e1-19bc187252a0
  text: Edit Pick up Instruction
VZ_FormFactor: DSK
VZ_EntityType: ICYDPickupHeader
VZ_FormType: FRM
VZ_RequiresLogin: true
VZ_MaterialDesign: true
VZ_Dependencies: <dependencies xmlns="http://cargowise.com/glow/2014/11/14/dependencies.xsd" />
VZ_FormData: >-
  <form
    id="db203009-5065-4b87-9346-a9c82d64239d" xmlns="http://wisetechglobal.com/glow/2017/05/26/form.xsd">
    <placeholder
      name="MinHeight"
      value="100%" />
    <control
      code="BOX"
      id="e9289a17-1dcf-45e8-84ee-1879fbc41e5b"
      binding="">
      <placeholder
        name="MinHeight"
        value="100%" />
      <placeholder
        name="Layout"
        value="grid" />
      <control
        code="PNL"
        id="937063ee-aab6-4223-b193-44ae03afe96a"
        binding="">
        <placeholder
          name="Columns"
          value="col-md-2 col-lg-2 col-xl-2" />
        <placeholder
          name="Layout"
          value="grid" />
        <placeholder
          name="MinHeight"
          value="100%" />
        <control
          code="BOX"
          id="f492802e-db47-48a2-8ff7-b7d940d30a9e"
          binding="">
          <placeholder
            name="Layout"
            value="grid" />
          <placeholder
            name="MinHeight"
            value="100%" />
          <control
            code="LBL"
            id="fc060d67-38bb-48e2-a25a-f94211fb248b"
            binding="">
            <placeholder
              name="Caption"
              value="Basic Information"
              resid="3e900109-ee59-4d9b-b32e-d52b920810c0" />
            <placeholder
              name="Typography"
              value="h6" />
            <placeholder
              name="Color"
              value="deep-purple darken-4" />
          </control>
          <control
            code="ADD"
            id="f8d15fe2-45dc-4a8f-964b-e3db676fdde7"
            binding="AddressTypeTRA.E2_OA_Address">
            <placeholder
              name="CaptionOverride"
              value="Transport Provider"
              resid="f881ff13-1822-4e2a-8ff0-c69d04970ee5" />
          </control>
          <control
            code="SRC"
            id="1294866b-01f7-42ee-82dd-7f9cc7af3ce5"
            binding="YPH_WW_Yard">
            <placeholder
              name="IsReadOnly"
              value="True" />
          </control>
        </control>
      </control>
      <control
        code="PNL"
        id="7a0088a8-f674-4a08-b072-73b15e48345f"
        binding="">
        <placeholder
          name="Columns"
          value="col-md-10 col-lg-10 col-xl-10" />
        <placeholder
          name="MinHeight"
          value="100%" />
        <control
          code="LBL"
          id="e78f3dc7-aa16-446d-af84-3cfc7c026dfb"
          binding="">
          <placeholder
            name="Caption"
            value="Pick up Instruction Details"
            resid="23784895-d832-4122-be3d-ca6394762537" />
          <placeholder
            name="Typography"
            value="h5" />
          <placeholder
            name="Margin"
            value="mb-5" />
        </control>
        <control
          code="RDT"
          id="a183a7c5-34fd-4023-a233-024469d7f271"
          binding="CYDPickups">
          <placeholder
            name="AllowAdd"
            value="True" />
          <placeholder
            name="FullPage"
            value="True" />
          <placeholder
            name="InlineEdit"
            value="cell" />
          <placeholder
            name="CaptionType"
            value="none" />
          <placeholder
            name="ShowFilters"
            value="True" />
          <placeholder
            name="FieldConfiguration">
            <xml>
              <fields xmlns="">
                <field
                  path="YPL_YEL_ReleaseAdviceLine"
                  width="300"
                  mode="Default" />
                <field
                  path="UnitNumber"
                  width="300"
                  mode="Default" />
                <field
                  path="YPL_RC_ContainerType"
                  width="300"
                  mode="Default" />
                <field
                  path="Client"
                  width="300"
                  mode="Default" />
                <field
                  path="YPL_Quantity"
                  width="300"
                  mode="Default" />
                <field
                  path="Size"
                  width="300"
                  mode="Default" />
                <field
                  path="YPL_Type"
                  width="300"
                  mode="Default" />
                <field
                  path="YPL_TransportReference"
                  width="300"
                  mode="Default" />
                <field
                  path="UnitLineItem.ContainerType.RC_ISOType"
                  width="300"
                  mode="Default" />
                <field
                  path="ReleaseAdviceLine.ReleaseAdvice.YRE_ReleaseNumber"
                  width="250"
                  mode="Mandatory"
                  readOnly="true" />
                <field
                  path="ReleaseAdviceLine.ReleaseAdvice.YRE_JobNumber"
                  width="250"
                  mode="Mandatory" />
                <field
                  path="VehicleRegistrationNumber"
                  width="250"
                  mode="Optional" />
                <field
                  path="ReleaseAdviceLine.ReleaseAdvice.YRE_FromDate"
                  width="250"
                  mode="Optional" />
                <field
                  path="ReleaseAdviceLine.ReleaseAdvice.YRE_ToDate"
                  width="250"
                  mode="Optional" />
                <field
                  path="ManufactureDate"
                  width="250"
                  mode="Mandatory" />
                <field
                  path="IsEmpty"
                  width="80"
                  mode="Mandatory" />
                <field
                  path="GrossWeight"
                  width="300"
                  mode="Optional" />
                <field
                  path="TareWeight"
                  width="300"
                  mode="Optional" />
                <field
                  path="UnitOfWeight"
                  width="300"
                  mode="Optional" />
              </fields>
            </xml>
          </placeholder>
          <placeholder
            name="ShowCustomize"
            value="True" />
          <placeholder
            name="ShowGrouping"
            value="True" />
        </control>
      </control>
    </control>
  </form>
