#transformationVersion: 70.0
#
VZ_PK: ca882dbfd51f48b8b7a3d8ae6dfec146
VZ_ConfigurationKey: ca882dbf-d51f-48b8-b7a3-d8ae6dfec146
VZ_FormID: OPP - New Scope
VZ_Caption:
  resKey: VZ_Caption|ca882dbfd51f48b8b7a3d8ae6dfec146
  text: Add scope
VZ_FormFactor: DSK
VZ_EntityType: ICrmOpportunityScope
VZ_FormType: FRM
VZ_RequiresLogin: true
VZ_MaterialDesign: true
VZ_ExtenderFunc: CargoWise.Glow.Business.CustomerRelationshipManagement.Opportunity.OpportunityScopeExtension.ExtendForm
VZ_Dependencies: <dependencies xmlns="http://cargowise.com/glow/2014/11/14/dependencies.xsd" />
VZ_FormData: >-
  <form
    id="7d131f4e-baba-4d5d-b5e4-6fc90b3a8833" xmlns="http://wisetechglobal.com/glow/2017/05/26/form.xsd">
    <placeholder
      name="Width"
      value="950" />
    <control
      code="PNL"
      id="45259fc7-5c4b-47e3-b9fd-002a48090017">
      <placeholder
        name="Layout"
        value="grid" />
      <control
        code="CLO"
        id="f175b8ec-a614-468e-aafe-f53923f70d37">
        <placeholder
          name="Sentiment"
          value="warning" />
        <placeholder
          name="VisibilityCondition"
          value="!COS_Enabled" />
        <placeholder
          name="Title"
          value="This opportunity scope is currently inactive."
          resid="db8f96fa-5992-46ae-9796-6bd68572a427" />
        <placeholder
          name="Description"
          value="Refine the scope, check conflicts and save, to mark as active. If you save as inactive, the scope won't be linked to future quotes or jobs."
          resid="65b43e6c-41f2-46b0-b638-b3b43cb5de5e" />
      </control>
      <control
        code="CLO"
        id="d495c0b8-a38d-4b37-b3b2-c2afddbc16d6">
        <placeholder
          name="Sentiment"
          value="warning" />
        <placeholder
          name="VisibilityCondition"
          value="OverlappedOpportunityScopes != null &amp;&amp; OverlappedOpportunityScopes != &quot;&quot; &amp;&amp; COS_Enabled &amp;&amp; HasScopeConflictInDifferentOpportunity" />
        <placeholder
          name="Description"
          value="calc(ConflictWarningDifferentOpportunity)" />
        <placeholder
          name="Title"
          value="Duplicates or conflicts with one or more existing opportunity scopes"
          resid="c14cf3e1-bc38-4dc7-a2cc-0bcc3cdd93a4" />
        <control
          code="HPL"
          id="0917f22e-c924-4b69-9c46-9b385d5d596f">
          <placeholder
            name="Hyperlink"
            value="calc(OpenConflictingOpportunity)" />
          <placeholder
            name="Target"
            value="_blank" />
          <placeholder
            name="Caption"
            value="Open conflicting opportunity"
            resid="9b65f166-b2e8-4104-8726-f2b77eda1f35" />
          <placeholder
            name="VisibilityCondition"
            value="HasScopeConflictInDifferentOpportunity" />
        </control>
      </control>
      <control
        code="CLO"
        id="7f973fad-c6e0-4058-80fa-05ad6e686da0">
        <placeholder
          name="Sentiment"
          value="warning" />
        <placeholder
          name="VisibilityCondition"
          value="OverlappedOpportunityScopes != null &amp;&amp; OverlappedOpportunityScopes != &quot;&quot; &amp;&amp; COS_Enabled &amp;&amp; !HasScopeConflictInDifferentOpportunity" />
        <placeholder
          name="Title"
          value="Duplicates or conflicts with one or more scopes in this opportunity"
          resid="24aca106-9c64-4fe9-a976-bec925590f78" />
        <placeholder
          name="Description"
          value="calc(ConflictWarningSameOpportunity)" />
      </control>
      <control
        code="CMB"
        id="27c9b074-cc1f-4eb7-9337-7c9d714b8823"
        binding="COS_ProductCode">
        <placeholder
          name="CaptionOverride"
          value="Product"
          resid="cf011eb7-2dc2-44ef-b4e9-9d218e52e7f5" />
        <placeholder
          name="IsReadOnly"
          value="True" />
        <placeholder
          name="Required"
          value="True" />
      </control>
    </control>
    <control
      code="PNL"
      id="96b507fe-0fe7-4c97-9e61-2015ebe78bf8">
      <placeholder
        name="Caption"
        value="Routing requirements"
        resid="bea56a20-60b3-4b7b-9575-83b7c8a805dc" />
      <placeholder
        name="Layout"
        value="grid" />
      <placeholder
        name="Margin"
        value="mt-3" />
      <control
        code="SRC"
        id="23c700f4-b39a-4003-938b-8381b349fc26"
        binding="COS_Origin">
        <placeholder
          name="Columns"
          value="col-md-6" />
        <placeholder
          name="Required"
          value="True" />
      </control>
      <control
        code="SRC"
        id="b0240cb5-220d-4862-873f-6043a446d87f"
        binding="COS_Destination">
        <placeholder
          name="Columns"
          value="col-md-6" />
        <placeholder
          name="Required"
          value="True" />
      </control>
      <control
        code="CLO"
        id="07d7e676-d81f-407c-a8cc-2f61fc20aee4">
        <placeholder
          name="Sentiment"
          value="warning" />
        <placeholder
          name="Description"
          value="calc(WarningMessageForConflictingOriginDestination)" />
        <placeholder
          name="VisibilityCondition"
          value="HasReverseConflicts &amp;&amp; WarningMessageForConflictingOriginDestination != &quot;&quot;" />
        <placeholder
          name="Variant"
          value="inline" />
        <placeholder
          name="Style"
          value="white-space:pre-line" />
      </control>
      <control
        code="BOX"
        id="902ba4d7-ba8a-42f2-ac0d-079bd63ea0d3">
        <placeholder
          name="VisibilityCondition"
          value="Opportunity.OpportunityScopeForwardingParametersRegistryList.Contains(&quot;PickupDeliveryAddress&quot;) || (OPPScopeConfigurableFieldsInfo != null &amp;&amp; (OPPScopeConfigurableFieldsInfo.PickupAddress != null || OPPScopeConfigurableFieldsInfo.DeliveryAddress != null || OPPScopeConfigurableFieldsInfo.PickupPostCode != &quot;&quot; || OPPScopeConfigurableFieldsInfo.DeliveryPostCode != &quot;&quot;))" />
        <control
          code="BOX"
          id="5046c009-21a6-4c10-862c-1c94d54e69a5">
          <placeholder
            name="Layout"
            value="grid" />
          <placeholder
            name="Margin"
            value="mt-1" />
          <control
            code="OPT"
            id="ca00b405-1ddd-4e13-8485-2e54ec7ec7cb"
            binding="IncludePickup">
            <placeholder
              name="CaptionOverride"
              value="Include pickup"
              resid="1c9712ca-4ee8-468f-9aeb-7559c941c87c" />
            <placeholder
              name="Columns"
              value="col-6 col-sm-6 col-md-6 col-lg-6 col-xl-6" />
          </control>
          <control
            code="OPT"
            id="f311ae0e-e830-4a1a-888c-ec3f4882c939"
            binding="IncludeDelivery">
            <placeholder
              name="CaptionOverride"
              value="Include delivery"
              resid="b8bce61e-97c6-4118-b353-810654a15c10" />
            <placeholder
              name="Columns"
              value="col-6 col-sm-6 col-md-6 col-lg-6 col-xl-6" />
          </control>
        </control>
        <control
          code="BOX"
          id="e07e31f1-fd68-4966-b0a1-2bad046fb1f8">
          <placeholder
            name="Layout"
            value="grid" />
          <placeholder
            name="VisibilityCondition"
            value="IncludePickup == true || IncludeDelivery == true" />
          <placeholder
            name="Margin"
            value="mt-n1" />
          <control
            code="BOX"
            id="e17ba5ef-a439-4b2d-bbcd-b742f42cb5d1">
            <placeholder
              name="Columns"
              value="col-md-6 col-lg-6 col-xl-6" />
            <control
              code="BOX"
              id="ea6b617d-b2d6-4719-a686-772ff202d089">
              <placeholder
                name="VisibilityCondition"
                value="IncludePickup" />
              <control
                code="SWT"
                id="5eead756-7ba9-4f2d-a4f9-74135e733f19"
                binding="ShowPickupPostcode">
                <placeholder
                  name="CaptionOverride"
                  value="Use postcode"
                  resid="82c4cbff-a68a-4d43-9fd2-7b99808bd65e" />
                <placeholder
                  name="Margin"
                  value="mb-2" />
              </control>
              <control
                code="ADD"
                id="380985c9-fb3b-40de-ac8b-ef79eec6e4eb"
                binding="COS_OA_PickupAddress">
                <placeholder
                  name="VisibilityCondition"
                  value="!ShowPickupPostcode" />
                <placeholder
                  name="Required"
                  value="calc(IncludePickup &amp;&amp; !ShowPickupPostcode)" />
                <placeholder
                  name="ShowSelectedAddress"
                  value="True" />
              </control>
              <control
                code="SRC"
                id="f9e60edb-33b4-4ea3-9fbe-d62c08776120"
                binding="COS_PickupAddressPostCode">
                <placeholder
                  name="Required"
                  value="calc(IncludePickup &amp;&amp; ShowPickupPostcode)" />
                <placeholder
                  name="VisibilityCondition"
                  value="ShowPickupPostcode" />
                <placeholder
                  name="CaptionOverride"
                  value="Pickup postcode"
                  resid="aa27672a-d382-4011-888e-e55924c96122" />
              </control>
            </control>
          </control>
          <control
            code="BOX"
            id="c5275871-40da-46db-b99c-a5a5b7a266b6">
            <placeholder
              name="Columns"
              value="col-md-6 col-lg-6 col-xl-6" />
            <control
              code="BOX"
              id="f7d7c80f-07d8-4c99-b5eb-6d96559157d3">
              <placeholder
                name="VisibilityCondition"
                value="IncludeDelivery" />
              <control
                code="SWT"
                id="e844914a-7b6e-45f0-8c25-2fde4601e110"
                binding="ShowDeliveryPostcode">
                <placeholder
                  name="CaptionOverride"
                  value="Use postcode"
                  resid="aa3b2f7b-cb3d-4342-a07b-2a5386f387c0" />
                <placeholder
                  name="Margin"
                  value="mb-2" />
              </control>
              <control
                code="ADD"
                id="1e410533-86e9-45ab-bbfd-fa19e740d2e5"
                binding="COS_OA_DeliveryAddress">
                <placeholder
                  name="VisibilityCondition"
                  value="!ShowDeliveryPostcode" />
                <placeholder
                  name="Required"
                  value="calc(IncludeDelivery &amp;&amp; !ShowDeliveryPostcode)" />
                <placeholder
                  name="ShowSelectedAddress"
                  value="True" />
              </control>
              <control
                code="SRC"
                id="eb361f82-6b08-4120-9d1b-476b0f008fc7"
                binding="COS_DeliveryAddressPostCode">
                <placeholder
                  name="VisibilityCondition"
                  value="ShowDeliveryPostcode" />
                <placeholder
                  name="Required"
                  value="calc(IncludeDelivery &amp;&amp; ShowDeliveryPostcode)" />
                <placeholder
                  name="CaptionOverride"
                  value="Delivery postcode"
                  resid="adb5b31b-53ff-4bee-960f-b44507e1b4ea" />
              </control>
            </control>
          </control>
        </control>
      </control>
    </control>
    <control
      code="PNL"
      id="6cb4c974-9f39-4268-8352-56b2cea19341">
      <placeholder
        name="Caption"
        value="Shipping requirements"
        resid="ad8650a9-0895-48d1-9af0-536150e1a936" />
      <placeholder
        name="Layout"
        value="grid" />
      <placeholder
        name="Margin"
        value="mt-3" />
      <control
        code="CMB"
        id="7046f566-29a5-44e3-94b8-7b1e59d2fbc0"
        binding="COS_TransportMode">
        <placeholder
          name="Columns"
          value="col-md-6" />
      </control>
      <control
        code="CMB"
        id="4b97d2a6-c56a-42bb-8258-437ccb2ee35e"
        binding="COS_ContainerMode">
        <placeholder
          name="Columns"
          value="col-md-6" />
      </control>
    </control>
    <control
      code="PNL"
      id="f92ff48f-c3a0-419d-b0e8-851bf250e672">
      <placeholder
        name="Layout"
        value="grid" />
      <placeholder
        name="Margin"
        value="mt-3" />
      <control
        code="SWT"
        id="897f3df0-e38f-4e4d-bf75-1e131066e2f5"
        binding="CaptureAdditionalRequirements">
        <placeholder
          name="VisibilityCondition"
          value="Opportunity.OpportunityScopeForwardingParametersRegistryList.Contains(&quot;Container&quot;) || Opportunity.OpportunityScopeForwardingParametersRegistryList.Contains(&quot;Commodity&quot;) || Opportunity.OpportunityScopeForwardingParametersRegistryList.Contains(&quot;Incoterm&quot;) || Opportunity.OpportunityScopeForwardingParametersRegistryList.Contains(&quot;ServiceLevel&quot;) || (OPPScopeConfigurableFieldsInfo != null &amp;&amp; (OPPScopeConfigurableFieldsInfo.ContainerType != null || OPPScopeConfigurableFieldsInfo.Commodity != &quot;&quot; || OPPScopeConfigurableFieldsInfo.Incoterm != &quot;&quot; || OPPScopeConfigurableFieldsInfo.ServiceLevel != &quot;&quot;))" />
        <placeholder
          name="CaptionOverride"
          value="Capture additional requirements"
          resid="2dc2ad2f-6dbd-406f-b329-4d01c89bf1ee" />
      </control>
      <control
        code="BOX"
        id="9c64ec1a-b9c4-43f9-8c92-842434ab22d7">
        <placeholder
          name="Layout"
          value="grid" />
        <placeholder
          name="VisibilityCondition"
          value="CaptureAdditionalRequirements" />
        <control
          code="BOX"
          id="98db5a07-48c0-4855-8064-595f698f95a4">
          <placeholder
            name="Layout"
            value="grid" />
          <placeholder
            name="VisibilityCondition"
            value="Opportunity.OpportunityScopeForwardingParametersRegistryList.Contains(&quot;Container&quot;) || Opportunity.OpportunityScopeForwardingParametersRegistryList.Contains(&quot;Commodity&quot;) || (OPPScopeConfigurableFieldsInfo != null &amp;&amp; (OPPScopeConfigurableFieldsInfo.ContainerType != null || OPPScopeConfigurableFieldsInfo.Commodity != &quot;&quot;))" />
          <control
            code="LBL"
            id="d218916b-ffb6-4c91-aab6-04fad5296838">
            <placeholder
              name="Caption"
              value="Cargo details"
              resid="2b5ed73e-4338-43c1-814d-6623bb1d23a6" />
            <placeholder
              name="Typography"
              value="title-sm-default" />
          </control>
          <control
            code="CLO"
            id="e9ac04ef-c54c-4bff-9c6f-1827aacf5767">
            <placeholder
              name="Sentiment"
              value="info" />
            <placeholder
              name="Description"
              value="Select a specific transport and container mode before setting cargo details"
              resid="0707a574-cab8-4b31-ad0c-9b19be3f517d" />
            <placeholder
              name="VisibilityCondition"
              value="COS_ContainerMode == &quot;ALL&quot;" />
          </control>
          <control
            code="SRC"
            id="56924c34-0b73-42f8-851b-02601e69c752"
            binding="COS_RC_ContainerType">
            <placeholder
              name="Columns"
              value="col-md-6" />
            <placeholder
              name="VisibilityCondition"
              value="Opportunity.OpportunityScopeForwardingParametersRegistryList.Contains(&quot;Container&quot;) || (OPPScopeConfigurableFieldsInfo != null &amp;&amp; OPPScopeConfigurableFieldsInfo.ContainerType != null)" />
          </control>
          <control
            code="SRC"
            id="9f90cd81-381a-464f-b84f-1fde81421200"
            binding="COS_RH_NKCommodityCode">
            <placeholder
              name="Columns"
              value="col-md-6" />
            <placeholder
              name="VisibilityCondition"
              value="Opportunity.OpportunityScopeForwardingParametersRegistryList.Contains(&quot;Commodity&quot;) || (OPPScopeConfigurableFieldsInfo != null &amp;&amp; OPPScopeConfigurableFieldsInfo.Commodity != &quot;&quot;)" />
          </control>
        </control>
        <control
          code="BOX"
          id="18456686-3cd8-4ad6-aa5b-6d530c7eb4aa">
          <placeholder
            name="Layout"
            value="grid" />
          <placeholder
            name="VisibilityCondition"
            value="Opportunity.OpportunityScopeForwardingParametersRegistryList.Contains(&quot;Incoterm&quot;) || Opportunity.OpportunityScopeForwardingParametersRegistryList.Contains(&quot;ServiceLevel&quot;) || (OPPScopeConfigurableFieldsInfo != null &amp;&amp; (OPPScopeConfigurableFieldsInfo.Incoterm != &quot;&quot; || OPPScopeConfigurableFieldsInfo.ServiceLevel != &quot;&quot;))" />
          <control
            code="LBL"
            id="e383d27b-8618-47b0-b162-28a312fba3e6">
            <placeholder
              name="Caption"
              value="Other"
              resid="74c5315e-1f83-4db2-a4a3-b74ab5306518" />
            <placeholder
              name="Typography"
              value="title-sm-default" />
          </control>
        </control>
        <control
          code="CMB"
          id="b84e3cf4-ba8a-427d-b1ae-153354943fb3"
          binding="COS_IncoTerm">
          <placeholder
            name="Columns"
            value="col-md-6" />
          <placeholder
            name="VisibilityCondition"
            value="Opportunity.OpportunityScopeForwardingParametersRegistryList.Contains(&quot;Incoterm&quot;) || (OPPScopeConfigurableFieldsInfo != null &amp;&amp; OPPScopeConfigurableFieldsInfo.Incoterm != &quot;&quot;)" />
        </control>
        <control
          code="SRC"
          id="4c040297-9064-4239-b36b-457f6e0927a9"
          binding="COS_RS_NKServiceLevel">
          <placeholder
            name="Columns"
            value="col-md-6" />
          <placeholder
            name="VisibilityCondition"
            value="Opportunity.OpportunityScopeForwardingParametersRegistryList.Contains(&quot;ServiceLevel&quot;) || (OPPScopeConfigurableFieldsInfo != null &amp;&amp; OPPScopeConfigurableFieldsInfo.ServiceLevel != &quot;&quot;)" />
        </control>
      </control>
      <control
        code="RTF"
        id="0a355f17-ba5e-4e6c-8b1b-18592467912b"
        binding="COS_Notes">
        <placeholder
          name="CaptionOverride"
          value="Notes"
          resid="6102afbc-1d42-4e6b-89f8-3d9c19bd0d75" />
      </control>
    </control>
    <control
      code="PNL"
      id="20ed9960-1d4e-46cc-8f5b-8f052727f73e">
      <placeholder
        name="Caption"
        value="Scope estimate"
        resid="83e8fdfe-f28d-41d3-8c90-0d2b729e1e64" />
      <placeholder
        name="Margin"
        value="mt-3" />
      <placeholder
        name="Layout"
        value="grid" />
      <control
        code="BOX"
        id="67ec34cd-0d0b-4e3c-a73c-25030d332038">
        <placeholder
          name="Layout"
          value="grid" />
        <control
          code="CMB"
          id="4df7e315-807e-4262-8b30-1488c670e3d8"
          binding="COS_ScopeFrequencyUnit">
          <placeholder
            name="CaptionOverride"
            value="Frequency"
            resid="1a734c37-7c10-4dd0-9a9b-26bf2c3106b4" />
          <placeholder
            name="Columns"
            value="col-6" />
          <placeholder
            name="Required"
            value="True" />
        </control>
      </control>
      <control
        code="BOX"
        id="c5e6b3d0-f639-4832-bc01-fa59b835c168">
        <placeholder
          name="Layout"
          value="grid" />
        <control
          code="NUM"
          id="15e56337-8ada-4703-87b6-4503334a36d1"
          binding="COS_EstimatedRevenue">
          <placeholder
            name="CaptionOverride"
            value="Revenue"
            resid="d4102c32-d910-4155-88fd-6af09115801c" />
          <placeholder
            name="Columns"
            value="col-6" />
        </control>
        <control
          code="SRC"
          id="a0e8b91c-54bf-4a08-8609-acf68586c3e1"
          binding="COS_RX_NKScopeCurrency">
          <placeholder
            name="CaptionOverride"
            value="Currency"
            resid="0e439f3c-3336-4e08-84bf-8cc39a6add66" />
          <placeholder
            name="Columns"
            value="col-6" />
        </control>
      </control>
      <control
        code="BOX"
        id="0dc2dab7-0005-4749-a699-fc288a4cecc3">
        <placeholder
          name="Layout"
          value="grid" />
        <control
          code="NUM"
          id="31af4a6c-870e-429a-858f-032b638456be"
          binding="EstimatedMargin">
          <placeholder
            name="CaptionOverride"
            value="Gross margin (%)"
            resid="bddf83b5-d904-4bc7-99e2-1b16e350dff9" />
          <placeholder
            name="Columns"
            value="col-6" />
        </control>
        <control
          code="NUM"
          id="07f9b455-bca2-4193-9697-f12843a505fb"
          binding="COS_EstimatedProfit">
          <placeholder
            name="CaptionOverride"
            value="Gross profit"
            resid="54decf66-8da3-4720-910e-91cb745b4355" />
          <placeholder
            name="Columns"
            value="col-6" />
        </control>
      </control>
      <control
        code="BOX"
        id="15e7536b-2e66-4445-a036-861ff7808771">
        <placeholder
          name="Layout"
          value="grid" />
        <control
          code="MHS"
          id="711023fe-946c-4500-a3e8-47eb89fbb7e0"
          binding="COS_EstimatedVolumeMeasure">
          <placeholder
            name="CaptionOverride"
            value="Volume"
            resid="5b094458-bd2c-40eb-a692-5be7b8426a1d" />
          <placeholder
            name="Columns"
            value="col-6" />
        </control>
        <control
          code="MHS"
          id="42334603-3a9c-484b-ac58-83ed4709a65d"
          binding="COS_EstimatedWeightMeasure">
          <placeholder
            name="CaptionOverride"
            value="Weight"
            resid="f4c39997-4919-401d-8ede-e873427c69a7" />
          <placeholder
            name="Columns"
            value="col-6" />
        </control>
      </control>
      <control
        code="BOX"
        id="ff82dfca-7404-4cfd-85aa-a6e8d5157528">
        <placeholder
          name="Style"
          value="border-radius: var(--s-radius-s); border: 1px solid var(--s-neutral-border-weak-default); background: var(--s-neutral-bg-weak-default);padding: var(--s-padding-l);" />
        <placeholder
          name="Margin"
          value="mt-3" />
        <placeholder
          name="Layout"
          value="flex" />
        <placeholder
          name="FlexJustify"
          value="justify-space-between" />
        <control
          code="BOX"
          id="9d7ea6ea-b99d-4536-bf5e-441988851fe6">
          <placeholder
            name="Layout"
            value="flex" />
          <placeholder
            name="FlexDirection"
            value="flex-column" />
          <placeholder
            name="Columns"
            value="col-6 col-sm-6 col-md-6 col-lg-6 col-xl-6" />
          <control
            code="LBL"
            id="8abd6666-cc9a-4dc2-8b65-844d82a83ef1">
            <placeholder
              name="Caption"
              value="Total annual value"
              resid="be28fdb3-e81b-462e-9e29-48860e7a6bfe" />
            <placeholder
              name="Typography"
              value="title-sm-default" />
          </control>
          <control
            code="LBL"
            id="74728659-5cfa-4345-bdb5-7efef214e617">
            <placeholder
              name="Caption"
              value="Opportunity scope estimate"
              resid="faf228ab-5419-484a-a390-31b56ccb21ee" />
            <placeholder
              name="Typography"
              value="text-sm-default" />
          </control>
        </control>
        <control
          code="BOX"
          id="016ef17b-ecf7-42e6-963c-5abdc9e06393">
          <placeholder
            name="Columns"
            value="col-6 col-sm-6 col-md-6 col-lg-6 col-xl-6" />
          <placeholder
            name="Layout"
            value="flex" />
          <control
            code="BOX"
            id="9e2554ac-ff57-4320-a346-ab6210e78130">
            <placeholder
              name="Columns"
              value="col-6 col-sm-6 col-md-6 col-lg-6 col-xl-6" />
            <placeholder
              name="Layout"
              value="flex" />
            <placeholder
              name="FlexDirection"
              value="flex-column" />
            <placeholder
              name="FlexJustify"
              value="justify-end" />
            <placeholder
              name="Margin"
              value="mr-4" />
            <placeholder
              name="FlexAlign"
              value="align-end" />
            <control
              code="BOX"
              id="82E5B29D-A2FF-4C46-B1DE-08CD782D20C1">
              <placeholder
                name="FlexGrow"
                value="flex-grow-1" />
              <placeholder
                name="Style"
                value="height:100%" />
              <placeholder
                name="Padding"
                value="pb-1" />
            </control>
            <control
              code="LBL"
              id="34949460-65c2-4f4e-924a-088490684c5b">
              <placeholder
                name="Caption"
                value="calc(&quot;Revenue (&quot; + COS_RX_NKScopeCurrency+ &quot;)&quot;)" />
              <placeholder
                name="Typography"
                value="text-sm-default" />
              <placeholder
                name="Align"
                value="right" />
              <placeholder
                name="Style"
                value="height:100%" />
            </control>
            <control
              code="LBL"
              id="22e0414f-7646-4edc-93b3-ea50bbc2e06e">
              <placeholder
                name="Caption"
                value="calc(&quot;Gross profit (&quot; + COS_RX_NKScopeCurrency+ &quot;)&quot;)" />
              <placeholder
                name="Typography"
                value="text-sm-default" />
              <placeholder
                name="Align"
                value="right" />
              <placeholder
                name="Style"
                value="height:100%" />
            </control>
            <control
              code="LBL"
              id="2e5a677b-6e6f-4251-aa88-6765d914993d">
              <placeholder
                name="Caption"
                value="calc(&quot;Volume (&quot; + COS_EstimatedVolumeUQ + &quot;)&quot;)" />
              <placeholder
                name="Typography"
                value="text-sm-default" />
              <placeholder
                name="Align"
                value="right" />
              <placeholder
                name="Style"
                value="height:100%" />
            </control>
          </control>
          <control
            code="BOX"
            id="0d28e9a8-4132-4465-8bc7-3568b27e6516">
            <placeholder
              name="Columns"
              value="col-3 col-sm-3 col-md-3 col-lg-3 col-xl-3" />
            <placeholder
              name="Layout"
              value="flex" />
            <placeholder
              name="FlexDirection"
              value="flex-column" />
            <placeholder
              name="Margin"
              value="mr-4" />
            <placeholder
              name="FlexAlign"
              value="align-end" />
            <control
              code="LBL"
              id="a02cf2e7-b3bc-4724-93ac-fd2dbf43a1a5">
              <placeholder
                name="Caption"
                value="Estimate (PY)"
                resid="f04a303a-8fd1-47b8-a0b7-956b271eb8bf" />
              <placeholder
                name="Typography"
                value="text-sm-default" />
              <placeholder
                name="Align"
                value="right" />
              <placeholder
                name="Padding"
                value="pb-1" />
            </control>
            <control
              code="LBL"
              id="08b5d4c8-3d24-40bb-86f6-dfabcc5b9359">
              <placeholder
                name="Caption"
                value="calc(YearlyRevenueFormatted)" />
              <placeholder
                name="Typography"
                value="text-md-strong-num" />
              <placeholder
                name="Align"
                value="right" />
              <placeholder
                name="Style"
                value="height:100%" />
            </control>
            <control
              code="LBL"
              id="d5bbd19b-e75c-4f20-a773-473d6c39f3b1">
              <placeholder
                name="Caption"
                value="calc(YearlyProfitFormatted)" />
              <placeholder
                name="Typography"
                value="text-md-strong-num" />
              <placeholder
                name="Align"
                value="right" />
              <placeholder
                name="Style"
                value="height:100%" />
            </control>
            <control
              code="LBL"
              id="D19A0245-5A31-4925-B578-F2C2D4FD2F0D">
              <placeholder
                name="Typography"
                value="text-md-strong-num" />
              <placeholder
                name="Caption"
                value="calc(EstimatedVolumeFormatted)" />
              <placeholder
                name="Align"
                value="right" />
              <placeholder
                name="Style"
                value="height:100%" />
            </control>
          </control>
          <control
            code="BOX"
            id="0a7b18e7-8a8e-4def-9755-2fa32a95a0f8">
            <placeholder
              name="Columns"
              value="col-3 col-sm-3 col-md-3 col-lg-3 col-xl-3" />
            <placeholder
              name="Layout"
              value="flex" />
            <placeholder
              name="FlexDirection"
              value="flex-column" />
            <placeholder
              name="FlexAlign"
              value="align-end" />
            <control
              code="LBL"
              id="25713cd6-8294-40c3-aa63-e701c8ac8de5">
              <placeholder
                name="Caption"
                value="Actual (YTD)"
                resid="dbcd7f48-fe93-4934-a399-32894c3ffbd8" />
              <placeholder
                name="Typography"
                value="text-sm-default" />
              <placeholder
                name="Align"
                value="right" />
              <placeholder
                name="Padding"
                value="pb-1" />
            </control>
            <control
              code="LBL"
              id="a5f7953e-083f-4008-a1b1-efa56385922f">
              <placeholder
                name="Caption"
                value="-"
                resid="8fc78290-0173-469a-b179-8df74abf3b44" />
              <placeholder
                name="Typography"
                value="text-md-strong-num" />
              <placeholder
                name="Align"
                value="right" />
              <placeholder
                name="Style"
                value="height:100%" />
            </control>
            <control
              code="LBL"
              id="49d67f4f-f024-45b5-8f51-086ad2bc89de">
              <placeholder
                name="Caption"
                value="-"
                resid="453c2988-7ddd-4279-b195-04491984c92a" />
              <placeholder
                name="Typography"
                value="text-md-strong-num" />
              <placeholder
                name="Align"
                value="right" />
              <placeholder
                name="Style"
                value="height:100%" />
            </control>
            <control
              code="LBL"
              id="baef7647-e384-48d5-83dc-4390c5d9ca6c">
              <placeholder
                name="Caption"
                value="-"
                resid="eb84da9f-0e52-4a1e-9ee8-a6e5b44c94cf" />
              <placeholder
                name="Typography"
                value="text-md-strong-num" />
              <placeholder
                name="Align"
                value="right" />
              <placeholder
                name="Style"
                value="height:100%" />
            </control>
          </control>
        </control>
      </control>
    </control>
    <control
      code="PNL"
      id="da95bcd5-5832-42e9-ad6c-ec9ae15ab41b">
      <placeholder
        name="Caption"
        value="Effective period"
        resid="9ee0fe11-a3b4-488f-85f9-6427f8b4bb22" />
      <placeholder
        name="Layout"
        value="grid" />
      <placeholder
        name="Margin"
        value="mt-3" />
      <control
        code="CLO"
        id="7fa4dbb6-a637-41a1-8b74-1770bd3555e9">
        <placeholder
          name="Sentiment"
          value="info" />
        <placeholder
          name="Description"
          value="Currently using this opportunity's overall effective period"
          resid="8c77b53c-72d3-4cce-b7c7-1956bcae8688" />
        <placeholder
          name="VisibilityCondition"
          value="!OverrideEffectivePeriod" />
      </control>
      <control
        code="SWT"
        id="7db422f4-99bc-4ab0-a594-6c6d11d55595"
        binding="OverrideEffectivePeriod">
        <placeholder
          name="CaptionOverride"
          value="Override overall opportunity effective period"
          resid="bbc0ae51-bb54-46e5-9e23-216d2f598d66" />
      </control>
      <control
        code="DAE"
        id="03ebddb1-1cd4-4571-85fe-c3bf102016ac"
        binding="EffectiveStartDate">
        <placeholder
          name="Columns"
          value="col-md-6" />
        <placeholder
          name="IsReadOnly"
          value="True" />
        <placeholder
          name="CaptionOverride"
          value="From"
          resid="2ca750f7-08f8-45df-b605-f406e83a0dae" />
        <placeholder
          name="VisibilityCondition"
          value="!OverrideEffectivePeriod" />
      </control>
      <control
        code="DAE"
        id="2bc1aaff-9988-4041-9712-a1ab5e83d5fc"
        binding="EffectiveEndDate">
        <placeholder
          name="Columns"
          value="col-md-6" />
        <placeholder
          name="IsReadOnly"
          value="True" />
        <placeholder
          name="CaptionOverride"
          value="To"
          resid="a8080f83-3f3e-4056-a4df-c9a86a310b98" />
        <placeholder
          name="VisibilityCondition"
          value="!OverrideEffectivePeriod" />
      </control>
      <control
        code="DAE"
        id="834e2e70-b061-4c83-a1e0-adeca749dadf"
        binding="COS_EffectiveStartDate">
        <placeholder
          name="Columns"
          value="col-md-6" />
        <placeholder
          name="CaptionOverride"
          value="From"
          resid="1ea512a9-f79b-4f18-bbfe-21c3bde3f337" />
        <placeholder
          name="VisibilityCondition"
          value="OverrideEffectivePeriod" />
      </control>
      <control
        code="DAE"
        id="9bfed933-4bd7-442c-a2c6-8f40abf1f487"
        binding="COS_EffectiveEndDate">
        <placeholder
          name="Columns"
          value="col-md-6" />
        <placeholder
          name="CaptionOverride"
          value="To"
          resid="eec63ecd-7bbb-4415-9e8e-e4ccb7d2d646" />
        <placeholder
          name="VisibilityCondition"
          value="OverrideEffectivePeriod" />
      </control>
    </control>
  </form>
