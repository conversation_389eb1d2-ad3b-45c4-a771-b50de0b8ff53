#transformationVersion: 70.0
#
VZ_PK: e22da07108c84525a02ad7dc669ea663
VZ_ConfigurationKey: e22da071-08c8-4525-a02a-d7dc669ea663
VZ_FormID: CYP Edit Ad Hoc Service
VZ_Caption:
  resKey: VZ_Caption|e22da07108c84525a02ad7dc669ea663
  text: Edit Ad Hoc Service
VZ_FormFactor: DSK
VZ_EntityType: ICYDAdHocService
VZ_FormType: FRM
VZ_RequiresLogin: true
VZ_MaterialDesign: true
VZ_Dependencies: <dependencies xmlns="http://cargowise.com/glow/2014/11/14/dependencies.xsd" />
VZ_StatusBindingPath: ServiceStatus
VZ_EntityStatusDisplayMode: ReadOnly
VZ_FormData: >-
  <form
    id="816a858e-8c8c-40f4-976f-31729f13b7d5" xmlns="http://wisetechglobal.com/glow/2017/05/26/form.xsd">
    <placeholder
      name="Layout"
      value="fill" />
    <control
      code="PNL"
      id="1e56148c-b17d-41a1-9d88-0ca66a47fae1"
      binding="">
      <placeholder
        name="Caption"
        value="Service order details"
        resid="0a396786-5b79-4ff2-af37-9f93d41a5047" />
      <placeholder
        name="Layout"
        value="grid" />
      <placeholder
        name="Margin"
        value="mb-3" />
      <control
        code="ADD"
        id="4ad689a1-1093-4a0a-ba3f-fc4a2cad791a"
        binding="ServiceOrder.AddressBKD.E2_OA_Address">
        <placeholder
          name="CaptionOverride"
          value="Client"
          resid="69a1dbe6-208f-441e-b7a4-577111a3e609" />
        <placeholder
          name="Required"
          value="True" />
        <placeholder
          name="Columns"
          value="col-6" />
      </control>
      <control
        code="TXT"
        id="d1639982-bc65-40f7-a68d-2f5df0d72e79"
        binding="ServiceOrder.YAO_ClientReference">
        <placeholder
          name="Columns"
          value="col-6" />
      </control>
      <control
        code="DAE"
        id="8f954503-2ba5-42a0-a218-2805f7fe057b"
        binding="ServiceOrder.YAO_BillingDate">
        <placeholder
          name="Columns"
          value="col-6" />
        <placeholder
          name="IsReadOnly"
          value="True" />
      </control>
      <control
        code="DTE"
        id="f87169bb-9c30-4ee6-ad9a-d61e11169109"
        binding="ServiceOrder.YAO_SystemCreateTimeUtc">
        <placeholder
          name="Columns"
          value="col-6" />
        <placeholder
          name="IsReadOnly"
          value="True" />
        <placeholder
          name="CaptionOverride"
          value="Created date/time"
          resid="887c2dc7-2e16-4508-941a-af207c81057b" />
      </control>
      <control
        code="TXT"
        id="911c311e-2e1c-4e51-b04f-42032f88c77d"
        binding="ServiceOrder.CreatedByStaff.GS_Code">
        <placeholder
          name="Columns"
          value="col-6" />
        <placeholder
          name="IsReadOnly"
          value="True" />
        <placeholder
          name="CaptionOverride"
          value="Create user code"
          resid="2ab536a0-afea-4e62-a45b-7003efaa00a0" />
      </control>
      <control
        code="TXT"
        id="44d69e95-4b5e-4322-a9a2-de6ab5198381"
        binding="ServiceOrder.CreatedByStaff.GS_FullName">
        <placeholder
          name="Columns"
          value="col-6" />
        <placeholder
          name="IsReadOnly"
          value="True" />
        <placeholder
          name="CaptionOverride"
          value="Create user name"
          resid="7c6f914c-3d49-4d0b-8936-168618392684" />
      </control>
    </control>
    <control
      code="PNL"
      id="e83c0449-6635-4ece-904e-6f29255fc322"
      binding="">
      <placeholder
        name="FillAvailable"
        value="True" />
      <placeholder
        name="Layout"
        value="grid" />
      <placeholder
        name="Caption"
        value="Service line details"
        resid="ed9cd80f-939f-4775-a292-c0d21a54965a" />
      <control
        code="BOX"
        id="7b9f8737-b78d-40a4-8ff3-1f921ed449fb">
        <placeholder
          name="Columns"
          value="col-6" />
        <placeholder
          name="FlexAlign"
          value="align-end" />
        <placeholder
          name="FlexWrap"
          value="flex-wrap" />
        <control
          code="CMB"
          id="99b4a844-89f4-4ac9-ade9-49b86abda37c"
          binding="ServiceType">
          <placeholder
            name="Columns"
            value="col-6" />
        </control>
      </control>
      <control
        code="SRC"
        id="7f483db3-fb0f-4d89-906e-4263685c349f"
        binding="YAS_YUS_YardUnitState">
        <placeholder
          name="Columns"
          value="col-6" />
        <placeholder
          name="CaptionOverride"
          value="Unit number"
          resid="840bb0a1-de77-4795-aaad-2da9ac2f8b8c" />
        <placeholder
          name="VisibilityCondition"
          value="!YardUnitState.YardUnitIconVisibility" />
      </control>
      <control
        code="BOX"
        id="e4d4f967-70fb-4fbd-819e-2bb11e16ee60">
        <placeholder
          name="Columns"
          value="col-6" />
        <placeholder
          name="FlexAlign"
          value="align-end" />
        <placeholder
          name="FlexWrap"
          value="flex-nowrap" />
        <placeholder
          name="Margin"
          value="ma-1" />
        <placeholder
          name="Layout"
          value="flex" />
        <placeholder
          name="VisibilityCondition"
          value="YardUnitState.YardUnitIconVisibility" />
        <control
          code="SRC"
          id="57ea73a4-dfc3-4c06-9f20-7ccf51e21736"
          binding="YAS_YUS_YardUnitState">
          <placeholder
            name="Columns"
            value="col-5" />
          <placeholder
            name="CaptionOverride"
            value="Unit number"
            resid="840bb0a1-de77-4795-aaad-2da9ac2f8b8c" />
          <placeholder
            name="Padding"
            value="pr-1" />
        </control>
        <control
          code="IBT"
          id="dcc1c107-742e-479a-bad7-e8bcc2b478a0">
          <placeholder
            name="Icon"
            value="s-icon-new-window" />
          <placeholder
            name="Transition"
            value="True" />
          <placeholder
            name="Style"
            value="height:31px;width:32px;min-width:32px" />
        </control>
      </control>
      <control
        code="TXT"
        id="e2a0fa2e-d60a-4493-886f-488c9a033b2b"
        binding="YardUnitState.ReceiveTransportationUnitId">
        <placeholder
          name="Columns"
          value="col-6" />
        <placeholder
          name="IsReadOnly"
          value="True" />
        <placeholder
          name="CaptionOverride"
          value="Yard in TPU ID"
          resid="df256971-dff3-4198-9442-9b0cf96eaa6c" />
      </control>
      <control
        code="TXT"
        id="44aeb0c5-01fd-4f3f-8ba6-7316df75ad98"
        binding="YardUnitState.TypeSize">
        <placeholder
          name="Columns"
          value="col-6" />
        <placeholder
          name="IsReadOnly"
          value="True" />
      </control>
      <control
        code="NUM"
        id="95c00a7d-5440-4107-9ab5-453a77e2e656"
        binding="JobService.ES_ServiceCount">
        <placeholder
          name="Columns"
          value="col-6" />
        <placeholder
          name="CaptionOverride"
          value="Service count"
          resid="5f740e1d-9dd8-4e3b-bf32-a762818ab567" />
      </control>
      <control
        code="SRC"
        id="bdcc8199-8d72-4b54-8464-34c8ea94a476"
        binding="JobService.ES_OH_Contractor">
        <placeholder
          name="Columns"
          value="col-6" />
      </control>
      <control
        code="DTE"
        id="babfec57-6e44-48f7-91d4-e322a693df69"
        binding="JobService.ES_BookedDateTimeOffset">
        <placeholder
          name="Columns"
          value="col-6" />
        <placeholder
          name="CaptionOverride"
          value="Start date"
          resid="938ca388-a5c6-4467-9bb0-ab34a9d0fe20" />
      </control>
      <control
        code="DTE"
        id="472ec921-39ae-43c6-9317-f38935f8ba94"
        binding="JobService.ES_CompletedDateTimeOffset">
        <placeholder
          name="Columns"
          value="col-6" />
        <placeholder
          name="CaptionOverride"
          value="Completion Date"
          resid="7480b780-f3cd-4ec3-a8f6-dca9b56051b1" />
      </control>
      <control
        code="TXT"
        id="0cee81b0-b61a-48df-aa3f-687b1916dbc3"
        binding="JobService.ES_ServiceNote" />
    </control>
  </form>
