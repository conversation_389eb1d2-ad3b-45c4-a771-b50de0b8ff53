#transformationVersion: 70.0
#
VZ_PK: ba57b6273a3b4461b283e6a8ef564178
VZ_ConfigurationKey: ba57b627-3a3b-4461-b283-e6a8ef564178
VZ_FormID: ETL - VDV3 - Destination Depot - Home Page
VZ_Caption:
  resKey: VZ_Caption|ba57b627-3a3b-4461-b283-e6a8ef564178
  text: Ecommerce Destination Depot Home Page
VZ_FormFactor: DSK
VZ_FormType: PAG
VZ_RequiresLogin: true
VZ_Dependencies: <dependencies xmlns="http://cargowise.com/glow/2014/11/14/dependencies.xsd" />
VZ_FormData: >-
  <form
    id="200f9c95-7e4f-40bb-bc85-c4cf9eff63e4" xmlns="http://wisetechglobal.com/glow/2017/05/26/form.xsd">
    <placeholder
      name="BackgroundImagePk"
      value="" />
    <placeholder
      name="BackgroundSize"
      value="Auto" />
    <placeholder
      name="BackgroundImageOverride"
      value="" />
    <placeholder
      name="FormLayout"
      value="Splitview" />
    <placeholder
      name="ExpandPaths"
      value="" />
    <placeholder
      name="ScanInputHandling"
      value="NON" />
    <placeholder
      name="RemovePadding"
      value="false" />
    <placeholder
      name="TurnOnPortableOneToOneDesignMode"
      value="false" />
    <placeholder
      name="ContainsDefaultButton"
      value="False" />
    <control
      code="GRP"
      id="3fd96b5c-aab8-4159-9e01-64574e2b6a7b"
      width="16.2"
      height="12.9">
      <placeholder
        name="CanBeHidden"
        value="True" />
      <placeholder
        name="VisibilityCondition"
        value="" />
      <placeholder
        name="Header"
        value="New Section"
        resid="d673e719-613d-46ee-9c84-712b6507012b" />
      <placeholder
        name="HeaderAlignment"
        value="Left" />
      <placeholder
        name="HeaderDisplayMode"
        value="Hidden" />
      <placeholder
        name="BackgroundImagePk"
        value="" />
      <placeholder
        name="BackgroundImageOverride"
        value="" />
      <control
        code="TIL"
        id="579bba95-f1c7-49bd-8818-edfc06abc7f1"
        left="2"
        top="0"
        width="4"
        height="4">
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="Size"
          value="Large" />
        <placeholder
          name="Color"
          value="Default" />
        <placeholder
          name="DisplayMode"
          value="TextAndIcon" />
        <placeholder
          name="Text"
          value="Scan to Receive Shipment"
          resid="c64e9a50-3635-4ba1-ad0e-b3a70e631300" />
        <placeholder
          name="Image"
          value="2c348de953fe4e8db9967317a1a942c4" />
        <placeholder
          name="ImageOverride"
          value="" />
        <placeholder
          name="Description"
          value="Scan Items from a HVL Shipment at the Destination Depot."
          resid="a844da64-f56c-4aea-bcb3-19f06336a289" />
        <placeholder
          name="Behaviour"
          value="Page" />
        <placeholder
          name="PagePK"
          value="8c36363ac8a541969bf0b0f8ed7bfd61" />
        <placeholder
          name="Url"
          value="" />
        <placeholder
          name="FormFlow"
          value="" />
      </control>
      <control
        code="TIL"
        id="da6bc16e-0e15-4a95-9d2d-e9f8173fe846"
        left="6"
        top="0"
        width="4"
        height="4">
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="Size"
          value="Large" />
        <placeholder
          name="Color"
          value="Default" />
        <placeholder
          name="DisplayMode"
          value="TextAndIcon" />
        <placeholder
          name="Text"
          value="Scan to Receive Consol"
          resid="120e11ed-b9df-428c-a064-d7ed1f36ba35" />
        <placeholder
          name="Image"
          value="9d3da3b738a34f6782deb9029a85cb01" />
        <placeholder
          name="ImageOverride"
          value="" />
        <placeholder
          name="Description"
          value="Scan Items from a Consol at the Destination Depot."
          resid="a1297a64-481e-4e27-b606-fdac8d876d2f" />
        <placeholder
          name="Behaviour"
          value="Page" />
        <placeholder
          name="PagePK"
          value="8c7c3344d28b4def9060b51894d52560" />
        <placeholder
          name="Url"
          value="" />
        <placeholder
          name="FormFlow"
          value="" />
      </control>
      <control
        code="TIL"
        id="287732fb-ce04-4aac-b5cc-c8b65629f813"
        left="10"
        top="0"
        width="4"
        height="4">
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="Size"
          value="Large" />
        <placeholder
          name="Color"
          value="Default" />
        <placeholder
          name="DisplayMode"
          value="TextAndIcon" />
        <placeholder
          name="Text"
          value="Manage Exception Items"
          resid="ce5bf0ae-ed7f-4b2f-be00-c6fc8bdb9d8f" />
        <placeholder
          name="Image"
          value="e69bfd87530d4de887276038ceae77c8" />
        <placeholder
          name="ImageOverride"
          value="" />
        <placeholder
          name="Description"
          value="Scan and manage exception Items at the Destination Depot."
          resid="d329bbc1-ea93-4d55-9ca3-0f8b9032a8f7" />
        <placeholder
          name="Behaviour"
          value="Page" />
        <placeholder
          name="PagePK"
          value="b99772b664834ed4977ed7fe5542f04c" />
        <placeholder
          name="Url"
          value="" />
        <placeholder
          name="FormFlow"
          value="" />
      </control>
      <control
        code="TIL"
        id="631610ba-496c-4225-ace4-96ea3a999606"
        left="2"
        top="4"
        width="4"
        height="4">
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="Size"
          value="Large" />
        <placeholder
          name="Color"
          value="Default" />
        <placeholder
          name="DisplayMode"
          value="TextAndIcon" />
        <placeholder
          name="Text"
          value="Reports"
          resid="5503637e-3d85-4a27-b5cc-1e62e6ed9e03" />
        <placeholder
          name="Image"
          value="f21815e737bc4d6089f88f6fe9e8a0f5" />
        <placeholder
          name="ImageOverride"
          value="" />
        <placeholder
          name="Description"
          value="View reports on Consols, Shipments, Consignments and Exceptions Items."
          resid="da83d228-e47b-4352-a764-666cd3848da5" />
        <placeholder
          name="Behaviour"
          value="Page" />
        <placeholder
          name="PagePK"
          value="1c78b7ebc37f4f7c9973ddd761ee8667" />
        <placeholder
          name="Url"
          value="" />
        <placeholder
          name="FormFlow"
          value="" />
      </control>
      <control
        code="TIL"
        id="ab1fd7f6-224e-4757-908a-cbeeba69dd0f"
        left="6"
        top="4"
        width="4"
        height="4">
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="Size"
          value="Large" />
        <placeholder
          name="Color"
          value="Default" />
        <placeholder
          name="DisplayMode"
          value="TextAndIcon" />
        <placeholder
          name="Text"
          value="Intercept"
          resid="04fa4efb-10e5-4a65-b2b7-8a354142b3b7" />
        <placeholder
          name="Image"
          value="fdd12491e80144d0a9ebfdd03145f625" />
        <placeholder
          name="ImageOverride"
          value="" />
        <placeholder
          name="Description"
          value="Intercept an Item for Special Handling."
          resid="7d1b0ef5-fbe4-4d4f-ac2a-e564cac3bc3c" />
        <placeholder
          name="Behaviour"
          value="Page" />
        <placeholder
          name="PagePK"
          value="f8850bac4c234f2daaf8fa8be7bf6317" />
        <placeholder
          name="Url"
          value="" />
        <placeholder
          name="FormFlow"
          value="" />
      </control>
      <control
        code="TIL"
        id="e3489b65-9259-41cb-9c0b-d10bd0264df7"
        left="10"
        top="4"
        width="4"
        height="4">
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="Size"
          value="Large" />
        <placeholder
          name="Color"
          value="Default" />
        <placeholder
          name="DisplayMode"
          value="TextAndIcon" />
        <placeholder
          name="Text"
          value="Tracking/Reports"
          resid="692fb864-a04f-4e09-b3d2-61288b2a2145" />
        <placeholder
          name="Image"
          value="fbcf0fdf52074ddf97ec2ce94d6f47a1" />
        <placeholder
          name="ImageOverride"
          value="" />
        <placeholder
          name="Description"
          value="Track Shipments, Booking Headers, Consignments or Items."
          resid="900bed59-f2c4-42ac-a00b-67acfa5b2262" />
        <placeholder
          name="Behaviour"
          value="Page" />
        <placeholder
          name="PagePK"
          value="6304763e5b644c069fb98fe353716da5" />
        <placeholder
          name="Url"
          value="" />
        <placeholder
          name="FormFlow"
          value="" />
      </control>
      <control
        code="TRC"
        id="64b800d3-5572-4ef4-9161-31ccafcc943b"
        left="2.1"
        top="8.4"
        width="11.9"
        height="4">
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="VisibilityCondition"
          value="" />
      </control>
    </control>
  </form>
