#transformationVersion: 70.0
#
VZ_PK: 79cf3e9f175a4a67af77918cc5984c3d
VZ_ConfigurationKey: 79cf3e9f-175a-4a67-af77-918cc5984c3d
VZ_FormID: ETL - VDV3 - Shipper Portal G1 - Shipment Tracking
VZ_Caption:
  resKey: VZ_Caption|79cf3e9f-175a-4a67-af77-918cc5984c3d
  text: Shipment Tracking
VZ_FormFactor: DSK
VZ_FormType: PAG
VZ_RequiresLogin: true
VZ_Dependencies: <dependencies xmlns="http://cargowise.com/glow/2014/11/14/dependencies.xsd" />
VZ_FormData: >-
  <form
    id="cc0da138-0797-4cd2-b813-d721a35753e5" xmlns="http://wisetechglobal.com/glow/2017/05/26/form.xsd">
    <placeholder
      name="BackgroundImagePk"
      value="" />
    <placeholder
      name="BackgroundSize"
      value="Auto" />
    <placeholder
      name="BackgroundImageOverride"
      value="" />
    <placeholder
      name="FormLayout"
      value="Splitview" />
    <placeholder
      name="ExpandPaths"
      value="" />
    <placeholder
      name="ScanInputHandling"
      value="NON" />
    <placeholder
      name="RemovePadding"
      value="false" />
    <placeholder
      name="TurnOnPortableOneToOneDesignMode"
      value="false" />
    <placeholder
      name="ContainsDefaultButton"
      value="False" />
    <control
      code="GRP"
      id="448f065b-f325-4d3e-8c58-43127297d4f4"
      left="0"
      top="0"
      width="6"
      height="4">
      <placeholder
        name="CanBeHidden"
        value="True" />
      <placeholder
        name="VisibilityCondition"
        value="" />
      <placeholder
        name="Header"
        value="New Section"
        resid="4b43b904-c7c1-4fa1-bd89-a8e940adc78a" />
      <placeholder
        name="HeaderAlignment"
        value="Left" />
      <placeholder
        name="HeaderDisplayMode"
        value="Hidden" />
      <placeholder
        name="BackgroundImagePk"
        value="" />
      <placeholder
        name="BackgroundImageOverride"
        value="" />
      <control
        code="TIL"
        id="ab426add-6984-4655-abb8-7b53c647d63f"
        left="0"
        top="0"
        width="2"
        height="2">
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="Size"
          value="Standard" />
        <placeholder
          name="Color"
          value="Primary" />
        <placeholder
          name="DisplayMode"
          value="TextAndIcon" />
        <placeholder
          name="Text"
          value="Shipments"
          resid="d4bd4857-9211-40f6-8e3b-c8b656c930d0" />
        <placeholder
          name="Image"
          value="d4c02b187e9d4e37be59b173a7caa29a" />
        <placeholder
          name="ImageOverride"
          value="" />
        <placeholder
          name="Description"
          value="" />
        <placeholder
          name="Behaviour"
          value="Page" />
        <placeholder
          name="PagePK"
          value="79cf3e9f175a4a67af77918cc5984c3d" />
        <placeholder
          name="Url"
          value="" />
        <placeholder
          name="FormFlow"
          value="" />
      </control>
      <control
        code="TIL"
        id="a8ee33e9-b91f-46de-875f-e06e3d239f13"
        left="2"
        top="0"
        width="2"
        height="2">
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="Size"
          value="Standard" />
        <placeholder
          name="Color"
          value="Default" />
        <placeholder
          name="DisplayMode"
          value="TextAndIcon" />
        <placeholder
          name="Text"
          value="Consignments"
          resid="0087bed9-e971-497c-a71e-c71d92aae5b8" />
        <placeholder
          name="Image"
          value="2c348de953fe4e8db9967317a1a942c4" />
        <placeholder
          name="ImageOverride"
          value="" />
        <placeholder
          name="Description"
          value="" />
        <placeholder
          name="Behaviour"
          value="Page" />
        <placeholder
          name="PagePK"
          value="3c95f54513ea4829aebfe9ffb9b4c647" />
        <placeholder
          name="Url"
          value="" />
        <placeholder
          name="FormFlow"
          value="" />
      </control>
      <control
        code="TIL"
        id="08845d9f-55a0-4d76-9577-47b4404758fe"
        left="4"
        top="0"
        width="2"
        height="2">
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="Size"
          value="Standard" />
        <placeholder
          name="Color"
          value="Default" />
        <placeholder
          name="DisplayMode"
          value="TextAndIcon" />
        <placeholder
          name="Text"
          value="Headers"
          resid="8c854a14-0dbe-4665-84dc-49f574c7d948" />
        <placeholder
          name="Image"
          value="05e1a4771ad542aeadfef74283eec66b" />
        <placeholder
          name="ImageOverride"
          value="" />
        <placeholder
          name="Description"
          value="" />
        <placeholder
          name="Behaviour"
          value="Page" />
        <placeholder
          name="PagePK"
          value="7923c78de01e49f9b4488e0a5ba38d92" />
        <placeholder
          name="Url"
          value="" />
        <placeholder
          name="FormFlow"
          value="" />
      </control>
      <control
        code="TIL"
        id="d7eab1cf-fc7a-4638-b0c3-06e3e2a938f9"
        left="0"
        top="2"
        width="2"
        height="2">
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="Size"
          value="Standard" />
        <placeholder
          name="Color"
          value="Default" />
        <placeholder
          name="DisplayMode"
          value="TextAndIcon" />
        <placeholder
          name="Text"
          value="Invoices"
          resid="839a6a32-f9ef-427b-a71c-97b5a0a23860" />
        <placeholder
          name="Image"
          value="b928eff5f6c743cf84380b025cc233a0" />
        <placeholder
          name="ImageOverride"
          value="" />
        <placeholder
          name="Description"
          value="Invoices"
          resid="b48c10d4-f221-4b22-8b9b-521bd4b3cb66" />
        <placeholder
          name="Behaviour"
          value="Page" />
        <placeholder
          name="PagePK"
          value="8e5a53552f56494195b2446ff05deb26" />
        <placeholder
          name="Url"
          value="" />
        <placeholder
          name="FormFlow"
          value="" />
      </control>
      <control
        code="TIL"
        id="8d1d97b4-d598-473b-9cdb-2454ce5bda0a"
        left="2"
        top="2"
        width="2"
        height="2">
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="Size"
          value="Standard" />
        <placeholder
          name="Color"
          value="Default" />
        <placeholder
          name="DisplayMode"
          value="TextAndIcon" />
        <placeholder
          name="Text"
          value="Reports"
          resid="87e6f0c3-6a64-443a-9fc0-7a726fb638e5" />
        <placeholder
          name="Image"
          value="fbcf0fdf52074ddf97ec2ce94d6f47a1" />
        <placeholder
          name="ImageOverride"
          value="" />
        <placeholder
          name="Description"
          value="" />
        <placeholder
          name="Behaviour"
          value="Page" />
        <placeholder
          name="PagePK"
          value="07990593823a43f0a3f8dcdd9f47da6c" />
        <placeholder
          name="Url"
          value="" />
        <placeholder
          name="FormFlow"
          value="" />
      </control>
    </control>
    <control
      code="SRL"
      id="b122f75e-2cbe-4c8a-abfe-6c74d191446b"
      left="7"
      top="0"
      right="0"
      bottom="0">
      <placeholder
        name="CanBeHidden"
        value="True" />
      <placeholder
        name="IsInitialFocus"
        value="False" />
      <placeholder
        name="CaptionOverride"
        value="Shipment Tracking Results"
        resid="94d547dc-876b-41ef-8e0e-e35dc2a5abd4" />
      <placeholder
        name="VisibilityCondition"
        value="" />
      <placeholder
        name="HeaderAlignment"
        value="Left" />
      <placeholder
        name="HeaderDisplayMode"
        value="Visible" />
      <placeholder
        name="FilterType"
        value="Global" />
      <placeholder
        name="EntityType"
        value="IJobShipment" />
      <placeholder
        name="ItemDisplayPath"
        value="" />
      <placeholder
        name="DisabledGridRowActions"
        value="" />
      <placeholder
        name="DefaultFilter">
        <xml>
          <ArrayOfFilterGroup xmlns:a="http://schemas.microsoft.com/2003/10/Serialization/Arrays" xmlns:i="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://cargowise.com/glow/2014/07/16/filters.xsd">
            <FilterGroup>
              <Filters>
                <Filter>
                  <FilterType>StringFilter</FilterType>
                  <Operation>StartsWith</Operation>
                  <PropertyPath>SHIPMENTID</PropertyPath>
                  <Values />
                </Filter>
                <Filter>
                  <FilterType>StringFilter</FilterType>
                  <Operation>StartsWith</Operation>
                  <PropertyPath>MASTERBILLNUMBER</PropertyPath>
                  <Values />
                </Filter>
                <Filter>
                  <FilterType>StringFilter</FilterType>
                  <Operation>StartsWith</Operation>
                  <PropertyPath>HOUSEBILLNUMBER</PropertyPath>
                  <Values />
                </Filter>
              </Filters>
              <IsImplicit>false</IsImplicit>
            </FilterGroup>
            <FilterGroup>
              <Filters>
                <Filter>
                  <FilterType>StringFilter</FilterType>
                  <Operation>Is</Operation>
                  <PropertyPath>SHIPMENTTYPE</PropertyPath>
                  <Values>
                    <a:string>HVL</a:string>
                  </Values>
                </Filter>
              </Filters>
              <IsImplicit>true</IsImplicit>
            </FilterGroup>
          </ArrayOfFilterGroup>
        </xml>
      </placeholder>
      <placeholder
        name="DefaultFilterPropertyPath"
        value="" />
      <placeholder
        name="FieldConfiguration">
        <xml>
          <fields xmlns="">
            <field
              path="SHIPMENTID"
              width="100"
              mode="Default" />
            <field
              path="MASTERBILLNUMBER"
              width="100"
              mode="Default" />
            <field
              path="VOYAGEFLIGHT"
              width="100"
              mode="Default" />
            <field
              path="HOUSEBILLNUMBER"
              width="100"
              mode="Default" />
            <field
              path="ORIGINPORT"
              width="100"
              mode="Default" />
            <field
              path="DESTINATIONPORT"
              width="100"
              mode="Default" />
            <field
              path="DEPARTUREDATELOCAL"
              width="100"
              mode="Default" />
            <field
              path="ARRIVALDATELOCAL"
              width="100"
              mode="Default" />
            <field
              path="COUNTSCANNEDALL"
              width="100"
              mode="Default" />
            <field
              path="COUNTSCANNEDCLEARED"
              width="100"
              mode="Default" />
            <field
              path="COUNTSCANNEDHELD"
              width="100"
              mode="Default" />
            <field
              path="COUNTSURPLUSITEMS"
              width="100"
              mode="Default" />
            <field
              path="COUNTSHORTITEMS"
              width="100"
              mode="Default" />
            <field
              path="ISCANCELLED"
              width="100"
              mode="Optional" />
            <field
              path="INCOTERMS"
              width="100"
              mode="Optional" />
            <field
              path="ETALOCAL"
              width="100"
              mode="Optional" />
            <field
              path="ETDLOCAL"
              width="100"
              mode="Optional" />
            <field
              path="ONBOARDDATE"
              width="100"
              mode="Optional" />
            <field
              path="TRANSPORTMODE"
              width="100"
              mode="Optional" />
            <field
              path="WEIGHT"
              width="100"
              mode="Optional" />
            <field
              path="VOLUME"
              width="100"
              mode="Optional" />
            <field
              path="INTERIMRECEIPTDATE"
              width="100"
              mode="Optional" />
            <field
              path="ISSUEDATE"
              width="100"
              mode="Optional" />
            <field
              path="SHIPMENTSTATUS"
              width="100"
              mode="Optional" />
            <field
              path="TOTALPACKAGES"
              width="100"
              mode="Optional" />
            <field
              path="SHIPPERREFERENCE"
              width="100"
              mode="Optional" />
            <field
              path="CONTAINERMODE"
              width="100"
              mode="Optional" />
            <field
              path="SERVICELEVEL"
              width="100"
              mode="Optional" />
            <field
              path="GOODSDESCRIPTION"
              width="100"
              mode="Optional" />
            <field
              path="ADDITIONALTERMS"
              width="100"
              mode="Optional" />
            <field
              path="RELEASETYPE"
              width="100"
              mode="Optional" />
            <field
              path="SHIPPEDONBOARD"
              width="100"
              mode="Optional" />
            <field
              path="CHARGESAPPLY"
              width="100"
              mode="Optional" />
            <field
              path="ESTIMATEDPICKUP"
              width="100"
              mode="Optional" />
            <field
              path="ESTIMATEDDELIVERYTIME"
              width="100"
              mode="Optional" />
            <field
              path="GOODSPICKEDUP"
              width="100"
              mode="Optional" />
            <field
              path="GOODSDELIVERED"
              width="100"
              mode="Optional" />
            <field
              path="CONSIGNORNAME"
              width="100"
              mode="Optional" />
            <field
              path="CONSIGNEENAME"
              width="100"
              mode="Optional" />
            <field
              path="PICKUPADDRESS"
              width="100"
              mode="Optional" />
            <field
              path="DELIVERYADDRESS"
              width="100"
              mode="Optional" />
            <field
              path="VESSELCODE"
              width="100"
              mode="Optional" />
            <field
              path="COUNTALL"
              width="100"
              mode="Optional" />
            <field
              path="COUNTCLEARED"
              width="100"
              mode="Optional" />
            <field
              path="COUNTHELD"
              width="100"
              mode="Optional" />
            <field
              path="COUNTNONENOTREPORTED"
              width="100"
              mode="Optional" />
            <field
              path="COUNTSCANNEDNONENOTREPORTED"
              width="100"
              mode="Optional" />
            <field
              path="CONSOLREFERENCE"
              width="100"
              mode="Optional" />
            <field
              path="CTO"
              width="100"
              mode="Optional" />
            <field
              path="CFS"
              width="100"
              mode="Optional" />
            <field
              path="CARRIER"
              width="100"
              mode="Optional" />
            <field
              path="OUTERPACKS"
              width="100"
              mode="Optional" />
            <field
              path="FIRSTLEGATD"
              width="100"
              mode="Optional" />
            <field
              path="LASTLEGATA"
              width="100"
              mode="Optional" />
            <field
              path="CTOAVAILABLEDATE"
              width="100"
              mode="Optional" />
            <field
              path="CONSOLCFSAVAILABILITY"
              width="100"
              mode="Optional" />
            <field
              path="SHIPMENTTYPE"
              width="100"
              mode="Optional" />
            <field
              path="SHIPMENTCFSAVAILABILITY"
              width="100"
              mode="Optional" />
            <field
              path="STORAGECOMMENCES"
              width="100"
              mode="Optional" />
            <field
              path="ROUTINGSTATUS"
              width="100"
              mode="Optional" />
            <field
              path="ORIGINPORTIATA"
              width="100"
              mode="Optional" />
            <field
              path="ORIGINPORTCOUNTRY"
              width="100"
              mode="Optional" />
            <field
              path="DESTINATIONPORTIATA"
              width="100"
              mode="Optional" />
            <field
              path="DESTINATIONPORTCOUNTRY"
              width="100"
              mode="Optional" />
            <field
              path="ENTRYNUMBER"
              width="100"
              mode="Optional" />
            <field
              path="INNERPACKAGES"
              width="100"
              mode="Optional" />
            <field
              path="CTOAVAILABILITY"
              width="100"
              mode="Optional" />
            <field
              path="FLIGHTATA"
              width="100"
              mode="Optional" />
            <field
              path="FLIGHTETA"
              width="100"
              mode="Optional" />
            <field
              path="OUTTURNDATE"
              width="100"
              mode="Optional" />
            <field
              path="LASTCOMPLETEDMILESTONE"
              width="100"
              mode="Optional" />
            <field
              path="BOOKINGDATE"
              width="100"
              mode="Optional" />
            <field
              path="CREATETIME"
              width="100"
              mode="Optional" />
            <field
              path="LASTEDITTIME"
              width="100"
              mode="Optional" />
            <field
              path="CONSOLID"
              width="100"
              mode="Optional" />
          </fields>
        </xml>
      </placeholder>
      <placeholder
        name="DefaultSortFields"
        value="" />
      <placeholder
        name="DefaultGroupFields"
        value="" />
      <placeholder
        name="EnableGrouping"
        value="False" />
      <placeholder
        name="AutoRefreshInterval"
        value="0" />
      <placeholder
        name="AllowMultiSelect"
        value="True" />
      <placeholder
        name="ShowWorkflowLabel"
        value="True" />
      <placeholder
        name="RowActionsMode"
        value="RowAndMenu" />
      <placeholder
        name="HideGridActions"
        value="False" />
      <placeholder
        name="MinimizeFilters"
        value="True" />
      <placeholder
        name="WorkflowOpenInNewWindow"
        value="False" />
      <placeholder
        name="SearchOnInit"
        value="True" />
      <placeholder
        name="ShowQuickSearch"
        value="False" />
      <placeholder
        name="QuickSearchCaption"
        value="" />
      <placeholder
        name="ValueField"
        value="" />
      <placeholder
        name="Category"
        value="Trackable" />
      <placeholder
        name="ActionMenuItems"
        value="" />
      <placeholder
        name="EditFormFlow"
        value="" />
      <placeholder
        name="ResetFilterOnInit"
        value="False" />
      <placeholder
        name="FilterDisplayMode"
        value="InlineAndPopup" />
    </control>
    <control
      code="GRP"
      id="83d6b0f8-eb72-4611-a27f-e6e71d4da8f7"
      left="0"
      top="4"
      width="6"
      height="5">
      <placeholder
        name="CanBeHidden"
        value="True" />
      <placeholder
        name="VisibilityCondition"
        value="" />
      <placeholder
        name="Header"
        value="Tracking"
        resid="73bc895c-e318-40cc-8c38-218d9996209a" />
      <placeholder
        name="HeaderAlignment"
        value="Left" />
      <placeholder
        name="HeaderDisplayMode"
        value="Visible" />
      <placeholder
        name="BackgroundImagePk"
        value="" />
      <placeholder
        name="BackgroundImageOverride"
        value="" />
      <control
        code="TRC"
        id="dfcd740c-8e7f-4a6e-884c-8dd5e4e8df32"
        left="0"
        top="0"
        width="6"
        height="4">
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="VisibilityCondition"
          value="" />
      </control>
    </control>
    <control
      code="FAV"
      id="7d132d0a-d2d4-4fd3-ab8c-a114cad89004"
      left="0"
      top="9"
      width="6"
      bottom="2">
      <placeholder
        name="CanBeHidden"
        value="True" />
      <placeholder
        name="VisibilityCondition"
        value="" />
      <placeholder
        name="EntityType"
        value="" />
      <placeholder
        name="HeaderDisplayMode"
        value="Visible" />
      <placeholder
        name="ShowFavorites"
        value="True" />
      <placeholder
        name="ShowRecents"
        value="True" />
    </control>
  </form>
