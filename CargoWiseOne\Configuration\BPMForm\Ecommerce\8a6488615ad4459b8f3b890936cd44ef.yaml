#transformationVersion: 70.0
#
VZ_PK: 8a6488615ad4459b8f3b890936cd44ef
VZ_ConfigurationKey: 8a648861-5ad4-459b-8f3b-890936cd44ef
VZ_FormID: ETL - VDV3 - Destination Depot - Allocate Item to Stand Alone Outer Package
VZ_Caption:
  resKey: VZ_Caption|8a648861-5ad4-459b-8f3b-890936cd44ef
  text: Allocate Item to Stand Alone Outer Package
VZ_FormFactor: DSK
VZ_EntityType: IHVLVOuterPackage
VZ_FormType: FRM
VZ_RequiresLogin: true
VZ_Dependencies: >-
  <dependencies xmlns="http://cargowise.com/glow/2014/11/14/dependencies.xsd">
    <expandPath
      path="LastMileCarrier" />
    <calculatedProperty
      path="CountTotalItemsLabel" />
    <calculatedProperty
      path="CountHeldItemsLabel" />
    <calculatedProperty
      path="CountClearedItemsLabel" />
    <calculatedProperty
      path="CountSurplusItemsLabel" />
  </dependencies>
VZ_FormData: >-
  <form
    id="9f1567f9-acca-43d1-9be7-83096c5968de" xmlns="http://wisetechglobal.com/glow/2017/05/26/form.xsd">
    <placeholder
      name="BackgroundImagePk"
      value="" />
    <placeholder
      name="BackgroundSize"
      value="Auto" />
    <placeholder
      name="BackgroundImageOverride"
      value="" />
    <placeholder
      name="FormLayout"
      value="Splitview" />
    <placeholder
      name="ExpandPaths"
      value="" />
    <placeholder
      name="ScanInputHandling"
      value="NON" />
    <placeholder
      name="RemovePadding"
      value="false" />
    <placeholder
      name="TurnOnPortableOneToOneDesignMode"
      value="false" />
    <placeholder
      name="ContainsDefaultButton"
      value="False" />
    <control
      code="GRP"
      id="ba8daf07-2072-41e5-9478-f845790b08b9"
      top="0"
      width="26"
      height="17.3">
      <placeholder
        name="CanBeHidden"
        value="True" />
      <placeholder
        name="VisibilityCondition"
        value="9108ed2d-4c17-4c16-a0e7-0d7c35543e0b" />
      <placeholder
        name="Header"
        value="New Section"
        resid="014d8546-5c87-42dc-ae36-ff9520747e92" />
      <placeholder
        name="HeaderAlignment"
        value="Center" />
      <placeholder
        name="HeaderDisplayMode"
        value="Hidden" />
      <placeholder
        name="BackgroundImagePk"
        value="" />
      <placeholder
        name="BackgroundImageOverride"
        value="" />
      <control
        code="LBL"
        id="a4e7b515-538f-4402-9315-6b227e962676"
        left="8.9"
        top="8.7"
        width="8"
        height="1"
        binding="CountTotalItemsLabel">
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="LabelType"
          value="Notification" />
        <placeholder
          name="LabelNotificationType"
          value="Information" />
        <placeholder
          name="ForegroundThemeColor"
          value="GeneralText" />
        <placeholder
          name="TextSize"
          value="Large" />
        <placeholder
          name="FontWeight"
          value="Normal" />
        <placeholder
          name="LabelAlignment"
          value="Center" />
        <placeholder
          name="BackgroundColor"
          value="" />
        <placeholder
          name="Padding"
          value="None" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="LBL"
        id="c9188669-6359-44a7-9442-c364443c64bd"
        left="8.9"
        top="9.7"
        width="8"
        height="1"
        binding="CountHeldItemsLabel">
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="LabelType"
          value="Notification" />
        <placeholder
          name="LabelNotificationType"
          value="Error" />
        <placeholder
          name="ForegroundThemeColor"
          value="GeneralText" />
        <placeholder
          name="TextSize"
          value="Large" />
        <placeholder
          name="FontWeight"
          value="Normal" />
        <placeholder
          name="LabelAlignment"
          value="Center" />
        <placeholder
          name="BackgroundColor"
          value="" />
        <placeholder
          name="Padding"
          value="None" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="LBL"
        id="64aaebbc-ee20-453b-af46-d175b2dba931"
        left="8.9"
        top="10.7"
        width="8"
        height="1"
        binding="CountClearedItemsLabel">
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="LabelType"
          value="Notification" />
        <placeholder
          name="LabelNotificationType"
          value="Success" />
        <placeholder
          name="ForegroundThemeColor"
          value="GeneralText" />
        <placeholder
          name="TextSize"
          value="Large" />
        <placeholder
          name="FontWeight"
          value="Normal" />
        <placeholder
          name="LabelAlignment"
          value="Center" />
        <placeholder
          name="BackgroundColor"
          value="" />
        <placeholder
          name="Padding"
          value="None" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="LBL"
        id="1967a69f-6faf-4457-a50c-a0e32cf66e30"
        left="8.9"
        top="11.7"
        width="8"
        height="0.9"
        binding="CountSurplusItemsLabel">
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="LabelType"
          value="Notification" />
        <placeholder
          name="LabelNotificationType"
          value="Warning" />
        <placeholder
          name="ForegroundThemeColor"
          value="GeneralText" />
        <placeholder
          name="TextSize"
          value="Large" />
        <placeholder
          name="FontWeight"
          value="Normal" />
        <placeholder
          name="LabelAlignment"
          value="Center" />
        <placeholder
          name="BackgroundColor"
          value="" />
        <placeholder
          name="Padding"
          value="None" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="FIN"
        id="25411347-9b59-4f60-a9eb-89e5251e8f8d"
        left="8.9"
        top="13.5"
        width="8"
        height="1"
        binding="HVLVItems">
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="Barcode"
          resid="6e47a566-eed4-4361-b925-464c47f4fe3b" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="DefaultFilter">
          <xml>
            <ArrayOfFilterGroup xmlns:a="http://schemas.microsoft.com/2003/10/Serialization/Arrays" xmlns:i="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://cargowise.com/glow/2014/07/16/filters.xsd">
              <FilterGroup>
                <Filters>
                  <Filter>
                    <FilterType>IsActiveFilter</FilterType>
                    <Operation>Is</Operation>
                    <PropertyPath>HVI_IsActive</PropertyPath>
                    <Values>
                      <a:string>true</a:string>
                    </Values>
                  </Filter>
                </Filters>
                <IsImplicit>false</IsImplicit>
              </FilterGroup>
            </ArrayOfFilterGroup>
          </xml>
        </placeholder>
        <placeholder
          name="ItemTemplateID"
          value="" />
        <placeholder
          name="ItemPosition"
          value="Bottom" />
        <placeholder
          name="ParentPropertyName"
          value="AttachableParentForIHVLVOuterPackageHVLVItems" />
        <placeholder
          name="ParentEntityName"
          value="IHVLVOuterPackage" />
        <placeholder
          name="EntityType"
          value="IHVLVItem" />
      </control>
      <control
        code="FLB"
        id="6e986556-1a40-4950-b899-3c4676912fb9"
        left="8.9"
        top="14.5"
        width="8"
        height="1">
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="LabelText"
          value="Please Scan Next Item"
          resid="cc5168b5-aa20-477e-b111-243cdf63c0d5" />
        <placeholder
          name="LabelType"
          value="NormalLabel" />
        <placeholder
          name="LabelNotificationType"
          value="Information" />
        <placeholder
          name="ForegroundThemeColor"
          value="DefaultButton" />
        <placeholder
          name="TextSize"
          value="Medium" />
        <placeholder
          name="FontWeight"
          value="Bold" />
        <placeholder
          name="LabelAlignment"
          value="Center" />
        <placeholder
          name="BackgroundColor"
          value="" />
        <placeholder
          name="Padding"
          value="None" />
      </control>
    </control>
    <control
      code="GRP"
      id="431c7191-e08b-4ae1-b100-d036f9e15696"
      top="0.7"
      width="26"
      height="8">
      <placeholder
        name="CanBeHidden"
        value="True" />
      <placeholder
        name="VisibilityCondition"
        value="709b3872-4e2d-463d-a2d0-38d6649c8eb7" />
      <placeholder
        name="Header"
        value="New Section"
        resid="d9a47ee2-176c-4298-a341-4a4241d5a531" />
      <placeholder
        name="HeaderAlignment"
        value="Left" />
      <placeholder
        name="HeaderDisplayMode"
        value="Hidden" />
      <placeholder
        name="BackgroundImagePk"
        value="" />
      <placeholder
        name="BackgroundImageOverride"
        value="" />
      <control
        code="LBL"
        id="7ea1dc62-590b-45e6-af7e-96d2ffc123c4"
        left="0.6"
        top="0.5"
        width="24.5"
        height="2.8"
        binding="HVO_PackageBarcode">
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="LabelType"
          value="Notification" />
        <placeholder
          name="LabelNotificationType"
          value="Information" />
        <placeholder
          name="ForegroundThemeColor"
          value="GeneralText" />
        <placeholder
          name="TextSize"
          value="ExtraExtraLarge" />
        <placeholder
          name="FontWeight"
          value="Normal" />
        <placeholder
          name="LabelAlignment"
          value="Center" />
        <placeholder
          name="BackgroundColor"
          value="" />
        <placeholder
          name="Padding"
          value="None" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="LBL"
        id="a1074162-a61c-408a-a1ab-443b52684374"
        left="0.6"
        top="3.8"
        width="24.5"
        height="2.8"
        binding="LastMileCarrier.OH_FullName">
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="LabelType"
          value="Notification" />
        <placeholder
          name="LabelNotificationType"
          value="Information" />
        <placeholder
          name="ForegroundThemeColor"
          value="GeneralText" />
        <placeholder
          name="TextSize"
          value="ExtraLarge" />
        <placeholder
          name="FontWeight"
          value="Normal" />
        <placeholder
          name="LabelAlignment"
          value="Center" />
        <placeholder
          name="BackgroundColor"
          value="" />
        <placeholder
          name="Padding"
          value="None" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
    </control>
    <control
      code="GRP"
      id="848c749d-618d-4c02-8097-ba7b3dc37b34"
      top="1.1"
      width="25"
      height="5.7">
      <placeholder
        name="CanBeHidden"
        value="True" />
      <placeholder
        name="VisibilityCondition"
        value="fc719d07-f4cf-4af5-9169-e5bfa218efcf" />
      <placeholder
        name="Header"
        value="New Section"
        resid="a74c07f3-08cf-4cfa-95f4-fdabb2846f0a" />
      <placeholder
        name="HeaderAlignment"
        value="Left" />
      <placeholder
        name="HeaderDisplayMode"
        value="Hidden" />
      <placeholder
        name="BackgroundImagePk"
        value="" />
      <placeholder
        name="BackgroundImageOverride"
        value="" />
      <control
        code="LBL"
        id="58fdb3e2-8b7c-4fd4-99b9-3f98d8afa110"
        left="0"
        top="1.3"
        width="24.6"
        height="3.5"
        binding="HVO_PackageBarcode">
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="LabelType"
          value="Notification" />
        <placeholder
          name="LabelNotificationType"
          value="Information" />
        <placeholder
          name="ForegroundThemeColor"
          value="GeneralText" />
        <placeholder
          name="TextSize"
          value="ExtraExtraLarge" />
        <placeholder
          name="FontWeight"
          value="Normal" />
        <placeholder
          name="LabelAlignment"
          value="Center" />
        <placeholder
          name="BackgroundColor"
          value="" />
        <placeholder
          name="Padding"
          value="None" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
    </control>
  </form>
