#transformationVersion: 70.0
#
VZ_PK: 891b8daacebc4dffa3ed7f6186314e95
VZ_ConfigurationKey: 891b8daa-cebc-4dff-a3ed-7f6186314e95
VZ_FormID: GDM - Gate In
VZ_Caption:
  resKey: Caption|891b8daa-cebc-4dff-a3ed-7f6186314e95
  text: Pending gate in
VZ_FormFactor: DSK
VZ_EntityType: IGteVehicleMovement
VZ_FormType: FRM
VZ_RequiresLogin: true
VZ_MaterialDesign: true
VZ_Dependencies: <dependencies xmlns="http://cargowise.com/glow/2014/11/14/dependencies.xsd" />
VZ_StatusBindingPath: Status
VZ_EntityStatusDisplayMode: ReadOnly
VZ_FormData: >-
  <form
    id="b422810d-07c9-4789-977b-03ceae0ac055" xmlns="http://wisetechglobal.com/glow/2017/05/26/form.xsd">
    <placeholder
      name="Align"
      value="left" />
    <placeholder
      name="Layout"
      value="flex" />
    <placeholder
      name="FlexDirection"
      value="flex-column" />
    <control
      code="BOX"
      id="8d691e88-32b4-45d2-91c2-829851f484a1"
      binding="">
      <placeholder
        name="Height"
        value="100%" />
      <placeholder
        name="Layout"
        value="grid" />
      <placeholder
        name="Margin"
        value="mt-2" />
      <control
        code="BOX"
        id="6ac328c5-f89e-4894-bdc8-cec5337cf80c"
        binding="">
        <placeholder
          name="Height"
          value="100%" />
        <placeholder
          name="Margin"
          value="mr-4" />
        <placeholder
          name="Columns"
          value="col-3" />
        <control
          code="PNL"
          id="7ab8d565-9577-4eb6-839a-4934d025d393"
          binding="">
          <placeholder
            name="Caption"
            value="Transport details"
            resid="5b4bba77-04ec-4151-ba24-010e42334fc5" />
          <control
            code="BOX"
            id="dae81f7c-cfe8-42a4-882d-9837025b7776"
            binding="">
            <placeholder
              name="Layout"
              value="grid" />
            <placeholder
              name="Margin"
              value="mb-2" />
            <control
              code="SRC"
              id="7ea5a7e4-2099-4311-b7df-d08abf0c3419"
              binding="VehicleRegistrationForBinding">
              <placeholder
                name="Columns"
                value="col-6" />
              <placeholder
                name="Margin"
                value="mb-2" />
              <placeholder
                name="CaptionOverride"
                value="Vehicle registration"
                resid="b89e76f0-5aa9-46c4-be35-aaea7653bbe8" />
              <placeholder
                name="Required"
                value="True" />
            </control>
            <control
              code="SRC"
              id="5fd047ff-285e-49dc-8742-5341279c6e0e"
              binding="GVM_RC_VehicleType">
              <placeholder
                name="Columns"
                value="col-6" />
              <placeholder
                name="Margin"
                value="mb-2" />
              <placeholder
                name="CaptionOverride"
                value="Vehicle type"
                resid="3ec8d60c-8475-4400-abcd-e4a8a1dbdddb" />
            </control>
            <control
              code="TXT"
              id="8c0d5fec-054d-4aed-8f52-db7037b1a812"
              binding="TransportCompany.OH_Code">
              <placeholder
                name="Columns"
                value="col-6" />
              <placeholder
                name="CaptionOverride"
                value="Transport provider"
                resid="7d76ce81-6859-4786-82eb-8ce84daf49ec" />
              <placeholder
                name="Margin"
                value="mb-2" />
              <placeholder
                name="VisibilityCondition"
                value="MainBooking != null" />
            </control>
            <control
              code="SRC"
              id="40f45a51-95a8-4422-971b-8b986fe0b0aa"
              binding="SelectedTransportCompanyForGateInWithoutBooking">
              <placeholder
                name="Columns"
                value="col-6" />
              <placeholder
                name="CaptionOverride"
                value="Transport provider"
                resid="23635111-e9ae-4845-9783-20d227dd0a72" />
              <placeholder
                name="Margin"
                value="mb-2" />
              <placeholder
                name="VisibilityCondition"
                value="MainBooking == null" />
              <placeholder
                name="Required"
                value="True" />
            </control>
            <control
              code="DTE"
              id="4dd21304-117f-425c-bc5c-94604fdbc035"
              binding="EarliestBookedSlot">
              <placeholder
                name="Columns"
                value="col-6" />
              <placeholder
                name="Margin"
                value="mb-2" />
              <placeholder
                name="CaptionOverride"
                value="Earliest booking slot"
                resid="308c9188-6c76-4f44-aebf-10ed3addf0b0" />
            </control>
            <control
              code="TXT"
              id="5aee77a8-a94b-4946-81da-8d10f0a5a2ee"
              binding="MainBooking.GBK_ReferenceNumber">
              <placeholder
                name="CaptionOverride"
                value="Gate booking"
                resid="140b8f03-3d0b-4d2d-8a5d-e702d099c298" />
              <placeholder
                name="Margin"
                value="mb-2" />
              <placeholder
                name="VisibilityCondition"
                value="!Exists" />
              <placeholder
                name="Columns"
                value="col-6" />
            </control>
            <control
              code="TXT"
              id="7a072fac-e217-4512-aa1a-c830a982b3b8"
              binding="MainBooking.GBK_ReferenceNumber">
              <placeholder
                name="Columns"
                value="col-6" />
              <placeholder
                name="CaptionOverride"
                value="Gate booking"
                resid="5c60f9c0-23ff-40f1-9753-1ea643e7e3b1" />
              <placeholder
                name="Margin"
                value="mb-2" />
              <placeholder
                name="VisibilityCondition"
                value="Exists" />
            </control>
            <control
              code="CMB"
              id="79a795e2-193e-448b-941a-ecb543bb3454"
              binding="MainBooking.GBK_BookingType">
              <placeholder
                name="Columns"
                value="col-6" />
              <placeholder
                name="Margin"
                value="mb-2" />
              <placeholder
                name="CaptionOverride"
                value="Booking type"
                resid="282aee90-f8c6-4bb8-8067-b22677b481c0" />
            </control>
            <control
              code="TXA"
              id="6451571d-decc-49a4-af0f-1ac56b50cb8d"
              binding="GateReferenceCheckGateIn">
              <placeholder
                name="CaptionOverride"
                value="Gate reference check"
                resid="ed99b494-bdf9-4988-b424-a5589e129440" />
              <placeholder
                name="Margin"
                value="mb-2" />
              <placeholder
                name="Rows"
                value="3" />
              <placeholder
                name="VisibilityCondition"
                value="MainBooking.Exists" />
            </control>
            <control
              code="SRC"
              id="f87f2744-238f-4f94-90ff-56190b76d641"
              binding="MainBooking.GBK_WW_Facility">
              <placeholder
                name="VisibilityCondition"
                value="MainBooking != null" />
              <placeholder
                name="IsReadOnly"
                value="True" />
              <placeholder
                name="CaptionOverride"
                value="Facility"
                resid="1a5c55ef-ca26-4d2b-8855-461d7ded4d2f" />
              <placeholder
                name="Columns"
                value="col-6" />
              <placeholder
                name="Margin"
                value="mb-2" />
            </control>
            <control
              code="SRC"
              id="863aac47-c2f4-45e5-a64a-f874b9b7eb24"
              binding="FacilityForGateInWithoutBooking">
              <placeholder
                name="Required"
                value="True" />
              <placeholder
                name="VisibilityCondition"
                value="MainBooking == null" />
              <placeholder
                name="CaptionOverride"
                value="Facility"
                resid="e3119c79-0c80-460e-be02-ec48181fba73" />
              <placeholder
                name="Columns"
                value="col-6" />
              <placeholder
                name="Margin"
                value="mb-2" />
            </control>
            <control
              code="SRC"
              id="b0941d35-33d0-45db-bd71-4ef0b4d65357"
              binding="GVM_WL_Location">
              <placeholder
                name="Columns"
                value="col-6" />
              <placeholder
                name="Margin"
                value="mb-2" />
              <placeholder
                name="Required"
                value="True" />
            </control>
            <control
              code="DVR"
              id="599263cd-30cc-4528-b2ab-9e2cbe51e94c">
              <placeholder
                name="Variant"
                value="solid" />
              <placeholder
                name="Margin"
                value="mb-2" />
            </control>
            <control
              code="TXT"
              id="dc3ac56a-71b9-42fb-a243-c38b9cf9de0a"
              binding="GateInActionNumber">
              <placeholder
                name="Margin"
                value="mb-2" />
              <placeholder
                name="VisibilityCondition"
                value="Exists" />
              <placeholder
                name="Columns"
                value="col-12" />
              <placeholder
                name="CaptionOverride"
                value="Gate in number"
                resid="407b70a4-72f9-48d9-9691-e523ecd0086c" />
            </control>
            <control
              code="SRC"
              id="3aa87cfb-3d3e-41a2-815b-32bd69faf4d8"
              binding="GateInVehicleEntry.GVE_DriverName">
              <placeholder
                name="CaptionOverride"
                value="Entry driver name"
                resid="e790fc54-41a7-4388-aac9-6d5c716b09e7" />
              <placeholder
                name="Columns"
                value="col-6" />
              <placeholder
                name="Margin"
                value="mb-2" />
            </control>
            <control
              code="SRC"
              id="5a37be81-fe1f-4cf1-86cb-6f397fb47ab6"
              binding="GateInVehicleEntry.GVE_DriverLicenseNumber">
              <placeholder
                name="CaptionOverride"
                value="Entry driver license"
                resid="74582088-6fbb-4c83-b862-b4d9ab18beaf" />
              <placeholder
                name="Columns"
                value="col-6" />
              <placeholder
                name="Margin"
                value="mb-2" />
            </control>
            <control
              code="DTE"
              id="bf7c9c5d-4ca8-4936-91a8-c8cb64c1260e"
              binding="GateInVehicleEntry.GVE_EntryTime">
              <placeholder
                name="Columns"
                value="col-6" />
              <placeholder
                name="Margin"
                value="mb-2" />
              <placeholder
                name="CaptionOverride"
                value="Entry date and time"
                resid="d9a7ad66-51d4-4610-8b9b-ee4e7112d3ef" />
              <placeholder
                name="Required"
                value="True" />
            </control>
            <control
              code="MHS"
              id="7d2f02bb-76d1-4d87-b65f-a86b9a63ffa7"
              binding="GateInVehicleEntry.GVE_WeightMeasure">
              <placeholder
                name="Columns"
                value="col-6" />
              <placeholder
                name="CaptionOverride"
                value="Entry weight"
                resid="9af06ae6-101a-41ef-8dce-c47bc0ab1cd5" />
              <placeholder
                name="Margin"
                value="mb-2" />
              <placeholder
                name="Required"
                value="True" />
            </control>
            <control
              code="SRC"
              id="d467a799-4978-4615-9adc-a0e1fd8cfcfd"
              binding="GateInVehicleEntry.EntryGateCode">
              <placeholder
                name="CaptionOverride"
                value="Entry gate"
                resid="755fba98-ce0d-45d1-b6fc-7ebc36dc0068" />
              <placeholder
                name="Margin"
                value="mb-2" />
              <placeholder
                name="Columns"
                value="col-6" />
              <placeholder
                name="VisibilityCondition"
                value="!Exists || IsDraft" />
              <placeholder
                name="Required"
                value="True" />
            </control>
            <control
              code="SRC"
              id="e0a0de7a-ecb3-46e0-b8a7-f9ebb136d379"
              binding="GateInVehicleEntry.Lane.GLN_GTE_Gate">
              <placeholder
                name="Columns"
                value="col-6" />
              <placeholder
                name="CaptionOverride"
                value="Entry gate"
                resid="0cbc9d10-1b76-4438-8742-850de2a0beba" />
              <placeholder
                name="Margin"
                value="mb-2" />
              <placeholder
                name="VisibilityCondition"
                value="Exists &amp;&amp; !IsDraft" />
              <placeholder
                name="IsReadOnly"
                value="True" />
              <placeholder
                name="Required"
                value="False" />
            </control>
            <control
              code="SRC"
              id="9fc1b0de-4149-4f72-b1c5-a1c26af6b941"
              binding="GateInVehicleEntry.GVE_GLN_Lane">
              <placeholder
                name="Columns"
                value="col-6" />
              <placeholder
                name="CaptionOverride"
                value="Entry lane"
                resid="691322cd-2e72-40df-a604-b9df58794b19" />
              <placeholder
                name="Margin"
                value="mb-2" />
              <placeholder
                name="Required"
                value="True" />
            </control>
          </control>
        </control>
      </control>
      <control
        code="PNL"
        id="c01d271f-9f8c-4572-9c04-81f2f0c7d2fe"
        binding="">
        <placeholder
          name="FitToHeight"
          value="True" />
        <placeholder
          name="Columns"
          value="col-9" />
        <placeholder
          name="Height"
          value="100%" />
        <control
          code="RDT"
          id="335f86f4-4c4f-4cdc-a70a-85947c56a559"
          binding="GteGateMovements">
          <placeholder
            name="AllowAdd"
            value="calc(!GateInVehicleEntry.Exists &amp;&amp; CanAddAdHocContainers)" />
          <placeholder
            name="InlineEdit"
            value="cell" />
          <placeholder
            name="CaptionOverride"
            value="Associated bookings"
            resid="5fb0dc0d-af69-4fa8-bd4a-95df6a53218c" />
          <placeholder
            name="DefaultFilter">
            <xml>
              <ArrayOfFilterGroup xmlns:a="http://schemas.microsoft.com/2003/10/Serialization/Arrays" xmlns:i="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://cargowise.com/glow/2014/07/16/filters.xsd">
                <FilterGroup>
                  <Filters>
                    <Filter>
                      <FilterType>StringFilter</FilterType>
                      <Operation>IsBlank</Operation>
                      <PropertyPath>GGM_CancelledReason</PropertyPath>
                      <Values />
                    </Filter>
                  </Filters>
                  <IsImplicit>true</IsImplicit>
                </FilterGroup>
              </ArrayOfFilterGroup>
            </xml>
          </placeholder>
          <placeholder
            name="ShowCustomize"
            value="True" />
          <placeholder
            name="FieldConfiguration">
            <xml>
              <fields xmlns="">
                <field
                  path="MovementType"
                  width="150"
                  mode="Default" />
                <field
                  path="MovementBooking.GBM_SourceReferenceNumber"
                  width="150"
                  mode="Default" />
                <field
                  path="BookingReferenceNumber"
                  width="150"
                  mode="Default"
                  readOnly="false" />
                <field
                  path="GGM_TransportReference"
                  width="150"
                  mode="Default" />
                <field
                  path="GGM_RH_NKCargoType"
                  width="150"
                  mode="Default" />
                <field
                  path="GGM_F3_NKPackageType"
                  width="150"
                  mode="Default" />
                <field
                  path="GGM_RC_UnitType"
                  width="150"
                  mode="Default" />
                <field
                  path="GGM_UnitNumber"
                  width="150"
                  mode="Default" />
                <field
                  path="MovementBooking.GBM_SlotStartTime"
                  width="150"
                  mode="Default" />
                <field
                  path="MovementBooking.GBM_SlotEndTime"
                  width="150"
                  mode="Default" />
              </fields>
            </xml>
          </placeholder>
          <placeholder
            name="AllowAttach"
            value="False" />
          <placeholder
            name="AllowDetach"
            value="False" />
          <placeholder
            name="ActionMenuItems">
            <xml>
              <formFlows xmlns="">
                <formFlow
                  inDrawer="True">af0773685c154e8f91653ebb54b67ad5</formFlow>
              </formFlows>
            </xml>
          </placeholder>
        </control>
      </control>
    </control>
  </form>
