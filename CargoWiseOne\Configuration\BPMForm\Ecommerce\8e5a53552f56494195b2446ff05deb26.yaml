#transformationVersion: 70.0
#
VZ_PK: 8e5a53552f56494195b2446ff05deb26
VZ_ConfigurationKey: 8e5a5355-2f56-4941-95b2-446ff05deb26
VZ_FormID: ETL - VDV3 - Shipper Portal G1 - Invoices
VZ_Caption:
  resKey: VZ_Caption|8e5a5355-2f56-4941-95b2-446ff05deb26
  text: Invoices
VZ_FormFactor: DSK
VZ_FormType: PAG
VZ_RequiresLogin: true
VZ_Dependencies: <dependencies xmlns="http://cargowise.com/glow/2014/11/14/dependencies.xsd" />
VZ_FormData: >-
  <form
    id="dbdef5dd-fbb8-4b1a-8179-e74c52c26e31" xmlns="http://wisetechglobal.com/glow/2017/05/26/form.xsd">
    <placeholder
      name="BackgroundImagePk"
      value="" />
    <placeholder
      name="BackgroundSize"
      value="Auto" />
    <placeholder
      name="BackgroundImageOverride"
      value="" />
    <placeholder
      name="FormLayout"
      value="Splitview" />
    <placeholder
      name="ExpandPaths"
      value="" />
    <placeholder
      name="ScanInputHandling"
      value="NON" />
    <placeholder
      name="RemovePadding"
      value="false" />
    <placeholder
      name="TurnOnPortableOneToOneDesignMode"
      value="false" />
    <placeholder
      name="ContainsDefaultButton"
      value="False" />
    <control
      code="GRP"
      id="bedcffbd-3210-4196-b037-2789f5a9c655"
      left="0"
      top="0"
      width="6"
      height="4">
      <placeholder
        name="CanBeHidden"
        value="True" />
      <placeholder
        name="VisibilityCondition"
        value="" />
      <placeholder
        name="Header"
        value="New Section"
        resid="5c50e9d2-1c59-4030-b77a-14d5806a0ec2" />
      <placeholder
        name="HeaderAlignment"
        value="Left" />
      <placeholder
        name="HeaderDisplayMode"
        value="Hidden" />
      <placeholder
        name="BackgroundImagePk"
        value="" />
      <placeholder
        name="BackgroundImageOverride"
        value="" />
      <control
        code="TIL"
        id="e925262a-23e4-49a9-90fe-f9aff7eb51fd"
        left="0"
        top="0"
        width="2"
        height="2">
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="Size"
          value="Standard" />
        <placeholder
          name="Color"
          value="Default" />
        <placeholder
          name="DisplayMode"
          value="TextAndIcon" />
        <placeholder
          name="Text"
          value="Shipments"
          resid="89aaabe4-d53a-4c5f-87ee-8fea63a6a437" />
        <placeholder
          name="Image"
          value="d4c02b187e9d4e37be59b173a7caa29a" />
        <placeholder
          name="ImageOverride"
          value="" />
        <placeholder
          name="Description"
          value="" />
        <placeholder
          name="Behaviour"
          value="Page" />
        <placeholder
          name="PagePK"
          value="79cf3e9f175a4a67af77918cc5984c3d" />
        <placeholder
          name="Url"
          value="" />
        <placeholder
          name="FormFlow"
          value="" />
      </control>
      <control
        code="TIL"
        id="722c58c1-b795-4fae-858c-6158bc5f8047"
        left="2"
        top="0"
        width="2"
        height="2">
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="Size"
          value="Standard" />
        <placeholder
          name="Color"
          value="Default" />
        <placeholder
          name="DisplayMode"
          value="TextAndIcon" />
        <placeholder
          name="Text"
          value="Consignments"
          resid="bbacec45-82d4-4e63-8ca4-50c815612032" />
        <placeholder
          name="Image"
          value="2c348de953fe4e8db9967317a1a942c4" />
        <placeholder
          name="ImageOverride"
          value="" />
        <placeholder
          name="Description"
          value="" />
        <placeholder
          name="Behaviour"
          value="Page" />
        <placeholder
          name="PagePK"
          value="3c95f54513ea4829aebfe9ffb9b4c647" />
        <placeholder
          name="Url"
          value="" />
        <placeholder
          name="FormFlow"
          value="" />
      </control>
      <control
        code="TIL"
        id="9cdc05bd-b2b2-4a0e-8189-1c8f7b0c00d7"
        left="4"
        top="0"
        width="2"
        height="2">
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="Size"
          value="Standard" />
        <placeholder
          name="Color"
          value="Default" />
        <placeholder
          name="DisplayMode"
          value="TextAndIcon" />
        <placeholder
          name="Text"
          value="Headers"
          resid="953e80a6-f2d0-456e-aa98-266350ac128d" />
        <placeholder
          name="Image"
          value="05e1a4771ad542aeadfef74283eec66b" />
        <placeholder
          name="ImageOverride"
          value="" />
        <placeholder
          name="Description"
          value="" />
        <placeholder
          name="Behaviour"
          value="Page" />
        <placeholder
          name="PagePK"
          value="7923c78de01e49f9b4488e0a5ba38d92" />
        <placeholder
          name="Url"
          value="" />
        <placeholder
          name="FormFlow"
          value="" />
      </control>
      <control
        code="TIL"
        id="da8cbfea-5146-4297-93eb-68a039aec2c2"
        left="0"
        top="2"
        width="2"
        height="2">
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="Size"
          value="Standard" />
        <placeholder
          name="Color"
          value="Primary" />
        <placeholder
          name="DisplayMode"
          value="TextAndIcon" />
        <placeholder
          name="Text"
          value="Invoices"
          resid="73833047-2532-4d1c-a228-a6533ab86812" />
        <placeholder
          name="Image"
          value="b928eff5f6c743cf84380b025cc233a0" />
        <placeholder
          name="ImageOverride"
          value="" />
        <placeholder
          name="Description"
          value="" />
        <placeholder
          name="Behaviour"
          value="Page" />
        <placeholder
          name="PagePK"
          value="8e5a53552f56494195b2446ff05deb26" />
        <placeholder
          name="Url"
          value="" />
        <placeholder
          name="FormFlow"
          value="" />
      </control>
      <control
        code="TIL"
        id="5018687d-cfac-4ed3-ae4e-096704edd442"
        left="2"
        top="2"
        width="2"
        height="2">
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="Size"
          value="Standard" />
        <placeholder
          name="Color"
          value="Default" />
        <placeholder
          name="DisplayMode"
          value="TextAndIcon" />
        <placeholder
          name="Text"
          value="Reports"
          resid="f6be36d8-23f0-4563-becb-642480a2621e" />
        <placeholder
          name="Image"
          value="fbcf0fdf52074ddf97ec2ce94d6f47a1" />
        <placeholder
          name="ImageOverride"
          value="" />
        <placeholder
          name="Description"
          value="" />
        <placeholder
          name="Behaviour"
          value="Page" />
        <placeholder
          name="PagePK"
          value="07990593823a43f0a3f8dcdd9f47da6c" />
        <placeholder
          name="Url"
          value="" />
        <placeholder
          name="FormFlow"
          value="" />
      </control>
    </control>
    <control
      code="SRL"
      id="6bc96a43-5f41-44ac-a242-a51e1f20b8a0"
      left="7"
      top="0"
      right="0"
      bottom="0">
      <placeholder
        name="CanBeHidden"
        value="True" />
      <placeholder
        name="IsInitialFocus"
        value="False" />
      <placeholder
        name="CaptionOverride"
        value="Invoices"
        resid="cdc14076-2de4-4544-ba04-b791e30ed615" />
      <placeholder
        name="VisibilityCondition"
        value="" />
      <placeholder
        name="HeaderAlignment"
        value="Left" />
      <placeholder
        name="HeaderDisplayMode"
        value="Visible" />
      <placeholder
        name="FilterType"
        value="Entity" />
      <placeholder
        name="EntityType"
        value="IAccARInvoice" />
      <placeholder
        name="ItemDisplayPath"
        value="" />
      <placeholder
        name="DisabledGridRowActions"
        value="" />
      <placeholder
        name="DefaultFilter">
        <xml>
          <ArrayOfFilterGroup xmlns:a="http://schemas.microsoft.com/2003/10/Serialization/Arrays" xmlns:i="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://cargowise.com/glow/2014/07/16/filters.xsd">
            <FilterGroup>
              <Filters>
                <Filter>
                  <FilterType>StringFilter</FilterType>
                  <Operation>StartsWith</Operation>
                  <PropertyPath>AH_ConsolidatedInvoiceRef</PropertyPath>
                  <Values />
                </Filter>
                <Filter>
                  <FilterType>DateTimeFilter</FilterType>
                  <Operation>IsInTheDateRange</Operation>
                  <PropertyPath>AH_InvoiceDate</PropertyPath>
                  <Values />
                </Filter>
                <Filter>
                  <FilterType>DateTimeFilter</FilterType>
                  <Operation>IsInTheDateRange</Operation>
                  <PropertyPath>AH_DueDate</PropertyPath>
                  <Values />
                </Filter>
              </Filters>
              <IsImplicit>false</IsImplicit>
            </FilterGroup>
          </ArrayOfFilterGroup>
        </xml>
      </placeholder>
      <placeholder
        name="DefaultFilterPropertyPath"
        value="" />
      <placeholder
        name="FieldConfiguration">
        <xml>
          <fields xmlns="">
            <field
              path="AH_ConsolidatedInvoiceRef"
              width="150"
              mode="Default" />
            <field
              path="AH_TransactionType"
              width="100"
              mode="Default" />
            <field
              path="AH_InvoiceDate"
              width="130"
              mode="Default" />
            <field
              path="AH_DueDate"
              width="130"
              mode="Default" />
            <field
              path="AH_FullyPaidDate"
              width="130"
              mode="Default" />
            <field
              path="AH_InvoiceAmount"
              width="150"
              mode="Default" />
            <field
              path="TransactionCurrency.RX_Code"
              width="70"
              mode="Default" />
            <field
              path="AH_OutstandingAmount"
              width="200"
              mode="Default" />
            <field
              path="AH_OH"
              width="250"
              mode="Optional" />
            <field
              path="AH_OA_InvoiceAddressOverride"
              width="250"
              mode="Optional" />
            <field
              path="AH_AgePeriod"
              width="100"
              mode="Optional" />
            <field
              path="AH_ChequeOrReference"
              width="250"
              mode="Optional" />
            <field
              path="AH_GB"
              width="250"
              mode="Optional" />
            <field
              path="AH_IsCancelled"
              width="80"
              mode="Optional" />
            <field
              path="AH_TransactionReference"
              width="220"
              mode="Optional" />
            <field
              path="AH_GC"
              width="250"
              mode="Optional" />
            <field
              path="AH_OC_InvoiceContactOverride"
              width="250"
              mode="Optional" />
            <field
              path="AH_GE"
              width="250"
              mode="Optional" />
            <field
              path="AH_Desc"
              width="250"
              mode="Optional" />
            <field
              path="AH_ExchangeRate"
              width="190"
              mode="Optional" />
            <field
              path="AH_InvoiceApproved"
              width="160"
              mode="Optional" />
            <field
              path="AH_InvoicePaymentReferenceCode"
              width="250"
              mode="Optional" />
            <field
              path="AH_PostDate"
              width="180"
              mode="Optional" />
            <field
              path="InvoiceURL"
              width="250"
              mode="Optional" />
            <field
              path="AH_JH"
              width="250"
              mode="Optional" />
            <field
              path="AH_PostPeriod"
              width="110"
              mode="Optional" />
            <field
              path="AH_InvoicePrinted"
              width="70"
              mode="Optional" />
            <field
              path="AH_RequisitionDate"
              width="180"
              mode="Optional" />
            <field
              path="AH_RequisitionStatus"
              width="180"
              mode="Optional" />
            <field
              path="AH_SystemCreateTimeUtc"
              width="180"
              mode="Optional" />
            <field
              path="AH_SystemCreateUser"
              width="180"
              mode="Optional" />
            <field
              path="AH_SystemLastEditTimeUtc"
              width="210"
              mode="Optional" />
            <field
              path="AH_SystemLastEditUser"
              width="210"
              mode="Optional" />
            <field
              path="AH_GSTAmount"
              width="200"
              mode="Optional" />
            <field
              path="AH_InvoiceTermDays"
              width="160"
              mode="Optional" />
            <field
              path="AH_InvoiceTerm"
              width="50"
              mode="Optional" />
            <field
              path="AH_OSTotal"
              width="250"
              mode="Optional" />
            <field
              path="AH_RX_NKTransactionCurrency"
              width="80"
              mode="Optional" />
            <field
              path="AH_TransactionNum"
              width="250"
              mode="Optional" />
            <field
              path="AH_WithholdingTax"
              width="200"
              mode="Optional" />
            <field
              path="GlbCompanyInfo.GC_Name"
              width="150"
              mode="Default" />
          </fields>
        </xml>
      </placeholder>
      <placeholder
        name="DefaultSortFields">
        <xml>
          <ArrayOfFieldSortDefinition xmlns:i="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://schemas.datacontract.org/2004/07/CargoWise.Glow.Infrastructure.Interfaces">
            <FieldSortDefinition>
              <FieldName>AH_InvoiceDate</FieldName>
              <IsAscending>false</IsAscending>
            </FieldSortDefinition>
          </ArrayOfFieldSortDefinition>
        </xml>
      </placeholder>
      <placeholder
        name="DefaultGroupFields"
        value="" />
      <placeholder
        name="EnableGrouping"
        value="False" />
      <placeholder
        name="AutoRefreshInterval"
        value="0" />
      <placeholder
        name="AllowMultiSelect"
        value="True" />
      <placeholder
        name="ShowWorkflowLabel"
        value="True" />
      <placeholder
        name="RowActionsMode"
        value="RowAndMenu" />
      <placeholder
        name="HideGridActions"
        value="False" />
      <placeholder
        name="MinimizeFilters"
        value="False" />
      <placeholder
        name="WorkflowOpenInNewWindow"
        value="False" />
      <placeholder
        name="SearchOnInit"
        value="True" />
      <placeholder
        name="ShowQuickSearch"
        value="False" />
      <placeholder
        name="QuickSearchCaption"
        value="" />
      <placeholder
        name="ValueField"
        value="" />
      <placeholder
        name="Category"
        value="" />
      <placeholder
        name="ActionMenuItems"
        value="" />
      <placeholder
        name="EditFormFlow"
        value="" />
      <placeholder
        name="ResetFilterOnInit"
        value="False" />
      <placeholder
        name="FilterDisplayMode"
        value="InlineAndPopup" />
    </control>
    <control
      code="GRP"
      id="3b7f4bd7-f3f4-40a4-b2ef-ffad8d30f405"
      left="0"
      top="4"
      width="6"
      height="5">
      <placeholder
        name="CanBeHidden"
        value="True" />
      <placeholder
        name="VisibilityCondition"
        value="" />
      <placeholder
        name="Header"
        value="Tracking"
        resid="6ae8af01-80d3-458f-8da7-02d7cf66180d" />
      <placeholder
        name="HeaderAlignment"
        value="Left" />
      <placeholder
        name="HeaderDisplayMode"
        value="Visible" />
      <placeholder
        name="BackgroundImagePk"
        value="" />
      <placeholder
        name="BackgroundImageOverride"
        value="" />
      <control
        code="TRC"
        id="4cbc246f-ba3b-4b3b-a467-1f2669a15b0d"
        left="0"
        top="0"
        width="6"
        height="4">
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="VisibilityCondition"
          value="" />
      </control>
    </control>
    <control
      code="FAV"
      id="c6a87e2c-f516-4802-9963-be834f3c0719"
      left="0"
      top="9"
      width="6"
      bottom="2">
      <placeholder
        name="CanBeHidden"
        value="True" />
      <placeholder
        name="VisibilityCondition"
        value="" />
      <placeholder
        name="EntityType"
        value="" />
      <placeholder
        name="HeaderDisplayMode"
        value="Visible" />
      <placeholder
        name="ShowFavorites"
        value="True" />
      <placeholder
        name="ShowRecents"
        value="True" />
    </control>
  </form>
