#transformationVersion: 70.0
#
VZ_PK: f8ebbd0eb5a848bda01aa8f3673fa047
VZ_ConfigurationKey: f8ebbd0e-b5a8-48bd-a01a-a8f3673fa047
VZ_FormID: Alias for Form flows
VZ_Caption:
  resKey: VZ_Caption|f8ebbd0eb5a848bda01aa8f3673fa047
  text: Alias for Form flows
VZ_FormFactor: DSK
VZ_FormType: PAG
VZ_RequiresLogin: true
VZ_MaterialDesign: true
VZ_Dependencies: <dependencies xmlns="http://cargowise.com/glow/2014/11/14/dependencies.xsd" />
VZ_FormData: >-
  <form
    id="bf55b78b-6ad4-4db1-ac32-61a68327699a" xmlns="http://wisetechglobal.com/glow/2017/05/26/form.xsd">
    <placeholder
      name="FitToHeight"
      value="True" />
    <placeholder
      name="Layout"
      value="fill" />
    <control
      code="PNL"
      id="aa13b003-652b-486c-aa0a-9aacc8ca1fc7"
      binding="">
      <placeholder
        name="Margin"
        value="mb-3" />
      <placeholder
        name="Class"
        value="blue lighten-4 bg-blue-lighten-4" />
      <placeholder
        name="Layout"
        value="flex" />
      <control
        code="BOX"
        id="8a9f2df7-ae3a-4df7-9880-c6cdb2cf5702">
        <placeholder
          name="Layout"
          value="flex" />
        <placeholder
          name="FlexDirection"
          value="flex-column" />
        <placeholder
          name="FlexJustify"
          value="justify-center" />
        <control
          code="BOX"
          id="064210c4-e791-4f98-b97e-a847010873c4">
          <placeholder
            name="Layout"
            value="flex" />
          <control
            code="IBT"
            id="1e66cfaf-9f0c-4139-8619-865e1dc1ede5"
            binding="">
            <placeholder
              name="Icon"
              value="s-icon-documentation" />
            <placeholder
              name="Tooltip"
              value="Documentation"
              resid="1c82d486-06a5-443a-bc28-ad81fd7f6d99" />
            <placeholder
              name="Target"
              value="_blank" />
            <placeholder
              name="Hyperlink"
              value="https://wisetechglobal.sharepoint.com/sites/Content-as-Code/Shared%20Documents/GLOW-Content/_permalinks/PB-FF-Alias.aspx" />
          </control>
          <control
            code="IBT"
            id="c923fa67-728c-4a64-a17c-c5f1cf6177f6"
            binding="">
            <placeholder
              name="Icon"
              value="s-icon-pt-devtools" />
            <placeholder
              name="Margin"
              value="ml-2" />
            <placeholder
              name="Tooltip"
              value="YAML"
              resid="ff85dd8f-a254-48b9-8f03-279db7954863" />
            <placeholder
              name="Target"
              value="_blank" />
            <placeholder
              name="Hyperlink"
              value="https://github.com/WiseTechGlobal/Glow/blob/master/CargoWiseOne/Configuration/BPMFormFlow/Internal/Library/fb3238d68613450e9148104f07b49fa6.yaml" />
          </control>
          <control
            code="IBT"
            id="66c6523a-a586-4f80-bf15-5fe88e4c188d"
            binding="">
            <placeholder
              name="Icon"
              value="s-icon-settings" />
            <placeholder
              name="Tooltip"
              value="Platform Builder"
              resid="8eee51ba-1625-412e-ac60-ee8bf1e9d540" />
            <placeholder
              name="Target"
              value="_blank" />
            <placeholder
              name="Margin"
              value="ml-2" />
            <placeholder
              name="Hyperlink"
              value="platformbuilder:?target=BPMFormFlow&amp;identifier=fb3238d68613450e9148104f07b49fa6" />
          </control>
        </control>
      </control>
      <control
        code="BOX"
        id="1190c4d9-b3e0-44a8-bd9f-27466dc7acd4">
        <placeholder
          name="Layout"
          value="flex" />
        <placeholder
          name="FlexJustify"
          value="justify-center" />
        <control
          code="BOX"
          id="ab662a10-26ad-4bf7-b242-41e5fb1e50b1">
          <placeholder
            name="Margin"
            value="ml-3" />
          <placeholder
            name="Layout"
            value="flex" />
          <placeholder
            name="FlexDirection"
            value="flex-column" />
          <placeholder
            name="FlexJustify"
            value="justify-center" />
          <placeholder
            name="FlexAlign"
            value="align-center" />
          <control
            code="LBL"
            id="20fa8069-65fa-4690-b763-b92e9d9c5718"
            binding="">
            <placeholder
              name="Display"
              value="block" />
            <placeholder
              name="Caption"
              value="To have a hands on experience of the example within the Documentation, click on the hyperlink below, which will use an alias to launch the 321 startup Form flow."
              resid="5dd2218c-926a-424d-8f42-0c34bf826384" />
            <placeholder
              name="Typography"
              value="body-strong" />
          </control>
        </control>
      </control>
    </control>
    <control
      code="PNL"
      id="bf89367e-0e3e-4b63-b49a-1869ffe575bd"
      binding="">
      <placeholder
        name="Layout"
        value="fill" />
      <placeholder
        name="FillAvailable"
        value="True" />
      <control
        code="HPL"
        id="52027dd6-d830-48ef-963f-534ce19e4fef">
        <placeholder
          name="Hyperlink"
          value="https://www-hyetip.cargowise.com/Portals/goto/GLOWLibrary" />
        <placeholder
          name="Caption"
          value="https://www-hyetip.cargowise.com/Portals/goto/GLOWLibrary"
          resid="8a0c9cda-705a-4710-b675-c3fa6793054d" />
      </control>
    </control>
  </form>
