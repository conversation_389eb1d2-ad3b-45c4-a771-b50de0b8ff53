#transformationVersion: 70.0
#
VZ_PK: 9350f6e9e0434d77bad75107fe4619c3
VZ_ConfigurationKey: 9350f6e9-e043-4d77-bad7-5107fe4619c3
VZ_FormID: ETL - VDV3 - Shipper Portal G1 - Confirmed Pending Allocation
VZ_Caption:
  resKey: VZ_Caption|9350f6e9-e043-4d77-bad7-5107fe4619c3
  text: Confirmed Pending Allocation
VZ_FormFactor: DSK
VZ_FormType: PAG
VZ_RequiresLogin: true
VZ_Dependencies: <dependencies xmlns="http://cargowise.com/glow/2014/11/14/dependencies.xsd" />
VZ_FormData: >-
  <form
    id="ac13f136-3831-490b-8156-e5fa21ea5151" xmlns="http://wisetechglobal.com/glow/2017/05/26/form.xsd">
    <placeholder
      name="BackgroundImagePk"
      value="" />
    <placeholder
      name="BackgroundSize"
      value="Auto" />
    <placeholder
      name="BackgroundImageOverride"
      value="" />
    <placeholder
      name="FormLayout"
      value="Splitview" />
    <placeholder
      name="ExpandPaths"
      value="" />
    <placeholder
      name="ScanInputHandling"
      value="NON" />
    <placeholder
      name="RemovePadding"
      value="false" />
    <placeholder
      name="TurnOnPortableOneToOneDesignMode"
      value="false" />
    <placeholder
      name="ContainsDefaultButton"
      value="False" />
    <control
      code="GRP"
      id="eb391f86-59f7-470a-9b60-42450456e509"
      left="0"
      top="0"
      width="6"
      height="4">
      <placeholder
        name="CanBeHidden"
        value="True" />
      <placeholder
        name="VisibilityCondition"
        value="" />
      <placeholder
        name="Header"
        value="New Section"
        resid="afd71365-dbea-4b25-ae28-be9d1771ea85" />
      <placeholder
        name="HeaderAlignment"
        value="Left" />
      <placeholder
        name="HeaderDisplayMode"
        value="Hidden" />
      <placeholder
        name="BackgroundImagePk"
        value="" />
      <placeholder
        name="BackgroundImageOverride"
        value="" />
      <control
        code="TIL"
        id="413b9e42-b965-47b2-9043-48a565fb1595"
        left="0"
        top="0"
        width="2"
        height="2">
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="Size"
          value="Standard" />
        <placeholder
          name="Color"
          value="Default" />
        <placeholder
          name="DisplayMode"
          value="TextAndIcon" />
        <placeholder
          name="Text"
          value="Booked"
          resid="72c3e6de-e2b7-4ce7-af15-118e2ccfdfe3" />
        <placeholder
          name="Image"
          value="6dad40067685420a9564d6ec7cf5ef14" />
        <placeholder
          name="ImageOverride"
          value="" />
        <placeholder
          name="Description"
          value="" />
        <placeholder
          name="Behaviour"
          value="Page" />
        <placeholder
          name="PagePK"
          value="07990593823a43f0a3f8dcdd9f47da6c" />
        <placeholder
          name="Url"
          value="" />
        <placeholder
          name="FormFlow"
          value="" />
      </control>
      <control
        code="TIL"
        id="85f2b708-c250-4216-ac93-9656e6236a37"
        left="2"
        top="0"
        width="2"
        height="2">
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="Size"
          value="Standard" />
        <placeholder
          name="Color"
          value="Primary" />
        <placeholder
          name="DisplayMode"
          value="TextAndIcon" />
        <placeholder
          name="Text"
          value="Unallocated"
          resid="0ca3d15b-3f0e-41b0-ae3b-066ee9de8061" />
        <placeholder
          name="Image"
          value="e8708fa36b0c417087d0509b8d33abb8" />
        <placeholder
          name="ImageOverride"
          value="" />
        <placeholder
          name="Description"
          value="" />
        <placeholder
          name="Behaviour"
          value="Page" />
        <placeholder
          name="PagePK"
          value="9350f6e9e0434d77bad75107fe4619c3" />
        <placeholder
          name="Url"
          value="" />
        <placeholder
          name="FormFlow"
          value="" />
      </control>
      <control
        code="TIL"
        id="6c8a8be0-fc04-48db-b326-ceac208649c3"
        left="4"
        top="0"
        width="2"
        height="2">
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="Size"
          value="Standard" />
        <placeholder
          name="Color"
          value="Default" />
        <placeholder
          name="DisplayMode"
          value="TextAndIcon" />
        <placeholder
          name="Text"
          value="Allocated"
          resid="4a2d8930-c993-4ffe-bd24-ae44afe0899f" />
        <placeholder
          name="Image"
          value="936a039c478a4b9a89dff1c4136526df" />
        <placeholder
          name="ImageOverride"
          value="" />
        <placeholder
          name="Description"
          value="" />
        <placeholder
          name="Behaviour"
          value="Page" />
        <placeholder
          name="PagePK"
          value="8b3a8a8d0f9e49319becca1aa8c14a97" />
        <placeholder
          name="Url"
          value="" />
        <placeholder
          name="FormFlow"
          value="" />
      </control>
      <control
        code="TIL"
        id="1dd582cd-dc1e-4095-9f09-339fb4ab6b10"
        left="0"
        top="2"
        width="2"
        height="2">
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="Size"
          value="Standard" />
        <placeholder
          name="Color"
          value="Default" />
        <placeholder
          name="DisplayMode"
          value="TextAndIcon" />
        <placeholder
          name="Text"
          value="Impediment"
          resid="938825b9-b5cf-45c1-8707-e3ad5abff59a" />
        <placeholder
          name="Image"
          value="e69bfd87530d4de887276038ceae77c8" />
        <placeholder
          name="ImageOverride"
          value="" />
        <placeholder
          name="Description"
          value="" />
        <placeholder
          name="Behaviour"
          value="Page" />
        <placeholder
          name="PagePK"
          value="695ebfa6927043d2a13b19afa4ede89f" />
        <placeholder
          name="Url"
          value="" />
        <placeholder
          name="FormFlow"
          value="" />
      </control>
      <control
        code="TIL"
        id="9c0e1bd3-df01-4052-9902-76bfad4f20ef"
        left="2"
        top="2"
        width="2"
        height="2">
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="Size"
          value="Standard" />
        <placeholder
          name="Color"
          value="Default" />
        <placeholder
          name="DisplayMode"
          value="TextAndIcon" />
        <placeholder
          name="Text"
          value="Short"
          resid="efe60d71-d7ba-4a73-af8f-026f25408ea9" />
        <placeholder
          name="Image"
          value="95cc057166414200b5f6fa8dd5455427" />
        <placeholder
          name="ImageOverride"
          value="" />
        <placeholder
          name="Description"
          value="" />
        <placeholder
          name="Behaviour"
          value="Page" />
        <placeholder
          name="PagePK"
          value="b6d01b8b47954f9088f754ee204065ca" />
        <placeholder
          name="Url"
          value="" />
        <placeholder
          name="FormFlow"
          value="" />
      </control>
      <control
        code="TIL"
        id="e9f68f72-3099-42de-89c6-6684b673f013"
        left="4"
        top="2"
        width="2"
        height="2">
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="Size"
          value="Standard" />
        <placeholder
          name="Color"
          value="Default" />
        <placeholder
          name="DisplayMode"
          value="TextAndIcon" />
        <placeholder
          name="Text"
          value="Surplus"
          resid="a1c91dfc-bdbd-460f-b56a-e3506f2a44c3" />
        <placeholder
          name="Image"
          value="74a27a164dc145d4b5a8916b1376be68" />
        <placeholder
          name="ImageOverride"
          value="" />
        <placeholder
          name="Description"
          value="" />
        <placeholder
          name="Behaviour"
          value="Page" />
        <placeholder
          name="PagePK"
          value="876b1d27c22741528068ce1e1bf0985b" />
        <placeholder
          name="Url"
          value="" />
        <placeholder
          name="FormFlow"
          value="" />
      </control>
    </control>
    <control
      code="SRL"
      id="35f7aaf0-66ec-4ca9-b9d4-8e0d60ecb5c4"
      left="7"
      top="0"
      right="0"
      bottom="0">
      <placeholder
        name="CanBeHidden"
        value="True" />
      <placeholder
        name="IsInitialFocus"
        value="False" />
      <placeholder
        name="CaptionOverride"
        value="Pending Allocation"
        resid="0b1343f0-497a-4d67-b4d6-864b7c3df586" />
      <placeholder
        name="VisibilityCondition"
        value="" />
      <placeholder
        name="HeaderAlignment"
        value="Left" />
      <placeholder
        name="HeaderDisplayMode"
        value="Visible" />
      <placeholder
        name="FilterType"
        value="Entity" />
      <placeholder
        name="EntityType"
        value="IHVLVItem" />
      <placeholder
        name="ItemDisplayPath"
        value="" />
      <placeholder
        name="DisabledGridRowActions"
        value="" />
      <placeholder
        name="DefaultFilter">
        <xml>
          <ArrayOfFilterGroup xmlns:a="http://schemas.microsoft.com/2003/10/Serialization/Arrays" xmlns:i="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://cargowise.com/glow/2014/07/16/filters.xsd">
            <FilterGroup>
              <Filters>
                <Filter>
                  <FilterType>SimpleLookupFilter</FilterType>
                  <Operation>Is</Operation>
                  <PropertyPath>HVI_Status</PropertyPath>
                  <Values>
                    <a:string>MAN</a:string>
                  </Values>
                </Filter>
                <Filter>
                  <FilterType>BoolFilter</FilterType>
                  <Operation>Is</Operation>
                  <PropertyPath>Consignment.BookingHeader.HVH_IsBookingConfirmed</PropertyPath>
                  <Values>
                    <a:string>true</a:string>
                  </Values>
                </Filter>
              </Filters>
              <IsImplicit>true</IsImplicit>
            </FilterGroup>
            <FilterGroup>
              <Filters>
                <Filter>
                  <FilterType>StringFilter</FilterType>
                  <Operation>StartsWith</Operation>
                  <PropertyPath>HVI_CurrentBarcode</PropertyPath>
                  <Values />
                </Filter>
                <Filter>
                  <FilterType>StringFilter</FilterType>
                  <Operation>StartsWith</Operation>
                  <PropertyPath>HVI_ShipperReference</PropertyPath>
                  <Values />
                </Filter>
                <Filter>
                  <FilterType>IsActiveFilter</FilterType>
                  <Operation>Is</Operation>
                  <PropertyPath>HVI_IsActive</PropertyPath>
                  <Values>
                    <a:string>true</a:string>
                  </Values>
                </Filter>
              </Filters>
              <IsImplicit>false</IsImplicit>
            </FilterGroup>
            <FilterGroup>
              <Filters>
                <Filter>
                  <FilterType>SimpleLookupFilter</FilterType>
                  <Operation>Is</Operation>
                  <PropertyPath>HVI_Status</PropertyPath>
                  <Values>
                    <a:string>RAO</a:string>
                  </Values>
                </Filter>
                <Filter>
                  <FilterType>BoolFilter</FilterType>
                  <Operation>Is</Operation>
                  <PropertyPath>Consignment.BookingHeader.HVH_IsBookingConfirmed</PropertyPath>
                  <Values>
                    <a:string>true</a:string>
                  </Values>
                </Filter>
              </Filters>
              <IsImplicit>true</IsImplicit>
            </FilterGroup>
          </ArrayOfFilterGroup>
        </xml>
      </placeholder>
      <placeholder
        name="DefaultFilterPropertyPath"
        value="" />
      <placeholder
        name="FieldConfiguration">
        <xml>
          <fields xmlns="">
            <field
              path="BookingHeader.HVH_BookingReference"
              width="200"
              mode="Optional" />
            <field
              path="HVI_ItemId"
              width="250"
              mode="Default" />
            <field
              path="HVI_CurrentBarcode"
              width="250"
              mode="Default" />
            <field
              path="HVI_ShipperReference"
              width="250"
              mode="Default" />
            <field
              path="HVI_Status"
              width="250"
              mode="Default" />
            <field
              path="HVI_JS_LoadedOnShipment"
              width="150"
              mode="Default" />
            <field
              path="HVI_ManifestedWeight"
              width="150"
              mode="Default" />
            <field
              path="HVI_ManifestedVolume"
              width="150"
              mode="Default" />
            <field
              path="HVI_ActualWeight"
              width="150"
              mode="Default" />
            <field
              path="HVI_ActualVolume"
              width="150"
              mode="Default" />
            <field
              path="HVI_F3_NKPackType"
              width="100"
              mode="Default" />
            <field
              path="HVI_IsDamaged"
              width="80"
              mode="Default" />
            <field
              path="HVI_IsPillaged"
              width="80"
              mode="Default" />
            <field
              path="HVI_IsUllaged"
              width="80"
              mode="Default" />
            <field
              path="ShipmentETailer"
              width="250"
              mode="Default" />
            <field
              path="HVI_Height"
              width="100"
              mode="Optional" />
            <field
              path="HVI_Width"
              width="100"
              mode="Optional" />
            <field
              path="HVI_Length"
              width="100"
              mode="Optional" />
            <field
              path="HVI_UnitOfDimension"
              width="40"
              mode="Optional" />
            <field
              path="HVI_GoodsDescription"
              width="250"
              mode="Optional" />
            <field
              path="HVI_HVL_LoadList"
              width="250"
              mode="Optional" />
            <field
              path="HVI_HVO_OuterPackage"
              width="250"
              mode="Optional" />
            <field
              path="HVI_KM_LastMileTransportBooking"
              width="250"
              mode="Optional" />
            <field
              path="HVI_HVC_Consignment"
              width="250"
              mode="Optional" />
            <field
              path="HVI_ContainerNumber"
              width="200"
              mode="Optional" />
            <field
              path="Consignment.HVC_ImportCustomsClearanceStatus"
              width="250"
              mode="Optional" />
            <field
              path="Consignment.HVC_ExportCustomsClearanceStatus"
              width="250"
              mode="Optional" />
            <field
              path="HVI_IsActive"
              width="90"
              mode="Optional" />
            <field
              path="HVI_IsValidatedForUniqueness"
              width="200"
              mode="Optional" />
            <field
              path="Consignment.HVC_ConsignmentId"
              width="250"
              mode="Optional" />
            <field
              path="Consignment.HVC_ImportReleaseStatus"
              width="250"
              mode="Optional" />
            <field
              path="EventListHasAnyWithInterceptEvent"
              width="250"
              mode="Optional" />
            <field
              path="EventListHasAnyWithInterceptTaskCompletedEvent"
              width="250"
              mode="Optional" />
            <field
              path="EventListHasAnyWithScannedEvent"
              width="250"
              mode="Optional" />
            <field
              path="Consignment.HVC_ShipperName"
              width="250"
              mode="Optional" />
            <field
              path="Consignment.HVC_ConsigneeName"
              width="250"
              mode="Optional" />
            <field
              path="Consignment.HVC_OA_ConsigneeAddress"
              width="250"
              mode="Optional" />
            <field
              path="Consignment.HVC_OA_ShipperAddress"
              width="250"
              mode="Optional" />
            <field
              path="Consignment.HVC_GoodsValue"
              width="250"
              mode="Optional" />
            <field
              path="Consignment.HVC_RN_NKConsigneeCountryCode"
              width="250"
              mode="Optional" />
            <field
              path="Consignment.HVC_JE_ImportDeclaration"
              width="250"
              mode="Optional" />
            <field
              path="Consignment.HVC_JE_ExportDeclaration"
              width="250"
              mode="Optional" />
            <field
              path="Consignment.HVC_RX_NKGoodsValueCurrency"
              width="250"
              mode="Optional" />
            <field
              path="Consignment.HVC_OH_LastMileCarrierBookingAgent"
              width="250"
              mode="Optional" />
            <field
              path="Consignment.Header.HCH_JS_Shipment"
              width="250"
              mode="Optional" />
            <field
              path="Consignment.HVC_RN_NKShipperCountryCode"
              width="250"
              mode="Optional" />
            <field
              path="Consignment.HVC_CarrierAccountNumber"
              width="250"
              mode="Optional" />
            <field
              path="Consignment.CarrierServiceCode"
              width="250"
              mode="Optional" />
            <field
              path="Consignment.HVC_ConsigneeAddress1"
              width="250"
              mode="Optional" />
            <field
              path="Consignment.HVC_ConsigneeAddress2"
              width="250"
              mode="Optional" />
            <field
              path="Consignment.HVC_ConsigneeAddressValidationStatus"
              width="250"
              mode="Optional" />
            <field
              path="Consignment.HVC_ConsigneeCity"
              width="250"
              mode="Optional" />
            <field
              path="Consignment.HVC_ConsigneeContact"
              width="250"
              mode="Optional" />
            <field
              path="Consignment.HVC_ConsigneeEmail"
              width="250"
              mode="Optional" />
            <field
              path="Consignment.HVC_ConsigneeFax"
              width="250"
              mode="Optional" />
            <field
              path="Consignment.HVC_ConsigneeInstructions"
              width="250"
              mode="Optional" />
            <field
              path="Consignment.HVC_ConsigneeMobile"
              width="250"
              mode="Optional" />
            <field
              path="Consignment.HVC_ConsigneePhone"
              width="250"
              mode="Optional" />
            <field
              path="Consignment.HVC_ConsigneePostcode"
              width="250"
              mode="Optional" />
            <field
              path="Consignment.HVC_ConsigneeState"
              width="250"
              mode="Optional" />
            <field
              path="Consignment.HVC_GoodsDescription"
              width="250"
              mode="Optional" />
            <field
              path="Consignment.HVC_INCO"
              width="180"
              mode="Optional" />
            <field
              path="Consignment.HVC_PL_NKLastMileCarrierServiceLevel"
              width="250"
              mode="Optional" />
            <field
              path="Consignment.HVC_PreScreeningStatus"
              width="250"
              mode="Optional" />
            <field
              path="Consignment.HVC_ShipperAddress1"
              width="250"
              mode="Optional" />
            <field
              path="Consignment.HVC_ShipperAddress2"
              width="250"
              mode="Optional" />
            <field
              path="Consignment.HVC_ShipperCity"
              width="250"
              mode="Optional" />
            <field
              path="Consignment.HVC_ShipperEmail"
              width="250"
              mode="Optional" />
            <field
              path="Consignment.HVC_ShipperFax"
              width="250"
              mode="Optional" />
            <field
              path="Consignment.HVC_ShipperMobile"
              width="250"
              mode="Optional" />
            <field
              path="Consignment.HVC_ShipperContact"
              width="250"
              mode="Optional" />
            <field
              path="Consignment.HVC_ShipperPhone"
              width="250"
              mode="Optional" />
            <field
              path="Consignment.HVC_ShipperPostcode"
              width="250"
              mode="Optional" />
            <field
              path="Consignment.HVC_ShipperReference"
              width="250"
              mode="Optional" />
            <field
              path="Consignment.HVC_ShipperState"
              width="250"
              mode="Optional" />
            <field
              path="Consignment.HVC_Status"
              width="200"
              mode="Optional" />
            <field
              path="Consignment.HVC_UndgClass"
              width="240"
              mode="Optional" />
            <field
              path="Consignment.HVC_VendorIdentifier"
              width="250"
              mode="Optional" />
            <field
              path="Consignment.HVC_WaybillNumber"
              width="250"
              mode="Optional" />
            <field
              path="Consignment.HVC_AuthorityToLeave"
              width="250"
              mode="Optional" />
            <field
              path="Consignment.HVC_IsHazardous"
              width="230"
              mode="Optional" />
            <field
              path="Consignment.HVC_IsActive"
              width="230"
              mode="Optional" />
            <field
              path="Consignment.HVC_IsSignatureRequired"
              width="250"
              mode="Optional" />
            <field
              path="Consignment.HVC_IsTaxPrePaid"
              width="250"
              mode="Optional" />
            <field
              path="Consignment.IsTranshipment"
              width="250"
              mode="Optional" />
            <field
              path="Consignment.HVC_IsPerishable"
              width="240"
              mode="Optional" />
            <field
              path="Consignment.HVC_IsPersonalEffects"
              width="250"
              mode="Optional" />
            <field
              path="Consignment.HVC_IsValidatedForUniqueness"
              width="250"
              mode="Optional" />
            <field
              path="Consignment.HVC_RequiresFumigation"
              width="250"
              mode="Optional" />
            <field
              path="Consignment.HVC_IsSelfBooked"
              width="250"
              mode="Optional" />
            <field
              path="Consignment.HVC_IsTimber"
              width="200"
              mode="Optional" />
            <field
              path="BookingHeader"
              width="140"
              mode="Optional" />
            <field
              path="DestinationDepot"
              width="250"
              mode="Optional" />
            <field
              path="OriginDepot"
              width="250"
              mode="Optional" />
            <field
              path="CargoWiseRef"
              width="250"
              mode="Optional" />
            <field
              path="CartageURL"
              width="250"
              mode="Optional" />
            <field
              path="GateInAtOriginDepot"
              width="230"
              mode="Optional" />
            <field
              path="HVI_IsUnmanifestedAtDestination"
              width="250"
              mode="Optional" />
            <field
              path="ItemHasBeenScanned"
              width="210"
              mode="Optional" />
            <field
              path="LastInterceptEventReference"
              width="250"
              mode="Optional" />
            <field
              path="URL"
              width="250"
              mode="Optional" />
            <field
              path="HVI_IsScannedAtDestination"
              width="100"
              mode="Optional" />
            <field
              path="HVI_ImportReleaseStatus"
              width="100"
              mode="Optional" />
            <field
              path="Consignment.HVC_OH_LastMileCarrier"
              width="250"
              mode="Optional" />
            <field
              path="Consignment.HVC_OA_DestinationDepot"
              width="250"
              mode="Optional" />
            <field
              path="Consignment.HVC_SystemCreateTimeUtc"
              width="250"
              mode="Optional" />
            <field
              path="Consignment.HVC_SystemLastEditTimeUtc"
              width="250"
              mode="Optional" />
            <field
              path="Consignment.HVC_ActualVolumeMeasure"
              width="130"
              mode="Optional" />
            <field
              path="Consignment.HVC_ActualWeightMeasure"
              width="130"
              mode="Optional" />
            <field
              path="Consignment.HVC_ManifestedVolumeMeasure"
              width="130"
              mode="Optional" />
            <field
              path="Consignment.HVC_ManifestedWeightMeasure"
              width="130"
              mode="Optional" />
            <field
              path="Consignment.HVC_ItemCount"
              width="250"
              mode="Optional" />
            <field
              path="Consignment.HVC_HVH_BookingHeader"
              width="250"
              mode="Optional" />
            <field
              path="Consignment.HVC_SystemLastEditUser"
              width="250"
              mode="Optional" />
            <field
              path="HVI_CarrierBookingStatus"
              width="220"
              mode="Optional" />
            <field
              path="VolumeWeight"
              width="250"
              mode="Optional" />
            <field
              path="Chargeable"
              width="250"
              mode="Optional" />
            <field
              path="DensityFactor"
              width="250"
              mode="Optional" />
          </fields>
        </xml>
      </placeholder>
      <placeholder
        name="DefaultSortFields"
        value="" />
      <placeholder
        name="DefaultGroupFields"
        value="" />
      <placeholder
        name="EnableGrouping"
        value="False" />
      <placeholder
        name="AutoRefreshInterval"
        value="0" />
      <placeholder
        name="AllowMultiSelect"
        value="True" />
      <placeholder
        name="ShowWorkflowLabel"
        value="True" />
      <placeholder
        name="RowActionsMode"
        value="RowAndMenu" />
      <placeholder
        name="HideGridActions"
        value="False" />
      <placeholder
        name="MinimizeFilters"
        value="False" />
      <placeholder
        name="WorkflowOpenInNewWindow"
        value="False" />
      <placeholder
        name="SearchOnInit"
        value="True" />
      <placeholder
        name="ShowQuickSearch"
        value="False" />
      <placeholder
        name="QuickSearchCaption"
        value="" />
      <placeholder
        name="ValueField"
        value="" />
      <placeholder
        name="Category"
        value="" />
      <placeholder
        name="ActionMenuItems"
        value="" />
      <placeholder
        name="EditFormFlow"
        value="" />
      <placeholder
        name="ResetFilterOnInit"
        value="False" />
      <placeholder
        name="FilterDisplayMode"
        value="InlineAndPopup" />
    </control>
    <control
      code="GRP"
      id="110f53af-6483-43b1-8a35-35626a3b6ed0"
      left="0"
      top="4"
      width="6"
      height="5">
      <placeholder
        name="CanBeHidden"
        value="True" />
      <placeholder
        name="VisibilityCondition"
        value="" />
      <placeholder
        name="Header"
        value="Tracking"
        resid="7687df78-54cb-4e10-b6c2-0522114ef9df" />
      <placeholder
        name="HeaderAlignment"
        value="Left" />
      <placeholder
        name="HeaderDisplayMode"
        value="Visible" />
      <placeholder
        name="BackgroundImagePk"
        value="" />
      <placeholder
        name="BackgroundImageOverride"
        value="" />
      <control
        code="TRC"
        id="d2410be6-3fd0-44ea-bacd-cc7a36eca239"
        left="0"
        top="0"
        width="6"
        height="4">
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="VisibilityCondition"
          value="" />
      </control>
    </control>
    <control
      code="FAV"
      id="76e7ae8f-38e4-438d-b4e3-b5ca68dc7c9d"
      left="0"
      top="9"
      width="6"
      bottom="2">
      <placeholder
        name="CanBeHidden"
        value="True" />
      <placeholder
        name="VisibilityCondition"
        value="" />
      <placeholder
        name="EntityType"
        value="" />
      <placeholder
        name="HeaderDisplayMode"
        value="Visible" />
      <placeholder
        name="ShowFavorites"
        value="True" />
      <placeholder
        name="ShowRecents"
        value="True" />
    </control>
  </form>
