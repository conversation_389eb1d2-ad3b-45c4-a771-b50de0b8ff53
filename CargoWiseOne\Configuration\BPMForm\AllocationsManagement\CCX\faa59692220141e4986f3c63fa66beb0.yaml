#transformationVersion: 70.0
#
VZ_PK: faa59692220141e4986f3c63fa66beb0
VZ_ConfigurationKey: faa59692-2201-41e4-986f-3c63fa66beb0
VZ_FormID: CCX - Carrier Contracts Web Search and Import
VZ_Caption:
  resKey: VZ_Caption|faa59692-2201-41e4-986f-3c63fa66beb0
  text: Carrier Contracts
VZ_FormFactor: DSK
VZ_FormType: PAG
VZ_RequiresLogin: true
VZ_ExtenderFunc: CargoWise.Glow.Business.Forwarding.Contracts.CWPopupWindow.CarrierContractLookupFormExtender.extendCarrierContractLookup
VZ_Dependencies: <dependencies xmlns="http://cargowise.com/glow/2014/11/14/dependencies.xsd" />
VZ_FormData: >-
  <form
    id="22e30cf1-56e5-4813-9d05-e5d03bb9269b" xmlns="http://wisetechglobal.com/glow/2017/05/26/form.xsd">
    <placeholder
      name="BackgroundImagePk"
      value="" />
    <placeholder
      name="BackgroundSize"
      value="Auto" />
    <placeholder
      name="BackgroundImageOverride"
      value="" />
    <placeholder
      name="FormLayout"
      value="Splitview" />
    <placeholder
      name="ExpandPaths"
      value="" />
    <placeholder
      name="ScanInputHandling"
      value="NON" />
    <placeholder
      name="RemovePadding"
      value="false" />
    <placeholder
      name="TurnOnPortableOneToOneDesignMode"
      value="false" />
    <placeholder
      name="ContainsDefaultButton"
      value="False" />
    <control
      code="SRL"
      id="85e0930c-7f60-452f-9e11-63820c634b50"
      left="1"
      top="1"
      right="1"
      bottom="1">
      <placeholder
        name="CanBeHidden"
        value="True" />
      <placeholder
        name="IsInitialFocus"
        value="False" />
      <placeholder
        name="CaptionOverride"
        value="Carrier Contracts"
        resid="bb7d9da3-2ff2-41ea-8826-b9340ce4eaaf" />
      <placeholder
        name="VisibilityCondition"
        value="" />
      <placeholder
        name="HeaderAlignment"
        value="Left" />
      <placeholder
        name="HeaderDisplayMode"
        value="Visible" />
      <placeholder
        name="FilterType"
        value="Entity" />
      <placeholder
        name="EntityType"
        value="IRatingContract" />
      <placeholder
        name="ItemDisplayPath"
        value="" />
      <placeholder
        name="DisabledGridRowActions"
        value="Remove,Activation,Tracking,Workflow,Documents,Notes,Messages,eDocs" />
      <placeholder
        name="DefaultFilter"
        value="" />
      <placeholder
        name="DefaultFilterPropertyPath"
        value="" />
      <placeholder
        name="FieldConfiguration"
        value="" />
      <placeholder
        name="DefaultSortFields"
        value="" />
      <placeholder
        name="DefaultGroupFields"
        value="" />
      <placeholder
        name="EnableGrouping"
        value="False" />
      <placeholder
        name="AutoRefreshInterval"
        value="0" />
      <placeholder
        name="AllowMultiSelect"
        value="False" />
      <placeholder
        name="ShowWorkflowLabel"
        value="False" />
      <placeholder
        name="RowActionsMode"
        value="None" />
      <placeholder
        name="HideGridActions"
        value="True" />
      <placeholder
        name="MinimizeFilters"
        value="False" />
      <placeholder
        name="WorkflowOpenInNewWindow"
        value="False" />
      <placeholder
        name="SearchOnInit"
        value="False" />
      <placeholder
        name="ShowQuickSearch"
        value="False" />
      <placeholder
        name="QuickSearchCaption"
        value="" />
      <placeholder
        name="ValueField"
        value="" />
      <placeholder
        name="Category"
        value="" />
      <placeholder
        name="ActionMenuItems"
        value="" />
      <placeholder
        name="EditFormFlow"
        value="" />
      <placeholder
        name="ResetFilterOnInit"
        value="False" />
      <placeholder
        name="FilterDisplayMode"
        value="InlineOnly" />
    </control>
  </form>
