#transformationVersion: 70.0
#
VZ_PK: a9b722d051e240e3b5c5e7060affde4f
VZ_ConfigurationKey: a9b722d0-51e2-40e3-b5c5-e7060affde4f
VZ_FormID: ETL - VDV3 - Origin Depot - Create New Load List
VZ_Caption:
  resKey: VZ_Caption|a9b722d0-51e2-40e3-b5c5-e7060affde4f
  text: Create New Load List
VZ_FormFactor: DSK
VZ_EntityType: IHVLVOriginLoadList
VZ_FormType: FRM
VZ_RequiresLogin: true
VZ_Dependencies: >-
  <dependencies xmlns="http://cargowise.com/glow/2014/11/14/dependencies.xsd">
    <expandPath
      path="ServiceLevel" />
    <expandPath
      path="HVLVItems" />
    <expandPath
      path="ContainerType" />
    <expandPath
      path="Origin" />
    <expandPath
      path="Destination" />
    <expandPath
      path="Carrier" />
    <expandPath
      path="HVLVOuterPackages" />
    <expandPath
      path="HVLVOuterPackages/HVLVItems" />
    <calculatedProperty
      path="ItemsAllocated" />
    <calculatedProperty
      path="ItemsShipped" />
    <datagrid
      path="HVLVOuterPackages">
      <expandPath
        path="PackageType" />
      <expandPath
        path="LastMileCarrier" />
      <expandPath
        path="LoadedOnConsol" />
      <expandPath
        path="Commodity" />
    </datagrid>
    <datagrid
      path="HVLVOuterPackages/HVLVItems">
      <expandPath
        path="PackType" />
      <expandPath
        path="LoadedOnShipment" />
      <expandPath
        path="Consignment.LastMileCarrier" />
      <expandPath
        path="Consignment" />
      <expandPath
        path="LastMileTransportBooking" />
      <expandPath
        path="LoadList" />
      <expandPath
        path="Consignment.ConsigneeAddress" />
      <expandPath
        path="Consignment.ShipperAddress" />
      <expandPath
        path="Consignment.ConsigneeCountry" />
      <expandPath
        path="Consignment.ImportDeclaration" />
      <expandPath
        path="Consignment.ExportDeclaration" />
      <expandPath
        path="Consignment.GoodsValueCurrency" />
      <expandPath
        path="Consignment.LastMileCarrierBookingAgent" />
      <expandPath
        path="Consignment.Header.Shipment" />
      <expandPath
        path="Consignment.ShipperCountry" />
      <expandPath
        path="Consignment.DestinationDepot" />
      <expandPath
        path="Consignment.BookingHeader" />
      <expandPath
        path="Consignment.LastEditedByStaff" />
    </datagrid>
    <datagrid
      path="HVLVItems">
      <expandPath
        path="PackType" />
      <expandPath
        path="LoadedOnShipment" />
      <expandPath
        path="Consignment.LastMileCarrier" />
      <expandPath
        path="Consignment" />
      <expandPath
        path="LastMileTransportBooking" />
      <expandPath
        path="OuterPackage" />
      <expandPath
        path="Consignment.ConsigneeAddress" />
      <expandPath
        path="Consignment.ShipperAddress" />
      <expandPath
        path="Consignment.ConsigneeCountry" />
      <expandPath
        path="Consignment.ImportDeclaration" />
      <expandPath
        path="Consignment.ExportDeclaration" />
      <expandPath
        path="Consignment.GoodsValueCurrency" />
      <expandPath
        path="Consignment.LastMileCarrierBookingAgent" />
      <expandPath
        path="Consignment.Header.Shipment" />
      <expandPath
        path="Consignment.ShipperCountry" />
      <expandPath
        path="Consignment.DestinationDepot" />
      <expandPath
        path="Consignment.BookingHeader" />
      <expandPath
        path="Consignment.LastEditedByStaff" />
    </datagrid>
  </dependencies>
VZ_FormData: >-
  <form
    id="08307e71-6d66-4cb5-b3c7-a33588fbfd14" xmlns="http://wisetechglobal.com/glow/2017/05/26/form.xsd">
    <action
      binding="Closed" />
    <action
      binding="Open" />
    <placeholder
      name="BackgroundImagePk"
      value="" />
    <placeholder
      name="BackgroundSize"
      value="Auto" />
    <placeholder
      name="BackgroundImageOverride"
      value="" />
    <placeholder
      name="FormLayout"
      value="Splitview" />
    <placeholder
      name="ExpandPaths"
      value="" />
    <placeholder
      name="ScanInputHandling"
      value="NON" />
    <placeholder
      name="RemovePadding"
      value="false" />
    <placeholder
      name="TurnOnPortableOneToOneDesignMode"
      value="false" />
    <placeholder
      name="ContainsDefaultButton"
      value="False" />
    <control
      code="GRP"
      id="6fd4c22e-f0d6-4e22-b84f-f4b6905220de"
      left="0"
      top="0"
      right="0"
      bottom="0">
      <placeholder
        name="CanBeHidden"
        value="True" />
      <placeholder
        name="VisibilityCondition"
        value="" />
      <placeholder
        name="Header"
        value="New Section"
        resid="59620ae5-933c-4a12-a163-b9c82151ccb5" />
      <placeholder
        name="HeaderAlignment"
        value="Left" />
      <placeholder
        name="HeaderDisplayMode"
        value="Hidden" />
      <placeholder
        name="BackgroundImagePk"
        value="" />
      <placeholder
        name="BackgroundImageOverride"
        value="" />
      <control
        code="ADD"
        id="cdddfaf2-9087-4f7d-98cc-c3167f19b41c"
        left="0"
        top="0"
        width="4"
        height="3"
        binding="HVL_OA_OriginDepot">
        <placeholder
          name="IsReadOnly"
          value="False" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="Origin Depot *"
          resid="a4a36db1-2744-4dd7-a028-dfdbe389389a" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="IsUNLOCOVisible"
          value="True" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="ADD"
        id="b133c72d-1fb2-4b00-a7b0-17a4f9fd2cc5"
        left="4"
        top="0"
        width="4"
        height="3"
        binding="HVL_OA_DestinationDepot">
        <placeholder
          name="IsReadOnly"
          value="False" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="Destination Depot *"
          resid="a0b8f5ec-0e3b-458c-8f68-feed4aef4d39" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="IsUNLOCOVisible"
          value="True" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="CMB"
        id="f9453935-cafd-4f05-84e8-ca1b64fb27ae"
        left="8.8"
        top="0"
        width="4"
        height="1"
        binding="HVL_TransportMode">
        <placeholder
          name="IsReadOnly"
          value="False" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="Transport Mode"
          resid="b154b73f-9520-4f9f-9b04-b10996c12c37" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="DisplayMode"
          value="Unspecified" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="SRC"
        id="0796c894-20cd-4c35-8c56-debba355982c"
        left="12.8"
        top="0"
        width="4"
        height="1"
        binding="HVL_RS_NKServiceLevel">
        <placeholder
          name="IsReadOnly"
          value="False" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="Service Level"
          resid="23d949ef-97b2-4b53-ab4e-f98d933be8b3" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="DisplayMode"
          value="CodeOnly" />
        <placeholder
          name="FieldConfiguration"
          value="" />
        <placeholder
          name="DefaultFilter"
          value="" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="TXT"
        id="52087b91-f397-4ecd-bb11-2fedcfeafdd3"
        left="16.8"
        top="0"
        width="4"
        height="1"
        binding="HVL_MasterBillNumber">
        <placeholder
          name="IsReadOnly"
          value="False" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="Master Bill/BOL"
          resid="36437379-f46d-498b-bb9f-a0407e427e12" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="OnSubmit"
          value="" />
        <placeholder
          name="MultiLine"
          value="False" />
        <placeholder
          name="EnableAutoComplete"
          value="False" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="OPT"
        id="1a3e62dd-7e95-4ae8-9ce5-dc9529bf3c31"
        left="20.8"
        top="0"
        width="4"
        height="1"
        binding="HVL_IsNeutralMaster">
        <placeholder
          name="IsReadOnly"
          value="False" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="Is Neutral Master"
          resid="44ee4f4b-793d-4180-ae3d-98312c4ae313" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="TXT"
        id="46912f5a-5917-43f8-b2df-afab899ac6e6"
        left="24.8"
        top="0"
        width="4"
        height="1"
        binding="HVL_HouseBillNumber">
        <placeholder
          name="IsReadOnly"
          value="False" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="OnSubmit"
          value="" />
        <placeholder
          name="MultiLine"
          value="False" />
        <placeholder
          name="EnableAutoComplete"
          value="False" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="NUM"
        id="7b72b868-a4a4-45e1-b6b1-160b25eae86c"
        left="29.4"
        top="0"
        width="3"
        height="1"
        binding="ItemsAllocated">
        <placeholder
          name="IsReadOnly"
          value="True" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="NumericFieldLeftAlignment"
          value="False" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="NUM"
        id="c9aa3299-5bca-4b16-959c-460268225983"
        left="32.4"
        top="0"
        width="3"
        height="1"
        binding="HVLVItems/HVI_ManifestedWeight">
        <placeholder
          name="IsReadOnly"
          value="True" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="Items Man. Wt."
          resid="9ad4c4aa-39ca-4aa7-bbbf-abec828f19b7" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="NumericFieldLeftAlignment"
          value="False" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="NUM"
        id="91804358-0646-402f-a752-a0c75d875544"
        left="35.4"
        top="0"
        width="3"
        height="1"
        binding="HVLVItems/HVI_ManifestedVolume">
        <placeholder
          name="IsReadOnly"
          value="True" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="Items Man. Vol."
          resid="f72706bb-9671-422f-918b-28dbb1790e73" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="NumericFieldLeftAlignment"
          value="False" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="NUM"
        id="42bf2d10-0753-4ea7-a1d8-b5ad8a3c32bd"
        left="38.4"
        top="0"
        width="3"
        height="1"
        binding="ContainerType.RC_CubicCapacity">
        <placeholder
          name="IsReadOnly"
          value="True" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="Container Capacity"
          resid="523c41a3-d141-4d4f-b43c-1125b72b4f46" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="NumericFieldLeftAlignment"
          value="False" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="SRC"
        id="c17132d5-d88a-4520-9f6c-431667d16529"
        left="8.8"
        top="1"
        width="4"
        height="1"
        binding="HVL_RL_NKOrigin">
        <placeholder
          name="IsReadOnly"
          value="False" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="Origin Port"
          resid="1e53b4d5-4e49-45d1-ae82-00397b69e653" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="DisplayMode"
          value="Unspecified" />
        <placeholder
          name="FieldConfiguration"
          value="" />
        <placeholder
          name="DefaultFilter"
          value="" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="SRC"
        id="0d733184-a09d-4214-bedb-1d0f49d63d29"
        left="12.8"
        top="1"
        width="4"
        height="1"
        binding="HVL_RL_NKDestination">
        <placeholder
          name="IsReadOnly"
          value="False" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="Destination Port"
          resid="df1fc3de-3038-4396-89f3-1a3aaa42de0b" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="DisplayMode"
          value="Unspecified" />
        <placeholder
          name="FieldConfiguration"
          value="" />
        <placeholder
          name="DefaultFilter"
          value="" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="DTE"
        id="7416de1a-08ee-41c3-b2ef-deec35924355"
        left="16.8"
        top="1"
        width="4"
        height="1"
        binding="HVL_E_Dep">
        <placeholder
          name="IsReadOnly"
          value="False" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="ETD"
          resid="a8356b75-1bd8-46b3-a93f-118054befcad" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="DefaultValueExpression"
          value="" />
        <placeholder
          name="IncludeTime"
          value="True" />
        <placeholder
          name="ShowNowButton"
          value="True" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="DTE"
        id="fff47fee-a6a7-4863-a5c7-db12ebbc2f04"
        left="20.8"
        top="1"
        width="4"
        height="1"
        binding="HVL_E_Arv">
        <placeholder
          name="IsReadOnly"
          value="False" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="ETA"
          resid="1d3c6ec7-e07e-4eb8-a5ed-29684ce62813" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="DefaultValueExpression"
          value="" />
        <placeholder
          name="IncludeTime"
          value="True" />
        <placeholder
          name="ShowNowButton"
          value="True" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="SRC"
        id="ff91a927-ba6b-418a-9765-931e69e94aad"
        left="24.8"
        top="1"
        width="4"
        height="1"
        binding="HVL_OH_Carrier">
        <placeholder
          name="IsReadOnly"
          value="False" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="Carrier"
          resid="06785baa-4a21-41fa-926c-ac8345413d65" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="DisplayMode"
          value="CodeOnly" />
        <placeholder
          name="FieldConfiguration"
          value="" />
        <placeholder
          name="DefaultFilter"
          value="" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="NUM"
        id="c0f04d25-cb34-4eb3-bf34-2d06307ac49a"
        left="29.4"
        top="1"
        width="3"
        height="1"
        binding="ItemsShipped">
        <placeholder
          name="IsReadOnly"
          value="True" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="NumericFieldLeftAlignment"
          value="False" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="NUM"
        id="e7f73651-b6e9-4f18-9451-72181b7a316c"
        left="32.4"
        top="1"
        width="3"
        height="1"
        binding="HVLVItems/HVI_ActualWeight">
        <placeholder
          name="IsReadOnly"
          value="True" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="Items Act. Wt."
          resid="53c502d6-35d1-46b3-a11d-acec57e9111d" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="NumericFieldLeftAlignment"
          value="False" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="NUM"
        id="99c9af4e-8889-49dd-8d6c-eee3da9f699a"
        left="35.4"
        top="1"
        width="3"
        height="1"
        binding="HVLVItems/HVI_ActualVolume">
        <placeholder
          name="IsReadOnly"
          value="True" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="Items Act. Vol."
          resid="e11b0aaa-08b8-477d-b54f-837221c53fa9" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="NumericFieldLeftAlignment"
          value="False" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="NUM"
        id="05d90570-04e3-4754-a704-849d17bee43c"
        left="38.4"
        top="1"
        width="3"
        height="1"
        binding="ContainerType.RC_GrossWeight">
        <placeholder
          name="IsReadOnly"
          value="True" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="Container Wt."
          resid="38a3aa08-eed3-44f9-96cc-fc91c05e551d" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="NumericFieldLeftAlignment"
          value="False" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="TXT"
        id="02fffb9e-16f0-4898-b635-da13c0b1117a"
        left="8.8"
        top="2"
        width="4"
        height="1"
        binding="HVL_VoyageFlight">
        <placeholder
          name="IsReadOnly"
          value="False" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="Voyage/Flight/Truck/Journey"
          resid="dc6a953d-6406-4f87-bc62-d4372186a486" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="OnSubmit"
          value="" />
        <placeholder
          name="MultiLine"
          value="False" />
        <placeholder
          name="EnableAutoComplete"
          value="False" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="SRC"
        id="d288c517-d956-4a58-a1f8-1d5790c0dd90"
        left="12.8"
        top="2"
        width="4"
        height="1"
        binding="HVL_VesselName">
        <placeholder
          name="IsReadOnly"
          value="False" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="Vessel"
          resid="08937055-ba60-4784-9291-3da6e130ffa3" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="DisplayMode"
          value="CodeOnly" />
        <placeholder
          name="FieldConfiguration"
          value="" />
        <placeholder
          name="DefaultFilter"
          value="" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="TXT"
        id="728c6d12-e379-4e71-bc39-1d2f4a9aae4f"
        left="16.8"
        top="2"
        width="4"
        height="1"
        binding="HVL_ContainerNumber">
        <placeholder
          name="IsReadOnly"
          value="False" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="OnSubmit"
          value="" />
        <placeholder
          name="MultiLine"
          value="False" />
        <placeholder
          name="EnableAutoComplete"
          value="False" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="SRC"
        id="69e77364-670c-424b-ac5c-756fb4777aef"
        left="20.8"
        top="2"
        width="4"
        height="1"
        binding="HVL_RC_ContainerType">
        <placeholder
          name="IsReadOnly"
          value="False" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="Container Type"
          resid="a886fbac-ad3a-43cd-a8dc-c62a3bbfe66a" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="DisplayMode"
          value="CodeOnly" />
        <placeholder
          name="FieldConfiguration"
          value="" />
        <placeholder
          name="DefaultFilter"
          value="" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="CMB"
        id="edfaaa08-d881-4883-8c21-ed96a5e0bb08"
        left="24.8"
        top="2"
        width="4"
        height="1"
        binding="HVL_INCO">
        <placeholder
          name="IsReadOnly"
          value="False" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="INCO Term"
          resid="3cc04ad7-d976-4299-aeff-829e89b00703" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="DisplayMode"
          value="CodeOnly" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="OPT"
        id="cbe4afc3-eeb3-4a42-94a1-d4b4cf69d4f5"
        left="29.4"
        top="2"
        width="4.2"
        height="1"
        binding="HVL_IsMasterHouse">
        <placeholder
          name="IsReadOnly"
          value="False" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="CMB"
        id="2161bcab-7a41-40e3-986e-28efb47d36a8"
        left="33.6"
        top="2"
        width="1.8"
        height="1"
        binding="HVL_Status">
        <placeholder
          name="IsReadOnly"
          value="False" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="DisplayMode"
          value="DescOnly" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="TBT"
        id="5233b473-6e47-4409-8275-f74ec36dc3a4"
        left="35.4"
        top="2"
        width="3"
        height="1">
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="VisibilityCondition"
          value="a0eb3fbb-7288-49d8-b0af-f81b6c657cc1" />
        <placeholder
          name="Content"
          value="Pending Status"
          resid="1d7ccd68-926f-4346-a596-267db5a0acb9" />
        <placeholder
          name="IsDefault"
          value="False" />
      </control>
      <control
        code="TBT"
        id="5b107b6d-a1c3-43d6-ae21-5be34ec8d5ea"
        left="35.4"
        top="2"
        width="3"
        height="1">
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="VisibilityCondition"
          value="1d37ab91-b2c8-4cb6-b92f-a9a6bd6e5040" />
        <placeholder
          name="Content"
          value="Open Status"
          resid="9be29d62-8cb0-4f1a-a25b-17bfc9d0d072" />
        <placeholder
          name="IsDefault"
          value="False" />
      </control>
      <control
        code="GRD"
        id="cf17ae23-1785-4085-bae2-b5225d4aebeb"
        left="0"
        top="3"
        height="4.8"
        right="0"
        binding="HVLVOuterPackages">
        <placeholder
          name="IsReadOnly"
          value="True" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="CaptionOverride"
          value="Outer Packages"
          resid="9119c9db-5fb6-481f-9040-40914e3010a6" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="HeaderAlignment"
          value="Left" />
        <placeholder
          name="HeaderDisplayMode"
          value="Visible" />
        <placeholder
          name="AllowAdd"
          value="True" />
        <placeholder
          name="AllowAttach"
          value="True" />
        <placeholder
          name="AllowDetach"
          value="True" />
        <placeholder
          name="AllowInlineEdit"
          value="True" />
        <placeholder
          name="FieldConfiguration">
          <xml>
            <fields xmlns="">
              <field
                path="HVO_PackageBarcode"
                width="250"
                mode="Default" />
              <field
                path="HVO_PackageReference"
                width="250"
                mode="Default" />
              <field
                path="HVO_F3_NKPackageType"
                width="250"
                mode="Default" />
              <field
                path="HVO_Status"
                width="250"
                mode="Default" />
              <field
                path="HVO_OH_LastMileCarrier"
                width="250"
                mode="Default" />
              <field
                path="CountAll"
                width="100"
                mode="Default" />
              <field
                path="LoadedOnConsol.JK_UniqueConsignRef"
                width="200"
                mode="Default" />
              <field
                path="HVO_ContainerNumber"
                width="200"
                mode="Default" />
              <field
                path="HVO_SystemCreateTimeUtc"
                width="180"
                mode="Optional" />
              <field
                path="HVO_SystemLastEditTimeUtc"
                width="180"
                mode="Optional" />
              <field
                path="CountClear"
                width="110"
                mode="Optional" />
              <field
                path="CountHeld"
                width="100"
                mode="Optional" />
              <field
                path="CountNonReported"
                width="180"
                mode="Optional" />
              <field
                path="HVO_Weight"
                width="100"
                mode="Default" />
              <field
                path="HVO_WeightUQ"
                width="90"
                mode="Default" />
              <field
                path="HVO_Volume"
                width="100"
                mode="Default" />
              <field
                path="HVO_VolumeUQ"
                width="90"
                mode="Default" />
              <field
                path="HVO_Length"
                width="100"
                mode="Default" />
              <field
                path="HVO_Width"
                width="100"
                mode="Default" />
              <field
                path="HVO_Height"
                width="100"
                mode="Default" />
              <field
                path="HVO_UnitOfDimension"
                width="170"
                mode="Default" />
              <field
                path="HVO_PL_NKLastMileCarrierServiceLevel"
                width="250"
                mode="Default" />
              <field
                path="HVO_RH_NKCommodityCode"
                width="350"
                mode="Default" />
            </fields>
          </xml>
        </placeholder>
        <placeholder
          name="DefaultSortFields"
          value="" />
        <placeholder
          name="ShowDocumentsIcon"
          value="True" />
        <placeholder
          name="RowActionsMode"
          value="RowAndMenu" />
        <placeholder
          name="HideGridActions"
          value="False" />
        <placeholder
          name="HideFilters"
          value="False" />
        <placeholder
          name="ActionMenuItems"
          value="" />
        <placeholder
          name="EditFormFlow"
          value="" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="GRD"
        id="a97b842f-c180-4194-b287-f3edf1242bfb"
        left="0"
        top="7.8"
        height="5.1"
        right="0"
        binding="HVLVOuterPackages/HVLVItems">
        <placeholder
          name="IsReadOnly"
          value="True" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="CaptionOverride"
          value="Items per Outer Package"
          resid="a9fff919-94d0-4413-a8d2-4e2e96a7ba55" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="HeaderAlignment"
          value="Left" />
        <placeholder
          name="HeaderDisplayMode"
          value="Visible" />
        <placeholder
          name="AllowAdd"
          value="True" />
        <placeholder
          name="AllowAttach"
          value="True" />
        <placeholder
          name="AllowDetach"
          value="True" />
        <placeholder
          name="AllowInlineEdit"
          value="True" />
        <placeholder
          name="FieldConfiguration">
          <xml>
            <fields xmlns="">
              <field
                path="HVI_ItemId"
                width="250"
                mode="Default" />
              <field
                path="HVI_CurrentBarcode"
                width="250"
                mode="Default" />
              <field
                path="HVI_ManifestedWeight"
                width="100"
                mode="Default" />
              <field
                path="HVI_ManifestedVolume"
                width="100"
                mode="Default" />
              <field
                path="HVI_ActualWeight"
                width="100"
                mode="Default" />
              <field
                path="HVI_ActualVolume"
                width="100"
                mode="Default" />
              <field
                path="HVI_F3_NKPackType"
                width="150"
                mode="Default" />
              <field
                path="HVI_Status"
                width="200"
                mode="Default" />
              <field
                path="HVI_JS_LoadedOnShipment"
                width="250"
                mode="Default" />
              <field
                path="Consignment.HVC_OH_LastMileCarrier"
                width="250"
                mode="Default" />
              <field
                path="HVI_IsActive"
                width="90"
                mode="Default" />
              <field
                path="BookingHeader"
                width="140"
                mode="Optional" />
              <field
                path="CargoWiseRef"
                width="250"
                mode="Optional" />
              <field
                path="CartageURL"
                width="250"
                mode="Optional" />
              <field
                path="HVI_HVC_Consignment"
                width="250"
                mode="Optional" />
              <field
                path="HVI_ContainerNumber"
                width="200"
                mode="Optional" />
              <field
                path="HVI_IsDamaged"
                width="70"
                mode="Optional" />
              <field
                path="DestinationDepot"
                width="250"
                mode="Optional" />
              <field
                path="GateInAtOriginDepot"
                width="230"
                mode="Optional" />
              <field
                path="HVI_GoodsDescription"
                width="250"
                mode="Optional" />
              <field
                path="HVI_Height"
                width="100"
                mode="Optional" />
              <field
                path="HVI_IsUnmanifestedAtDestination"
                width="250"
                mode="Optional" />
              <field
                path="LastInterceptEventReference"
                width="250"
                mode="Optional" />
              <field
                path="HVI_KM_LastMileTransportBooking"
                width="250"
                mode="Optional" />
              <field
                path="HVI_Length"
                width="100"
                mode="Optional" />
              <field
                path="HVI_HVL_LoadList"
                width="250"
                mode="Optional" />
              <field
                path="OriginDepot"
                width="250"
                mode="Optional" />
              <field
                path="HVI_IsPillaged"
                width="80"
                mode="Optional" />
              <field
                path="HVI_IsValidatedForUniqueness"
                width="200"
                mode="Optional" />
              <field
                path="HVI_ShipperReference"
                width="250"
                mode="Optional" />
              <field
                path="URL"
                width="250"
                mode="Optional" />
              <field
                path="HVI_IsUllaged"
                width="70"
                mode="Optional" />
              <field
                path="HVI_UnitOfDimension"
                width="40"
                mode="Optional" />
              <field
                path="HVI_Width"
                width="100"
                mode="Optional" />
              <field
                path="BookingHeader.HVH_BookingReference"
                width="200"
                mode="Optional" />
              <field
                path="Consignment.HVC_ImportCustomsClearanceStatus"
                width="250"
                mode="Optional" />
              <field
                path="Consignment.HVC_ExportCustomsClearanceStatus"
                width="250"
                mode="Optional" />
              <field
                path="Consignment.HVC_ConsignmentId"
                width="250"
                mode="Optional" />
              <field
                path="Consignment.HVC_ImportReleaseStatus"
                width="250"
                mode="Optional" />
              <field
                path="EventListHasAnyWithInterceptEvent"
                width="250"
                mode="Optional" />
              <field
                path="EventListHasAnyWithInterceptTaskCompletedEvent"
                width="250"
                mode="Optional" />
              <field
                path="EventListHasAnyWithScannedEvent"
                width="250"
                mode="Optional" />
              <field
                path="Consignment.HVC_ShipperName"
                width="250"
                mode="Optional" />
              <field
                path="Consignment.HVC_ConsigneeName"
                width="250"
                mode="Optional" />
              <field
                path="Consignment.HVC_OA_ConsigneeAddress"
                width="250"
                mode="Optional" />
              <field
                path="Consignment.HVC_OA_ShipperAddress"
                width="250"
                mode="Optional" />
              <field
                path="Consignment.HVC_GoodsValue"
                width="250"
                mode="Optional" />
              <field
                path="Consignment.HVC_RN_NKConsigneeCountryCode"
                width="250"
                mode="Optional" />
              <field
                path="Consignment.HVC_JE_ImportDeclaration"
                width="250"
                mode="Optional" />
              <field
                path="Consignment.HVC_JE_ExportDeclaration"
                width="250"
                mode="Optional" />
              <field
                path="Consignment.HVC_RX_NKGoodsValueCurrency"
                width="250"
                mode="Optional" />
              <field
                path="Consignment.HVC_OH_LastMileCarrierBookingAgent"
                width="250"
                mode="Optional" />
              <field
                path="Consignment.Header.HCH_JS_Shipment"
                width="250"
                mode="Optional" />
              <field
                path="Consignment.HVC_RN_NKShipperCountryCode"
                width="250"
                mode="Optional" />
              <field
                path="Consignment.HVC_CarrierAccountNumber"
                width="250"
                mode="Optional" />
              <field
                path="Consignment.CarrierServiceCode"
                width="250"
                mode="Optional" />
              <field
                path="Consignment.HVC_ConsigneeAddress1"
                width="250"
                mode="Optional" />
              <field
                path="Consignment.HVC_ConsigneeAddress2"
                width="250"
                mode="Optional" />
              <field
                path="Consignment.HVC_ConsigneeAddressValidationStatus"
                width="250"
                mode="Optional" />
              <field
                path="Consignment.HVC_ConsigneeCity"
                width="250"
                mode="Optional" />
              <field
                path="Consignment.HVC_ConsigneeContact"
                width="250"
                mode="Optional" />
              <field
                path="Consignment.HVC_ConsigneeEmail"
                width="250"
                mode="Optional" />
              <field
                path="Consignment.HVC_ConsigneeFax"
                width="250"
                mode="Optional" />
              <field
                path="Consignment.HVC_ConsigneeInstructions"
                width="250"
                mode="Optional" />
              <field
                path="Consignment.HVC_ConsigneeMobile"
                width="250"
                mode="Optional" />
              <field
                path="Consignment.HVC_ConsigneePhone"
                width="250"
                mode="Optional" />
              <field
                path="Consignment.HVC_ConsigneePostcode"
                width="250"
                mode="Optional" />
              <field
                path="Consignment.HVC_ConsigneeState"
                width="250"
                mode="Optional" />
              <field
                path="Consignment.HVC_GoodsDescription"
                width="250"
                mode="Optional" />
              <field
                path="Consignment.HVC_INCO"
                width="180"
                mode="Optional" />
              <field
                path="Consignment.HVC_PL_NKLastMileCarrierServiceLevel"
                width="250"
                mode="Optional" />
              <field
                path="Consignment.HVC_PreScreeningStatus"
                width="250"
                mode="Optional" />
              <field
                path="Consignment.HVC_ShipperAddress1"
                width="250"
                mode="Optional" />
              <field
                path="Consignment.HVC_ShipperAddress2"
                width="250"
                mode="Optional" />
              <field
                path="Consignment.HVC_ShipperCity"
                width="250"
                mode="Optional" />
              <field
                path="Consignment.HVC_ShipperEmail"
                width="250"
                mode="Optional" />
              <field
                path="Consignment.HVC_ShipperFax"
                width="250"
                mode="Optional" />
              <field
                path="Consignment.HVC_ShipperMobile"
                width="250"
                mode="Optional" />
              <field
                path="Consignment.HVC_ShipperContact"
                width="250"
                mode="Optional" />
              <field
                path="Consignment.HVC_ShipperPhone"
                width="250"
                mode="Optional" />
              <field
                path="Consignment.HVC_ShipperPostcode"
                width="250"
                mode="Optional" />
              <field
                path="Consignment.HVC_ShipperReference"
                width="250"
                mode="Optional" />
              <field
                path="Consignment.HVC_ShipperState"
                width="250"
                mode="Optional" />
              <field
                path="Consignment.HVC_Status"
                width="200"
                mode="Optional" />
              <field
                path="Consignment.HVC_UndgClass"
                width="240"
                mode="Optional" />
              <field
                path="Consignment.HVC_VendorIdentifier"
                width="250"
                mode="Optional" />
              <field
                path="Consignment.HVC_WaybillNumber"
                width="250"
                mode="Optional" />
              <field
                path="Consignment.HVC_AuthorityToLeave"
                width="250"
                mode="Optional" />
              <field
                path="Consignment.HVC_IsHazardous"
                width="230"
                mode="Optional" />
              <field
                path="Consignment.HVC_IsActive"
                width="230"
                mode="Optional" />
              <field
                path="Consignment.HVC_IsSignatureRequired"
                width="250"
                mode="Optional" />
              <field
                path="Consignment.HVC_IsTaxPrePaid"
                width="250"
                mode="Optional" />
              <field
                path="Consignment.IsTranshipment"
                width="250"
                mode="Optional" />
              <field
                path="Consignment.HVC_IsPerishable"
                width="240"
                mode="Optional" />
              <field
                path="Consignment.HVC_IsPersonalEffects"
                width="250"
                mode="Optional" />
              <field
                path="Consignment.HVC_IsValidatedForUniqueness"
                width="250"
                mode="Optional" />
              <field
                path="Consignment.HVC_RequiresFumigation"
                width="250"
                mode="Optional" />
              <field
                path="Consignment.HVC_IsSelfBooked"
                width="250"
                mode="Optional" />
              <field
                path="Consignment.HVC_IsTimber"
                width="200"
                mode="Optional" />
              <field
                path="ItemHasBeenScanned"
                width="210"
                mode="Optional" />
              <field
                path="HVI_IsScannedAtDestination"
                width="100"
                mode="Optional" />
              <field
                path="HVI_ImportReleaseStatus"
                width="100"
                mode="Optional" />
              <field
                path="Consignment.HVC_OA_DestinationDepot"
                width="250"
                mode="Optional" />
              <field
                path="Consignment.HVC_SystemCreateTimeUtc"
                width="250"
                mode="Optional" />
              <field
                path="Consignment.HVC_SystemLastEditTimeUtc"
                width="250"
                mode="Optional" />
              <field
                path="Consignment.HVC_ActualVolumeMeasure"
                width="130"
                mode="Optional" />
              <field
                path="Consignment.HVC_ActualWeightMeasure"
                width="130"
                mode="Optional" />
              <field
                path="Consignment.HVC_ManifestedVolumeMeasure"
                width="130"
                mode="Optional" />
              <field
                path="Consignment.HVC_ManifestedWeightMeasure"
                width="130"
                mode="Optional" />
              <field
                path="Consignment.HVC_ItemCount"
                width="250"
                mode="Optional" />
              <field
                path="Consignment.HVC_HVH_BookingHeader"
                width="250"
                mode="Optional" />
              <field
                path="Consignment.HVC_SystemLastEditUser"
                width="250"
                mode="Optional" />
              <field
                path="ShipmentETailer"
                width="250"
                mode="Default" />
              <field
                path="HVI_CarrierBookingStatus"
                width="220"
                mode="Optional" />
              <field
                path="VolumeWeight"
                width="250"
                mode="Optional" />
              <field
                path="Chargeable"
                width="250"
                mode="Optional" />
              <field
                path="DensityFactor"
                width="250"
                mode="Optional" />
            </fields>
          </xml>
        </placeholder>
        <placeholder
          name="DefaultSortFields"
          value="" />
        <placeholder
          name="ShowDocumentsIcon"
          value="True" />
        <placeholder
          name="RowActionsMode"
          value="RowAndMenu" />
        <placeholder
          name="HideGridActions"
          value="False" />
        <placeholder
          name="HideFilters"
          value="False" />
        <placeholder
          name="ActionMenuItems"
          value="" />
        <placeholder
          name="EditFormFlow"
          value="" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="GRD"
        id="58078e5e-3762-4203-bc2e-ddd9dbc5744f"
        left="0"
        top="12.9"
        right="0"
        bottom="0"
        binding="HVLVItems">
        <placeholder
          name="IsReadOnly"
          value="True" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="CaptionOverride"
          value="Total Items"
          resid="af6052d9-7c41-4513-8b5a-6cf0919a73e6" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="HeaderAlignment"
          value="Left" />
        <placeholder
          name="HeaderDisplayMode"
          value="Visible" />
        <placeholder
          name="AllowAdd"
          value="True" />
        <placeholder
          name="AllowAttach"
          value="True" />
        <placeholder
          name="AllowDetach"
          value="True" />
        <placeholder
          name="AllowInlineEdit"
          value="True" />
        <placeholder
          name="FieldConfiguration">
          <xml>
            <fields xmlns="">
              <field
                path="HVI_ItemId"
                width="250"
                mode="Default" />
              <field
                path="HVI_CurrentBarcode"
                width="250"
                mode="Default" />
              <field
                path="HVI_ManifestedWeight"
                width="100"
                mode="Default" />
              <field
                path="HVI_ManifestedVolume"
                width="100"
                mode="Default" />
              <field
                path="HVI_ActualWeight"
                width="100"
                mode="Default" />
              <field
                path="HVI_ActualVolume"
                width="100"
                mode="Default" />
              <field
                path="HVI_F3_NKPackType"
                width="150"
                mode="Default" />
              <field
                path="HVI_Status"
                width="200"
                mode="Default" />
              <field
                path="HVI_JS_LoadedOnShipment"
                width="250"
                mode="Default" />
              <field
                path="Consignment.HVC_OH_LastMileCarrier"
                width="250"
                mode="Default" />
              <field
                path="HVI_IsActive"
                width="90"
                mode="Default" />
              <field
                path="BookingHeader"
                width="140"
                mode="Optional" />
              <field
                path="CargoWiseRef"
                width="250"
                mode="Optional" />
              <field
                path="CartageURL"
                width="250"
                mode="Optional" />
              <field
                path="HVI_HVC_Consignment"
                width="250"
                mode="Optional" />
              <field
                path="HVI_ContainerNumber"
                width="200"
                mode="Optional" />
              <field
                path="HVI_IsDamaged"
                width="70"
                mode="Optional" />
              <field
                path="DestinationDepot"
                width="250"
                mode="Optional" />
              <field
                path="GateInAtOriginDepot"
                width="230"
                mode="Optional" />
              <field
                path="HVI_GoodsDescription"
                width="250"
                mode="Optional" />
              <field
                path="HVI_Height"
                width="100"
                mode="Optional" />
              <field
                path="HVI_IsUnmanifestedAtDestination"
                width="250"
                mode="Optional" />
              <field
                path="LastInterceptEventReference"
                width="250"
                mode="Optional" />
              <field
                path="HVI_KM_LastMileTransportBooking"
                width="250"
                mode="Optional" />
              <field
                path="HVI_Length"
                width="100"
                mode="Optional" />
              <field
                path="OriginDepot"
                width="250"
                mode="Optional" />
              <field
                path="HVI_HVO_OuterPackage"
                width="250"
                mode="Optional" />
              <field
                path="HVI_IsPillaged"
                width="80"
                mode="Optional" />
              <field
                path="HVI_IsValidatedForUniqueness"
                width="200"
                mode="Optional" />
              <field
                path="HVI_ShipperReference"
                width="250"
                mode="Optional" />
              <field
                path="URL"
                width="250"
                mode="Optional" />
              <field
                path="HVI_IsUllaged"
                width="70"
                mode="Optional" />
              <field
                path="HVI_UnitOfDimension"
                width="40"
                mode="Optional" />
              <field
                path="HVI_Width"
                width="100"
                mode="Optional" />
              <field
                path="BookingHeader.HVH_BookingReference"
                width="200"
                mode="Optional" />
              <field
                path="Consignment.HVC_ImportCustomsClearanceStatus"
                width="250"
                mode="Optional" />
              <field
                path="Consignment.HVC_ExportCustomsClearanceStatus"
                width="250"
                mode="Optional" />
              <field
                path="Consignment.HVC_ConsignmentId"
                width="250"
                mode="Optional" />
              <field
                path="Consignment.HVC_ImportReleaseStatus"
                width="250"
                mode="Optional" />
              <field
                path="EventListHasAnyWithInterceptEvent"
                width="250"
                mode="Optional" />
              <field
                path="EventListHasAnyWithInterceptTaskCompletedEvent"
                width="250"
                mode="Optional" />
              <field
                path="EventListHasAnyWithScannedEvent"
                width="250"
                mode="Optional" />
              <field
                path="Consignment.HVC_ShipperName"
                width="250"
                mode="Optional" />
              <field
                path="Consignment.HVC_ConsigneeName"
                width="250"
                mode="Optional" />
              <field
                path="Consignment.HVC_OA_ConsigneeAddress"
                width="250"
                mode="Optional" />
              <field
                path="Consignment.HVC_OA_ShipperAddress"
                width="250"
                mode="Optional" />
              <field
                path="Consignment.HVC_GoodsValue"
                width="250"
                mode="Optional" />
              <field
                path="Consignment.HVC_RN_NKConsigneeCountryCode"
                width="250"
                mode="Optional" />
              <field
                path="Consignment.HVC_JE_ImportDeclaration"
                width="250"
                mode="Optional" />
              <field
                path="Consignment.HVC_JE_ExportDeclaration"
                width="250"
                mode="Optional" />
              <field
                path="Consignment.HVC_RX_NKGoodsValueCurrency"
                width="250"
                mode="Optional" />
              <field
                path="Consignment.HVC_OH_LastMileCarrierBookingAgent"
                width="250"
                mode="Optional" />
              <field
                path="Consignment.Header.HCH_JS_Shipment"
                width="250"
                mode="Optional" />
              <field
                path="Consignment.HVC_RN_NKShipperCountryCode"
                width="250"
                mode="Optional" />
              <field
                path="Consignment.HVC_CarrierAccountNumber"
                width="250"
                mode="Optional" />
              <field
                path="Consignment.CarrierServiceCode"
                width="250"
                mode="Optional" />
              <field
                path="Consignment.HVC_ConsigneeAddress1"
                width="250"
                mode="Optional" />
              <field
                path="Consignment.HVC_ConsigneeAddress2"
                width="250"
                mode="Optional" />
              <field
                path="Consignment.HVC_ConsigneeAddressValidationStatus"
                width="250"
                mode="Optional" />
              <field
                path="Consignment.HVC_ConsigneeCity"
                width="250"
                mode="Optional" />
              <field
                path="Consignment.HVC_ConsigneeContact"
                width="250"
                mode="Optional" />
              <field
                path="Consignment.HVC_ConsigneeEmail"
                width="250"
                mode="Optional" />
              <field
                path="Consignment.HVC_ConsigneeFax"
                width="250"
                mode="Optional" />
              <field
                path="Consignment.HVC_ConsigneeInstructions"
                width="250"
                mode="Optional" />
              <field
                path="Consignment.HVC_ConsigneeMobile"
                width="250"
                mode="Optional" />
              <field
                path="Consignment.HVC_ConsigneePhone"
                width="250"
                mode="Optional" />
              <field
                path="Consignment.HVC_ConsigneePostcode"
                width="250"
                mode="Optional" />
              <field
                path="Consignment.HVC_ConsigneeState"
                width="250"
                mode="Optional" />
              <field
                path="Consignment.HVC_GoodsDescription"
                width="250"
                mode="Optional" />
              <field
                path="Consignment.HVC_INCO"
                width="180"
                mode="Optional" />
              <field
                path="Consignment.HVC_PL_NKLastMileCarrierServiceLevel"
                width="250"
                mode="Optional" />
              <field
                path="Consignment.HVC_PreScreeningStatus"
                width="250"
                mode="Optional" />
              <field
                path="Consignment.HVC_ShipperAddress1"
                width="250"
                mode="Optional" />
              <field
                path="Consignment.HVC_ShipperAddress2"
                width="250"
                mode="Optional" />
              <field
                path="Consignment.HVC_ShipperCity"
                width="250"
                mode="Optional" />
              <field
                path="Consignment.HVC_ShipperEmail"
                width="250"
                mode="Optional" />
              <field
                path="Consignment.HVC_ShipperFax"
                width="250"
                mode="Optional" />
              <field
                path="Consignment.HVC_ShipperMobile"
                width="250"
                mode="Optional" />
              <field
                path="Consignment.HVC_ShipperContact"
                width="250"
                mode="Optional" />
              <field
                path="Consignment.HVC_ShipperPhone"
                width="250"
                mode="Optional" />
              <field
                path="Consignment.HVC_ShipperPostcode"
                width="250"
                mode="Optional" />
              <field
                path="Consignment.HVC_ShipperReference"
                width="250"
                mode="Optional" />
              <field
                path="Consignment.HVC_ShipperState"
                width="250"
                mode="Optional" />
              <field
                path="Consignment.HVC_Status"
                width="200"
                mode="Optional" />
              <field
                path="Consignment.HVC_UndgClass"
                width="240"
                mode="Optional" />
              <field
                path="Consignment.HVC_VendorIdentifier"
                width="250"
                mode="Optional" />
              <field
                path="Consignment.HVC_WaybillNumber"
                width="250"
                mode="Optional" />
              <field
                path="Consignment.HVC_AuthorityToLeave"
                width="250"
                mode="Optional" />
              <field
                path="Consignment.HVC_IsHazardous"
                width="230"
                mode="Optional" />
              <field
                path="Consignment.HVC_IsActive"
                width="230"
                mode="Optional" />
              <field
                path="Consignment.HVC_IsSignatureRequired"
                width="250"
                mode="Optional" />
              <field
                path="Consignment.HVC_IsTaxPrePaid"
                width="250"
                mode="Optional" />
              <field
                path="Consignment.IsTranshipment"
                width="250"
                mode="Optional" />
              <field
                path="Consignment.HVC_IsPerishable"
                width="240"
                mode="Optional" />
              <field
                path="Consignment.HVC_IsPersonalEffects"
                width="250"
                mode="Optional" />
              <field
                path="Consignment.HVC_IsValidatedForUniqueness"
                width="250"
                mode="Optional" />
              <field
                path="Consignment.HVC_RequiresFumigation"
                width="250"
                mode="Optional" />
              <field
                path="Consignment.HVC_IsSelfBooked"
                width="250"
                mode="Optional" />
              <field
                path="Consignment.HVC_IsTimber"
                width="200"
                mode="Optional" />
              <field
                path="ItemHasBeenScanned"
                width="210"
                mode="Optional" />
              <field
                path="HVI_IsScannedAtDestination"
                width="100"
                mode="Optional" />
              <field
                path="HVI_ImportReleaseStatus"
                width="100"
                mode="Optional" />
              <field
                path="Consignment.HVC_OA_DestinationDepot"
                width="250"
                mode="Optional" />
              <field
                path="Consignment.HVC_SystemCreateTimeUtc"
                width="250"
                mode="Optional" />
              <field
                path="Consignment.HVC_SystemLastEditTimeUtc"
                width="250"
                mode="Optional" />
              <field
                path="Consignment.HVC_ActualVolumeMeasure"
                width="130"
                mode="Optional" />
              <field
                path="Consignment.HVC_ActualWeightMeasure"
                width="130"
                mode="Optional" />
              <field
                path="Consignment.HVC_ManifestedVolumeMeasure"
                width="130"
                mode="Optional" />
              <field
                path="Consignment.HVC_ManifestedWeightMeasure"
                width="130"
                mode="Optional" />
              <field
                path="Consignment.HVC_ItemCount"
                width="250"
                mode="Optional" />
              <field
                path="Consignment.HVC_HVH_BookingHeader"
                width="250"
                mode="Optional" />
              <field
                path="Consignment.HVC_SystemLastEditUser"
                width="250"
                mode="Optional" />
              <field
                path="ShipmentETailer"
                width="250"
                mode="Default" />
              <field
                path="HVI_CarrierBookingStatus"
                width="220"
                mode="Optional" />
              <field
                path="VolumeWeight"
                width="250"
                mode="Optional" />
              <field
                path="Chargeable"
                width="250"
                mode="Optional" />
              <field
                path="DensityFactor"
                width="250"
                mode="Optional" />
            </fields>
          </xml>
        </placeholder>
        <placeholder
          name="DefaultSortFields"
          value="" />
        <placeholder
          name="ShowDocumentsIcon"
          value="True" />
        <placeholder
          name="RowActionsMode"
          value="RowAndMenu" />
        <placeholder
          name="HideGridActions"
          value="False" />
        <placeholder
          name="HideFilters"
          value="False" />
        <placeholder
          name="ActionMenuItems"
          value="" />
        <placeholder
          name="EditFormFlow"
          value="" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
    </control>
  </form>
