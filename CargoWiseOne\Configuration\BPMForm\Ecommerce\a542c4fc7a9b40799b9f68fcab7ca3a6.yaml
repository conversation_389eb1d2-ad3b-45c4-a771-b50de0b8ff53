#transformationVersion: 70.0
#
VZ_PK: a542c4fc7a9b40799b9f68fcab7ca3a6
VZ_ConfigurationKey: a542c4fc-7a9b-4079-9b9f-68fcab7ca3a6
VZ_FormID: ETL - VDV3 - Scan Item Page with Button
VZ_Caption:
  resKey: VZ_Caption|a542c4fc-7a9b-4079-9b9f-68fcab7ca3a6
  text: Scan Item
VZ_FormFactor: DSK
VZ_FormType: PAG
VZ_RequiresLogin: true
VZ_Dependencies: <dependencies xmlns="http://cargowise.com/glow/2014/11/14/dependencies.xsd" />
VZ_FormData: >-
  <form
    id="78b01ced-df93-4581-b206-19ed66e8b616" xmlns="http://wisetechglobal.com/glow/2017/05/26/form.xsd">
    <placeholder
      name="BackgroundImagePk"
      value="" />
    <placeholder
      name="BackgroundSize"
      value="Auto" />
    <placeholder
      name="BackgroundImageOverride"
      value="" />
    <placeholder
      name="FormLayout"
      value="Splitview" />
    <placeholder
      name="ExpandPaths"
      value="" />
    <placeholder
      name="ScanInputHandling"
      value="NON" />
    <placeholder
      name="RemovePadding"
      value="false" />
    <placeholder
      name="TurnOnPortableOneToOneDesignMode"
      value="false" />
    <placeholder
      name="ContainsDefaultButton"
      value="False" />
    <control
      code="GRP"
      id="75e6b73d-475e-440f-94a6-a63dc6ab5727"
      width="26"
      height="14">
      <placeholder
        name="CanBeHidden"
        value="True" />
      <placeholder
        name="VisibilityCondition"
        value="" />
      <placeholder
        name="Header"
        value="New Section"
        resid="e3afe32f-01d0-420b-bd6f-90d987bed01e" />
      <placeholder
        name="HeaderAlignment"
        value="Left" />
      <placeholder
        name="HeaderDisplayMode"
        value="Hidden" />
      <placeholder
        name="BackgroundImagePk"
        value="" />
      <placeholder
        name="BackgroundImageOverride"
        value="" />
      <control
        code="FLB"
        id="f7e30c07-9eb7-4948-81bc-078158e82b9f"
        left="9"
        top="11"
        width="8"
        height="1">
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="LabelText"
          value="Please Scan Next Item"
          resid="7950e9db-df21-48ec-9caa-d71f457db0a7" />
        <placeholder
          name="LabelType"
          value="NormalLabel" />
        <placeholder
          name="LabelNotificationType"
          value="Information" />
        <placeholder
          name="ForegroundThemeColor"
          value="DefaultButton" />
        <placeholder
          name="TextSize"
          value="Medium" />
        <placeholder
          name="FontWeight"
          value="Bold" />
        <placeholder
          name="LabelAlignment"
          value="Center" />
        <placeholder
          name="BackgroundColor"
          value="" />
      </control>
      <control
        code="TBT"
        id="577b1c8f-2bf7-4a08-823b-e747f1de8a65"
        left="8.8"
        top="12"
        width="8.5"
        height="1">
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="Content"
          value="Edit Last Scanned Item"
          resid="97180a7d-efe0-4c3b-87f6-caf584853a2e" />
        <placeholder
          name="IsDefault"
          value="False" />
      </control>
      <control
        code="FFI"
        id="d4090e5d-8eaa-4557-89f5-2046588194b7"
        width="9"
        height="1"
        bottom="3">
        <placeholder
          name="CanBeHidden"
          value="False" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="Barcode"
          resid="c38d6744-12b9-444e-9e55-fe41a4e2074c" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="EntityType"
          value="IHVLVItem" />
        <placeholder
          name="DefaultFilter">
          <xml>
            <ArrayOfFilterGroup xmlns:a="http://schemas.microsoft.com/2003/10/Serialization/Arrays" xmlns:i="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://cargowise.com/glow/2014/07/16/filters.xsd">
              <FilterGroup>
                <Filters>
                  <Filter>
                    <FilterType>IsActiveFilter</FilterType>
                    <Operation>Is</Operation>
                    <PropertyPath>HVI_IsActive</PropertyPath>
                    <Values>
                      <a:string>true</a:string>
                    </Values>
                  </Filter>
                </Filters>
                <IsImplicit>false</IsImplicit>
              </FilterGroup>
            </ArrayOfFilterGroup>
          </xml>
        </placeholder>
        <placeholder
          name="ItemTemplateID"
          value="" />
        <placeholder
          name="FormFlowPK"
          value="" />
        <placeholder
          name="ItemPosition"
          value="Top" />
      </control>
    </control>
  </form>
