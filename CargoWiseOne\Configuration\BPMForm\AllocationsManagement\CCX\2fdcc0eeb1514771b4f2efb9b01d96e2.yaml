#transformationVersion: 70.0
#
VZ_PK: 2fdcc0eeb1514771b4f2efb9b01d96e2
VZ_ConfigurationKey: 2fdcc0ee-b151-4771-b4f2-efb9b01d96e2
VZ_FormID: CCX - Allocate Consolidations & Containers (Row Action)
VZ_Caption:
  resKey: VZ_Caption|2fdcc0ee-b151-4771-b4f2-efb9b01d96e2
  text: Allocate Consolidations & Containers
VZ_FormFactor: DSK
VZ_EntityType: IRatingContractAllocationLine
VZ_FormType: FRM
VZ_RequiresLogin: true
VZ_ExtenderFunc: CargoWise.Glow.Business.Forwarding.Contracts.AllocateConsolidationsAndContainersFormExtender.AllocateConsolidationsAndContainers
VZ_Dependencies: <dependencies xmlns="http://cargowise.com/glow/2014/11/14/dependencies.xsd" />
VZ_FormData: >-
  <form
    id="490cf370-2e77-4e23-b718-067a2139210b" xmlns="http://wisetechglobal.com/glow/2017/05/26/form.xsd">
    <placeholder
      name="BackgroundImagePk"
      value="" />
    <placeholder
      name="BackgroundSize"
      value="Auto" />
    <placeholder
      name="BackgroundImageOverride"
      value="" />
    <placeholder
      name="FormLayout"
      value="Splitview" />
    <placeholder
      name="ExpandPaths"
      value="" />
    <placeholder
      name="ScanInputHandling"
      value="NON" />
    <placeholder
      name="RemovePadding"
      value="false" />
    <placeholder
      name="TurnOnPortableOneToOneDesignMode"
      value="false" />
    <placeholder
      name="ContainsDefaultButton"
      value="False" />
    <control
      code="SRL"
      id="87f1e8e7-9ff1-4d71-8799-d76ff91d081b"
      left="0"
      top="0"
      height="3"
      right="0">
      <placeholder
        name="CanBeHidden"
        value="True" />
      <placeholder
        name="IsInitialFocus"
        value="False" />
      <placeholder
        name="CaptionOverride"
        value="Selected Allocation Route"
        resid="fdc78079-4d98-458a-a260-e29cf5137862" />
      <placeholder
        name="VisibilityCondition"
        value="" />
      <placeholder
        name="HeaderAlignment"
        value="Left" />
      <placeholder
        name="HeaderDisplayMode"
        value="Visible" />
      <placeholder
        name="FilterType"
        value="Entity" />
      <placeholder
        name="EntityType"
        value="IRatingContractAllocationLine" />
      <placeholder
        name="ItemDisplayPath"
        value="" />
      <placeholder
        name="DisabledGridRowActions"
        value="" />
      <placeholder
        name="DefaultFilter">
        <xml>
          <ArrayOfFilterGroup xmlns:a="http://schemas.microsoft.com/2003/10/Serialization/Arrays" xmlns:i="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://cargowise.com/glow/2014/07/16/filters.xsd">
            <FilterGroup>
              <Filters>
                <Filter>
                  <FilterType>StringFilter</FilterType>
                  <Operation>Is</Operation>
                  <PropertyPath>RCA_AllocationLineID</PropertyPath>
                  <Values>
                    <a:string>&lt;RCA_AllocationLineID&gt;</a:string>
                  </Values>
                </Filter>
                <Filter>
                  <FilterType>AdvancedGuidLookupFilter</FilterType>
                  <Operation>Is</Operation>
                  <PropertyPath>RCA_RCT_RatingContract</PropertyPath>
                  <Values>
                    <a:string>&lt;RCA_RCT_RatingContract&gt;</a:string>
                  </Values>
                </Filter>
              </Filters>
              <IsImplicit>true</IsImplicit>
            </FilterGroup>
          </ArrayOfFilterGroup>
        </xml>
      </placeholder>
      <placeholder
        name="DefaultFilterPropertyPath"
        value="" />
      <placeholder
        name="FieldConfiguration"
        value="" />
      <placeholder
        name="DefaultSortFields"
        value="" />
      <placeholder
        name="DefaultGroupFields"
        value="" />
      <placeholder
        name="EnableGrouping"
        value="False" />
      <placeholder
        name="AutoRefreshInterval"
        value="0" />
      <placeholder
        name="AllowMultiSelect"
        value="True" />
      <placeholder
        name="ShowWorkflowLabel"
        value="True" />
      <placeholder
        name="RowActionsMode"
        value="None" />
      <placeholder
        name="HideGridActions"
        value="True" />
      <placeholder
        name="MinimizeFilters"
        value="False" />
      <placeholder
        name="WorkflowOpenInNewWindow"
        value="False" />
      <placeholder
        name="SearchOnInit"
        value="True" />
      <placeholder
        name="ShowQuickSearch"
        value="False" />
      <placeholder
        name="QuickSearchCaption"
        value="" />
      <placeholder
        name="ValueField"
        value="" />
      <placeholder
        name="Category"
        value="" />
      <placeholder
        name="ActionMenuItems"
        value="" />
      <placeholder
        name="EditFormFlow"
        value="" />
      <placeholder
        name="ResetFilterOnInit"
        value="True" />
      <placeholder
        name="FilterDisplayMode"
        value="Hidden" />
    </control>
    <control
      code="SRL"
      id="3ec8111f-631f-4bc9-985d-f812a06bedf2"
      left="0"
      top="3.5"
      right="0"
      bottom="1">
      <placeholder
        name="CanBeHidden"
        value="True" />
      <placeholder
        name="IsInitialFocus"
        value="False" />
      <placeholder
        name="CaptionOverride"
        value="Search Consolidations with/without Containers for Allocation"
        resid="7dca5d9f-9199-439b-9f66-226203f4c038" />
      <placeholder
        name="VisibilityCondition"
        value="" />
      <placeholder
        name="HeaderAlignment"
        value="Left" />
      <placeholder
        name="HeaderDisplayMode"
        value="Visible" />
      <placeholder
        name="FilterType"
        value="Entity" />
      <placeholder
        name="EntityType"
        value="IJobConsolAndContainer" />
      <placeholder
        name="ItemDisplayPath"
        value="" />
      <placeholder
        name="DisabledGridRowActions"
        value="" />
      <placeholder
        name="DefaultFilter">
        <xml>
          <ArrayOfFilterGroup xmlns:a="http://schemas.microsoft.com/2003/10/Serialization/Arrays" xmlns:i="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://cargowise.com/glow/2014/07/16/filters.xsd">
            <FilterGroup>
              <Filters>
                <Filter>
                  <FilterType>StringFilter</FilterType>
                  <Operation>Is</Operation>
                  <PropertyPath>JCK_TransportMode</PropertyPath>
                  <Values>
                    <a:string>&lt;RatingContract.RCT_TransportMode&gt;</a:string>
                  </Values>
                </Filter>
                <Filter>
                  <FilterType>AdvancedGuidLookupFilter</FilterType>
                  <Operation>Is</Operation>
                  <PropertyPath>JobConsol.ShippingLineAddress.OA_OH</PropertyPath>
                  <Values>
                    <a:string>&lt;RatingContract.RCT_OH&gt;</a:string>
                  </Values>
                </Filter>
                <Filter>
                  <FilterType>DateTimeFilter</FilterType>
                  <Operation>IsInTheDateRange</Operation>
                  <PropertyPath>JobConsol.TransportRoutings.JW_ETD</PropertyPath>
                  <Values>
                    <a:string>&lt;StartDateWithContractFallback&gt;</a:string>
                    <a:string>&lt;ExpiryDateWithContractFallback&gt;</a:string>
                  </Values>
                </Filter>
                <Filter>
                  <FilterType>StringFilter</FilterType>
                  <Operation>Is</Operation>
                  <PropertyPath>JCK_LoadPort</PropertyPath>
                  <Values>
                    <a:string>&lt;LoadLocationWithProxy&gt;</a:string>
                  </Values>
                </Filter>
                <Filter>
                  <FilterType>StringFilter</FilterType>
                  <Operation>Is</Operation>
                  <PropertyPath>JCK_DischargePort</PropertyPath>
                  <Values>
                    <a:string>&lt;DischargeLocationWithProxy&gt;</a:string>
                  </Values>
                </Filter>
                <Filter>
                  <FilterType>StringFilter</FilterType>
                  <Operation>Is</Operation>
                  <PropertyPath>JobConsol.TransportRoutings.JW_Vessel</PropertyPath>
                  <Values>
                    <a:string>&lt;VesselWithProxy&gt;</a:string>
                  </Values>
                </Filter>
                <Filter>
                  <FilterType>StringFilter</FilterType>
                  <Operation>Is</Operation>
                  <PropertyPath>JobConsol.TransportRoutings.JW_VoyageFlight</PropertyPath>
                  <Values>
                    <a:string>&lt;VoyageWithProxy&gt;</a:string>
                  </Values>
                </Filter>
                <Filter>
                  <FilterType>AdvancedGuidLookupFilter</FilterType>
                  <Operation>Is</Operation>
                  <PropertyPath>JobContainer.JC_RC</PropertyPath>
                  <Values>
                    <a:string>&lt;RCA_RC_ContainerType&gt;</a:string>
                  </Values>
                </Filter>
                <Filter>
                  <FilterType>StringFilter</FilterType>
                  <Operation>IsBlank</Operation>
                  <PropertyPath>ContainerAllocationLine.RCA_AllocationLineID</PropertyPath>
                  <Values />
                </Filter>
                <Filter>
                  <FilterType>StringFilter</FilterType>
                  <Operation>IsBlank</Operation>
                  <PropertyPath>ConsolAllocationLine.RCA_AllocationLineID</PropertyPath>
                  <Values />
                </Filter>
              </Filters>
              <IsImplicit>false</IsImplicit>
            </FilterGroup>
            <FilterGroup>
              <Filters>
                <Filter>
                  <FilterType>StringFilter</FilterType>
                  <Operation>IsBlank</Operation>
                  <PropertyPath>JCK_ContractNumber</PropertyPath>
                  <Values />
                </Filter>
              </Filters>
              <IsImplicit>true</IsImplicit>
            </FilterGroup>
            <FilterGroup>
              <Filters>
                <Filter>
                  <FilterType>StringFilter</FilterType>
                  <Operation>Is</Operation>
                  <PropertyPath>JCK_ContractNumber</PropertyPath>
                  <Values>
                    <a:string>&lt;RatingContract.RCT_ContractNumber&gt;</a:string>
                  </Values>
                </Filter>
              </Filters>
              <IsImplicit>true</IsImplicit>
            </FilterGroup>
          </ArrayOfFilterGroup>
        </xml>
      </placeholder>
      <placeholder
        name="DefaultFilterPropertyPath"
        value="" />
      <placeholder
        name="FieldConfiguration"
        value="" />
      <placeholder
        name="DefaultSortFields"
        value="" />
      <placeholder
        name="DefaultGroupFields"
        value="" />
      <placeholder
        name="EnableGrouping"
        value="False" />
      <placeholder
        name="AutoRefreshInterval"
        value="0" />
      <placeholder
        name="AllowMultiSelect"
        value="True" />
      <placeholder
        name="ShowWorkflowLabel"
        value="True" />
      <placeholder
        name="RowActionsMode"
        value="None" />
      <placeholder
        name="HideGridActions"
        value="False" />
      <placeholder
        name="MinimizeFilters"
        value="False" />
      <placeholder
        name="WorkflowOpenInNewWindow"
        value="False" />
      <placeholder
        name="SearchOnInit"
        value="False" />
      <placeholder
        name="ShowQuickSearch"
        value="False" />
      <placeholder
        name="QuickSearchCaption"
        value="" />
      <placeholder
        name="ValueField"
        value="" />
      <placeholder
        name="Category"
        value="" />
      <placeholder
        name="ActionMenuItems"
        value="" />
      <placeholder
        name="EditFormFlow"
        value="" />
      <placeholder
        name="ResetFilterOnInit"
        value="True" />
      <placeholder
        name="FilterDisplayMode"
        value="InlineAndPopup" />
    </control>
    <control
      code="TBT"
      id="fb65f468-9f63-4354-b068-19756804ecd9"
      width="5"
      height="1"
      right="10"
      bottom="0">
      <placeholder
        name="CanBeHidden"
        value="True" />
      <placeholder
        name="IsInitialFocus"
        value="False" />
      <placeholder
        name="VisibilityCondition"
        value="" />
      <placeholder
        name="Content"
        value="Allocate Consol(s) to Contract"
        resid="685d3af9-671a-4fd2-b676-8a79b5af0488" />
      <placeholder
        name="IsDefault"
        value="False" />
    </control>
    <control
      code="TBT"
      id="79654142-5488-4a5f-8576-ebab0fda649c"
      width="5"
      height="1"
      right="5"
      bottom="0">
      <placeholder
        name="CanBeHidden"
        value="True" />
      <placeholder
        name="IsInitialFocus"
        value="False" />
      <placeholder
        name="VisibilityCondition"
        value="" />
      <placeholder
        name="Content"
        value="Allocate Consol(s) to Route"
        resid="c060e7fc-13c4-42af-844a-c11a2bd2a283" />
      <placeholder
        name="IsDefault"
        value="False" />
    </control>
    <control
      code="TBT"
      id="0d0e6a01-f21c-4f5a-af41-eef679290029"
      width="5"
      height="1"
      right="0"
      bottom="0">
      <placeholder
        name="CanBeHidden"
        value="True" />
      <placeholder
        name="IsInitialFocus"
        value="False" />
      <placeholder
        name="VisibilityCondition"
        value="" />
      <placeholder
        name="Content"
        value="Allocate Container(s) to Route"
        resid="b2326513-a909-4dae-a38e-b717685094ee" />
      <placeholder
        name="IsDefault"
        value="False" />
    </control>
  </form>
