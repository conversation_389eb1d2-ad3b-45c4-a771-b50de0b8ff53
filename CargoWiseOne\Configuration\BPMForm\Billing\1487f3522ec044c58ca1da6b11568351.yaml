#transformationVersion: 70.0
#
VZ_PK: 1487f3522ec044c58ca1da6b11568351
VZ_ConfigurationKey: 1487f352-2ec0-44c5-8ca1-da6b11568351
VZ_FormID: Billing - Edit Charges
VZ_Caption:
  resKey: VZ_Caption|1487f352-2ec0-44c5-8ca1-da6b11568351
  text: Edit Charges
VZ_FormFactor: DSK
VZ_EntityType: IJobHeader
VZ_FormType: FRM
VZ_RequiresLogin: true
VZ_ExtenderFunc: CargoWise.Glow.Business.LandTransport.EditChargesExtension.ExtendEditChargesPage
VZ_Dependencies: >-
  <dependencies xmlns="http://cargowise.com/glow/2014/11/14/dependencies.xsd">
    <expandPath
      path="JobCharges" />
    <datagrid
      path="JobCharges">
      <expandPath
        path="AccChargeCode" />
      <expandPath
        path="SellCurrency" />
      <expandPath
        path="SellAccount" />
      <expandPath
        path="CostCurrency" />
      <expandPath
        path="CostAccount" />
      <expandPath
        path="GlbDepartment" />
      <expandPath
        path="GlbBranch" />
      <expandPath
        path="InternalBranch" />
      <expandPath
        path="InternalDept" />
      <expandPath
        path="InternalJob" />
      <expandPath
        path="CostVATClass" />
      <expandPath
        path="SellVATClass" />
      <expandPath
        path="AccBankAccount" />
      <expandPath
        path="AccChequeBook" />
      <expandPath
        path="APLine" />
      <expandPath
        path="ARLine" />
      <expandPath
        path="CFXLine" />
      <expandPath
        path="CostGSTRate" />
      <expandPath
        path="SellGSTRate" />
      <expandPath
        path="JobConsolCost" />
      <expandPath
        path="GatewaySellHeader" />
      <expandPath
        path="GlbCompany" />
      <expandPath
        path="RevenueLine" />
      <expandPath
        path="SellInvoiceAddress" />
      <expandPath
        path="SellInvoiceContact" />
      <expandPath
        path="Product" />
      <expandPath
        path="SellInvoiceCurrency" />
    </datagrid>
  </dependencies>
VZ_FormData: >-
  <form
    id="66ff87b4-a087-4bf7-b8f7-5e00b8bd60b8" xmlns="http://wisetechglobal.com/glow/2017/05/26/form.xsd">
    <placeholder
      name="BackgroundImagePk"
      value="" />
    <placeholder
      name="BackgroundSize"
      value="Auto" />
    <placeholder
      name="BackgroundImageOverride"
      value="" />
    <placeholder
      name="FormLayout"
      value="Splitview" />
    <placeholder
      name="ExpandPaths"
      value="" />
    <placeholder
      name="ScanInputHandling"
      value="NON" />
    <placeholder
      name="RemovePadding"
      value="false" />
    <placeholder
      name="TurnOnPortableOneToOneDesignMode"
      value="false" />
    <placeholder
      name="ContainsDefaultButton"
      value="False" />
    <control
      code="GRD"
      id="f9ca8d89-80c5-48fe-886c-fe7ed6b64995"
      left="0"
      top="0"
      right="0"
      bottom="2"
      binding="JobCharges">
      <placeholder
        name="IsReadOnly"
        value="False" />
      <placeholder
        name="CanBeHidden"
        value="True" />
      <placeholder
        name="CaptionOverride"
        value="Charges"
        resid="cb6f630a-8a33-4571-baa5-842263a1374a" />
      <placeholder
        name="VisibilityCondition"
        value="" />
      <placeholder
        name="HeaderAlignment"
        value="Left" />
      <placeholder
        name="HeaderDisplayMode"
        value="Visible" />
      <placeholder
        name="AllowAdd"
        value="True" />
      <placeholder
        name="AllowAttach"
        value="True" />
      <placeholder
        name="AllowDetach"
        value="True" />
      <placeholder
        name="AllowInlineEdit"
        value="True" />
      <placeholder
        name="FieldConfiguration">
        <xml>
          <fields xmlns="">
            <field
              path="JR_AC"
              width="250"
              mode="Mandatory" />
            <field
              path="JR_DisplaySequence"
              width="120"
              mode="Default" />
            <field
              path="JR_InvoiceType"
              width="70"
              mode="Mandatory" />
            <field
              path="JR_OSSellAmt"
              width="100"
              mode="Default" />
            <field
              path="JR_RX_NKSellCurrency"
              width="70"
              mode="Mandatory" />
            <field
              path="JR_LocalSellAmt"
              width="100"
              mode="Mandatory" />
            <field
              path="JR_EstimatedRevenue"
              width="125"
              mode="Default" />
            <field
              path="JR_OH_SellAccount"
              width="300"
              mode="Mandatory" />
            <field
              path="JR_SellReference"
              width="150"
              mode="Default" />
            <field
              path="JR_ARLinePostingStatus"
              width="150"
              mode="Default" />
            <field
              path="JR_OSCostAmt"
              width="100"
              mode="Default" />
            <field
              path="JR_RX_NKCostCurrency"
              width="145"
              mode="Default" />
            <field
              path="JR_LocalCostAmt"
              width="100"
              mode="Default" />
            <field
              path="JR_EstimatedCost"
              width="100"
              mode="Default" />
            <field
              path="JR_OH_CostAccount"
              width="300"
              mode="Default" />
            <field
              path="JR_APLinePostingStatus"
              width="150"
              mode="Default" />
            <field
              path="JR_GE"
              width="165"
              mode="Default" />
            <field
              path="JR_GB"
              width="150"
              mode="Default" />
            <field
              path="JR_GB_InternalBranch"
              width="150"
              mode="Default" />
            <field
              path="JR_GE_InternalDept"
              width="165"
              mode="Default" />
            <field
              path="JR_JH_InternalJob"
              width="100"
              mode="Default" />
            <field
              path="JR_OSCostExRate"
              width="150"
              mode="Default" />
            <field
              path="JR_SellRatingOverrideComment"
              width="250"
              mode="Default" />
            <field
              path="JR_CostRatingOverrideComment"
              width="250"
              mode="Default" />
            <field
              path="JR_OSSellExRate"
              width="150"
              mode="Default" />
            <field
              path="JR_Desc"
              width="250"
              mode="Default" />
            <field
              path="JR_A9_CostVATClass"
              width="100"
              mode="Optional" />
            <field
              path="JR_A9_SellVATClass"
              width="100"
              mode="Optional" />
            <field
              path="JR_AB"
              width="100"
              mode="Optional" />
            <field
              path="JR_AgentDeclaredCostAmt"
              width="100"
              mode="Optional" />
            <field
              path="JR_AgentDeclaredSellAmt"
              width="100"
              mode="Optional" />
            <field
              path="JR_AK"
              width="100"
              mode="Optional" />
            <field
              path="JR_AL_APLine"
              width="100"
              mode="Optional" />
            <field
              path="JR_AL_ARLine"
              width="100"
              mode="Optional" />
            <field
              path="JR_AL_CFXLine"
              width="100"
              mode="Optional" />
            <field
              path="JR_APDocumentReceivedDate"
              width="100"
              mode="Optional" />
            <field
              path="JR_APInvoiceDate"
              width="100"
              mode="Optional" />
            <field
              path="JR_APInvoiceNum"
              width="100"
              mode="Optional" />
            <field
              path="JR_APNumberOfSupportingDocuments"
              width="100"
              mode="Optional" />
            <field
              path="JR_ARNumberOfSupportingDocuments"
              width="100"
              mode="Optional" />
            <field
              path="JR_AT_CostGSTRate"
              width="100"
              mode="Optional" />
            <field
              path="JR_AT_SellGSTRate"
              width="100"
              mode="Optional" />
            <field
              path="JR_ChargeType"
              width="100"
              mode="Optional" />
            <field
              path="JR_ChequeNo"
              width="100"
              mode="Optional" />
            <field
              path="JR_CostGovtChargeCode"
              width="100"
              mode="Optional" />
            <field
              path="JR_CostPlaceOfSupply"
              width="100"
              mode="Optional" />
            <field
              path="JR_CostPlaceOfSupplyType"
              width="100"
              mode="Optional" />
            <field
              path="JR_CostRated"
              width="100"
              mode="Optional" />
            <field
              path="JR_CostRatingOverride"
              width="100"
              mode="Optional" />
            <field
              path="JR_CostReference"
              width="100"
              mode="Optional" />
            <field
              path="JR_CostTaxDate"
              width="100"
              mode="Optional" />
            <field
              path="JR_DeclaredOSCostAmt"
              width="100"
              mode="Optional" />
            <field
              path="JR_E6"
              width="100"
              mode="Optional" />
            <field
              path="JR_E6_GatewaySellHeader"
              width="100"
              mode="Optional" />
            <field
              path="JR_GC"
              width="100"
              mode="Optional" />
            <field
              path="JR_IsCostTaxAmountOverridden"
              width="100"
              mode="Optional" />
            <field
              path="JR_IsIncludedInProfitShare"
              width="100"
              mode="Optional" />
            <field
              path="JR_JR_RevenueLine"
              width="100"
              mode="Optional" />
            <field
              path="JR_LineCFX"
              width="100"
              mode="Optional" />
            <field
              path="JR_LineType"
              width="100"
              mode="Optional" />
            <field
              path="JR_MarginPercentage"
              width="100"
              mode="Optional" />
            <field
              path="JR_OA_SellInvoiceAddress"
              width="100"
              mode="Optional" />
            <field
              path="JR_OC_SellInvoiceContact"
              width="100"
              mode="Optional" />
            <field
              path="JR_OP_Product"
              width="100"
              mode="Optional" />
            <field
              path="JR_OrderReference"
              width="100"
              mode="Optional" />
            <field
              path="JR_OSCostGSTAmt"
              width="100"
              mode="Optional" />
            <field
              path="JR_OSCostWHTAmt"
              width="100"
              mode="Optional" />
            <field
              path="JR_OSSellWHTAmt"
              width="100"
              mode="Optional" />
            <field
              path="JR_PaymentDate"
              width="100"
              mode="Optional" />
            <field
              path="JR_PaymentType"
              width="100"
              mode="Optional" />
            <field
              path="JR_PreventInvoicePrintGrouping"
              width="100"
              mode="Optional" />
            <field
              path="JR_ProductQuantity"
              width="100"
              mode="Optional" />
            <field
              path="JR_ProFormaCost"
              width="100"
              mode="Optional" />
            <field
              path="JR_ProFormaRevenue"
              width="100"
              mode="Optional" />
            <field
              path="JR_RX_NKSellInvoiceCurrency"
              width="100"
              mode="Optional" />
            <field
              path="JR_SellGovtChargeCode"
              width="100"
              mode="Optional" />
            <field
              path="JR_SellPlaceOfSupply"
              width="100"
              mode="Optional" />
            <field
              path="JR_SellPlaceOfSupplyType"
              width="100"
              mode="Optional" />
            <field
              path="JR_SellRated"
              width="100"
              mode="Optional" />
            <field
              path="JR_SellRatingOverride"
              width="100"
              mode="Optional" />
            <field
              path="JR_SellTaxDate"
              width="100"
              mode="Optional" />
          </fields>
        </xml>
      </placeholder>
      <placeholder
        name="DefaultSortFields"
        value="" />
      <placeholder
        name="ShowDocumentsIcon"
        value="True" />
      <placeholder
        name="RowActionsMode"
        value="RowAndMenu" />
      <placeholder
        name="HideGridActions"
        value="False" />
      <placeholder
        name="HideFilters"
        value="False" />
      <placeholder
        name="ActionMenuItems"
        value="" />
      <placeholder
        name="EditFormFlow"
        value="" />
      <placeholder
        name="SuppressDependencyContributions"
        value="False" />
    </control>
    <control
      code="TIL"
      id="f59f7ffb-eb97-47a1-9fc4-93d221b134c5"
      width="4"
      height="2"
      right="0"
      bottom="0">
      <placeholder
        name="CanBeHidden"
        value="True" />
      <placeholder
        name="VisibilityCondition"
        value="" />
      <placeholder
        name="Size"
        value="Wide" />
      <placeholder
        name="Color"
        value="Default" />
      <placeholder
        name="DisplayMode"
        value="TextOnly" />
      <placeholder
        name="Text"
        value="Validate and Save"
        resid="fa9d1ce4-6c2e-437b-8257-401817b2fd62" />
      <placeholder
        name="Image"
        value="" />
      <placeholder
        name="ImageOverride"
        value="" />
      <placeholder
        name="Description"
        value="" />
      <placeholder
        name="Behaviour"
        value="Extender" />
      <placeholder
        name="PagePK"
        value="" />
      <placeholder
        name="Url"
        value="" />
      <placeholder
        name="FormFlow"
        value="" />
    </control>
  </form>
