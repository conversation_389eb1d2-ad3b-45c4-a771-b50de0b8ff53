#transformationVersion: 70.0
#
VZ_PK: 0b5c0c405ed846a0a462241e180f973c
VZ_ConfigurationKey: 0b5c0c40-5ed8-46a0-a462-241e180f973c
VZ_FormID: ETL - VDV3 - Destination Depot - Surplus at Destination
VZ_Caption:
  resKey: VZ_Caption|0b5c0c40-5ed8-46a0-a462-241e180f973c
  text: Surplus at Destination
VZ_FormFactor: DSK
VZ_FormType: PAG
VZ_RequiresLogin: true
VZ_Dependencies: <dependencies xmlns="http://cargowise.com/glow/2014/11/14/dependencies.xsd" />
VZ_FormData: >-
  <form
    id="ac13f136-3831-490b-8156-e5fa21ea5151" xmlns="http://wisetechglobal.com/glow/2017/05/26/form.xsd">
    <placeholder
      name="BackgroundImagePk"
      value="" />
    <placeholder
      name="BackgroundSize"
      value="Auto" />
    <placeholder
      name="BackgroundImageOverride"
      value="" />
    <placeholder
      name="FormLayout"
      value="Splitview" />
    <placeholder
      name="ExpandPaths"
      value="" />
    <placeholder
      name="ScanInputHandling"
      value="NON" />
    <placeholder
      name="RemovePadding"
      value="false" />
    <placeholder
      name="TurnOnPortableOneToOneDesignMode"
      value="false" />
    <placeholder
      name="ContainsDefaultButton"
      value="False" />
    <control
      code="GRP"
      id="eb391f86-59f7-470a-9b60-42450456e509"
      left="0"
      top="0"
      width="6"
      height="4">
      <placeholder
        name="CanBeHidden"
        value="True" />
      <placeholder
        name="VisibilityCondition"
        value="" />
      <placeholder
        name="Header"
        value="New Section"
        resid="050de8a6-799f-4291-b279-229443276537" />
      <placeholder
        name="HeaderAlignment"
        value="Left" />
      <placeholder
        name="HeaderDisplayMode"
        value="Hidden" />
      <placeholder
        name="BackgroundImagePk"
        value="" />
      <placeholder
        name="BackgroundImageOverride"
        value="" />
      <control
        code="TIL"
        id="9c0e1bd3-df01-4052-9902-76bfad4f20ef"
        left="0"
        top="0"
        width="2"
        height="2">
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="Size"
          value="Standard" />
        <placeholder
          name="Color"
          value="Default" />
        <placeholder
          name="DisplayMode"
          value="TextAndIcon" />
        <placeholder
          name="Text"
          value="Impediment"
          resid="8af651d9-c5c4-4de8-adcf-a07592abc1ce" />
        <placeholder
          name="Image"
          value="e69bfd87530d4de887276038ceae77c8" />
        <placeholder
          name="ImageOverride"
          value="" />
        <placeholder
          name="Description"
          value="" />
        <placeholder
          name="Behaviour"
          value="Page" />
        <placeholder
          name="PagePK"
          value="9de1a081d3124bb383082d0cc82f24d8" />
        <placeholder
          name="Url"
          value="" />
        <placeholder
          name="FormFlow"
          value="" />
      </control>
      <control
        code="TIL"
        id="e9f68f72-3099-42de-89c6-6684b673f013"
        left="2"
        top="0"
        width="2"
        height="2">
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="Size"
          value="Standard" />
        <placeholder
          name="Color"
          value="Default" />
        <placeholder
          name="DisplayMode"
          value="TextAndIcon" />
        <placeholder
          name="Text"
          value="Short"
          resid="33131064-4405-4fd0-bea4-b366eae2b0dd" />
        <placeholder
          name="Image"
          value="95cc057166414200b5f6fa8dd5455427" />
        <placeholder
          name="ImageOverride"
          value="" />
        <placeholder
          name="Description"
          value="" />
        <placeholder
          name="Behaviour"
          value="Page" />
        <placeholder
          name="PagePK"
          value="1b825c8cf71d4f1e8cf225c7c6d788e5" />
        <placeholder
          name="Url"
          value="" />
        <placeholder
          name="FormFlow"
          value="" />
      </control>
      <control
        code="TIL"
        id="2310cb40-6a1c-4630-8f3a-f7d9db403f7b"
        left="4"
        top="0"
        width="2"
        height="2">
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="Size"
          value="Standard" />
        <placeholder
          name="Color"
          value="Primary" />
        <placeholder
          name="DisplayMode"
          value="TextAndIcon" />
        <placeholder
          name="Text"
          value="Surplus"
          resid="66b0c400-0b48-4dea-92d1-a638a96da8e5" />
        <placeholder
          name="Image"
          value="74a27a164dc145d4b5a8916b1376be68" />
        <placeholder
          name="ImageOverride"
          value="" />
        <placeholder
          name="Description"
          value="" />
        <placeholder
          name="Behaviour"
          value="Page" />
        <placeholder
          name="PagePK"
          value="0b5c0c405ed846a0a462241e180f973c" />
        <placeholder
          name="Url"
          value="" />
        <placeholder
          name="FormFlow"
          value="" />
      </control>
      <control
        code="TIL"
        id="628d7a92-c0ef-48d4-ad77-219c514c33c1"
        left="0"
        top="2"
        width="2"
        height="2">
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="Size"
          value="Standard" />
        <placeholder
          name="Color"
          value="Default" />
        <placeholder
          name="DisplayMode"
          value="TextAndIcon" />
        <placeholder
          name="Text"
          value="Tracking"
          resid="1720e307-2a7b-48a3-8cad-480ef4b4734f" />
        <placeholder
          name="Image"
          value="702537aece2b47078b449d342f609b5e" />
        <placeholder
          name="ImageOverride"
          value="" />
        <placeholder
          name="Description"
          value="" />
        <placeholder
          name="Behaviour"
          value="Page" />
        <placeholder
          name="PagePK"
          value="6304763e5b644c069fb98fe353716da5" />
        <placeholder
          name="Url"
          value="" />
        <placeholder
          name="FormFlow"
          value="" />
      </control>
    </control>
    <control
      code="SRL"
      id="35f7aaf0-66ec-4ca9-b9d4-8e0d60ecb5c4"
      left="7"
      top="0"
      right="0"
      bottom="0">
      <placeholder
        name="CanBeHidden"
        value="True" />
      <placeholder
        name="IsInitialFocus"
        value="False" />
      <placeholder
        name="CaptionOverride"
        value="Surplus"
        resid="2bd4ca7a-7695-43ca-8d0e-05f81d0d4031" />
      <placeholder
        name="VisibilityCondition"
        value="" />
      <placeholder
        name="HeaderAlignment"
        value="Left" />
      <placeholder
        name="HeaderDisplayMode"
        value="Visible" />
      <placeholder
        name="FilterType"
        value="Entity" />
      <placeholder
        name="EntityType"
        value="IHVLVItem" />
      <placeholder
        name="ItemDisplayPath"
        value="" />
      <placeholder
        name="DisabledGridRowActions"
        value="" />
      <placeholder
        name="DefaultFilter">
        <xml>
          <ArrayOfFilterGroup xmlns:a="http://schemas.microsoft.com/2003/10/Serialization/Arrays" xmlns:i="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://cargowise.com/glow/2014/07/16/filters.xsd">
            <FilterGroup>
              <Filters>
                <Filter>
                  <FilterType>StringFilter</FilterType>
                  <Operation>StartsWith</Operation>
                  <PropertyPath>HVI_CurrentBarcode</PropertyPath>
                  <Values />
                </Filter>
                <Filter>
                  <FilterType>StringFilter</FilterType>
                  <Operation>StartsWith</Operation>
                  <PropertyPath>HVI_ShipperReference</PropertyPath>
                  <Values />
                </Filter>
                <Filter>
                  <FilterType>IsActiveFilter</FilterType>
                  <Operation>Is</Operation>
                  <PropertyPath>HVI_IsActive</PropertyPath>
                  <Values>
                    <a:string>true</a:string>
                  </Values>
                </Filter>
              </Filters>
              <IsImplicit>false</IsImplicit>
            </FilterGroup>
            <FilterGroup>
              <Filters>
                <Filter>
                  <FilterType>SimpleLookupFilter</FilterType>
                  <Operation>Is</Operation>
                  <PropertyPath>HVI_Status</PropertyPath>
                  <Values>
                    <a:string>SUD</a:string>
                  </Values>
                </Filter>
              </Filters>
              <IsImplicit>true</IsImplicit>
            </FilterGroup>
          </ArrayOfFilterGroup>
        </xml>
      </placeholder>
      <placeholder
        name="DefaultFilterPropertyPath"
        value="" />
      <placeholder
        name="FieldConfiguration">
        <xml>
          <fields xmlns="">
            <field
              path="BookingHeader.HVH_BookingReference"
              width="200"
              mode="Optional" />
            <field
              path="HVI_ItemId"
              width="250"
              mode="Default" />
            <field
              path="HVI_CurrentBarcode"
              width="250"
              mode="Default" />
            <field
              path="HVI_ShipperReference"
              width="250"
              mode="Default" />
            <field
              path="HVI_Status"
              width="250"
              mode="Default" />
            <field
              path="HVI_JS_LoadedOnShipment"
              width="150"
              mode="Default" />
            <field
              path="HVI_ManifestedWeight"
              width="150"
              mode="Default" />
            <field
              path="HVI_ManifestedVolume"
              width="150"
              mode="Default" />
            <field
              path="HVI_ActualWeight"
              width="150"
              mode="Default" />
            <field
              path="HVI_ActualVolume"
              width="150"
              mode="Default" />
            <field
              path="HVI_F3_NKPackType"
              width="100"
              mode="Default" />
            <field
              path="HVI_IsDamaged"
              width="80"
              mode="Default" />
            <field
              path="HVI_IsPillaged"
              width="80"
              mode="Default" />
            <field
              path="HVI_IsUllaged"
              width="80"
              mode="Default" />
            <field
              path="ShipmentETailer"
              width="250"
              mode="Default" />
            <field
              path="HVI_Height"
              width="100"
              mode="Optional" />
            <field
              path="HVI_Width"
              width="100"
              mode="Optional" />
            <field
              path="HVI_Length"
              width="100"
              mode="Optional" />
            <field
              path="HVI_UnitOfDimension"
              width="40"
              mode="Optional" />
            <field
              path="HVI_GoodsDescription"
              width="250"
              mode="Optional" />
            <field
              path="HVI_HVL_LoadList"
              width="250"
              mode="Optional" />
            <field
              path="HVI_HVO_OuterPackage"
              width="250"
              mode="Optional" />
            <field
              path="HVI_KM_LastMileTransportBooking"
              width="250"
              mode="Optional" />
            <field
              path="HVI_HVC_Consignment"
              width="250"
              mode="Optional" />
            <field
              path="HVI_ContainerNumber"
              width="200"
              mode="Optional" />
            <field
              path="Consignment.HVC_ImportCustomsClearanceStatus"
              width="250"
              mode="Optional" />
            <field
              path="Consignment.HVC_ExportCustomsClearanceStatus"
              width="250"
              mode="Optional" />
            <field
              path="HVI_IsActive"
              width="90"
              mode="Optional" />
            <field
              path="HVI_IsValidatedForUniqueness"
              width="200"
              mode="Optional" />
            <field
              path="Consignment.HVC_ConsignmentId"
              width="250"
              mode="Optional" />
            <field
              path="Consignment.HVC_ImportReleaseStatus"
              width="250"
              mode="Optional" />
            <field
              path="EventListHasAnyWithInterceptEvent"
              width="250"
              mode="Optional" />
            <field
              path="EventListHasAnyWithInterceptTaskCompletedEvent"
              width="250"
              mode="Optional" />
            <field
              path="EventListHasAnyWithScannedEvent"
              width="250"
              mode="Optional" />
            <field
              path="Consignment.HVC_ShipperName"
              width="250"
              mode="Optional" />
            <field
              path="Consignment.HVC_ConsigneeName"
              width="250"
              mode="Optional" />
            <field
              path="Consignment.HVC_OA_ConsigneeAddress"
              width="250"
              mode="Optional" />
            <field
              path="Consignment.HVC_OA_ShipperAddress"
              width="250"
              mode="Optional" />
            <field
              path="Consignment.HVC_GoodsValue"
              width="250"
              mode="Optional" />
            <field
              path="Consignment.HVC_RN_NKConsigneeCountryCode"
              width="250"
              mode="Optional" />
            <field
              path="Consignment.HVC_JE_ImportDeclaration"
              width="250"
              mode="Optional" />
            <field
              path="Consignment.HVC_JE_ExportDeclaration"
              width="250"
              mode="Optional" />
            <field
              path="Consignment.HVC_RX_NKGoodsValueCurrency"
              width="250"
              mode="Optional" />
            <field
              path="Consignment.HVC_OH_LastMileCarrierBookingAgent"
              width="250"
              mode="Optional" />
            <field
              path="Consignment.Header.HCH_JS_Shipment"
              width="250"
              mode="Optional" />
            <field
              path="Consignment.HVC_RN_NKShipperCountryCode"
              width="250"
              mode="Optional" />
            <field
              path="Consignment.HVC_CarrierAccountNumber"
              width="250"
              mode="Optional" />
            <field
              path="Consignment.CarrierServiceCode"
              width="250"
              mode="Optional" />
            <field
              path="Consignment.HVC_ConsigneeAddress1"
              width="250"
              mode="Optional" />
            <field
              path="Consignment.HVC_ConsigneeAddress2"
              width="250"
              mode="Optional" />
            <field
              path="Consignment.HVC_ConsigneeAddressValidationStatus"
              width="250"
              mode="Optional" />
            <field
              path="Consignment.HVC_ConsigneeCity"
              width="250"
              mode="Optional" />
            <field
              path="Consignment.HVC_ConsigneeContact"
              width="250"
              mode="Optional" />
            <field
              path="Consignment.HVC_ConsigneeEmail"
              width="250"
              mode="Optional" />
            <field
              path="Consignment.HVC_ConsigneeFax"
              width="250"
              mode="Optional" />
            <field
              path="Consignment.HVC_ConsigneeInstructions"
              width="250"
              mode="Optional" />
            <field
              path="Consignment.HVC_ConsigneeMobile"
              width="250"
              mode="Optional" />
            <field
              path="Consignment.HVC_ConsigneePhone"
              width="250"
              mode="Optional" />
            <field
              path="Consignment.HVC_ConsigneePostcode"
              width="250"
              mode="Optional" />
            <field
              path="Consignment.HVC_ConsigneeState"
              width="250"
              mode="Optional" />
            <field
              path="Consignment.HVC_GoodsDescription"
              width="250"
              mode="Optional" />
            <field
              path="Consignment.HVC_INCO"
              width="180"
              mode="Optional" />
            <field
              path="Consignment.HVC_PL_NKLastMileCarrierServiceLevel"
              width="250"
              mode="Optional" />
            <field
              path="Consignment.HVC_PreScreeningStatus"
              width="250"
              mode="Optional" />
            <field
              path="Consignment.HVC_ShipperAddress1"
              width="250"
              mode="Optional" />
            <field
              path="Consignment.HVC_ShipperAddress2"
              width="250"
              mode="Optional" />
            <field
              path="Consignment.HVC_ShipperCity"
              width="250"
              mode="Optional" />
            <field
              path="Consignment.HVC_ShipperEmail"
              width="250"
              mode="Optional" />
            <field
              path="Consignment.HVC_ShipperFax"
              width="250"
              mode="Optional" />
            <field
              path="Consignment.HVC_ShipperMobile"
              width="250"
              mode="Optional" />
            <field
              path="Consignment.HVC_ShipperContact"
              width="250"
              mode="Optional" />
            <field
              path="Consignment.HVC_ShipperPhone"
              width="250"
              mode="Optional" />
            <field
              path="Consignment.HVC_ShipperPostcode"
              width="250"
              mode="Optional" />
            <field
              path="Consignment.HVC_ShipperReference"
              width="250"
              mode="Optional" />
            <field
              path="Consignment.HVC_ShipperState"
              width="250"
              mode="Optional" />
            <field
              path="Consignment.HVC_Status"
              width="200"
              mode="Optional" />
            <field
              path="Consignment.HVC_UndgClass"
              width="240"
              mode="Optional" />
            <field
              path="Consignment.HVC_VendorIdentifier"
              width="250"
              mode="Optional" />
            <field
              path="Consignment.HVC_WaybillNumber"
              width="250"
              mode="Optional" />
            <field
              path="Consignment.HVC_AuthorityToLeave"
              width="250"
              mode="Optional" />
            <field
              path="Consignment.HVC_IsHazardous"
              width="230"
              mode="Optional" />
            <field
              path="Consignment.HVC_IsActive"
              width="230"
              mode="Optional" />
            <field
              path="Consignment.HVC_IsSignatureRequired"
              width="250"
              mode="Optional" />
            <field
              path="Consignment.HVC_IsTaxPrePaid"
              width="250"
              mode="Optional" />
            <field
              path="Consignment.IsTranshipment"
              width="250"
              mode="Optional" />
            <field
              path="Consignment.HVC_IsPerishable"
              width="240"
              mode="Optional" />
            <field
              path="Consignment.HVC_IsPersonalEffects"
              width="250"
              mode="Optional" />
            <field
              path="Consignment.HVC_IsValidatedForUniqueness"
              width="250"
              mode="Optional" />
            <field
              path="Consignment.HVC_RequiresFumigation"
              width="250"
              mode="Optional" />
            <field
              path="Consignment.HVC_IsSelfBooked"
              width="250"
              mode="Optional" />
            <field
              path="Consignment.HVC_IsTimber"
              width="200"
              mode="Optional" />
            <field
              path="BookingHeader"
              width="140"
              mode="Optional" />
            <field
              path="DestinationDepot"
              width="250"
              mode="Optional" />
            <field
              path="OriginDepot"
              width="250"
              mode="Optional" />
            <field
              path="CargoWiseRef"
              width="250"
              mode="Optional" />
            <field
              path="CartageURL"
              width="250"
              mode="Optional" />
            <field
              path="GateInAtOriginDepot"
              width="230"
              mode="Optional" />
            <field
              path="HVI_IsUnmanifestedAtDestination"
              width="250"
              mode="Optional" />
            <field
              path="ItemHasBeenScanned"
              width="210"
              mode="Optional" />
            <field
              path="LastInterceptEventReference"
              width="250"
              mode="Optional" />
            <field
              path="URL"
              width="250"
              mode="Optional" />
            <field
              path="HVI_IsScannedAtDestination"
              width="100"
              mode="Optional" />
            <field
              path="HVI_ImportReleaseStatus"
              width="100"
              mode="Optional" />
            <field
              path="Consignment.HVC_OH_LastMileCarrier"
              width="250"
              mode="Optional" />
            <field
              path="Consignment.HVC_OA_DestinationDepot"
              width="250"
              mode="Optional" />
            <field
              path="Consignment.HVC_SystemCreateTimeUtc"
              width="250"
              mode="Optional" />
            <field
              path="Consignment.HVC_SystemLastEditTimeUtc"
              width="250"
              mode="Optional" />
            <field
              path="Consignment.HVC_ActualVolumeMeasure"
              width="130"
              mode="Optional" />
            <field
              path="Consignment.HVC_ActualWeightMeasure"
              width="130"
              mode="Optional" />
            <field
              path="Consignment.HVC_ManifestedVolumeMeasure"
              width="130"
              mode="Optional" />
            <field
              path="Consignment.HVC_ManifestedWeightMeasure"
              width="130"
              mode="Optional" />
            <field
              path="Consignment.HVC_ItemCount"
              width="250"
              mode="Optional" />
            <field
              path="Consignment.HVC_HVH_BookingHeader"
              width="250"
              mode="Optional" />
            <field
              path="Consignment.HVC_SystemLastEditUser"
              width="250"
              mode="Optional" />
            <field
              path="HVI_CarrierBookingStatus"
              width="220"
              mode="Optional" />
            <field
              path="VolumeWeight"
              width="250"
              mode="Optional" />
            <field
              path="Chargeable"
              width="250"
              mode="Optional" />
            <field
              path="DensityFactor"
              width="250"
              mode="Optional" />
          </fields>
        </xml>
      </placeholder>
      <placeholder
        name="DefaultSortFields"
        value="" />
      <placeholder
        name="DefaultGroupFields"
        value="" />
      <placeholder
        name="EnableGrouping"
        value="False" />
      <placeholder
        name="AutoRefreshInterval"
        value="0" />
      <placeholder
        name="AllowMultiSelect"
        value="True" />
      <placeholder
        name="ShowWorkflowLabel"
        value="True" />
      <placeholder
        name="RowActionsMode"
        value="RowAndMenu" />
      <placeholder
        name="HideGridActions"
        value="False" />
      <placeholder
        name="MinimizeFilters"
        value="False" />
      <placeholder
        name="WorkflowOpenInNewWindow"
        value="False" />
      <placeholder
        name="SearchOnInit"
        value="True" />
      <placeholder
        name="ShowQuickSearch"
        value="False" />
      <placeholder
        name="QuickSearchCaption"
        value="" />
      <placeholder
        name="ValueField"
        value="" />
      <placeholder
        name="Category"
        value="" />
      <placeholder
        name="ActionMenuItems"
        value="" />
      <placeholder
        name="EditFormFlow"
        value="" />
      <placeholder
        name="ResetFilterOnInit"
        value="False" />
      <placeholder
        name="FilterDisplayMode"
        value="InlineAndPopup" />
    </control>
    <control
      code="GRP"
      id="610b286f-f6f8-409c-85c6-df8469fdec3b"
      left="0"
      top="4"
      width="6"
      height="5">
      <placeholder
        name="CanBeHidden"
        value="True" />
      <placeholder
        name="VisibilityCondition"
        value="" />
      <placeholder
        name="Header"
        value="Tracking"
        resid="4588eeab-ef38-458f-9e41-b316ddc11913" />
      <placeholder
        name="HeaderAlignment"
        value="Left" />
      <placeholder
        name="HeaderDisplayMode"
        value="Visible" />
      <placeholder
        name="BackgroundImagePk"
        value="" />
      <placeholder
        name="BackgroundImageOverride"
        value="" />
      <control
        code="TRC"
        id="bbab7c08-8f95-4f22-9dfe-712fb01d38e4"
        left="0"
        top="0"
        width="6"
        height="4">
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="VisibilityCondition"
          value="" />
      </control>
    </control>
    <control
      code="FAV"
      id="76e7ae8f-38e4-438d-b4e3-b5ca68dc7c9d"
      left="0"
      top="9"
      width="6"
      bottom="2">
      <placeholder
        name="CanBeHidden"
        value="True" />
      <placeholder
        name="VisibilityCondition"
        value="" />
      <placeholder
        name="EntityType"
        value="" />
      <placeholder
        name="HeaderDisplayMode"
        value="Visible" />
      <placeholder
        name="ShowFavorites"
        value="True" />
      <placeholder
        name="ShowRecents"
        value="True" />
    </control>
  </form>
