#transformationVersion: 70.0
#
VZ_PK: 88366e9929e0496cb40d1a0b17d00ff1
VZ_ConfigurationKey: 88366e99-29e0-496c-b40d-1a0b17d00ff1
VZ_FormID: ETL - VDV3 - Origin Depot - Allocate Item to Load List with Button
VZ_Caption:
  resKey: VZ_Caption|88366e99-29e0-496c-b40d-1a0b17d00ff1
  text: Allocate Item to Load List
VZ_FormFactor: DSK
VZ_EntityType: IHVLVOriginLoadList
VZ_FormType: FRM
VZ_RequiresLogin: true
VZ_Dependencies: >-
  <dependencies xmlns="http://cargowise.com/glow/2014/11/14/dependencies.xsd">
    <expandPath
      path="CurrentItem" />
    <expandPath
      path="ContainerType" />
    <calculatedProperty
      path="MasterFlight" />
    <calculatedProperty
      path="DestinationDepotUNLOCO" />
    <calculatedProperty
      path="CurrentItem" />
    <calculatedProperty
      path="CurrentItem.ReturnLastMileCarrierAndBarcode" />
    <calculatedProperty
      path="ItemsAllocated" />
    <calculatedProperty
      path="ItemsManifestedWeight" />
    <calculatedProperty
      path="ItemsManifestedVolume" />
  </dependencies>
VZ_FormData: >-
  <form
    id="14762282-c587-440d-9c49-01c6d9158786" xmlns="http://wisetechglobal.com/glow/2017/05/26/form.xsd">
    <placeholder
      name="BackgroundImagePk"
      value="" />
    <placeholder
      name="BackgroundSize"
      value="Auto" />
    <placeholder
      name="BackgroundImageOverride"
      value="" />
    <placeholder
      name="FormLayout"
      value="Splitview" />
    <placeholder
      name="ExpandPaths"
      value="" />
    <placeholder
      name="ScanInputHandling"
      value="NON" />
    <placeholder
      name="RemovePadding"
      value="false" />
    <placeholder
      name="TurnOnPortableOneToOneDesignMode"
      value="false" />
    <placeholder
      name="ContainsDefaultButton"
      value="False" />
    <control
      code="GRP"
      id="d55750fa-9fc9-437e-b633-8b71b2f28bb0"
      top="0"
      width="26"
      height="17.9">
      <placeholder
        name="CanBeHidden"
        value="True" />
      <placeholder
        name="VisibilityCondition"
        value="" />
      <placeholder
        name="Header"
        value="New Section"
        resid="523d6f11-a82d-41ae-b27e-69c5f3629ea5" />
      <placeholder
        name="HeaderAlignment"
        value="Left" />
      <placeholder
        name="HeaderDisplayMode"
        value="Hidden" />
      <placeholder
        name="BackgroundImagePk"
        value="" />
      <placeholder
        name="BackgroundImageOverride"
        value="" />
      <control
        code="LBL"
        id="e9ad68cc-2c3a-458c-9441-e60c3f4e279c"
        left="1"
        top="0.9"
        width="24"
        height="3"
        binding="MasterFlight">
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="LabelType"
          value="NormalLabel" />
        <placeholder
          name="LabelNotificationType"
          value="Information" />
        <placeholder
          name="ForegroundThemeColor"
          value="GeneralText" />
        <placeholder
          name="TextSize"
          value="ExtraLarge" />
        <placeholder
          name="FontWeight"
          value="Bold" />
        <placeholder
          name="LabelAlignment"
          value="Center" />
        <placeholder
          name="BackgroundColor"
          value="DestDepotColour" />
        <placeholder
          name="Padding"
          value="None" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="LBL"
        id="4723adf6-15b3-4878-a6bb-4e5b9253d587"
        left="1"
        top="3.9"
        width="24"
        height="3"
        binding="DestinationDepotUNLOCO">
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="LabelType"
          value="NormalLabel" />
        <placeholder
          name="LabelNotificationType"
          value="Information" />
        <placeholder
          name="ForegroundThemeColor"
          value="GeneralText" />
        <placeholder
          name="TextSize"
          value="ExtraLarge" />
        <placeholder
          name="FontWeight"
          value="Bold" />
        <placeholder
          name="LabelAlignment"
          value="Center" />
        <placeholder
          name="BackgroundColor"
          value="DestDepotColour" />
        <placeholder
          name="Padding"
          value="None" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="LBL"
        id="fbf15aa6-c90f-41cd-8b05-2cd55e2d945c"
        left="1"
        top="6.9"
        width="24"
        height="3"
        binding="CurrentItem.ReturnLastMileCarrierAndBarcode">
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="LabelType"
          value="NormalLabel" />
        <placeholder
          name="LabelNotificationType"
          value="Information" />
        <placeholder
          name="ForegroundThemeColor"
          value="GeneralText" />
        <placeholder
          name="TextSize"
          value="ExtraLarge" />
        <placeholder
          name="FontWeight"
          value="Bold" />
        <placeholder
          name="LabelAlignment"
          value="Center" />
        <placeholder
          name="BackgroundColor"
          value="LoadList.DestDepotColour" />
        <placeholder
          name="Padding"
          value="None" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="NUM"
        id="5f98a7e5-0c37-433a-a579-1153b778f6bf"
        left="5"
        top="11.4"
        width="4"
        height="1"
        binding="ItemsAllocated">
        <placeholder
          name="IsReadOnly"
          value="True" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="NumericFieldLeftAlignment"
          value="False" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="NUM"
        id="cebf65e8-e7a3-4404-b9fe-1b1e2cb33cbb"
        left="9"
        top="11.4"
        width="4"
        height="1"
        binding="ItemsManifestedWeight">
        <placeholder
          name="IsReadOnly"
          value="True" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="NumericFieldLeftAlignment"
          value="False" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="NUM"
        id="997be962-6c21-465f-9080-114215611567"
        left="13"
        top="11.4"
        width="4"
        height="1"
        binding="ItemsManifestedVolume">
        <placeholder
          name="IsReadOnly"
          value="True" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="NumericFieldLeftAlignment"
          value="False" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="NUM"
        id="f6ad1325-8ffe-4c64-8872-d63e8dd37bbe"
        left="17"
        top="11.4"
        width="4"
        height="1"
        binding="ContainerType.RC_CubicCapacity">
        <placeholder
          name="IsReadOnly"
          value="True" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="Container Capacity"
          resid="8e15bf2f-d492-4996-834c-9baca97786ac" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="NumericFieldLeftAlignment"
          value="False" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="FFI"
        id="da6166ce-4288-468c-8ba5-c21464951608"
        left="9"
        top="13.6"
        width="8"
        height="1">
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="Barcode"
          resid="34d0ea6f-074f-4085-ad67-5b405d960a70" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="EntityType"
          value="IHVLVItem" />
        <placeholder
          name="DefaultFilter">
          <xml>
            <ArrayOfFilterGroup xmlns:a="http://schemas.microsoft.com/2003/10/Serialization/Arrays" xmlns:i="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://cargowise.com/glow/2014/07/16/filters.xsd">
              <FilterGroup>
                <Filters>
                  <Filter>
                    <FilterType>IsActiveFilter</FilterType>
                    <Operation>Is</Operation>
                    <PropertyPath>HVI_IsActive</PropertyPath>
                    <Values>
                      <a:string>true</a:string>
                    </Values>
                  </Filter>
                </Filters>
                <IsImplicit>false</IsImplicit>
              </FilterGroup>
            </ArrayOfFilterGroup>
          </xml>
        </placeholder>
        <placeholder
          name="ItemTemplateID"
          value="" />
        <placeholder
          name="FormFlowPK"
          value="" />
        <placeholder
          name="ItemPosition"
          value="Top" />
      </control>
      <control
        code="FLB"
        id="3c3992b0-78de-4047-82de-7afb16c2f343"
        left="9"
        top="14.7"
        width="8"
        height="1">
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="LabelText"
          value="Please Scan Next Item"
          resid="a3470d88-6e7c-4c6b-b604-153f011db851" />
        <placeholder
          name="LabelType"
          value="NormalLabel" />
        <placeholder
          name="LabelNotificationType"
          value="Information" />
        <placeholder
          name="ForegroundThemeColor"
          value="DefaultButton" />
        <placeholder
          name="TextSize"
          value="Medium" />
        <placeholder
          name="FontWeight"
          value="Bold" />
        <placeholder
          name="LabelAlignment"
          value="Center" />
        <placeholder
          name="BackgroundColor"
          value="" />
        <placeholder
          name="Padding"
          value="None" />
      </control>
      <control
        code="TBT"
        id="7bfbf71c-ed63-4ada-afc9-28990fe9ce8f"
        left="9"
        top="15.7"
        width="8"
        height="1">
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="Content"
          value="Edit Last Scanned Item"
          resid="77bfe018-3bea-4ffb-9d8a-3205cc8bde52" />
        <placeholder
          name="IsDefault"
          value="False" />
      </control>
    </control>
  </form>
