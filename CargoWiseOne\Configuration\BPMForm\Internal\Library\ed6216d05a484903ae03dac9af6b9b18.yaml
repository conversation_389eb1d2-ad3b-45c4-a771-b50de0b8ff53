#transformationVersion: 70.0
#
VZ_PK: ed6216d05a484903ae03dac9af6b9b18
VZ_ConfigurationKey: ed6216d0-5a48-4903-ae03-dac9af6b9b18
VZ_FormID: Transition Text Field Page
VZ_Caption:
  resKey: VZ_Caption|ed6216d05a484903ae03dac9af6b9b18
  text: Transition Text Field
VZ_FormFactor: DSK
VZ_FormType: PAG
VZ_RequiresLogin: true
VZ_MaterialDesign: true
VZ_Dependencies: <dependencies xmlns="http://cargowise.com/glow/2014/11/14/dependencies.xsd" />
VZ_FormData: >-
  <form
    id="1f945b85-7e49-4b00-8c7b-48a88d699179" xmlns="http://wisetechglobal.com/glow/2017/05/26/form.xsd">
    <placeholder
      name="Layout"
      value="fill" />
    <control
      code="PNL"
      id="d9a67bfa-f837-45bc-ae4c-b701e1e09902">
      <placeholder
        name="Class"
        value="blue lighten-4 bg-blue-lighten-4" />
      <placeholder
        name="Margin"
        value="mb-3" />
      <control
        code="BOX"
        id="8ce84b78-10c4-4378-8d0e-17d0fb8bc2c3">
        <placeholder
          name="Layout"
          value="flex" />
        <control
          code="IBT"
          id="816e7520-af3c-4676-b452-cf06130c8a1d">
          <placeholder
            name="Icon"
            value="s-icon-documentation" />
          <placeholder
            name="Tooltip"
            value="Documentation"
            resid="452dd970-b927-4a69-8af0-3d806554b712" />
          <placeholder
            name="Hyperlink"
            value="https://wisetechglobal.sharepoint.com/sites/Content-as-Code/Shared%20Documents/GLOW-Content/_permalinks/PB-VD-TransitionTextFieldControl.aspx" />
        </control>
        <control
          code="IBT"
          id="bcccf92c-30dd-44ed-9370-8c355269b69d">
          <placeholder
            name="Icon"
            value="s-icon-pt-devtools" />
          <placeholder
            name="Tooltip"
            value="YAML"
            resid="667583fe-ff9b-4ef5-b646-5e7f878aff27" />
          <placeholder
            name="Margin"
            value="ml-2" />
        </control>
        <control
          code="IBT"
          id="db2c3db7-d0db-4d2e-ad1d-2a8d596962c0">
          <placeholder
            name="Icon"
            value="s-icon-settings" />
          <placeholder
            name="Tooltip"
            value="Platform Builder"
            resid="5bde37a4-8906-400a-84e3-edde6404730b" />
          <placeholder
            name="Margin"
            value="ml-2" />
        </control>
        <control
          code="BOX"
          id="46c2cc15-a0b3-4450-a544-d140bbba0760">
          <placeholder
            name="Margin"
            value="ml-2" />
          <control
            code="LBL"
            id="0b46f326-477c-4ffe-b8e3-4c42e3eaed27">
            <placeholder
              name="Caption"
              value="To have a hands on experience of the example, click the button &quot;Transition Text Field Example&quot; and take note of a value from the &quot;Suggested Translation&quot; column in the data table below.."
              resid="52cba724-6734-4f5c-b4bc-b50fb9a256e0" />
            <placeholder
              name="Margin"
              value="ml-1" />
          </control>
        </control>
      </control>
    </control>
    <control
      code="BOX"
      id="9ceb2811-84f4-4469-ada1-ffffba08a22a">
      <placeholder
        name="Margin"
        value="mb-3" />
      <control
        code="BTN"
        id="f668d6da-5b0f-4e8b-bde7-8c88bd8f3b22">
        <placeholder
          name="Caption"
          value="Transition Text Field Example"
          resid="f60311a4-85ea-466e-8d71-da6a0a98c74d" />
        <placeholder
          name="FormFlowConfiguration">
          <xml>
            <formFlows xmlns="">
              <formFlow
                newSession="True">ab02ca987c184abfadbbb4df6fa7667b</formFlow>
            </formFlows>
          </xml>
        </placeholder>
        <placeholder
          name="Variant"
          value="fill" />
        <placeholder
          name="Sentiment"
          value="primary" />
      </control>
    </control>
    <control
      code="PNL"
      id="e645b168-14b8-4f09-8a06-cbab5c47213c">
      <placeholder
        name="Layout"
        value="fill" />
      <placeholder
        name="FillAvailable"
        value="True" />
      <control
        code="SDT"
        id="d9ca06cb-e68c-4677-a10b-ae765b7d1e2f">
        <placeholder
          name="EntityType"
          value="ITranslationFeedback" />
        <placeholder
          name="CaptionOverride"
          value="List to explain the usage of the &quot;Transition Text Field&quot; control."
          resid="33674613-903c-4945-9e76-85ff0c71ee2a" />
        <placeholder
          name="ShowAddActions"
          value="False" />
        <placeholder
          name="HideExport"
          value="True" />
        <placeholder
          name="HideImport"
          value="True" />
        <placeholder
          name="HideRefresh"
          value="True" />
        <placeholder
          name="DisabledGridRowActions"
          value="Activation,Documents,eDocs,Logs,Messages,Notes,Remove,Tracking,Workflow" />
        <placeholder
          name="HideActions"
          value="False" />
        <placeholder
          name="HideItemActions"
          value="True" />
        <placeholder
          name="FitToHeight"
          value="True" />
      </control>
    </control>
  </form>
