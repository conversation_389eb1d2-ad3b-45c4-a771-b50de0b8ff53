#transformationVersion: 70.0
#
VZ_PK: 0edfc0db69bb462aacbe1f301d0b7b7c
VZ_ConfigurationKey: 0edfc0db-69bb-462a-acbe-1f301d0b7b7c
VZ_FormID: CCA - View Consol Routing Legs
VZ_Caption:
  resKey: VZ_Caption|0edfc0db69bb462aacbe1f301d0b7b7c
  text: View Consol Routing Legs
VZ_FormFactor: DSK
VZ_EntityType: ICCAJobConsolView
VZ_FormType: FRM
VZ_RequiresLogin: true
VZ_MaterialDesign: true
VZ_Dependencies: <dependencies xmlns="http://cargowise.com/glow/2014/11/14/dependencies.xsd" />
VZ_FormData: >-
  <form
    id="23bfa67c-730d-4804-94b1-9bca7eab84a4" xmlns="http://wisetechglobal.com/glow/2017/05/26/form.xsd">
    <placeholder
      name="Layout"
      value="fill" />
    <control
      code="SDT"
      id="3a1d21ad-4c7e-4e5d-afdb-24c6974ba13c"
      binding="">
      <placeholder
        name="CaptionOverride"
        value="Consol Routing Legs"
        resid="********-6e2a-4ef1-b023-e38311011932" />
      <placeholder
        name="HideItemActions"
        value="True" />
      <placeholder
        name="FullPage"
        value="True" />
      <placeholder
        name="EntityType"
        value="ITransportRouting[[IJobConsol]]" />
      <placeholder
        name="DefaultFilter">
        <xml>
          <ArrayOfFilterGroup xmlns:a="http://schemas.microsoft.com/2003/10/Serialization/Arrays" xmlns:i="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://cargowise.com/glow/2014/07/16/filters.xsd">
            <FilterGroup>
              <Filters>
                <Filter>
                  <FilterType>AdvancedGuidLookupFilter</FilterType>
                  <Operation>Is</Operation>
                  <PropertyPath>JW_ParentGUID</PropertyPath>
                  <Values>
                    <a:string>&lt;JKVV_PK&gt;</a:string>
                  </Values>
                </Filter>
              </Filters>
              <IsImplicit>true</IsImplicit>
            </FilterGroup>
          </ArrayOfFilterGroup>
        </xml>
      </placeholder>
      <placeholder
        name="FieldConfiguration">
        <xml>
          <fields xmlns="">
            <field
              path="JW_CarrierBookingReference"
              width="250"
              mode="Default" />
            <field
              path="RouteSetNumber"
              width="200"
              mode="Default" />
            <field
              path="JW_LegOrder"
              width="90"
              mode="Default" />
            <field
              path="JW_TransportMode"
              width="140"
              mode="Default" />
            <field
              path="JW_TransportType"
              width="140"
              mode="Default" />
            <field
              path="JW_Vessel"
              width="250"
              mode="Default" />
            <field
              path="JW_VoyageFlight"
              width="130"
              mode="Default" />
            <field
              path="JW_RL_NKLoadPort"
              width="90"
              mode="Default" />
            <field
              path="JW_RL_NKDiscPort"
              width="90"
              mode="Default" />
            <field
              path="JW_ETD"
              width="180"
              mode="Default" />
            <field
              path="JW_ETA"
              width="180"
              mode="Default" />
            <field
              path="JW_AircraftType"
              width="100"
              mode="Optional" />
            <field
              path="JW_ATA"
              width="100"
              mode="Optional" />
            <field
              path="JW_ATD"
              width="100"
              mode="Optional" />
            <field
              path="JW_CustomDecimal1"
              width="100"
              mode="Optional" />
            <field
              path="JW_CustomFlag1"
              width="100"
              mode="Optional" />
            <field
              path="JW_CustomLink1"
              width="100"
              mode="FilterOnly" />
            <field
              path="JW_DepotAvailabilityDate"
              width="100"
              mode="Optional" />
            <field
              path="JW_DepotCutOff"
              width="100"
              mode="Optional" />
            <field
              path="JW_DepotReceivalCommences"
              width="100"
              mode="Optional" />
            <field
              path="JW_DepotStorageDate"
              width="100"
              mode="Optional" />
            <field
              path="JW_Distance"
              width="100"
              mode="Optional" />
            <field
              path="JW_DistanceUnit"
              width="100"
              mode="Optional" />
            <field
              path="JW_DocumentaryCutOff"
              width="100"
              mode="Optional" />
            <field
              path="JW_IsCargoOnly"
              width="100"
              mode="Optional" />
            <field
              path="JW_IsCharter"
              width="100"
              mode="Optional" />
            <field
              path="JW_IsLinked"
              width="100"
              mode="Optional" />
            <field
              path="JW_JX"
              width="100"
              mode="Optional" />
            <field
              path="JW_OA_ArrivalLocation"
              width="100"
              mode="Optional" />
            <field
              path="JW_OA_CarrierAddress"
              width="100"
              mode="Optional" />
            <field
              path="JW_OA_CreditorAddress"
              width="100"
              mode="Optional" />
            <field
              path="JW_OA_DepartureLocation"
              width="100"
              mode="Optional" />
            <field
              path="JW_OnlineScheduleStatus"
              width="100"
              mode="Optional" />
            <field
              path="JW_ParentGUID"
              width="100"
              mode="Optional" />
            <field
              path="JW_PL_NKCarrierServiceLevel"
              width="100"
              mode="Optional" />
            <field
              path="JW_RQ_TruckType"
              width="100"
              mode="Optional" />
            <field
              path="JW_STA"
              width="100"
              mode="Optional" />
            <field
              path="JW_Status"
              width="100"
              mode="Optional" />
            <field
              path="JW_STD"
              width="100"
              mode="Optional" />
            <field
              path="JW_TerminalAvailabilityDate"
              width="100"
              mode="Optional" />
            <field
              path="JW_TerminalCutOff"
              width="100"
              mode="Optional" />
            <field
              path="JW_TerminalReceivalCommences"
              width="100"
              mode="Optional" />
            <field
              path="JW_TerminalStorageDate"
              width="100"
              mode="Optional" />
            <field
              path="JW_VesselScreeningStatus"
              width="100"
              mode="Optional" />
            <field
              path="JW_VGMCutOff"
              width="100"
              mode="Optional" />
            <field
              path="JW_ServiceString"
              width="300"
              mode="Optional" />
          </fields>
        </xml>
      </placeholder>
    </control>
  </form>
