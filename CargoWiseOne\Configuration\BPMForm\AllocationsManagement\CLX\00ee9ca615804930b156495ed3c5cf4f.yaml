#transformationVersion: 70.0
#
VZ_PK: 00ee9ca615804930b156495ed3c5cf4f
VZ_ConfigurationKey: 00ee9ca6-1580-4930-b156-495ed3c5cf4f
VZ_FormID: CLX - Client Contracts Landing Page
VZ_Caption:
  resKey: VZ_Caption|00ee9ca6-1580-4930-b156-495ed3c5cf4f
  text: Client Contract & Allocations
VZ_FormFactor: DSK
VZ_FormType: PAG
VZ_RequiresLogin: true
VZ_Dependencies: <dependencies xmlns="http://cargowise.com/glow/2014/11/14/dependencies.xsd" />
VZ_FormData: >-
  <form
    id="929ff03a-9a04-4bfe-8ed6-0951748286a3" xmlns="http://wisetechglobal.com/glow/2017/05/26/form.xsd">
    <placeholder
      name="BackgroundImagePk"
      value="" />
    <placeholder
      name="BackgroundSize"
      value="Auto" />
    <placeholder
      name="BackgroundImageOverride"
      value="" />
    <placeholder
      name="FormLayout"
      value="Splitview" />
    <placeholder
      name="ExpandPaths"
      value="" />
    <placeholder
      name="ScanInputHandling"
      value="NON" />
    <placeholder
      name="RemovePadding"
      value="false" />
    <placeholder
      name="TurnOnPortableOneToOneDesignMode"
      value="false" />
    <placeholder
      name="ContainsDefaultButton"
      value="False" />
    <control
      code="TIL"
      id="50922004-eb3b-48c7-bc3e-dd7e7d088698"
      left="0"
      top="0"
      width="4"
      height="2">
      <placeholder
        name="CanBeHidden"
        value="True" />
      <placeholder
        name="VisibilityCondition"
        value="" />
      <placeholder
        name="Size"
        value="Wide" />
      <placeholder
        name="Color"
        value="Primary" />
      <placeholder
        name="DisplayMode"
        value="TextAndIcon" />
      <placeholder
        name="Text"
        value="New Contract"
        resid="a4422ea1-2fe0-4276-8644-0952ff5e36eb" />
      <placeholder
        name="Image"
        value="74a27a164dc145d4b5a8916b1376be68" />
      <placeholder
        name="ImageOverride"
        value="" />
      <placeholder
        name="Description"
        value="" />
      <placeholder
        name="Behaviour"
        value="FormFlow" />
      <placeholder
        name="PagePK"
        value="" />
      <placeholder
        name="Url"
        value="www.google.com" />
      <placeholder
        name="FormFlow"
        value="e814feb2b3624d6c927d319374098c70" />
    </control>
    <control
      code="SRL"
      id="871f9128-a16b-4e44-a232-456d580ff254"
      left="5"
      top="0"
      right="1"
      bottom="1">
      <placeholder
        name="CanBeHidden"
        value="False" />
      <placeholder
        name="IsInitialFocus"
        value="False" />
      <placeholder
        name="CaptionOverride"
        value="Client Contracts"
        resid="ac06ee79-9906-4131-8c77-32b5df7ce32e" />
      <placeholder
        name="VisibilityCondition"
        value="" />
      <placeholder
        name="HeaderAlignment"
        value="Left" />
      <placeholder
        name="HeaderDisplayMode"
        value="Visible" />
      <placeholder
        name="FilterType"
        value="Entity" />
      <placeholder
        name="EntityType"
        value="IClientContract" />
      <placeholder
        name="ItemDisplayPath"
        value="" />
      <placeholder
        name="DisabledGridRowActions"
        value="" />
      <placeholder
        name="DefaultFilter">
        <xml>
          <ArrayOfFilterGroup xmlns:a="http://schemas.microsoft.com/2003/10/Serialization/Arrays" xmlns:i="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://cargowise.com/glow/2014/07/16/filters.xsd">
            <FilterGroup>
              <Filters>
                <Filter>
                  <FilterType>StringFilter</FilterType>
                  <Operation>Is</Operation>
                  <PropertyPath>RCT_ContractType</PropertyPath>
                  <Values>
                    <a:string>CLI</a:string>
                  </Values>
                </Filter>
              </Filters>
              <IsImplicit>true</IsImplicit>
            </FilterGroup>
          </ArrayOfFilterGroup>
        </xml>
      </placeholder>
      <placeholder
        name="FieldConfiguration"
        value="" />
      <placeholder
        name="DefaultSortFields"
        value="" />
      <placeholder
        name="DefaultGroupFields"
        value="" />
      <placeholder
        name="EnableGrouping"
        value="False" />
      <placeholder
        name="AutoRefreshInterval"
        value="0" />
      <placeholder
        name="AllowMultiSelect"
        value="True" />
      <placeholder
        name="ShowWorkflowLabel"
        value="True" />
      <placeholder
        name="RowActionsMode"
        value="RowAndMenu" />
      <placeholder
        name="HideGridActions"
        value="False" />
      <placeholder
        name="MinimizeFilters"
        value="False" />
      <placeholder
        name="WorkflowOpenInNewWindow"
        value="False" />
      <placeholder
        name="SearchOnInit"
        value="True" />
      <placeholder
        name="ShowQuickSearch"
        value="False" />
      <placeholder
        name="QuickSearchCaption"
        value="" />
      <placeholder
        name="ValueField"
        value="" />
      <placeholder
        name="Category"
        value="" />
      <placeholder
        name="ActionMenuItems">
        <xml>
          <formFlows xmlns="">
            <formFlow
              newSession="True">fff6983ac1074c5bbb38f2c5eae390be</formFlow>
          </formFlows>
        </xml>
      </placeholder>
      <placeholder
        name="EditFormFlow"
        value="fff6983ac1074c5bbb38f2c5eae390be" />
      <placeholder
        name="ResetFilterOnInit"
        value="False" />
      <placeholder
        name="FilterDisplayMode"
        value="InlineAndPopup" />
    </control>
  </form>
