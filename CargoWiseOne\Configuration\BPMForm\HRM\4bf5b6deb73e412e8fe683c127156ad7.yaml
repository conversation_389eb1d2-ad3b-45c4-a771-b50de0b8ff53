#transformationVersion: 70.0
#
VZ_PK: 4bf5b6deb73e412e8fe683c127156ad7
VZ_ConfigurationKey: 4bf5b6de-b73e-412e-8fe6-83c127156ad7
VZ_FormID: HRM - Change Request Template Selector
VZ_Caption:
  resKey: VZ_Caption|4bf5b6de-b73e-412e-8fe6-83c127156ad7
  text: Propose a change to this staff member
VZ_FormFactor: DSK
VZ_EntityType: IGlbStaffChangeRequest
VZ_FormType: FRM
VZ_RequiresLogin: true
VZ_MaterialDesign: true
VZ_Dependencies: <dependencies xmlns="http://cargowise.com/glow/2014/11/14/dependencies.xsd" />
VZ_FormData: >-
  <form
    id="004d2428-519a-49a4-b673-7d67050811b4" xmlns="http://wisetechglobal.com/glow/2017/05/26/form.xsd">
    <placeholder
      name="Width"
      value="900" />
    <placeholder
      name="Layout"
      value="grid" />
    <control
      code="ESB"
      id="76bf42ab-925a-4383-be7d-8192980dca92"
      binding="">
      <placeholder
        name="EntityType"
        value="IGlbStaffChangeRequestTemplate" />
      <placeholder
        name="Transition"
        value="True" />
      <placeholder
        name="CaptionOverride"
        value="Select the kind of change you would like to make"
        resid="38502571-f224-4d84-94e5-338ad309ea5a" />
      <placeholder
        name="DefaultFilter">
        <xml>
          <ArrayOfFilterGroup xmlns:a="http://schemas.microsoft.com/2003/10/Serialization/Arrays" xmlns:i="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://cargowise.com/glow/2014/07/16/filters.xsd">
            <FilterGroup>
              <Filters>
                <Filter>
                  <FilterType>SimpleLookupFilter</FilterType>
                  <Operation>IsNot</Operation>
                  <PropertyPath>GlbStaffChangeRequestTemplateItems.GGI_PropertyName</PropertyPath>
                  <Values>
                    <a:string>BOD</a:string>
                  </Values>
                </Filter>
                <Filter>
                  <FilterType>SimpleLookupFilter</FilterType>
                  <Operation>Is</Operation>
                  <PropertyPath>GSG_Usage</PropertyPath>
                  <Values>
                    <a:string>REV</a:string>
                  </Values>
                </Filter>
              </Filters>
              <IsImplicit>true</IsImplicit>
            </FilterGroup>
          </ArrayOfFilterGroup>
        </xml>
      </placeholder>
      <placeholder
        name="VisibilityCondition"
        value="Usage==&quot;REV&quot;" />
    </control>
    <control
      code="ESB"
      id="251c47aa-defb-4bd1-b6a4-2fd19d0a63b5"
      binding="">
      <placeholder
        name="EntityType"
        value="IGlbStaffChangeRequestTemplate" />
      <placeholder
        name="Transition"
        value="True" />
      <placeholder
        name="CaptionOverride"
        value="Select the kind of change you would like to make"
        resid="38502571-f224-4d84-94e5-338ad309ea5a" />
      <placeholder
        name="DefaultFilter">
        <xml>
          <ArrayOfFilterGroup xmlns:a="http://schemas.microsoft.com/2003/10/Serialization/Arrays" xmlns:i="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://cargowise.com/glow/2014/07/16/filters.xsd">
            <FilterGroup>
              <Filters>
                <Filter>
                  <FilterType>SimpleLookupFilter</FilterType>
                  <Operation>IsNot</Operation>
                  <PropertyPath>GlbStaffChangeRequestTemplateItems.GGI_PropertyName</PropertyPath>
                  <Values>
                    <a:string>BOD</a:string>
                  </Values>
                </Filter>
                <Filter>
                  <FilterType>SimpleLookupFilter</FilterType>
                  <Operation>Is</Operation>
                  <PropertyPath>GSG_Usage</PropertyPath>
                  <Values>
                    <a:string>ALW</a:string>
                  </Values>
                </Filter>
              </Filters>
              <IsImplicit>true</IsImplicit>
            </FilterGroup>
          </ArrayOfFilterGroup>
        </xml>
      </placeholder>
      <placeholder
        name="VisibilityCondition"
        value="Usage==&quot;ALW&quot;" />
    </control>
    <control
      code="ERR"
      id="b59837fa-afbb-4bef-87cb-d28484d7b6d6"
      binding="">
      <placeholder
        name="VisibilityCondition"
        value="SelectedTemplateValidationResult == &quot;JRC&quot; " />
      <placeholder
        name="Caption"
        value="You do not have security rights to apply for the Job Role change"
        resid="e1f37b24-41ef-474f-9455-12fad97e813e" />
    </control>
    <control
      code="ERR"
      id="473a4b1c-81ae-4321-89a5-77dd0aebf41a"
      binding="">
      <placeholder
        name="VisibilityCondition"
        value="SelectedTemplateValidationResult == &quot;TC&quot; " />
      <placeholder
        name="Caption"
        value="You do not have security rights to apply for the Team change"
        resid="8f73feee-78c1-4b60-8da9-241dd1c22cbc" />
    </control>
    <control
      code="ERR"
      id="cd0765ff-224c-4ead-9e8a-0321e9a318b4"
      binding="">
      <placeholder
        name="VisibilityCondition"
        value="SelectedTemplateValidationResult == &quot;MC&quot; " />
      <placeholder
        name="Caption"
        value="You do not have security rights to apply for the Manager change"
        resid="48afe40e-172d-48a9-bef6-08f03f82a1f6" />
    </control>
    <control
      code="ERR"
      id="a9478162-660f-4149-8ac3-83c2ed1c99c3"
      binding="">
      <placeholder
        name="VisibilityCondition"
        value="SelectedTemplateValidationResult == &quot;WPC&quot; " />
      <placeholder
        name="Caption"
        value="You do not have security rights to apply for the Work Pattern change"
        resid="797c1667-0459-4664-b659-9a416477b0e7" />
    </control>
    <control
      code="ERR"
      id="2c16a9ca-2857-44b7-b1e4-7bc9ce69c5e9"
      binding="">
      <placeholder
        name="Caption"
        value="Some of the required documents are missing for this Change Request"
        resid="0ae2a9dc-0cb6-43ba-a1c5-3a48f52d2508" />
      <placeholder
        name="VisibilityCondition"
        value="SelectedTemplateValidationResult == &quot;DOC&quot; " />
    </control>
    <control
      code="DAE"
      id="2a48733e-70c4-4d29-9e5f-64589d737d2e"
      binding="EffectiveDate">
      <placeholder
        name="VisibilityCondition"
        value="Template != null" />
      <placeholder
        name="CaptionOverride"
        value="When should the change take effect?"
        resid="9a218d1e-25db-42e5-8d69-6687895edd34" />
      <placeholder
        name="CaptionType"
        value="short" />
    </control>
    <control
      code="BOX"
      id="3106a483-14c9-4d21-a8f2-69545e4671b4"
      binding="">
      <control
        code="LBL"
        id="d1af701f-e519-46d5-9836-a9a737d56c9e"
        binding="">
        <placeholder
          name="Caption"
          value="Please check each tab to ensure that all relevant information has been provided"
          resid="c787bc2b-2e86-4ee8-8ea8-e85a05960a65" />
        <placeholder
          name="Typography"
          value="body" />
        <placeholder
          name="VisibilityCondition"
          value="Template != null" />
      </control>
    </control>
    <control
      code="TBS"
      id="ebb8ab53-80e2-4480-b92b-8577a3077129"
      binding="">
      <placeholder
        name="VisibilityCondition"
        value="Template != null" />
      <control
        code="TAB"
        id="7e299714-af73-455c-b87d-a163d2810a78"
        binding="">
        <placeholder
          name="Caption"
          value="Role"
          resid="1e3da38e-902a-4e2c-949b-ed896a0f44ba" />
        <placeholder
          name="VisibilityCondition"
          value="Template.HasJobRole" />
      </control>
      <control
        code="TAI"
        id="b5929db0-1668-4653-aac3-c76ee5ae37bf"
        binding="">
        <placeholder
          name="VisibilityCondition"
          value="Template.HasJobRole" />
        <control
          code="BOX"
          id="2e4d5788-5323-4102-8c12-50634999b27b">
          <placeholder
            name="Margin"
            value="mt-2" />
          <placeholder
            name="Layout"
            value="grid" />
          <control
            code="TXT"
            id="17f9c55b-f8ce-4ce2-8315-787a89fd2227"
            binding="JobRoleOnEffectiveDate.GEH_JobTitle">
            <placeholder
              name="IsReadOnly"
              value="True" />
            <placeholder
              name="CaptionOverride"
              value="Job title before change"
              resid="eb71e5d7-e62e-4705-a678-d84b49aaa7de" />
          </control>
          <control
            code="CMB"
            id="40a95458-e9ef-4ec3-92ac-d2d8a61af856"
            binding="JobRoleOnEffectiveDate.GEH_JobFamily">
            <placeholder
              name="IsReadOnly"
              value="True" />
            <placeholder
              name="CaptionOverride"
              value="Job family before change"
              resid="a3cb8b43-fcff-4d62-a36e-60eafd343583" />
          </control>
          <control
            code="OPT"
            id="cc02ec18-ca66-4c6c-860f-f7b099d13398"
            binding="NoChangeToRole">
            <placeholder
              name="VisibilityCondition"
              value="Template.GlbStaffChangeRequestTemplateItems.Count() &gt; 1" />
            <placeholder
              name="CaptionOverride"
              value="No change to role"
              resid="4830fb53-4a53-4219-b288-c6f4aef8fa7d" />
            <placeholder
              name="Margin"
              value="mt-2" />
          </control>
          <control
            code="TXT"
            id="010014e1-a61e-4297-a8ce-2be64300924f"
            binding="GlbEmploymentHistories/GEH_JobTitle">
            <placeholder
              name="VisibilityCondition"
              value="!NoChangeToRole" />
            <placeholder
              name="CaptionOverride"
              value="New job title"
              resid="3299df74-32e8-48c9-ae20-7c4d8035ccef" />
            <placeholder
              name="Margin"
              value="mt-0" />
          </control>
          <control
            code="CMB"
            id="552e67fb-32c6-494b-98e7-7b861e2f29f8"
            binding="GlbEmploymentHistories/GEH_JobFamily">
            <placeholder
              name="VisibilityCondition"
              value="!NoChangeToRole" />
            <placeholder
              name="CaptionOverride"
              value="New job family"
              resid="d21619d2-c863-4cfc-b57a-3d609a880c6d" />
          </control>
          <control
            code="OPT"
            id="ef20c34a-18d1-4a75-8a5d-26cfec9f114e"
            binding="GlbEmploymentHistories/GEH_IsPromotion">
            <placeholder
              name="VisibilityCondition"
              value="!NoChangeToRole" />
            <placeholder
              name="CaptionOverride"
              value="This is a promotion"
              resid="4c45caef-6813-4da9-aaa3-a25818d89bed" />
            <placeholder
              name="Margin"
              value="mt-2" />
          </control>
        </control>
      </control>
      <control
        code="TAB"
        id="36dae4fb-2d80-4c53-be32-de54cd80474f"
        binding="">
        <placeholder
          name="Caption"
          value="Team"
          resid="98852adb-368c-47ac-9c23-5fbd96c72d82" />
        <placeholder
          name="VisibilityCondition"
          value="Template.HasTeam" />
      </control>
      <control
        code="TAI"
        id="529362f8-2c5b-4523-9303-426b7823050a"
        binding="">
        <placeholder
          name="VisibilityCondition"
          value="Template.HasTeam" />
        <control
          code="BOX"
          id="585a43d0-fad6-4291-b775-2c31eb6d306f">
          <placeholder
            name="Margin"
            value="mt-2" />
          <placeholder
            name="Layout"
            value="grid" />
          <control
            code="SRC"
            id="be3e57e2-70e6-4b4d-badb-178e6fedf13b"
            binding="TeamOnEffectiveDate.GET_GST_NKTeamCode">
            <placeholder
              name="IsReadOnly"
              value="True" />
            <placeholder
              name="CaptionOverride"
              value="Team before change"
              resid="e8d83a53-7da4-4b63-a6da-580ce8517f7e" />
          </control>
          <control
            code="OPT"
            id="0255bb61-1c29-4c0f-81f1-5fc797f3a6a6"
            binding="NoChangeToTeam">
            <placeholder
              name="VisibilityCondition"
              value="Template.GlbStaffChangeRequestTemplateItems.Count() &gt; 1" />
            <placeholder
              name="CaptionOverride"
              value="No change to team"
              resid="9e58964e-b20c-48b7-9b6c-4381100424c6" />
          </control>
          <control
            code="SRC"
            id="9d72cf50-3b58-4865-837f-d97acd3b6d7e"
            binding="GlbEmploymentTeams/GET_GST_NKTeamCode">
            <placeholder
              name="VisibilityCondition"
              value="!NoChangeToTeam" />
            <placeholder
              name="CaptionOverride"
              value="New team"
              resid="c4b26010-cd81-433b-9fd5-79916331aedf" />
            <placeholder
              name="Margin"
              value="mt-2" />
          </control>
          <control
            code="TXT"
            id="dd8160b1-6ed0-49c9-bb2e-9943a31488a4"
            binding="GlbEmploymentTeams/ParentTeam">
            <placeholder
              name="VisibilityCondition"
              value="!NoChangeToTeam" />
            <placeholder
              name="CaptionOverride"
              value="New parent team"
              resid="e7e678cd-acbb-4bc9-bc54-6b62c65e5c2b" />
          </control>
        </control>
      </control>
      <control
        code="TAB"
        id="d071d199-28b6-4de5-b4d8-6761c299bc22"
        binding="">
        <placeholder
          name="Caption"
          value="Manager"
          resid="f8bf0429-e22e-4949-bd0a-bb52d170929c" />
        <placeholder
          name="VisibilityCondition"
          value="Template.HasManager" />
      </control>
      <control
        code="TAI"
        id="0b34ae1e-05cc-4f69-8374-da76361d41ef"
        binding="">
        <placeholder
          name="VisibilityCondition"
          value="Template.HasManager" />
        <control
          code="BOX"
          id="4c366fc0-a7e8-4f2d-8c34-2c6400b13f15">
          <placeholder
            name="Margin"
            value="mt-2" />
          <placeholder
            name="Layout"
            value="grid" />
          <control
            code="CMB"
            id="9c169a4d-241d-4f24-bbfe-72bcffc0b67e"
            binding="GlbStaffManagers/GSM_ManagerType">
            <placeholder
              name="CaptionOverride"
              value="Manager Type"
              resid="4fcedb39-74be-4a85-85ba-8df36f1b7de4" />
          </control>
          <control
            code="SRC"
            id="0f44bfd1-5f92-4997-9624-f92b0c5fdc90"
            binding="ManagerOnEffectiveDate.GSM_GS_Manager">
            <placeholder
              name="CaptionOverride"
              value="Manager before change"
              resid="6b064618-6fc0-4cca-9b8e-ae46009a68fd" />
            <placeholder
              name="IsReadOnly"
              value="True" />
          </control>
          <control
            code="OPT"
            id="7d52b38e-f79e-492e-b09e-346d4717482f"
            binding="NoChangeToManager">
            <placeholder
              name="VisibilityCondition"
              value="Template.GlbStaffChangeRequestTemplateItems.Count() &gt; 1" />
            <placeholder
              name="CaptionOverride"
              value="No change to manager"
              resid="7b948f5e-2c4e-4880-ae35-4c6ce96c17b8" />
          </control>
          <control
            code="SRC"
            id="97add286-5a9d-47d3-8a59-d53847effabd"
            binding="GlbStaffManagers/GSM_GS_Manager">
            <placeholder
              name="VisibilityCondition"
              value="!NoChangeToManager" />
            <placeholder
              name="CaptionOverride"
              value="New manager (search by staff code or name)"
              resid="ad1f356b-7d8d-4ecc-829b-5946e11707d9" />
            <placeholder
              name="Margin"
              value="mt-2" />
          </control>
        </control>
      </control>
      <control
        code="TAB"
        id="c33938c5-4b1c-43d5-a7c1-6d173b443e8a"
        binding="">
        <placeholder
          name="Caption"
          value="Work Pattern"
          resid="733c9c2c-8cfc-4544-af43-7711753c67a6" />
        <placeholder
          name="VisibilityCondition"
          value="Template.HasWorkPattern" />
      </control>
      <control
        code="TAI"
        id="cb72084f-5a16-4476-815a-cdf1086ba647"
        binding="">
        <placeholder
          name="VisibilityCondition"
          value="Template.HasWorkPattern" />
        <control
          code="BOX"
          id="de82dc34-3695-4ba4-b462-cc1d1496678f">
          <placeholder
            name="Layout"
            value="grid" />
          <placeholder
            name="Margin"
            value="mt-2" />
          <control
            code="LBL"
            id="c26d9ff4-677c-4e05-9817-f2c5bfa38b0a"
            binding="">
            <placeholder
              name="Caption"
              value="Work pattern before change"
              resid="9e5ba754-412f-4b59-b3a3-5e075eaa1884" />
            <placeholder
              name="Typography"
              value="body" />
          </control>
          <control
            code="SCH"
            id="472a10c9-7f01-4ca8-b637-bbe3f8163c4a"
            binding="">
            <placeholder
              name="IsReadOnly"
              value="True" />
            <placeholder
              name="ViewModel"
              value="WeekScheduleViewModel" />
            <placeholder
              name="ViewModelDataItem"
              value="WorkPatternOnEffectiveDate" />
            <placeholder
              name="Padding"
              value="px-2" />
          </control>
        </control>
        <control
          code="OPT"
          id="74956758-c2b0-400e-91ac-74f5d4c9d046"
          binding="NoChangeToWorkPattern">
          <placeholder
            name="VisibilityCondition"
            value="Template.GlbStaffChangeRequestTemplateItems.Count() &gt; 1" />
          <placeholder
            name="CaptionOverride"
            value="No change to work pattern"
            resid="10fcd0b6-6c2c-447e-a9ed-c8e17d6bf33a" />
        </control>
        <control
          code="BOX"
          id="4de58541-225d-4c72-a29b-4c4f109617a8"
          binding="">
          <placeholder
            name="Margin"
            value="mt-2" />
          <placeholder
            name="VisibilityCondition"
            value="!NoChangeToWorkPattern" />
          <placeholder
            name="Layout"
            value="grid" />
          <control
            code="LBL"
            id="9b580222-e1fe-49a3-ad9a-9c944145758d">
            <placeholder
              name="Caption"
              value="New work pattern"
              resid="2d890aae-ea7f-4838-8e94-1acae6e4ac32" />
            <placeholder
              name="Typography"
              value="body" />
          </control>
          <control
            code="ESB"
            id="03f8f04b-7495-4f14-b59f-915b47c536c2"
            binding="">
            <placeholder
              name="EntityType"
              value="IGlbWorkPatternTemplate" />
            <placeholder
              name="Transition"
              value="True" />
            <placeholder
              name="Columns"
              value="col-sm-6" />
            <placeholder
              name="CaptionOverride"
              value="Select a template"
              resid="70dec1dd-abaf-4675-93ec-4b39867d4e69" />
          </control>
          <control
            code="DUR"
            id="d05acc06-8c5d-43d9-a42b-199f57a86815"
            binding="WorkPattern.GWP_StandardDuration">
            <placeholder
              name="Columns"
              value="col-sm-6" />
          </control>
          <control
            code="SCH"
            id="922e98f0-ed22-4c89-966a-dae9f80136a1"
            binding="">
            <placeholder
              name="ViewModel"
              value="WeekScheduleViewModel" />
            <placeholder
              name="ShowTicks"
              value="True" />
            <placeholder
              name="ShowInactiveDays"
              value="True" />
            <placeholder
              name="ViewModelDataItem"
              value="WorkPattern" />
          </control>
        </control>
      </control>
      <control
        code="TAB"
        id="2d924594-8435-4f5e-87f2-e05c06ee8011">
        <placeholder
          name="Caption"
          value="Documents"
          resid="bec6f6cd-6054-4866-9d0b-a252db8bfce4" />
      </control>
      <control
        code="TAI"
        id="1eeb74d8-d961-462d-8e6d-2d2c9940f6ab">
        <control
          code="RLS"
          id="ac3ac07c-4a92-4806-bb38-3ee10ab61f4a"
          binding="Template.GlbStaffChangeRequestTemplateDocuments">
          <placeholder
            name="CaptionOverride"
            value="Required Documents" />
          <placeholder
            name="HideDefaultFooter"
            value="True" />
          <placeholder
            name="Columns"
            value="" />
          <placeholder
            name="ContentTemplateID"
            value="a1fec9c2cf8343ceb9a49d7a0f67eb58" />
          <placeholder
            name="Caption"
            value="Required documents"
            resid="c35e13c8-0e4d-4c54-834d-8564edcc027e" />
          <placeholder
            name="IsReadOnly"
            value="True" />
          <placeholder
            name="HideItemActions"
            value="True" />
          <placeholder
            name="ShowSearch"
            value="False" />
          <placeholder
            name="VisibilityCondition"
            value="Template.GlbStaffChangeRequestTemplateDocuments.Any()" />
          <placeholder
            name="HideActions"
            value="True" />
          <placeholder
            name="Margin"
            value="mt-2" />
        </control>
        <control
          code="BOX"
          id="816810bd-d32a-475a-9ef6-693e6ee499cb"
          binding="">
          <placeholder
            name="Margin"
            value="mt-5" />
          <control
            code="FIL"
            id="59576218-3a09-4db7-a8e2-86e2aa7f4ec7" />
        </control>
      </control>
    </control>
  </form>
