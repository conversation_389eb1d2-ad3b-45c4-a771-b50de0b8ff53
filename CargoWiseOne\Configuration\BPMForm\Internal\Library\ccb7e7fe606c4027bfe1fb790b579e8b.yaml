#transformationVersion: 70.0
#
VZ_PK: ccb7e7fe606c4027bfe1fb790b579e8b
VZ_ConfigurationKey: ccb7e7fe-606c-4027-bfe1-fb790b579e8b
VZ_FormID: Form-flow Configuration on Controls Example
VZ_Caption:
  resKey: VZ_Caption|ccb7e7fe606c4027bfe1fb790b579e8b
  text: Form-flow Configuration on Controls Example
VZ_FormFactor: DSK
VZ_EntityType: IDtbLandTransportConsignment
VZ_FormType: FRM
VZ_RequiresLogin: true
VZ_MaterialDesign: true
VZ_Dependencies: <dependencies xmlns="http://cargowise.com/glow/2014/11/14/dependencies.xsd" />
VZ_FormData: >-
  <form
    id="c66d555d-68a4-4d4e-8ed8-838b2aac8ff5" xmlns="http://wisetechglobal.com/glow/2017/05/26/form.xsd">
    <placeholder
      name="Layout"
      value="fill" />
    <control
      code="PNL"
      id="55b1bbdf-5b51-4e15-acbb-ac13714c74d6"
      binding="">
      <placeholder
        name="Margin"
        value="mb-3" />
      <placeholder
        name="Class"
        value="blue lighten-4 bg-blue-lighten-4" />
      <control
        code="LBL"
        id="e3a98853-dbef-4a95-b5e8-33d82fdfa08c"
        binding="">
        <placeholder
          name="Display"
          value="block" />
        <placeholder
          name="Typography"
          value="body-strong" />
        <placeholder
          name="Caption"
          value="For the controls below, when you click them you will find a popup dialog of another form which was configured with &quot;Show In Dialog&quot;."
          resid="940341e7-4b36-440c-af89-2c713e0c9de5" />
      </control>
    </control>
    <control
      code="PNL"
      id="6abfe86a-5c59-42f3-ad72-3738a2f1f67e"
      binding="">
      <placeholder
        name="Caption"
        value="Controls"
        resid="b6e9a2d5-143a-494e-9515-1245507ba7a4" />
      <placeholder
        name="Margin"
        value="mb-3" />
      <control
        code="BOX"
        id="c8c7bb93-c9a6-46c7-a8d7-726fe093645f">
        <placeholder
          name="Layout"
          value="flex" />
        <control
          code="BTN"
          id="a653599b-73e8-4e75-a0ca-b4a9588fb325"
          binding="">
          <placeholder
            name="Caption"
            value="Button (BTN)"
            resid="9df73d4b-6b50-43c6-9238-0b8dfb8e0516" />
          <placeholder
            name="FormFlowConfiguration">
            <xml>
              <formFlows xmlns="">
                <formFlow
                  inDialog="True">196d5a4cf3f24dc0902036d132abe0e4</formFlow>
              </formFlows>
            </xml>
          </placeholder>
        </control>
        <control
          code="IBT"
          id="31c66032-6b5c-4223-8556-25f14ceac1f8">
          <placeholder
            name="Icon"
            value="s-icon-vue" />
          <placeholder
            name="Margin"
            value="ml-3" />
          <placeholder
            name="FormFlowConfiguration">
            <xml>
              <formFlows xmlns="">
                <formFlow
                  inDialog="True">196d5a4cf3f24dc0902036d132abe0e4</formFlow>
              </formFlows>
            </xml>
          </placeholder>
        </control>
        <control
          code="LBL"
          id="2e073cf0-3ef7-4df5-8a62-57eec3360df0"
          binding="">
          <placeholder
            name="Margin"
            value="ml-3" />
          <placeholder
            name="Caption"
            value="Label (LBL)"
            resid="01c39749-0edf-43f4-84a0-8051ba0995d3" />
          <placeholder
            name="Padding"
            value="pa-2" />
          <placeholder
            name="FormFlowConfiguration">
            <xml>
              <formFlows xmlns="">
                <formFlow
                  inDialog="True">196d5a4cf3f24dc0902036d132abe0e4</formFlow>
              </formFlows>
            </xml>
          </placeholder>
          <placeholder
            name="Align"
            value="center" />
        </control>
      </control>
    </control>
    <control
      code="PNL"
      id="359520be-8827-47a5-99b9-7a8783caf896"
      binding="">
      <placeholder
        name="Caption"
        value="Containers"
        resid="95c05b84-7d74-4575-9b9c-0c5533fed355" />
      <placeholder
        name="Margin"
        value="mb-3" />
      <placeholder
        name="Layout"
        value="grid" />
      <placeholder
        name="FlexDirection"
        value="flex-column" />
      <control
        code="CRD"
        id="3c897afd-a982-442d-b483-c00aa405481d"
        binding="">
        <placeholder
          name="Columns"
          value="col-md-6" />
        <placeholder
          name="MinHeight"
          value="70" />
        <placeholder
          name="Style"
          value="align-items: center" />
        <placeholder
          name="FormFlowConfiguration">
          <xml>
            <formFlows xmlns="">
              <formFlow
                inDialog="True">196d5a4cf3f24dc0902036d132abe0e4</formFlow>
            </formFlows>
          </xml>
        </placeholder>
        <control
          code="BOX"
          id="6d9c2af6-f019-471f-9188-41ef1c8f1aee">
          <placeholder
            name="Layout"
            value="flex" />
          <placeholder
            name="FillAvailable"
            value="True" />
          <placeholder
            name="FlexJustify"
            value="justify-center" />
          <placeholder
            name="FlexAlign"
            value="align-center" />
          <placeholder
            name="Height"
            value="55" />
          <placeholder
            name="Margin"
            value="ma-2" />
          <control
            code="LBL"
            id="f0bb81b4-ba0d-4804-8638-274f4cd0fe76"
            binding="">
            <placeholder
              name="Caption"
              value="Card (CRD)"
              resid="7924a054-31b8-444d-a05f-0a9dbf65ff42" />
            <placeholder
              name="Align"
              value="center" />
            <placeholder
              name="Display"
              value="block" />
          </control>
        </control>
      </control>
      <control
        code="NCD"
        id="90724935-9de0-4126-8f59-1a62f8e9b3f0"
        binding="">
        <placeholder
          name="Columns"
          value="col-md-6" />
        <placeholder
          name="Caption"
          value="Navigation Card (NCD)"
          resid="0a38e850-d31c-49bc-9d79-24dffd51862a" />
        <placeholder
          name="MinHeight"
          value="70" />
        <placeholder
          name="FormFlowConfiguration">
          <xml>
            <formFlows xmlns="">
              <formFlow
                inDialog="True">196d5a4cf3f24dc0902036d132abe0e4</formFlow>
            </formFlows>
          </xml>
        </placeholder>
      </control>
    </control>
    <control
      code="PNL"
      id="34210f4a-3be4-47d2-805a-9376d9eae1e7"
      binding="">
      <placeholder
        name="Margin"
        value="mb-3" />
      <placeholder
        name="Class"
        value="blue lighten-4 bg-blue-lighten-4" />
      <control
        code="LBL"
        id="6af9511b-c500-4896-9d1b-ea44dd56ed1a"
        binding="">
        <placeholder
          name="Display"
          value="block" />
        <placeholder
          name="Typography"
          value="body-strong" />
        <placeholder
          name="Caption"
          value="For the data tables, as mentioned in the content there are two separate placeholders &quot;Edit Form Flow Configuration&quot; and &quot;New Form Flow Configuration&quot;"
          resid="76007387-b5d3-48fd-8428-9265ce86b55e" />
      </control>
    </control>
    <control
      code="PNL"
      id="2e486c87-72e7-4ca9-b4c4-4d03f08ae17a"
      binding="">
      <placeholder
        name="Caption"
        value="Data Tables"
        resid="eb789863-1120-4c48-b5c9-93aaa68a46c5" />
      <placeholder
        name="FillAvailable"
        value="True" />
      <placeholder
        name="Layout"
        value="fill" />
      <placeholder
        name="FlexWrap"
        value="flex-wrap" />
      <control
        code="SDT"
        id="72f7f830-65f7-4261-b2b6-da9b46f43836"
        binding="">
        <placeholder
          name="CaptionOverride"
          value="Search Data Table (SDT)"
          resid="0160b0aa-6816-47c6-af0a-e9e747f27264" />
        <placeholder
          name="EntityType"
          value="IRefEquipment" />
        <placeholder
          name="ItemsPerPage"
          value="3" />
        <placeholder
          name="EditFormFlowConfiguration">
          <xml>
            <formFlows xmlns="">
              <formFlow
                newSession="True"
                inDialog="True">e3a5a39f06bc4f30abf15ea55399bcf0</formFlow>
            </formFlows>
          </xml>
        </placeholder>
        <placeholder
          name="NewFormFlowConfiguration">
          <xml>
            <formFlows xmlns="">
              <formFlow
                newSession="True"
                inDialog="True">196d5a4cf3f24dc0902036d132abe0e4</formFlow>
              <formFlow>8c914492aa304eb1806f0d1653da0a25</formFlow>
            </formFlows>
          </xml>
        </placeholder>
        <placeholder
          name="ActionMenuItems">
          <xml>
            <formFlows xmlns="">
              <formFlow
                newSession="True"
                inDialog="True">e3a5a39f06bc4f30abf15ea55399bcf0</formFlow>
              <formFlow
                newSession="False"
                inDrawer="True">d2058fddca0f477db5296afe1c31db49</formFlow>
            </formFlows>
          </xml>
        </placeholder>
        <placeholder
          name="Margin"
          value="mb-2" />
        <placeholder
          name="HideFilters"
          value="True" />
      </control>
      <control
        code="DVR"
        id="e3f4c00e-d73f-41d4-b281-a45f9e005e60" />
      <control
        code="RDT"
        id="1eab6864-5b0d-4f8a-83f5-56d610688735"
        binding="AdditionalServices">
        <placeholder
          name="Margin"
          value="mt-2" />
        <placeholder
          name="CaptionOverride"
          value="Related Data Table (RDT)"
          resid="461ba2aa-8479-4576-8e19-7118dbc03212" />
        <placeholder
          name="ItemsPerPage"
          value="3" />
        <placeholder
          name="AllowAdd"
          value="True" />
        <placeholder
          name="NewFormFlowConfiguration">
          <xml>
            <formFlows xmlns="">
              <formFlow
                newSession="True"
                inDialog="True">4342e855019b47e4bec73f729a395b21</formFlow>
            </formFlows>
          </xml>
        </placeholder>
        <placeholder
          name="ActionMenuItems">
          <xml>
            <formFlows xmlns="">
              <formFlow
                newSession="False"
                inDialog="True">e5b124399efc4570ba0f48f27e007198</formFlow>
              <formFlow
                inDrawer="True">7d60910924d04137ad4569e633d48fb5</formFlow>
            </formFlows>
          </xml>
        </placeholder>
        <placeholder
          name="EditFormFlow"
          value="e5b124399efc4570ba0f48f27e007198" />
        <placeholder
          name="FitToHeight"
          value="True" />
      </control>
    </control>
  </form>
