#transformationVersion: 70.0
#
VZ_PK: a3353cedae044e5197e3277e99b6f664
VZ_ConfigurationKey: a3353ced-ae04-4e51-97e3-277e99b6f664
VZ_FormID: HRM - Bulk Import - Teams
VZ_Caption:
  resKey: VZ_Caption|a3353ced-ae04-4e51-97e3-277e99b6f664
  text: Bulk Import/Export - Teams
VZ_FormFactor: DSK
VZ_FormType: PAG
VZ_RequiresLogin: true
VZ_MaterialDesign: true
VZ_Dependencies: <dependencies xmlns="http://cargowise.com/glow/2014/11/14/dependencies.xsd" />
VZ_FormData: >-
  <form
    id="52ea24de-8b0d-4f2c-942b-74145d54eb9a" xmlns="http://wisetechglobal.com/glow/2017/05/26/form.xsd">
    <placeholder
      name="Layout"
      value="fill" />
    <control
      code="SDT"
      id="cd5555b6-05b1-4c63-bdd9-9e908fda0b61"
      binding="">
      <placeholder
        name="CaptionOverride"
        value="Teams"
        resid="99d6c683-d9d1-4f33-baef-b09832c5afaa" />
      <placeholder
        name="EntityType"
        value="IGlbEmploymentTeam" />
      <placeholder
        name="FullPage"
        value="True" />
      <placeholder
        name="SearchMode"
        value="Entity" />
      <placeholder
        name="DefaultFilter">
        <xml>
          <ArrayOfFilterGroup xmlns:a="http://schemas.microsoft.com/2003/10/Serialization/Arrays" xmlns:i="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://cargowise.com/glow/2014/07/16/filters.xsd">
            <FilterGroup>
              <Filters>
                <Filter>
                  <FilterType>IsActiveFilter</FilterType>
                  <Operation>Is</Operation>
                  <PropertyPath>Staff.GS_IsActive</PropertyPath>
                  <Values>
                    <a:string>true</a:string>
                  </Values>
                </Filter>
                <Filter>
                  <FilterType>AdvancedGuidLookupFilter</FilterType>
                  <Operation>Is</Operation>
                  <PropertyPath>GET_GS_Staff</PropertyPath>
                  <Values />
                </Filter>
                <Filter>
                  <FilterType>StringFilter</FilterType>
                  <Operation>Contains</Operation>
                  <PropertyPath>Staff.StaffName</PropertyPath>
                  <Values />
                </Filter>
              </Filters>
              <IsImplicit>false</IsImplicit>
            </FilterGroup>
            <FilterGroup>
              <Filters>
                <Filter>
                  <FilterType>BoolFilter</FilterType>
                  <Operation>Is</Operation>
                  <PropertyPath>GET_IsApproved</PropertyPath>
                  <Values>
                    <a:string>true</a:string>
                  </Values>
                </Filter>
              </Filters>
              <IsImplicit>true</IsImplicit>
            </FilterGroup>
          </ArrayOfFilterGroup>
        </xml>
      </placeholder>
      <placeholder
        name="DefaultSortFields">
        <xml>
          <ArrayOfFieldSortDefinition xmlns:i="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://schemas.datacontract.org/2004/07/CargoWise.Glow.Infrastructure.Interfaces">
            <FieldSortDefinition>
              <FieldName>Staff.GS_FullName</FieldName>
              <IsAscending>true</IsAscending>
            </FieldSortDefinition>
          </ArrayOfFieldSortDefinition>
        </xml>
      </placeholder>
    </control>
  </form>
