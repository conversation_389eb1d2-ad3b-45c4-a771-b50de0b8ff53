#transformationVersion: 70.0
#
VZ_PK: fdf35cd75fb847e081bfbd066c252e7c
VZ_ConfigurationKey: fdf35cd7-5fb8-47e0-81bf-bd066c252e7c
VZ_FormID: CYS View Yard Unit Details
VZ_Caption:
  resKey: VZ_Caption|fdf35cd75fb847e081bfbd066c252e7c
  text: View Yard Unit Details
VZ_FormFactor: MOB
VZ_EntityType: ICYDYardUnitState
VZ_FormType: FRM
VZ_RequiresLogin: true
VZ_MaterialDesign: true
VZ_Dependencies: <dependencies xmlns="http://cargowise.com/glow/2014/11/14/dependencies.xsd" />
VZ_FormData: >-
  <form
    id="39d1df72-a6a4-472a-805f-d591cf5ee199" xmlns="http://wisetechglobal.com/glow/2017/05/26/form.xsd">
    <placeholder
      name="MaxWidth"
      value="600" />
    <placeholder
      name="Padding"
      value="pa-0" />
    <control
      code="BOX"
      id="8d608ecd-8681-401e-98c7-e84538ff6bb1">
      <placeholder
        name="Padding"
        value="pb-3 pl-4" />
      <control
        code="LBL"
        id="f9a09a9e-977b-4635-942a-2e9b8ef9e195">
        <placeholder
          name="Caption"
          value="Yard Unit"
          resid="75644631-8501-4c9b-b55d-e1c749f28afc" />
        <placeholder
          name="Typography"
          value="title-large" />
      </control>
    </control>
    <control
      code="BOX"
      id="6b2fb2a8-b678-4372-be5d-b958a9b5a03c">
      <placeholder
        name="Style"
        value="background-color:#fff; border-top: 1px solid var(--s-neutral-border-weak-default); border-bottom: 1px solid var(--s-neutral-border-weak-default);" />
      <placeholder
        name="Padding"
        value="pa-4" />
      <placeholder
        name="Margin"
        value="mb-4" />
      <control
        code="LBL"
        id="dfbbcf9b-08f7-49e0-8eda-ad28c00932e2">
        <placeholder
          name="Caption"
          value="Container details"
          resid="bb013f23-47a6-43cd-b4e2-5bf0268de020" />
        <placeholder
          name="FontWeight"
          value="bold" />
      </control>
      <control
        code="TXT"
        id="fbaf38f6-fdb5-45b4-9120-4eafac2041e2"
        binding="YUS_UnitID">
        <placeholder
          name="Columns"
          value="2" />
        <placeholder
          name="IsReadOnly"
          value="True" />
        <placeholder
          name="Margin"
          value="mt-3 mb-4" />
      </control>
      <control
        code="BOX"
        id="22bbdbc3-36cc-4f85-aa40-63798cf7c5fb">
        <placeholder
          name="Layout"
          value="grid" />
        <placeholder
          name="Margin"
          value="mb-4" />
        <placeholder
          name="FlexJustify"
          value="justify-space-between" />
        <control
          code="CMB"
          id="8deecaa4-e1c3-4787-bd2b-0b6c2d4798a4"
          binding="UnitLineItem.YLI_Type">
          <placeholder
            name="IsReadOnly"
            value="True" />
          <placeholder
            name="CaptionOverride"
            value="Unit type"
            resid="fd46b017-1a26-4bb2-b7fa-0091a261340f" />
          <placeholder
            name="Columns"
            value="col-6" />
        </control>
        <control
          code="SRC"
          id="b82d9de4-74de-4c33-b0a3-109ac5aaacf0"
          binding="UnitLineItem.YLI_REG_Grade">
          <placeholder
            name="Margin"
            value="ml-2" />
          <placeholder
            name="Columns"
            value="col-6" />
          <placeholder
            name="IsReadOnly"
            value="True" />
        </control>
      </control>
      <control
        code="SRC"
        id="aaeaa4f1-4424-415c-b5f0-40469b94ba18"
        binding="UnitLineItem.YLI_RC_ContainerType">
        <placeholder
          name="Columns"
          value="col-2" />
        <placeholder
          name="CaptionOverride"
          value="Type Size"
          resid="6e28b87f-5963-44c3-8745-53b87da924fa" />
        <placeholder
          name="Margin"
          value="mb-4" />
        <placeholder
          name="IsReadOnly"
          value="True" />
      </control>
      <control
        code="TXT"
        id="d96a3a6f-f437-4d7a-b31c-2912e6010b49"
        binding="Status">
        <placeholder
          name="IsReadOnly"
          value="True" />
        <placeholder
          name="Margin"
          value="mb-4" />
        <placeholder
          name="CaptionOverride"
          value="Unit condition"
          resid="47af6b6b-6a00-4935-adeb-ef793bd06373" />
      </control>
      <control
        code="ADD"
        id="c16ffbf0-0f0c-41b2-bd97-7e2124c87321"
        binding="Client">
        <placeholder
          name="CaptionOverride"
          value="Customer"
          resid="7f401c94-52c9-48bb-986a-e1df043477ef" />
        <placeholder
          name="Margin"
          value="mb-4" />
        <placeholder
          name="IsReadOnly"
          value="True" />
      </control>
      <control
        code="ADD"
        id="b0a1d2aa-4147-45a3-b537-3df9c112c441"
        binding="Lessee">
        <placeholder
          name="Columns"
          value="col-2" />
        <placeholder
          name="Margin"
          value="mb-4" />
        <placeholder
          name="IsReadOnly"
          value="True" />
      </control>
      <control
        code="MHS"
        id="09b3d72a-7b08-475b-8029-bd97013acd39"
        binding="TareWeight">
        <placeholder
          name="Margin"
          value="mb-4" />
        <placeholder
          name="IsReadOnly"
          value="True" />
      </control>
      <control
        code="MHS"
        id="8b0e1023-d237-4ae9-abd7-281238d985b9"
        binding="MaxGrossWeight">
        <placeholder
          name="Margin"
          value="mb-4" />
        <placeholder
          name="IsReadOnly"
          value="True" />
      </control>
      <control
        code="MHS"
        id="a4a6fb8c-527b-4c1c-a999-63208b54807d"
        binding="UnitLineItem.YLI_GrossWeightMeasure">
        <placeholder
          name="Columns"
          value="2" />
        <placeholder
          name="CaptionOverride"
          value="Gross Weight"
          resid="0c85aaf2-6454-487a-92e8-5f9df404ec11" />
        <placeholder
          name="Margin"
          value="mb-4" />
        <placeholder
          name="IsReadOnly"
          value="True" />
        <placeholder
          name="VisibilityCondition"
          value="!UnitLineItem.YLI_IsEmpty" />
      </control>
      <control
        code="DAE"
        id="ead92cf3-558f-4f29-ab2d-65c6db9d490f"
        binding="UnitLineItem.YLI_ManufactureDate">
        <placeholder
          name="Columns"
          value="col-2" />
        <placeholder
          name="Margin"
          value="mb-4" />
        <placeholder
          name="IsReadOnly"
          value="True" />
      </control>
      <control
        code="OPT"
        id="acc59f30-96dd-476a-9009-482a8656df96"
        binding="UnitLineItem.YLI_IsEmpty">
        <placeholder
          name="CaptionOverride"
          value="Empty container?"
          resid="35566531-898b-43b1-8532-93d84ab5f6d7" />
        <placeholder
          name="Margin"
          value="mb-4" />
        <placeholder
          name="IsReadOnly"
          value="True" />
      </control>
      <control
        code="OPT"
        id="0b00d213-80c9-4e65-9c70-1807fd784bd1"
        binding="UnitLineItem.YLI_IsDamaged">
        <placeholder
          name="CaptionOverride"
          value="Is damaged?"
          resid="fe77acdc-455c-46e1-8101-585972e59aa4" />
        <placeholder
          name="Margin"
          value="mb-4" />
        <placeholder
          name="IsReadOnly"
          value="True" />
      </control>
    </control>
  </form>
