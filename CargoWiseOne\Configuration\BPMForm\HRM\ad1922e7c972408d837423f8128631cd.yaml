#transformationVersion: 70.0
#
VZ_PK: ad1922e7c972408d837423f8128631cd
VZ_ConfigurationKey: ad1922e7-c972-408d-8374-23f8128631cd
VZ_FormID: HRM - Leave Request
VZ_Caption:
  resKey: VZ_Caption|ad1922e7-c972-408d-8374-23f8128631cd
  text: Leave Request
VZ_FormFactor: DSK
VZ_EntityType: IGlbStaffHoliday
VZ_FormType: FRM
VZ_RequiresLogin: true
VZ_MaterialDesign: true
VZ_Dependencies: <dependencies xmlns="http://cargowise.com/glow/2014/11/14/dependencies.xsd" />
VZ_FormData: >-
  <form
    id="944314aa-5b26-43a2-92b1-dcea59c3b7e3" xmlns="http://wisetechglobal.com/glow/2017/05/26/form.xsd">
    <placeholder
      name="Width"
      value="1200" />
    <placeholder
      name="Layout"
      value="grid" />
    <control
      code="LBL"
      id="e664db32-be3d-4c6f-8f5b-4e15efc5be57"
      binding="">
      <placeholder
        name="Caption"
        value="This Leave Request has been approved"
        resid="c4687575-2194-451c-9fa1-182b86f5db83" />
      <placeholder
        name="Typography"
        value="display-small" />
      <placeholder
        name="VisibilityCondition"
        value="GA_ApprovalStatus == &quot;APP&quot;" />
      <placeholder
        name="Color"
        value="success" />
    </control>
    <control
      code="LBL"
      id="a9398e45-54c5-4ece-acef-66f8edbb2a8b"
      binding="">
      <placeholder
        name="Caption"
        value="This Leave Request has been declined"
        resid="0ce8e155-ad4f-4461-89c6-0e8856925352" />
      <placeholder
        name="Typography"
        value="display-small" />
      <placeholder
        name="Color"
        value="error" />
      <placeholder
        name="VisibilityCondition"
        value="GA_ApprovalStatus == &quot;DEC&quot;" />
    </control>
    <control
      code="LBL"
      id="f8bda975-a219-42a4-8aae-1c9b87c3e4af"
      binding="">
      <placeholder
        name="Caption"
        value="This Leave Request has been canceled"
        resid="b1410e5e-556b-4f01-8bdd-42d1d82a8aef" />
      <placeholder
        name="Typography"
        value="display-small" />
      <placeholder
        name="VisibilityCondition"
        value="GA_ApprovalStatus == &quot;CAN&quot;" />
      <placeholder
        name="Color"
        value="warning" />
    </control>
    <control
      code="SRC"
      id="9af741a5-af67-40bf-9870-b26a486f7bb9"
      binding="GA_WorkHolidayType">
      <placeholder
        name="CaptionOverride"
        value="Leave Type"
        resid="104f0dc7-3b35-4c09-9f91-745fdfadba0f" />
    </control>
    <control
      code="BOX"
      id="64a22752-845d-42cc-88cb-ef6be49407cf">
      <placeholder
        name="VisibilityCondition"
        value="LeaveInstructions != null" />
      <placeholder
        name="Style"
        value="background-color: rgb(250, 250, 250); border-color: blue; border-radius: 20px; border-style: groove;" />
      <placeholder
        name="Columns"
        value="col-12" />
      <placeholder
        name="Layout"
        value="grid" />
      <control
        code="IMG"
        id="7a57c3c7-ada5-4467-9499-fe76edffa4c1">
        <placeholder
          name="Columns"
          value="col-1" />
        <placeholder
          name="FallbackIcon"
          value="s-icon-info-circle-filled" />
        <placeholder
          name="Margin"
          value="mt-15" />
        <placeholder
          name="Style"
          value="color: blue;" />
      </control>
      <control
        code="BOX"
        id="0963cfa8-f1f2-400f-b2ba-681550fd167d">
        <placeholder
          name="Columns"
          value="col-11" />
        <control
          code="RTF"
          id="1e8ca659-019c-44b2-9e44-6829814757ba"
          binding="LeaveInstructions">
          <placeholder
            name="IsReadOnly"
            value="True" />
          <placeholder
            name="Toolbar"
            value="false" />
          <placeholder
            name="CaptionType"
            value="none" />
          <placeholder
            name="Height"
            value="250" />
        </control>
      </control>
    </control>
    <control
      code="OPT"
      id="221ab331-5359-4a02-a97f-f4ef5f9c9ca0"
      binding="CashOutLeaves">
      <placeholder
        name="Padding"
        value="pa-0" />
      <placeholder
        name="Margin"
        value="ma-0" />
      <placeholder
        name="CaptionOverride"
        value="Cash out leaves"
        resid="0c23f8a5-e576-48bc-89a8-305b22de2d65" />
      <placeholder
        name="VisibilityCondition"
        value="GlbStaff.Country.RN_Code == &quot;BR&quot;" />
    </control>
    <control
      code="OPT"
      id="42ecb39b-eb62-49a6-9910-77c396554971"
      binding="IsPartDay">
      <placeholder
        name="CaptionOverride"
        value="Part Day Leave"
        resid="f0f7a61e-c0ad-4016-81fd-97f52f5034f5" />
      <placeholder
        name="Padding"
        value="pa-0" />
      <placeholder
        name="Margin"
        value="ma-0" />
    </control>
    <control
      code="DAE"
      id="3e196ce4-8511-4551-a3e7-fedbc590a9df"
      binding="GA_StartTime">
      <placeholder
        name="CaptionOverride"
        value="Start Date"
        resid="86e3fdcd-9223-4d87-a015-c677d6e39107" />
      <placeholder
        name="Columns"
        value="col-12 col-sm-6" />
    </control>
    <control
      code="DAE"
      id="8c819de8-8318-4d5c-965c-682d273c41ad"
      binding="GA_EndTime">
      <placeholder
        name="CaptionOverride"
        value="End Date"
        resid="33d8f429-abb6-4249-9e8c-922455bf2626" />
      <placeholder
        name="VisibilityCondition"
        value="!IsPartDay" />
      <placeholder
        name="Columns"
        value="col-12 col-sm-6" />
    </control>
    <control
      code="TIM"
      id="a751fd73-0fb8-4291-ae6f-d23961e3a44a"
      binding="StartTime">
      <placeholder
        name="CaptionOverride"
        value="Start Time"
        resid="8aa7f6b5-a957-4534-aeda-beee6fd43b2d" />
      <placeholder
        name="VisibilityCondition"
        value="IsPartDay" />
      <placeholder
        name="Columns"
        value="col-12 col-sm-3" />
    </control>
    <control
      code="TIM"
      id="08effcaf-9c69-4e64-bb63-4b6c93727c7f"
      binding="EndTime">
      <placeholder
        name="CaptionOverride"
        value="End Time"
        resid="9034242f-9c0a-490b-8787-2996fa959331" />
      <placeholder
        name="VisibilityCondition"
        value="IsPartDay" />
      <placeholder
        name="Columns"
        value="col-12 col-sm-3" />
    </control>
    <control
      code="NUM"
      id="4620a38d-2b35-4f6f-837e-ebd33bd2494f"
      binding="LeaveHours">
      <placeholder
        name="CaptionOverride"
        value="Leave Requested (hours)"
        resid="13abc743-6483-4259-be49-c15b1c2ed8de" />
      <placeholder
        name="IsReadOnly"
        value="True" />
      <placeholder
        name="VisibilityCondition"
        value="!IsPartDay" />
    </control>
    <control
      code="NUM"
      id="bb630a96-7181-4255-96df-7c0a6328860f"
      binding="LeaveHours">
      <placeholder
        name="CaptionOverride"
        value="Leave Requested (hours)"
        resid="41903c63-d6d4-4d29-a258-99953bc9dac1" />
      <placeholder
        name="VisibilityCondition"
        value="IsPartDay" />
      <placeholder
        name="IsReadOnly"
        value="True" />
      <placeholder
        name="Columns"
        value="col-12 col-sm-6" />
    </control>
    <control
      code="DUR"
      id="d88d7c66-f286-46f8-83b6-cd6c181fd58c"
      binding="GA_OverrideLeaveTaken">
      <placeholder
        name="CaptionOverride"
        value="Override Leave Requested"
        resid="a40f8829-4133-462e-84a2-b9ac075a896f" />
      <placeholder
        name="VisibilityCondition"
        value="IsPartDay" />
      <placeholder
        name="Columns"
        value="col-12 col-sm-6" />
    </control>
    <control
      code="PNL"
      id="b4a90c54-88b3-457f-9a96-3f4a17fd89d2">
      <placeholder
        name="VisibilityCondition"
        value="LeaveHours &gt; LeaveBalance &amp;&amp; !IsForNonAccruingType &amp;&amp; (LeaveFallbackType == null || LeaveFallbackType == &quot;&quot;)" />
      <control
        code="CLO"
        id="2f7ef1c5-dd11-4bb1-a499-93bc4ede021f">
        <placeholder
          name="Sentiment"
          value="warning" />
        <placeholder
          name="Description"
          value="Leave balance is insufficient."
          resid="e0735da6-4079-4592-b3dc-760311a84933" />
      </control>
    </control>
    <control
      code="PNL"
      id="9924a3a8-8b57-464a-93de-8768c25d1d34">
      <placeholder
        name="VisibilityCondition"
        value="CausesNegativeFutureBalance &amp;&amp; !IsForNonAccruingType" />
      <control
        code="CLO"
        id="cfed6301-393d-4cee-be24-8357d311f58f">
        <placeholder
          name="Sentiment"
          value="warning" />
        <control
          code="BOX"
          id="cf8ae440-f187-4590-a968-c9c96927d3c0">
          <control
            code="LBL"
            id="c4dbb511-6256-4ee1-a756-a3e161dd3157">
            <placeholder
              name="Caption"
              value="You have approved"
              resid="019f2bb6-2b59-42f8-b5f2-987704fbb265" />
            <placeholder
              name="Align"
              value="left" />
            <placeholder
              name="Columns"
              value="col-3 col-sm-3 col-md-3 col-lg-3 col-xl-3" />
            <placeholder
              name="Color"
              value="warning" />
          </control>
          <control
            code="DSF"
            id="1f432381-7eaf-4c81-9415-a76a4a5694b3"
            binding="LeaveTypeName">
            <placeholder
              name="CaptionType"
              value="none" />
            <placeholder
              name="Margin"
              value="mx-1" />
            <placeholder
              name="Color"
              value="warning" />
          </control>
          <control
            code="LBL"
            id="c10668ba-a2a4-468d-8b54-6f18ff1e5e0c">
            <placeholder
              name="Caption"
              value="at later dates. This leave request may cause your balance to be insufficient for those dates."
              resid="8b7cfaa4-a4b9-43bd-b680-798640a49094" />
            <placeholder
              name="Color"
              value="warning" />
          </control>
        </control>
      </control>
    </control>
    <control
      code="PNL"
      id="a5134096-0854-4e29-b84a-6b6a67e44bf9">
      <placeholder
        name="VisibilityCondition"
        value="LeaveFallbackTypeName != null &amp;&amp; LeaveFallbackTypeName != &quot;&quot; &amp;&amp; LeaveTypeName != null &amp;&amp; LeaveTypeName != &quot;&quot;" />
      <control
        code="CLO"
        id="3e635ed2-a1a2-4c93-9928-2d91b9b55347">
        <placeholder
          name="Sentiment"
          value="warning" />
        <control
          code="BOX"
          id="f12b2b59-852e-4683-81d6-ab68b76054f9">
          <control
            code="LBL"
            id="fdf7e2aa-b1dc-4f15-963b-fa22d4d0edd9">
            <placeholder
              name="Caption"
              value="Leave balance is insufficient. This leave request for"
              resid="ac1533e3-ff3d-42ca-addd-c93dcb07adfe" />
            <placeholder
              name="Color"
              value="warning" />
          </control>
          <control
            code="DSF"
            id="55b7e89b-4d69-4e72-a4a5-6e250c9bef37"
            binding="LeaveTypeName">
            <placeholder
              name="CaptionType"
              value="none" />
            <placeholder
              name="Color"
              value="warning" />
            <placeholder
              name="Margin"
              value="mx-1" />
          </control>
          <control
            code="LBL"
            id="f631df5a-24b1-499a-a58c-0dba90e0e133">
            <placeholder
              name="Caption"
              value="may fall back to"
              resid="9de77f21-bba5-42b3-84f3-b2a542cdf0b2" />
            <placeholder
              name="Color"
              value="warning" />
          </control>
          <control
            code="DSF"
            id="89a276d9-1144-48db-9af3-8dd407e60743"
            binding="LeaveFallbackTypeName">
            <placeholder
              name="Margin"
              value="ml-1" />
            <placeholder
              name="Color"
              value="warning" />
            <placeholder
              name="CaptionType"
              value="none" />
          </control>
        </control>
      </control>
    </control>
    <control
      code="BOX"
      id="be7405aa-0d15-4edc-8355-998b569cfd1f"
      binding="">
      <placeholder
        name="VisibilityCondition"
        value="!IsForNonAccruingType" />
      <control
        code="LBL"
        id="d757a33a-5bb5-4217-bf49-d32d57b9afa0"
        binding="">
        <placeholder
          name="Caption"
          value="Estimated leave balance (hours):"
          resid="93e25933-b73b-4a2d-8e02-93369f097ab1" />
        <placeholder
          name="Typography"
          value="body" />
      </control>
      <control
        code="DSF"
        id="2968a99f-f26f-4c9c-9a09-a1c90cf40f35"
        binding="LeaveBalance">
        <placeholder
          name="CaptionType"
          value="none" />
        <placeholder
          name="Typography"
          value="body" />
      </control>
      <control
        code="PNL"
        id="d1947f1e-c641-4b8c-a49c-17b075d1d23f">
        <placeholder
          name="Caption"
          value="Public Holidays Within Selected Date Range"
          resid="5a85c007-6153-4d87-b441-d3f3d3c4c9ea" />
        <placeholder
          name="VisibilityCondition"
          value="GA_StartTime &lt;= GA_EndTime" />
        <control
          code="CMP"
          id="53422891-335d-4274-a7c2-3375274daf00">
          <placeholder
            name="Component"
            value="cargoWiseOne.productHrm.components.LeaveRequestPublicHolidayList" />
        </control>
      </control>
    </control>
    <control
      code="FIL"
      id="59f5e4c7-0fc5-4573-9f27-3aabf294d999"
      binding="">
      <placeholder
        name="Caption"
        value="Attachments"
        resid="55d80dd1-b2f3-48fb-8bde-991d20219179" />
      <placeholder
        name="DocumentType"
        value="MSC" />
      <placeholder
        name="CanDeleteExisting"
        value="True" />
    </control>
    <control
      code="TXA"
      id="c2482924-27cf-4c1d-b3f8-7450dbf7648a"
      binding="GA_LeaveComment" />
  </form>
