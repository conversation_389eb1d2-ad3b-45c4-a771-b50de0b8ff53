#transformationVersion: 70.0
#
VZ_PK: 4de283a9e99a429288d348ba5236736d
VZ_ConfigurationKey: 4de283a9-e99a-4292-88d3-48ba5236736d
VZ_FormID: HRM - My Team Leave
VZ_Caption:
  resKey: VZ_Caption|4de283a9-e99a-4292-88d3-48ba5236736d
  text: My Team Leave
VZ_FormFactor: DSK
VZ_EntityType: IGlbStaff
VZ_FormType: FRM
VZ_RequiresLogin: true
VZ_MaterialDesign: true
VZ_Dependencies: <dependencies xmlns="http://cargowise.com/glow/2014/11/14/dependencies.xsd" />
VZ_FormData: >-
  <form
    id="88062377-7ecd-4c68-ac60-8106d100d752" xmlns="http://wisetechglobal.com/glow/2017/05/26/form.xsd">
    <placeholder
      name="Layout"
      value="grid" />
    <control
      code="TBS"
      id="5b317f1e-1a70-4927-9986-34a4b46b4ee4">
      <control
        code="TAB"
        id="276fa18c-4200-43b7-aa81-9dc859dda654">
        <placeholder
          name="Caption"
          value="Pending Approval"
          resid="446b2d07-8e2a-43c0-acb3-533e96052aa6" />
        <placeholder
          name="VisibilityCondition"
          value="%.CurrentUserCheckpoints.Contains(&quot;CanViewMyStaffLeaveRequests&quot;)" />
      </control>
      <control
        code="TAI"
        id="f189fab1-4ce2-41bb-af9b-6a61d11f3658">
        <placeholder
          name="VisibilityCondition"
          value="%.CurrentUserCheckpoints.Contains(&quot;CanViewMyStaffLeaveRequests&quot;)" />
        <control
          code="SDT"
          id="609e08b4-0519-4341-98ad-6a5d846cdfc5"
          binding="">
          <placeholder
            name="CaptionOverride"
            value="Pending Approval Team Leave"
            resid="1d1e4eb8-4078-4bdf-9d0c-e56174d39637" />
          <placeholder
            name="FullPage"
            value="True" />
          <placeholder
            name="EntityType"
            value="IWorkflowTask[[IGlbStaffHoliday]]" />
          <placeholder
            name="DefaultFilter">
            <xml>
              <ArrayOfFilterGroup xmlns:a="http://schemas.microsoft.com/2003/10/Serialization/Arrays" xmlns:i="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://cargowise.com/glow/2014/07/16/filters.xsd">
                <FilterGroup>
                  <Filters>
                    <Filter>
                      <FilterType>BoolFilter</FilterType>
                      <Operation>Is</Operation>
                      <PropertyPath>IsApprovalTask</PropertyPath>
                      <Values>
                        <a:string>true</a:string>
                      </Values>
                    </Filter>
                    <Filter>
                      <FilterType>StringFilter</FilterType>
                      <Operation>Is</Operation>
                      <PropertyPath>AssignedStaffMember.GS_Code</PropertyPath>
                      <Values>
                        <a:string>&lt;%.CurrentStaffCode&gt;</a:string>
                      </Values>
                    </Filter>
                    <Filter>
                      <FilterType>SimpleLookupFilter</FilterType>
                      <Operation>Is</Operation>
                      <PropertyPath>P9_Status</PropertyPath>
                      <Values>
                        <a:string>ASN</a:string>
                      </Values>
                    </Filter>
                  </Filters>
                  <IsImplicit>true</IsImplicit>
                </FilterGroup>
              </ArrayOfFilterGroup>
            </xml>
          </placeholder>
          <placeholder
            name="FieldConfiguration">
            <xml>
              <fields xmlns="">
                <field
                  path="P9_Description"
                  width="250"
                  mode="Default" />
                <field
                  path="Parent.GA_StartTime"
                  width="180"
                  mode="Mandatory" />
                <field
                  path="Parent.GA_EndTime"
                  width="180"
                  mode="Mandatory" />
                <field
                  path="Parent.GA_GS"
                  width="200"
                  mode="Mandatory" />
                <field
                  path="Parent.GlbStaff.GS_Code"
                  width="80"
                  mode="Default" />
                <field
                  path="Parent.GlbStaff.GS_FullName"
                  width="120"
                  mode="Default" />
                <field
                  path="Parent.GA_WorkHolidayType"
                  width="200"
                  mode="Mandatory" />
                <field
                  path="Parent.LeaveTypeCode"
                  width="80"
                  mode="Default" />
                <field
                  path="Parent.LeaveTypeName"
                  width="120"
                  mode="Default" />
                <field
                  path="Parent.GA_DaysLeaveTaken"
                  width="90"
                  mode="Default" />
                <field
                  path="Parent.LeaveHours"
                  width="90"
                  mode="Default" />
                <field
                  path="P9_GS_NKAssignedStaffMember"
                  width="210"
                  mode="Default" />
                <field
                  path="P9_CompletedTimeUtc"
                  width="180"
                  mode="Optional" />
                <field
                  path="IsApprovalTask"
                  width="150"
                  mode="Optional" />
                <field
                  path="ApprovalType"
                  width="250"
                  mode="Optional" />
                <field
                  path="ApprovalStatus"
                  width="250"
                  mode="Optional" />
                <field
                  path="AssignedStaffMember.GS_Code"
                  width="250"
                  mode="Optional" />
                <field
                  path="IsManagedTask"
                  width="150"
                  mode="FilterOnly" />
              </fields>
            </xml>
          </placeholder>
          <placeholder
            name="ShowSelect"
            value="True" />
          <placeholder
            name="DefaultSortFields">
            <xml>
              <ArrayOfFieldSortDefinition xmlns:i="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://schemas.datacontract.org/2004/07/CargoWise.Glow.Infrastructure.Interfaces">
                <FieldSortDefinition>
                  <FieldName>Parent.GA_StartTime</FieldName>
                  <IsAscending>false</IsAscending>
                </FieldSortDefinition>
              </ArrayOfFieldSortDefinition>
            </xml>
          </placeholder>
          <placeholder
            name="DisabledGridRowActions"
            value="Workflow" />
        </control>
      </control>
      <control
        code="TAB"
        id="d760e59e-4cbd-4dcb-a981-fca7de124fa5">
        <placeholder
          name="Caption"
          value="Upcoming"
          resid="e27a5196-386d-4693-97ab-04e0e2bd2801" />
        <placeholder
          name="VisibilityCondition"
          value="%.CurrentUserCheckpoints.Contains(&quot;CanViewMyStaffLeaveRequests&quot;)" />
      </control>
      <control
        code="TAI"
        id="951297eb-0fcd-4a0b-83fd-e9ecfcfe7761">
        <placeholder
          name="VisibilityCondition"
          value="%.CurrentUserCheckpoints.Contains(&quot;CanViewMyStaffLeaveRequests&quot;)" />
        <control
          code="SDT"
          id="c3e5b558-1b59-49f4-83d1-154eadd6832a"
          binding="">
          <placeholder
            name="CaptionOverride"
            value="Upcoming Team Leave"
            resid="6604613e-e0ab-4682-9ac3-73247aefcaf6" />
          <placeholder
            name="FullPage"
            value="True" />
          <placeholder
            name="EntityType"
            value="IWorkflowTask[[IGlbStaffHoliday]]" />
          <placeholder
            name="DefaultFilter">
            <xml>
              <ArrayOfFilterGroup xmlns:a="http://schemas.microsoft.com/2003/10/Serialization/Arrays" xmlns:i="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://cargowise.com/glow/2014/07/16/filters.xsd">
                <FilterGroup>
                  <Filters>
                    <Filter>
                      <FilterType>BoolFilter</FilterType>
                      <Operation>Is</Operation>
                      <PropertyPath>IsApprovalTask</PropertyPath>
                      <Values>
                        <a:string>true</a:string>
                      </Values>
                    </Filter>
                    <Filter>
                      <FilterType>BoolFilter</FilterType>
                      <Operation>Is</Operation>
                      <PropertyPath>IsManagedTask</PropertyPath>
                      <Values>
                        <a:string>true</a:string>
                      </Values>
                    </Filter>
                    <Filter>
                      <FilterType>DateTimeFilter</FilterType>
                      <Operation>IsInTheFuture</Operation>
                      <PropertyPath>Parent.GA_StartTime</PropertyPath>
                      <Values />
                    </Filter>
                    <Filter>
                      <FilterType>StringFilter</FilterType>
                      <Operation>Is</Operation>
                      <PropertyPath>P9_Outcome</PropertyPath>
                      <Values>
                        <a:string>APP</a:string>
                      </Values>
                    </Filter>
                    <Filter>
                      <FilterType>StringFilter</FilterType>
                      <Operation>Is</Operation>
                      <PropertyPath>AssignedStaffMember.GS_Code</PropertyPath>
                      <Values>
                        <a:string>&lt;%.CurrentStaffCode&gt;</a:string>
                      </Values>
                    </Filter>
                  </Filters>
                  <IsImplicit>true</IsImplicit>
                </FilterGroup>
              </ArrayOfFilterGroup>
            </xml>
          </placeholder>
          <placeholder
            name="FieldConfiguration">
            <xml>
              <fields xmlns="">
                <field
                  path="P9_Description"
                  width="250"
                  mode="Default" />
                <field
                  path="Parent.GA_StartTime"
                  width="180"
                  mode="Mandatory" />
                <field
                  path="Parent.GA_EndTime"
                  width="180"
                  mode="Mandatory" />
                <field
                  path="Parent.GA_GS"
                  width="200"
                  mode="Mandatory" />
                <field
                  path="Parent.GlbStaff.GS_Code"
                  width="120"
                  mode="Default" />
                <field
                  path="Parent.GlbStaff.GS_FullName"
                  width="80"
                  mode="Default" />
                <field
                  path="Parent.GA_WorkHolidayType"
                  width="200"
                  mode="Mandatory" />
                <field
                  path="Parent.LeaveTypeCode"
                  width="80"
                  mode="Default" />
                <field
                  path="Parent.LeaveTypeName"
                  width="120"
                  mode="Default" />
                <field
                  path="Parent.GA_DaysLeaveTaken"
                  width="90"
                  mode="Default" />
                <field
                  path="Parent.LeaveHours"
                  width="90"
                  mode="Default" />
                <field
                  path="P9_GS_NKAssignedStaffMember"
                  width="210"
                  mode="Default" />
                <field
                  path="P9_CompletedTimeUtc"
                  width="180"
                  mode="Optional" />
                <field
                  path="IsApprovalTask"
                  width="150"
                  mode="Optional" />
                <field
                  path="ApprovalType"
                  width="250"
                  mode="Optional" />
                <field
                  path="ApprovalStatus"
                  width="250"
                  mode="Default" />
                <field
                  path="AssignedStaffMember.GS_Code"
                  width="250"
                  mode="Optional" />
                <field
                  path="IsManagedTask"
                  width="150"
                  mode="FilterOnly" />
              </fields>
            </xml>
          </placeholder>
          <placeholder
            name="DisabledGridRowActions"
            value="Remove" />
          <placeholder
            name="ItemsPerPage"
            value="15" />
          <placeholder
            name="DefaultSortFields">
            <xml>
              <ArrayOfFieldSortDefinition xmlns:i="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://schemas.datacontract.org/2004/07/CargoWise.Glow.Infrastructure.Interfaces">
                <FieldSortDefinition>
                  <FieldName>Parent.GA_StartTime</FieldName>
                  <IsAscending>false</IsAscending>
                </FieldSortDefinition>
              </ArrayOfFieldSortDefinition>
            </xml>
          </placeholder>
        </control>
      </control>
      <control
        code="TAB"
        id="1c16e35e-3945-4d10-9c8c-c3dd4889b721">
        <placeholder
          name="Caption"
          value="Activity"
          resid="d6d0ca44-340f-4c2b-b8c0-92141bf1153d" />
        <placeholder
          name="VisibilityCondition"
          value="%.CurrentUserCheckpoints.Contains(&quot;CanViewMyStaffLeaveRequests&quot;)" />
      </control>
      <control
        code="TAI"
        id="8e362f6b-5006-457c-8298-1f368272350a">
        <placeholder
          name="VisibilityCondition"
          value="%.CurrentUserCheckpoints.Contains(&quot;CanViewMyStaffLeaveRequests&quot;)" />
        <control
          code="SDT"
          id="3e39fc36-6b85-413e-b162-b82022bb073c"
          binding="">
          <placeholder
            name="CaptionOverride"
            value="Active Team Leave"
            resid="4321c62f-4c62-4c09-a8ca-9ab712e4ed92" />
          <placeholder
            name="FullPage"
            value="True" />
          <placeholder
            name="EntityType"
            value="IWorkflowTask[[IGlbStaffHoliday]]" />
          <placeholder
            name="DefaultFilter">
            <xml>
              <ArrayOfFilterGroup xmlns:a="http://schemas.microsoft.com/2003/10/Serialization/Arrays" xmlns:i="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://cargowise.com/glow/2014/07/16/filters.xsd">
                <FilterGroup>
                  <Filters>
                    <Filter>
                      <FilterType>BoolFilter</FilterType>
                      <Operation>Is</Operation>
                      <PropertyPath>IsApprovalTask</PropertyPath>
                      <Values>
                        <a:string>true</a:string>
                      </Values>
                    </Filter>
                    <Filter>
                      <FilterType>BoolFilter</FilterType>
                      <Operation>Is</Operation>
                      <PropertyPath>IsManagedTask</PropertyPath>
                      <Values>
                        <a:string>true</a:string>
                      </Values>
                    </Filter>
                    <Filter>
                      <FilterType>StringFilter</FilterType>
                      <Operation>Is</Operation>
                      <PropertyPath>AssignedStaffMember.GS_Code</PropertyPath>
                      <Values>
                        <a:string>&lt;%.CurrentStaffCode&gt;</a:string>
                      </Values>
                    </Filter>
                  </Filters>
                  <IsImplicit>true</IsImplicit>
                </FilterGroup>
              </ArrayOfFilterGroup>
            </xml>
          </placeholder>
          <placeholder
            name="FieldConfiguration">
            <xml>
              <fields xmlns="">
                <field
                  path="P9_Description"
                  width="250"
                  mode="Default" />
                <field
                  path="Parent.GA_StartTime"
                  width="180"
                  mode="Mandatory" />
                <field
                  path="Parent.GA_EndTime"
                  width="180"
                  mode="Mandatory" />
                <field
                  path="Parent.GA_GS"
                  width="200"
                  mode="Mandatory" />
                <field
                  path="Parent.GlbStaff.GS_Code"
                  width="120"
                  mode="Default" />
                <field
                  path="Parent.GlbStaff.GS_FullName"
                  width="80"
                  mode="Default" />
                <field
                  path="Parent.GA_WorkHolidayType"
                  width="200"
                  mode="Mandatory" />
                <field
                  path="Parent.LeaveTypeCode"
                  width="80"
                  mode="Default" />
                <field
                  path="Parent.LeaveTypeName"
                  width="120"
                  mode="Default" />
                <field
                  path="Parent.GA_DaysLeaveTaken"
                  width="90"
                  mode="Default" />
                <field
                  path="Parent.LeaveHours"
                  width="90"
                  mode="Default" />
                <field
                  path="P9_GS_NKAssignedStaffMember"
                  width="210"
                  mode="Default" />
                <field
                  path="P9_CompletedTimeUtc"
                  width="180"
                  mode="Optional" />
                <field
                  path="IsApprovalTask"
                  width="150"
                  mode="Optional" />
                <field
                  path="ApprovalType"
                  width="250"
                  mode="Optional" />
                <field
                  path="ApprovalStatus"
                  width="250"
                  mode="Default" />
                <field
                  path="AssignedStaffMember.GS_Code"
                  width="250"
                  mode="Optional" />
                <field
                  path="IsManagedTask"
                  width="150"
                  mode="FilterOnly" />
              </fields>
            </xml>
          </placeholder>
          <placeholder
            name="DisabledGridRowActions"
            value="Remove" />
          <placeholder
            name="ItemsPerPage"
            value="15" />
          <placeholder
            name="DefaultSortFields">
            <xml>
              <ArrayOfFieldSortDefinition xmlns:i="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://schemas.datacontract.org/2004/07/CargoWise.Glow.Infrastructure.Interfaces">
                <FieldSortDefinition>
                  <FieldName>Parent.GA_StartTime</FieldName>
                  <IsAscending>false</IsAscending>
                </FieldSortDefinition>
              </ArrayOfFieldSortDefinition>
            </xml>
          </placeholder>
        </control>
      </control>
      <control
        code="TAB"
        id="0c048b94-3cb0-48b2-8459-28614e504151">
        <placeholder
          name="Caption"
          value="Calendar"
          resid="9cf20927-3344-4bb0-87c2-d6bbbfed7689" />
      </control>
      <control
        code="TAI"
        id="0c7e9070-8649-4566-bbe6-2fd9c4b44f1d">
        <placeholder
          name="FlexDirection"
          value="flex-column" />
        <control
          code="PNL"
          id="1b9e55c2-5906-4528-8f27-aa02a7011c9f">
          <control
            code="SDT"
            id="e55cf68d-2787-4a78-9939-0fc75395f96e">
            <placeholder
              name="Margin"
              value="my-2" />
            <placeholder
              name="EntityType"
              value="IGlbStaff" />
            <placeholder
              name="HideDefaultFooter"
              value="False" />
            <placeholder
              name="HideItemActions"
              value="True" />
            <placeholder
              name="ShowAddActions"
              value="False" />
            <placeholder
              name="ItemsPerPage"
              value="5" />
            <placeholder
              name="BindingForFilter"
              value="StaffLeaveFilter" />
            <placeholder
              name="HideActions"
              value="False" />
            <placeholder
              name="DefaultSortFields">
              <xml>
                <ArrayOfFieldSortDefinition xmlns:i="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://schemas.datacontract.org/2004/07/CargoWise.Glow.Infrastructure.Interfaces">
                  <FieldSortDefinition>
                    <FieldName>GS_FriendlyName</FieldName>
                    <IsAscending>true</IsAscending>
                  </FieldSortDefinition>
                </ArrayOfFieldSortDefinition>
              </xml>
            </placeholder>
            <placeholder
              name="FieldConfiguration">
              <xml>
                <fields xmlns="">
                  <field
                    path="GS_Birthdate"
                    width="150"
                    mode="FilterOnly" />
                  <field
                    path="GS_NextReviewDate"
                    width="190"
                    mode="FilterOnly" />
                  <field
                    path="GS_DepartureDate"
                    width="120"
                    mode="Optional" />
                  <field
                    path="GS_LastDayOfWork"
                    width="160"
                    mode="FilterOnly" />
                  <field
                    path="GS_ProbationEndDate"
                    width="180"
                    mode="Optional" />
                  <field
                    path="GS_ResidencyExpiry"
                    width="180"
                    mode="Optional" />
                  <field
                    path="GS_SystemCreateTimeUtc"
                    width="220"
                    mode="Optional" />
                  <field
                    path="GS_SystemLastEditTimeUtc"
                    width="250"
                    mode="Optional" />
                  <field
                    path="GS_EmploymentDate"
                    width="120"
                    mode="Optional" />
                  <field
                    path="GS_Code"
                    width="100"
                    mode="Default" />
                  <field
                    path="CreatedByStaff.GS_Code"
                    width="190"
                    mode="Optional" />
                  <field
                    path="CreatedByStaff.GS_FullName"
                    width="230"
                    mode="Optional" />
                  <field
                    path="GS_UserAddress1"
                    width="200"
                    mode="Optional" />
                  <field
                    path="GS_UserAddress2"
                    width="200"
                    mode="Optional" />
                  <field
                    path="GS_WagesBankAccount"
                    width="220"
                    mode="Optional" />
                  <field
                    path="GS_WagesBankBsb"
                    width="160"
                    mode="Optional" />
                  <field
                    path="GS_WagesBankName"
                    width="220"
                    mode="Optional" />
                  <field
                    path="GS_WagesBankSwift"
                    width="250"
                    mode="Optional" />
                  <field
                    path="GS_EmailAddress"
                    width="220"
                    mode="Optional" />
                  <field
                    path="GS_EmergencyContactEmail"
                    width="250"
                    mode="Optional" />
                  <field
                    path="GS_EmergencyContactName"
                    width="220"
                    mode="Optional" />
                  <field
                    path="GS_EmergencyHomePhone"
                    width="200"
                    mode="Optional" />
                  <field
                    path="GS_FullName"
                    width="250"
                    mode="Default" />
                  <field
                    path="Country.RN_Desc"
                    width="190"
                    mode="Default" />
                  <field
                    path="GS_HomePhone"
                    width="200"
                    mode="Optional" />
                  <field
                    path="GS_State"
                    width="170"
                    mode="Optional" />
                  <field
                    path="GS_FriendlyName"
                    width="200"
                    mode="Optional" />
                  <field
                    path="GS_Postcode"
                    width="170"
                    mode="Optional" />
                  <field
                    path="GS_MobilePhone"
                    width="200"
                    mode="Optional" />
                  <field
                    path="GS_LoginName"
                    width="180"
                    mode="Optional" />
                  <field
                    path="GS_NextOfKinEmail"
                    width="250"
                    mode="Optional" />
                  <field
                    path="GS_NextOfKinHomePhone"
                    width="200"
                    mode="Optional" />
                  <field
                    path="GS_NextOfKin"
                    width="250"
                    mode="Optional" />
                  <field
                    path="GS_ResidencyStatus"
                    width="160"
                    mode="Optional" />
                  <field
                    path="TotalWorkingHoursText"
                    width="200"
                    mode="Optional" />
                  <field
                    path="GS_WorkPhone"
                    width="200"
                    mode="Optional" />
                  <field
                    path="GS_IsActive"
                    width="100"
                    mode="Optional" />
                  <field
                    path="GS_IsController"
                    width="100"
                    mode="Optional" />
                  <field
                    path="GS_IsOperational"
                    width="100"
                    mode="Optional" />
                  <field
                    path="GS_PublishHomePhone"
                    width="140"
                    mode="Optional" />
                  <field
                    path="GS_PublishMobilePhone"
                    width="200"
                    mode="Optional" />
                  <field
                    path="CurrentEmployingEntity.GHB_EffectiveDate"
                    width="250"
                    mode="Optional" />
                  <field
                    path="CurrentEmployingEntity.Branch.GB_BranchName"
                    width="190"
                    mode="Optional" />
                  <field
                    path="CurrentEmployingEntity.Branch.GB_Code"
                    width="190"
                    mode="Optional" />
                  <field
                    path="CurrentPhysicalWorkAddress.GEL_EffectiveDate"
                    width="250"
                    mode="Optional" />
                  <field
                    path="CurrentPhysicalWorkAddress.GEL_Address1"
                    width="220"
                    mode="Optional" />
                  <field
                    path="CurrentPhysicalWorkAddress.GEL_Address2"
                    width="220"
                    mode="Optional" />
                  <field
                    path="CurrentPhysicalWorkAddress.GEL_City"
                    width="200"
                    mode="Optional" />
                  <field
                    path="CurrentPhysicalWorkAddress.GEL_PostCode"
                    width="250"
                    mode="Optional" />
                  <field
                    path="CurrentPhysicalWorkAddress.GEL_State"
                    width="200"
                    mode="Optional" />
                  <field
                    path="CurrentRemuneration.GSR_EffectiveDate"
                    width="220"
                    mode="Optional" />
                  <field
                    path="CurrentRemuneration.GSR_FullTimeEquivalent"
                    width="180"
                    mode="Optional" />
                  <field
                    path="CurrentRemuneration.MostRecentSalary"
                    width="250"
                    mode="Optional" />
                  <field
                    path="CurrentRemuneration.MostRecentSalary.GSI_Value"
                    width="200"
                    mode="Optional" />
                  <field
                    path="CurrentRemuneration.MostRecentSalary.GSI_GrantDate"
                    width="200"
                    mode="Optional" />
                  <field
                    path="CurrentRemuneration.MostRecentSalary.GSI_IsFTEScalable"
                    width="220"
                    mode="Optional" />
                  <field
                    path="CurrentRemuneration.MostRecentSalary.IsOneOff"
                    width="200"
                    mode="Optional" />
                  <field
                    path="CurrentRemuneration.MostRecentSalary.GSI_IsPartOfPackage"
                    width="220"
                    mode="Optional" />
                  <field
                    path="CurrentRemuneration.MostRecentSalary.GSI_EntitlementCode"
                    width="200"
                    mode="Optional" />
                  <field
                    path="CurrentRemuneration.MostRecentSalary.GSI_Frequency"
                    width="220"
                    mode="Optional" />
                  <field
                    path="CurrentRemuneration.MostRecentSalary.GSI_Comment"
                    width="200"
                    mode="Optional" />
                  <field
                    path="CurrentRole.GEH_EffectiveDate"
                    width="190"
                    mode="Optional" />
                  <field
                    path="CurrentRole.GEH_DepartureReason"
                    width="200"
                    mode="Optional" />
                  <field
                    path="CurrentRole.GEH_DepartureComments"
                    width="220"
                    mode="Optional" />
                  <field
                    path="CurrentTeam.EffectiveDate"
                    width="200"
                    mode="Optional" />
                  <field
                    path="CurrentTeam.GET_GST_NKTeamCode"
                    width="200"
                    mode="Optional" />
                  <field
                    path="CurrentTeam.ParentTeam"
                    width="200"
                    mode="Optional" />
                  <field
                    path="LastEditedByStaff.GS_Code"
                    width="190"
                    mode="Optional" />
                  <field
                    path="LastEditedByStaff.GS_FullName"
                    width="190"
                    mode="Optional" />
                  <field
                    path="MostRecentCostCenter.GSK_EndDate"
                    width="230"
                    mode="Optional" />
                  <field
                    path="MostRecentCostCenter.GSK_StartDate"
                    width="230"
                    mode="Optional" />
                  <field
                    path="MostRecentCostCenter.Branch.GB_BranchName"
                    width="200"
                    mode="Optional" />
                  <field
                    path="MostRecentCostCenter.Branch.GB_Code"
                    width="220"
                    mode="Optional" />
                  <field
                    path="MostRecentCostCenter.Department.GE_Code"
                    width="200"
                    mode="Optional" />
                  <field
                    path="MostRecentCostCenter.Department.GE_Desc"
                    width="200"
                    mode="Optional" />
                  <field
                    path="MostRecentPeopleLeader.GSM_EndDate"
                    width="230"
                    mode="Optional" />
                  <field
                    path="Nationality.RN_Desc"
                    width="200"
                    mode="Optional" />
                  <field
                    path="CurrentRole.GEH_JobFamily"
                    width="190"
                    mode="Optional" />
                  <field
                    path="MostRecentCostCenter"
                    width="230"
                    mode="Optional" />
                  <field
                    path="CurrentRemuneration"
                    width="200"
                    mode="Optional" />
                  <field
                    path="CurrentTeam.TeamNameDescription"
                    width="190"
                    mode="Optional" />
                  <field
                    path="MostRecentPeopleLeader.GSM_EffectiveDate"
                    width="240"
                    mode="Optional" />
                  <field
                    path="MostRecentPeopleLeader.Manager.GS_FullName"
                    width="220"
                    mode="Optional" />
                  <field
                    path="MostRecentPeopleLeader.Manager.GS_Code"
                    width="200"
                    mode="Optional" />
                  <field
                    path="GS_GE_HomeDepartment"
                    width="200"
                    mode="Optional" />
                  <field
                    path="GS_ActiveDirectoryObjectGuid"
                    width="100"
                    mode="FilterOnly" />
                  <field
                    path="GS_CanLogin"
                    width="100"
                    mode="Optional" />
                  <field
                    path="GS_City"
                    width="100"
                    mode="Optional" />
                  <field
                    path="GS_GC_PreferredPaymentCompany"
                    width="100"
                    mode="Optional" />
                  <field
                    path="GS_PublishEmailAddress"
                    width="100"
                    mode="Optional" />
                  <field
                    path="GS_RN_NKCountryCode"
                    width="100"
                    mode="FilterOnly" />
                  <field
                    path="GS_RN_NKNationalityCode"
                    width="100"
                    mode="FilterOnly" />
                  <field
                    path="GS_SystemLastEditUser"
                    width="160"
                    mode="FilterOnly" />
                  <field
                    path="CurrentPhysicalWorkAddress.GEL_LocationSource"
                    width="220"
                    mode="Optional" />
                  <field
                    path="MostRecentCostCenter.BranchManagementCodeDescription"
                    width="250"
                    mode="Optional" />
                  <field
                    path="MostRecentCostCenter.Branch.GB_AccountingGroupCode"
                    width="250"
                    mode="Optional" />
                  <field
                    path="CurrentWorkingBasis.IsFullTime"
                    width="250"
                    mode="Optional" />
                  <field
                    path="GlbStaffWorkingBases"
                    width="190"
                    mode="FilterOnly" />
                  <field
                    path="GlbStaffReviews"
                    width="130"
                    mode="FilterOnly" />
                  <field
                    path="GlbStaffCostCentres"
                    width="180"
                    mode="FilterOnly" />
                  <field
                    path="Managers"
                    width="80"
                    mode="FilterOnly" />
                  <field
                    path="GlbEmploymentTeams"
                    width="160"
                    mode="FilterOnly" />
                  <field
                    path="DirectReports"
                    width="140"
                    mode="FilterOnly" />
                  <field
                    path="GlbEmploymentLocations"
                    width="200"
                    mode="FilterOnly" />
                  <field
                    path="HRMRemHistories"
                    width="170"
                    mode="FilterOnly" />
                  <field
                    path="GlbStaffClassifications"
                    width="210"
                    mode="FilterOnly" />
                  <field
                    path="GlbStaffRemunerations"
                    width="190"
                    mode="FilterOnly" />
                  <field
                    path="GlbEmploymentHistories"
                    width="200"
                    mode="FilterOnly" />
                  <field
                    path="IndirectManagers"
                    width="250"
                    mode="FilterOnly" />
                  <field
                    path="MostRecentPeopleLeader.GSM_ManagerType"
                    width="250"
                    mode="Optional" />
                  <field
                    path="CurrentBeneficiaryEntity.Branch.GlbCompany.GC_Name"
                    width="220"
                    mode="Optional" />
                  <field
                    path="CurrentBeneficiaryEntity.Branch.GlbCompany.GC_Code"
                    width="190"
                    mode="Optional" />
                  <field
                    path="CurrentEmployingEntity.Branch.GlbCompany.GC_Name"
                    width="220"
                    mode="Optional" />
                  <field
                    path="CurrentEmployingEntity.Branch.GlbCompany.GC_Code"
                    width="220"
                    mode="Optional" />
                  <field
                    path="CurrentRemuneration.MostRecentWaysOfWorkingAllowance.GSI_Frequency"
                    width="220"
                    mode="Optional" />
                  <field
                    path="CurrentRemuneration.MostRecentWaysOfWorkingAllowance.GSI_Value"
                    width="190"
                    mode="Optional" />
                  <field
                    path="CurrentRemuneration.MostRecentWaysOfWorkingAllowance.GSI_GrantDate"
                    width="250"
                    mode="Optional" />
                  <field
                    path="CurrentRemuneration.MostRecentWaysOfWorkingAllowance.GSI_IsPartOfPackage"
                    width="200"
                    mode="Optional" />
                  <field
                    path="CurrentRemuneration.MostRecentWaysOfWorkingAllowance.IsOneOff"
                    width="200"
                    mode="Optional" />
                  <field
                    path="CurrentRemuneration.MostRecentWaysOfWorkingAllowance.GSI_IsFTEScalable"
                    width="200"
                    mode="Optional" />
                  <field
                    path="GS_Gender"
                    width="120"
                    mode="Optional" />
                  <field
                    path="GS_DueBack"
                    width="180"
                    mode="Optional" />
                  <field
                    path="GS_WorkExtension"
                    width="100"
                    mode="Optional" />
                  <field
                    path="CurrentJobTitle"
                    width="250"
                    mode="Default" />
                  <field
                    path="GS_Title"
                    width="250"
                    mode="Optional" />
                  <field
                    path="GS_IsDriver"
                    width="90"
                    mode="Optional" />
                  <field
                    path="GS_EmergencyContactRelationship"
                    width="150"
                    mode="Optional" />
                  <field
                    path="GS_NextOfKinRelationship"
                    width="150"
                    mode="Optional" />
                  <field
                    path="CurrentBeneficiaryEntity.Branch.GB_Code"
                    width="220"
                    mode="Optional" />
                  <field
                    path="CurrentBeneficiaryEntity.Branch.GB_BranchName"
                    width="220"
                    mode="Optional" />
                  <field
                    path="GS_GB_HomeBranch"
                    width="250"
                    mode="FilterOnly" />
                  <field
                    path="CurrentRemuneration.Country.RN_Desc"
                    width="200"
                    mode="Optional" />
                  <field
                    path="CurrentPhysicalWorkAddress.Country.RN_Desc"
                    width="200"
                    mode="Optional" />
                  <field
                    path="GS_SystemCreateUser"
                    width="160"
                    mode="FilterOnly" />
                  <field
                    path="CurrentRemuneration.Currency.RX_Desc"
                    width="200"
                    mode="Optional" />
                  <field
                    path="GS_GivenName"
                    width="250"
                    mode="Optional" />
                  <field
                    path="GS_MiddleName"
                    width="250"
                    mode="Optional" />
                  <field
                    path="GS_Surname"
                    width="250"
                    mode="Optional" />
                  <field
                    path="GS_PreferredSurname"
                    width="250"
                    mode="Optional" />
                  <field
                    path="GS_FullNameInMotherLanguage"
                    width="250"
                    mode="Optional" />
                  <field
                    path="StaffName"
                    width="250"
                    mode="FilterOnly"
                    isFilterable="%.CurrentUserCheckpoints.Contains(&quot;CanSearchStaffLegalName&quot;)"
                    isVisible="%.CurrentUserCheckpoints.Contains(&quot;CanSearchStaffLegalName&quot;)" />
                  <field
                    path="CurrentEmployingBranch"
                    width="250"
                    mode="FilterOnly" />
                  <field
                    path="CurrentEmployingCompany"
                    width="250"
                    mode="FilterOnly" />
                  <field
                    path="CurrentBeneficiaryBranch"
                    width="250"
                    mode="FilterOnly" />
                  <field
                    path="CurrentBeneficiaryCompany"
                    width="250"
                    mode="FilterOnly" />
                  <field
                    path="CurrentRemuneration.Country.RN_Code"
                    width="120"
                    mode="Optional" />
                  <field
                    path="CurrentRemuneration.Currency.RX_Code"
                    width="120"
                    mode="Optional" />
                  <field
                    path="CurrentTeam.Team.GST_Code"
                    width="250"
                    mode="Optional" />
                  <field
                    path="HomeBranch.GB_BranchName"
                    width="250"
                    mode="Optional" />
                  <field
                    path="HomeBranch.GB_Code"
                    width="250"
                    mode="Optional" />
                  <field
                    path="HrlStaffPolicies"
                    width="200"
                    mode="FilterOnly" />
                  <field
                    path="HasSubordinates"
                    width="160"
                    mode="FilterOnly" />
                  <field
                    path="StaffNameOrCodeGeneral"
                    width="250"
                    mode="FilterOnly" />
                  <field
                    path="GlbGroupLinks.GK_GG"
                    width="250"
                    mode="FilterOnly" />
                  <field
                    path="GS_IsResource"
                    width="110"
                    mode="Optional" />
                  <field
                    path="GS_IsSystemAccount"
                    width="140"
                    mode="Optional" />
                  <field
                    path="Exists"
                    width="70"
                    mode="Optional" />
                  <field
                    path="IndirectManagers.GSS_GS_Manager"
                    width="250"
                    mode="FilterOnly" />
                  <field
                    path="CurrentWorkingBasisCode"
                    width="250"
                    mode="Optional" />
                  <field
                    path="ReviewAllocations"
                    width="180"
                    mode="FilterOnly" />
                  <field
                    path="IsSelf"
                    width="300"
                    mode="FilterOnly" />
                </fields>
              </xml>
            </placeholder>
            <placeholder
              name="DefaultFilter">
              <xml>
                <ArrayOfFilterGroup xmlns:a="http://schemas.microsoft.com/2003/10/Serialization/Arrays" xmlns:i="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://cargowise.com/glow/2014/07/16/filters.xsd">
                  <FilterGroup>
                    <Filters>
                      <Filter>
                        <FilterType>StringFilter</FilterType>
                        <Operation>Is</Operation>
                        <PropertyPath>IsSelf</PropertyPath>
                        <Values>
                          <a:string>true</a:string>
                        </Values>
                      </Filter>
                    </Filters>
                    <IsImplicit>false</IsImplicit>
                  </FilterGroup>
                </ArrayOfFilterGroup>
              </xml>
            </placeholder>
            <placeholder
              name="CaptionOverride"
              value="Use the filters to search for staff to see their availability"
              resid="a2388ed1-e598-42d7-90cd-2b7db6e6f274" />
          </control>
        </control>
        <control
          code="BOX"
          id="e50c3859-5794-4bab-bd95-2b881e43fc8c">
          <placeholder
            name="Padding"
            value="pb-1" />
          <control
            code="CMP"
            id="458eb384-0de3-4f6a-bb5c-077728accbe0">
            <placeholder
              name="Component"
              value="cargoWiseOne.productHrm.components.TeamLeaveCalendar" />
          </control>
        </control>
      </control>
    </control>
  </form>
