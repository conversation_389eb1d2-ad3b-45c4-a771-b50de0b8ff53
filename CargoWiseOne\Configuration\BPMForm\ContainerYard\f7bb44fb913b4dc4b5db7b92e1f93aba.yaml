#transformationVersion: 70.0
#
VZ_PK: f7bb44fb913b4dc4b5db7b92e1f93aba
VZ_ConfigurationKey: f7bb44fb-913b-4dc4-b5db-7b92e1f93aba
VZ_FormID: CYP Maintenance & Repair Listing
VZ_Caption:
  resKey: VZ_Caption|f7bb44fb913b4dc4b5db7b92e1f93aba
  text: Maintenance & Repair
VZ_FormFactor: DSK
VZ_FormType: PAG
VZ_RequiresLogin: true
VZ_MaterialDesign: true
VZ_Dependencies: <dependencies xmlns="http://cargowise.com/glow/2014/11/14/dependencies.xsd" />
VZ_FormData: >-
  <form
    id="1bd6c8dc-ce9c-4db9-ac79-8aa44fedcc54" xmlns="http://wisetechglobal.com/glow/2017/05/26/form.xsd">
    <placeholder
      name="Layout"
      value="fill" />
    <control
      code="TBS"
      id="6b3c60e5-a3bb-43e0-a215-5939a1f2e897">
      <control
        code="TAB"
        id="bb690094-2b4c-45ab-a38f-b92042a81e7e">
        <placeholder
          name="Caption"
          value="Estimate of repairs"
          resid="483972e4-36c7-4d84-acf8-dc1d5b7fea95" />
      </control>
      <control
        code="TAI"
        id="330eb296-35e2-45c7-a894-09876f13be5f">
        <control
          code="SDT"
          id="46332523-0d52-4c6e-9d98-ade2fd3f2f73">
          <placeholder
            name="CaptionOverride"
            value="Estimates of repair"
            resid="bad58c13-0249-4000-871a-ba54a7cdc96d" />
          <placeholder
            name="FullPage"
            value="True" />
          <placeholder
            name="SearchMode"
            value="Index" />
          <placeholder
            name="EntityType"
            value="IMNRWorkOrderHeader[[ICYDYardUnitState]]" />
          <placeholder
            name="FieldConfiguration">
            <xml>
              <fields xmlns="">
                <field
                  path="MNRSTATUS"
                  width="180"
                  mode="Default" />
                <field
                  path="ESTIMATEID"
                  width="180"
                  mode="Default" />
                <field
                  path="ESTIMATETYPE"
                  width="180"
                  mode="Default" />
                <field
                  path="YARDUNITID"
                  width="180"
                  mode="Default" />
                <field
                  path="TYPESIZE"
                  width="180"
                  mode="Default" />
                <field
                  path="YARDUNITTYPE"
                  width="180"
                  mode="Default" />
                <field
                  path="FINALIZEDTIME"
                  width="180"
                  mode="Default" />
                <field
                  path="CLIENT"
                  width="180"
                  mode="Default" />
                <field
                  path="LESSEE"
                  width="180"
                  mode="Default" />
                <field
                  path="SLTSURVEYOR"
                  width="180"
                  caption="Structural Surveyor"
                  resid="67e0b989-51da-4ab7-8b8a-9becd61c581a"
                  mode="Default" />
                <field
                  path="MACSURVEYOR"
                  width="180"
                  caption="Machinery surveyor"
                  resid="da98defa-da7f-4319-800b-0b43faa5204c"
                  mode="Default" />
                <field
                  path="EQUIPMENTCONDITION"
                  width="180"
                  mode="Default" />
                <field
                  path="YardUnitGrade"
                  width="180"
                  mode="Default" />
                <field
                  path="MNRMANUFACTUREDATE"
                  width="180"
                  mode="Default" />
                <field
                  path="MNRMaxGrossWeight"
                  width="180"
                  mode="Default" />
                <field
                  path="MNRYardInID"
                  width="180"
                  mode="Default" />
                <field
                  path="YARDINTIME"
                  width="180"
                  mode="Default" />
                <field
                  path="CREATEUSER"
                  width="180"
                  mode="Optional" />
                <field
                  path="CREATEUSERNAME"
                  width="180"
                  mode="Optional" />
                <field
                  path="CREATETIME"
                  width="180"
                  mode="Optional" />
                <field
                  path="MNRREVISIONNUMBER"
                  width="180"
                  mode="Default" />
                <field
                  path="REQUIREDCLIENTAPPROVAL"
                  width="180"
                  mode="Default" />
                <field
                  path="REQUIREDLESSEEAPPROVAL"
                  width="180"
                  mode="Default" />
              </fields>
            </xml>
          </placeholder>
          <placeholder
            name="ActionMenuItems">
            <xml>
              <formFlows xmlns="">
                <formFlow>18a51a1830424d4c96a1959aeee68c9a</formFlow>
                <formFlow>9fbf1312121c4708abe0797e8e30b0b3</formFlow>
              </formFlows>
            </xml>
          </placeholder>
          <placeholder
            name="DefaultFilter">
            <xml>
              <ArrayOfFilterGroup xmlns:a="http://schemas.microsoft.com/2003/10/Serialization/Arrays" xmlns:i="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://cargowise.com/glow/2014/07/16/filters.xsd">
                <FilterGroup>
                  <Filters>
                    <Filter>
                      <FilterType>StringFilter</FilterType>
                      <Operation>Is</Operation>
                      <PropertyPath>FACILITY</PropertyPath>
                      <Values>
                        <a:string>&lt;%.CurrentBranch.Yard.WW_WarehouseCode&gt;</a:string>
                      </Values>
                    </Filter>
                  </Filters>
                  <IsImplicit>true</IsImplicit>
                </FilterGroup>
              </ArrayOfFilterGroup>
            </xml>
          </placeholder>
          <placeholder
            name="EditFormFlowConfiguration">
            <xml>
              <formFlows xmlns="">
                <formFlow>9fbf1312121c4708abe0797e8e30b0b3</formFlow>
              </formFlows>
            </xml>
          </placeholder>
          <placeholder
            name="DisabledGridRowActions"
            value="Remove" />
          <placeholder
            name="CaptionType"
            value="none" />
        </control>
      </control>
      <control
        code="TAB"
        id="ad5bdfdc-9c00-4182-b914-1edd2ad65ebe">
        <placeholder
          name="Caption"
          value="Awaiting surveys"
          resid="c89354cc-4cb1-4608-b710-ca11bc05c548" />
      </control>
      <control
        code="TAI"
        id="73a2ac79-40b1-47e4-aedb-82283a40ab7c">
        <control
          code="SDT"
          id="a6106a18-be1f-4732-9227-9db44a118ec4">
          <placeholder
            name="EntityType"
            value="IMNRSurvey" />
          <placeholder
            name="SearchMode"
            value="Entity" />
          <placeholder
            name="CaptionType"
            value="none" />
          <placeholder
            name="FieldConfiguration">
            <xml>
              <fields xmlns="">
                <field
                  path="YardUnitState.YUS_UnitID"
                  width="160"
                  caption="Unit number"
                  resid="158c1c78-d5bb-4b25-b0fb-0218dc97db3e"
                  mode="Mandatory" />
                <field
                  path="MRS_Type"
                  width="160"
                  caption="Survey type"
                  resid="01de558f-417d-4d88-a846-9a9337753078"
                  mode="Default" />
                <field
                  path="Status"
                  width="160"
                  mode="Default"
                  readOnly="true" />
                <field
                  path="UnitCondition"
                  width="160"
                  mode="Default"
                  readOnly="true" />
                <field
                  path="YardUnitState.ReceiveTransportationUnit.YTU_GateInTime"
                  width="160"
                  caption="Yard in time"
                  resid="962e4408-c4a4-4218-80d5-722e4cbdf698"
                  mode="Default" />
                <field
                  path="YardUnitState.ReceiveTransportationUnit.YTU_TransportationUnitID"
                  width="160"
                  caption="Yard in ID"
                  resid="2882e467-2b44-46b3-b4ce-0ca121752358"
                  mode="Default" />
                <field
                  path="CreatedByStaff.GS_FullName"
                  width="160"
                  caption="Surveyor"
                  resid="629e5853-0e3f-4640-b36b-e262b449b901"
                  mode="Optional" />
                <field
                  path="YardUnitState.UnitGrade"
                  width="160"
                  caption="Grade"
                  resid="4c753ca9-fa33-484f-a47f-868e25446fc5"
                  mode="Default" />
                <field
                  path="YardUnitState.TypeSize"
                  width="160"
                  caption="Type size"
                  resid="516b08ec-5602-444d-8b06-0a766a68ac07"
                  mode="Default" />
                <field
                  path="YardUnitState.Client"
                  width="180"
                  mode="Default" />
                <field
                  path="YardUnitState.Lessee"
                  width="180"
                  mode="Default" />
              </fields>
            </xml>
          </placeholder>
          <placeholder
            name="HideExport"
            value="True" />
          <placeholder
            name="HideImport"
            value="True" />
          <placeholder
            name="DisabledGridRowActions"
            value="Activation,Documents,eDocs,Messages,Remove,Tracking,Workflow" />
          <placeholder
            name="DefaultFilter">
            <xml>
              <ArrayOfFilterGroup xmlns:a="http://schemas.microsoft.com/2003/10/Serialization/Arrays" xmlns:i="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://cargowise.com/glow/2014/07/16/filters.xsd">
                <FilterGroup>
                  <Filters>
                    <Filter>
                      <FilterType>BoolFilter</FilterType>
                      <Operation>Is</Operation>
                      <PropertyPath>MRS_IsCompleted</PropertyPath>
                      <Values>
                        <a:string>false</a:string>
                      </Values>
                    </Filter>
                  </Filters>
                  <IsImplicit>true</IsImplicit>
                </FilterGroup>
                <FilterGroup>
                  <Filters>
                    <Filter>
                      <FilterType>AdvancedGuidLookupFilter</FilterType>
                      <Operation>IsBlank</Operation>
                      <PropertyPath>MRS_MWO_MNRWorkOrderHeader</PropertyPath>
                      <Values />
                    </Filter>
                    <Filter>
                      <FilterType>BoolFilter</FilterType>
                      <Operation>Is</Operation>
                      <PropertyPath>MRS_IsCompleted</PropertyPath>
                      <Values>
                        <a:string>true</a:string>
                      </Values>
                    </Filter>
                    <Filter>
                      <FilterType>BoolFilter</FilterType>
                      <Operation>Is</Operation>
                      <PropertyPath>MRS_RequireEstimate</PropertyPath>
                      <Values>
                        <a:string>true</a:string>
                      </Values>
                    </Filter>
                  </Filters>
                  <IsImplicit>true</IsImplicit>
                </FilterGroup>
              </ArrayOfFilterGroup>
            </xml>
          </placeholder>
          <placeholder
            name="FullPage"
            value="True" />
        </control>
      </control>
    </control>
  </form>
