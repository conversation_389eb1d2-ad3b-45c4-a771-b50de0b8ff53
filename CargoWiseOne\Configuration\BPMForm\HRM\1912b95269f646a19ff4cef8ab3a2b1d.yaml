#transformationVersion: 70.0
#
VZ_PK: 1912b95269f646a19ff4cef8ab3a2b1d
VZ_ConfigurationKey: 1912b952-69f6-46a1-9ff4-cef8ab3a2b1d
VZ_FormID: HRM - Change Requests
VZ_Caption:
  resKey: VZ_Caption|1912b952-69f6-46a1-9ff4-cef8ab3a2b1d
  text: Change Requests
VZ_FormFactor: DSK
VZ_FormType: PAG
VZ_RequiresLogin: true
VZ_MaterialDesign: true
VZ_Dependencies: <dependencies xmlns="http://cargowise.com/glow/2014/11/14/dependencies.xsd" />
VZ_FormData: >-
  <form
    id="fb370872-2cfa-4a6d-9199-01dc10be896b" xmlns="http://wisetechglobal.com/glow/2017/05/26/form.xsd">
    <placeholder
      name="Layout"
      value="fill" />
    <control
      code="SDT"
      id="d8eff89b-6145-42e3-a9d7-5e4d0e7521bd"
      binding="">
      <placeholder
        name="CaptionOverride"
        value="Change Requests"
        resid="5a314586-005a-4cf3-abde-d168a784acf1" />
      <placeholder
        name="EntityType"
        value="IGlbStaffChangeRequest" />
      <placeholder
        name="DefaultFilter">
        <xml>
          <ArrayOfFilterGroup xmlns:a="http://schemas.microsoft.com/2003/10/Serialization/Arrays" xmlns:i="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://cargowise.com/glow/2014/07/16/filters.xsd">
            <FilterGroup>
              <Filters>
                <Filter>
                  <FilterType>StringFilter</FilterType>
                  <Operation>Is</Operation>
                  <PropertyPath>GCR_Status</PropertyPath>
                  <Values>
                    <a:string>REQ</a:string>
                  </Values>
                </Filter>
              </Filters>
              <IsImplicit>false</IsImplicit>
            </FilterGroup>
          </ArrayOfFilterGroup>
        </xml>
      </placeholder>
      <placeholder
        name="EditFormFlowConfiguration">
        <xml>
          <formFlows xmlns="">
            <formFlow>1bcace699bec4374add622247289e926</formFlow>
          </formFlows>
        </xml>
      </placeholder>
      <placeholder
        name="FullPage"
        value="True" />
      <placeholder
        name="DisabledGridRowActions"
        value="Remove" />
    </control>
  </form>
