#transformationVersion: 70.0
#
VZ_PK: 2d2bd29eefc04220aab67f93242520a6
VZ_ConfigurationKey: 2d2bd29e-efc0-4220-aab6-7f93242520a6
VZ_FormID: CCX - Relevant Sailing Schedules
VZ_Caption:
  resKey: VZ_Caption|2d2bd29e-efc0-4220-aab6-7f93242520a6
  text: Relevant Sailing Schedules
VZ_FormFactor: DSK
VZ_EntityType: IRatingContractAllocationLine
VZ_FormType: FRM
VZ_RequiresLogin: true
VZ_ExtenderFunc: CargoWise.Glow.Business.Forwarding.Contracts.RelevantSailingSchedulesFormExtender.RelevantSailingSchedules
VZ_Dependencies: <dependencies xmlns="http://cargowise.com/glow/2014/11/14/dependencies.xsd" />
VZ_FormData: >-
  <form
    id="3d7c050b-8c9e-4d58-8435-4c6256ae4a56" xmlns="http://wisetechglobal.com/glow/2017/05/26/form.xsd">
    <placeholder
      name="BackgroundImagePk"
      value="" />
    <placeholder
      name="BackgroundSize"
      value="Auto" />
    <placeholder
      name="BackgroundImageOverride"
      value="" />
    <placeholder
      name="FormLayout"
      value="Splitview" />
    <placeholder
      name="ExpandPaths"
      value="" />
    <placeholder
      name="ScanInputHandling"
      value="NON" />
    <placeholder
      name="RemovePadding"
      value="false" />
    <placeholder
      name="TurnOnPortableOneToOneDesignMode"
      value="false" />
    <placeholder
      name="ContainsDefaultButton"
      value="False" />
    <control
      code="SRL"
      id="f1d5d9b7-7981-4fe1-84ee-5b75995755d9"
      left="0"
      top="0"
      right="0"
      bottom="1">
      <placeholder
        name="CanBeHidden"
        value="True" />
      <placeholder
        name="IsInitialFocus"
        value="False" />
      <placeholder
        name="CaptionOverride"
        value="Schedules"
        resid="4eff1eda-129a-44d7-84c2-14c48eb71766" />
      <placeholder
        name="VisibilityCondition"
        value="" />
      <placeholder
        name="HeaderAlignment"
        value="Left" />
      <placeholder
        name="HeaderDisplayMode"
        value="Visible" />
      <placeholder
        name="FilterType"
        value="Entity" />
      <placeholder
        name="EntityType"
        value="IJobSailing" />
      <placeholder
        name="ItemDisplayPath"
        value="" />
      <placeholder
        name="DisabledGridRowActions"
        value="" />
      <placeholder
        name="DefaultFilter">
        <xml>
          <ArrayOfFilterGroup xmlns:a="http://schemas.microsoft.com/2003/10/Serialization/Arrays" xmlns:i="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://cargowise.com/glow/2014/07/16/filters.xsd">
            <FilterGroup>
              <Filters>
                <Filter>
                  <FilterType>AdvancedGuidLookupFilter</FilterType>
                  <Operation>Is</Operation>
                  <PropertyPath>JobVoyOrigin.JobVoyage.JV_OH_Line</PropertyPath>
                  <Values>
                    <a:string>&lt;RatingContract.OrgHeader.OH_PK&gt;</a:string>
                  </Values>
                </Filter>
                <Filter>
                  <FilterType>StringFilter</FilterType>
                  <Operation>StartsWith</Operation>
                  <PropertyPath>JobVoyOrigin.PortOfLoading.RL_Code</PropertyPath>
                  <Values>
                    <a:string>&lt;LoadLocationWithProxy&gt;</a:string>
                  </Values>
                </Filter>
                <Filter>
                  <FilterType>StringFilter</FilterType>
                  <Operation>StartsWith</Operation>
                  <PropertyPath>JobVoyDestination.PortOfDischarge.RL_Code</PropertyPath>
                  <Values>
                    <a:string>&lt;DischargeLocationWithProxy&gt;</a:string>
                  </Values>
                </Filter>
                <Filter>
                  <FilterType>StringFilter</FilterType>
                  <Operation>Is</Operation>
                  <PropertyPath>JobVoyOrigin.JobVoyage.JV_VoyageFlight</PropertyPath>
                  <Values>
                    <a:string>&lt;VoyageWithProxy&gt;</a:string>
                  </Values>
                </Filter>
                <Filter>
                  <FilterType>AdvancedStringLookupFilter</FilterType>
                  <Operation>Is</Operation>
                  <PropertyPath>JobVoyOrigin.JobVoyage.JV_RV_NKVessel</PropertyPath>
                  <Values>
                    <a:string>&lt;VesselWithProxy&gt;</a:string>
                  </Values>
                </Filter>
                <Filter>
                  <FilterType>DateTimeFilter</FilterType>
                  <Operation>IsInTheDateRange</Operation>
                  <PropertyPath>JobVoyOrigin.JA_A_DEP</PropertyPath>
                  <Values>
                    <a:string>&lt;StartDateWithContractFallback&gt;</a:string>
                    <a:string>&lt;ExpiryDateWithContractFallback&gt;</a:string>
                  </Values>
                </Filter>
              </Filters>
              <IsImplicit>false</IsImplicit>
            </FilterGroup>
            <FilterGroup>
              <Filters>
                <Filter>
                  <FilterType>AdvancedGuidLookupFilter</FilterType>
                  <Operation>Is</Operation>
                  <PropertyPath>JobVoyOrigin.JobVoyage.JV_OH_Line</PropertyPath>
                  <Values>
                    <a:string>&lt;RatingContract.OrgHeader.OH_PK&gt;</a:string>
                  </Values>
                </Filter>
                <Filter>
                  <FilterType>StringFilter</FilterType>
                  <Operation>StartsWith</Operation>
                  <PropertyPath>JobVoyOrigin.PortOfLoading.RL_Code</PropertyPath>
                  <Values>
                    <a:string>&lt;LoadLocationWithProxy&gt;</a:string>
                  </Values>
                </Filter>
                <Filter>
                  <FilterType>StringFilter</FilterType>
                  <Operation>StartsWith</Operation>
                  <PropertyPath>JobVoyDestination.PortOfDischarge.RL_Code</PropertyPath>
                  <Values>
                    <a:string>&lt;DischargeLocationWithProxy&gt;</a:string>
                  </Values>
                </Filter>
                <Filter>
                  <FilterType>StringFilter</FilterType>
                  <Operation>Is</Operation>
                  <PropertyPath>JobVoyOrigin.JobVoyage.JV_VoyageFlight</PropertyPath>
                  <Values>
                    <a:string>&lt;VoyageWithProxy&gt;</a:string>
                  </Values>
                </Filter>
                <Filter>
                  <FilterType>AdvancedStringLookupFilter</FilterType>
                  <Operation>Is</Operation>
                  <PropertyPath>JobVoyOrigin.JobVoyage.JV_RV_NKVessel</PropertyPath>
                  <Values>
                    <a:string>&lt;VesselWithProxy&gt;</a:string>
                  </Values>
                </Filter>
                <Filter>
                  <FilterType>DateTimeFilter</FilterType>
                  <Operation>IsInTheDateRange</Operation>
                  <PropertyPath>JobVoyOrigin.JA_E_DEP</PropertyPath>
                  <Values>
                    <a:string>&lt;StartDateWithContractFallback&gt;</a:string>
                    <a:string>&lt;ExpiryDateWithContractFallback&gt;</a:string>
                  </Values>
                </Filter>
              </Filters>
              <IsImplicit>false</IsImplicit>
            </FilterGroup>
          </ArrayOfFilterGroup>
        </xml>
      </placeholder>
      <placeholder
        name="DefaultFilterPropertyPath"
        value="" />
      <placeholder
        name="FieldConfiguration"
        value="" />
      <placeholder
        name="DefaultSortFields"
        value="" />
      <placeholder
        name="DefaultGroupFields"
        value="" />
      <placeholder
        name="EnableGrouping"
        value="False" />
      <placeholder
        name="AutoRefreshInterval"
        value="0" />
      <placeholder
        name="AllowMultiSelect"
        value="True" />
      <placeholder
        name="ShowWorkflowLabel"
        value="True" />
      <placeholder
        name="RowActionsMode"
        value="RowAndMenu" />
      <placeholder
        name="HideGridActions"
        value="False" />
      <placeholder
        name="MinimizeFilters"
        value="False" />
      <placeholder
        name="WorkflowOpenInNewWindow"
        value="False" />
      <placeholder
        name="SearchOnInit"
        value="True" />
      <placeholder
        name="ShowQuickSearch"
        value="False" />
      <placeholder
        name="QuickSearchCaption"
        value="" />
      <placeholder
        name="ValueField"
        value="" />
      <placeholder
        name="Category"
        value="" />
      <placeholder
        name="ActionMenuItems"
        value="" />
      <placeholder
        name="EditFormFlow"
        value="" />
      <placeholder
        name="ResetFilterOnInit"
        value="True" />
      <placeholder
        name="FilterDisplayMode"
        value="InlineAndPopup" />
    </control>
    <control
      code="TBT"
      id="28271c9f-dfc8-497b-918f-45cc66ee69cb"
      width="5"
      height="1"
      right="0"
      bottom="0">
      <placeholder
        name="CanBeHidden"
        value="True" />
      <placeholder
        name="IsInitialFocus"
        value="False" />
      <placeholder
        name="VisibilityCondition"
        value="" />
      <placeholder
        name="Content"
        value="Import Routing Information"
        resid="c04ea578-3b06-45f4-8cb4-fe353306a747" />
      <placeholder
        name="IsDefault"
        value="False" />
    </control>
  </form>
