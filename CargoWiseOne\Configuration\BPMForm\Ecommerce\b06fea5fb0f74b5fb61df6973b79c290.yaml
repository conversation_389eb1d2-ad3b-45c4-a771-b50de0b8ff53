#transformationVersion: 70.0
#
VZ_PK: b06fea5fb0f74b5fb61df6973b79c290
VZ_ConfigurationKey: b06fea5f-b0f7-4b5f-b61d-f6973b79c290
VZ_FormID: ETL - VDV3 - Shipper Portal - Consignment Tracking
VZ_Caption:
  resKey: VZ_Caption|b06fea5f-b0f7-4b5f-b61d-f6973b79c290
  text: Consignments
VZ_FormFactor: DSK
VZ_FormType: PAG
VZ_RequiresLogin: true
VZ_MaterialDesign: true
VZ_Dependencies: <dependencies xmlns="http://cargowise.com/glow/2014/11/14/dependencies.xsd" />
VZ_FormData: >-
  <form
    id="ac13f136-3831-490b-8156-e5fa21ea5151" xmlns="http://wisetechglobal.com/glow/2017/05/26/form.xsd">
    <control
      code="SEP"
      id="83f94d8c-5c9d-4ec3-b94b-6f8400b97b0f"
      binding="">
      <placeholder
        name="ContentTemplateID"
        value="0290e4b48dd14594b0eb7f2551848d68" />
      <placeholder
        name="CaptionOverride"
        value="Consignment Tracking Results"
        resid="8a67fbf5-c512-4cfa-8af3-a813644c72bc" />
      <placeholder
        name="EntityType"
        value="IHVLVConsignment" />
      <placeholder
        name="HeaderContentTemplateID"
        value="7702dba521b143ab9fd349cfda80f12c" />
      <placeholder
        name="ShowTrackingExcelExport"
        value="True" />
      <placeholder
        name="SearchMode"
        value="Index" />
      <placeholder
        name="FilterType"
        value="Global" />
      <placeholder
        name="DefaultFilter">
        <xml>
          <ArrayOfFilterGroup xmlns:a="http://schemas.microsoft.com/2003/10/Serialization/Arrays" xmlns:i="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://cargowise.com/glow/2014/07/16/filters.xsd">
            <FilterGroup>
              <Filters>
                <Filter>
                  <FilterType>StringFilter</FilterType>
                  <Operation>StartsWith</Operation>
                  <PropertyPath>ITEMID</PropertyPath>
                  <Values />
                </Filter>
                <Filter>
                  <FilterType>StringFilter</FilterType>
                  <Operation>StartsWith</Operation>
                  <PropertyPath>WAYBILLNUMBER</PropertyPath>
                  <Values />
                </Filter>
              </Filters>
              <IsImplicit>false</IsImplicit>
            </FilterGroup>
          </ArrayOfFilterGroup>
        </xml>
      </placeholder>
      <placeholder
        name="DefaultFilterPropertyPath"
        value="" />
      <placeholder
        name="FieldConfiguration">
        <xml>
          <fields xmlns="">
            <field
              path="EMANIFEST"
              width="100"
              mode="Default" />
            <field
              path="WAYBILLNUMBER"
              width="100"
              mode="Default" />
            <field
              path="CONSIGNMENTSHIPPERREFERENCE"
              width="100"
              mode="Default" />
            <field
              path="ITEMCOUNT"
              width="100"
              mode="Default" />
            <field
              path="SHIPPERNAME"
              width="100"
              mode="Default" />
            <field
              path="CONSIGNEE"
              width="100"
              mode="Default" />
            <field
              path="ORIGINPORT"
              width="100"
              mode="Default" />
            <field
              path="DESTINATIONPORT"
              width="100"
              mode="Default" />
            <field
              path="GOODSDESCRIPTION"
              width="100"
              mode="Default" />
            <field
              path="MANIFESTEDWEIGHT"
              width="100"
              mode="Default" />
            <field
              path="MANIFESTEDVOLUME"
              width="100"
              mode="Default" />
            <field
              path="ACTUALWEIGHT"
              width="100"
              mode="Default" />
            <field
              path="ACTUALVOLUME"
              width="100"
              mode="Default" />
            <field
              path="ISACTIVE"
              width="100"
              mode="Optional" />
            <field
              path="CREATETIME"
              width="100"
              mode="Optional" />
            <field
              path="CONSIGNEECOUNTRY"
              width="100"
              mode="Optional" />
            <field
              path="CREATEUSER"
              width="100"
              mode="Optional" />
            <field
              path="CURRENCY"
              width="100"
              mode="Optional" />
            <field
              path="LASTMILECARRIERSERVICELEVEL"
              width="100"
              mode="Optional" />
            <field
              path="SHIPPERCOUNTRY"
              width="100"
              mode="Optional" />
            <field
              path="CONSIGNEEADDRESS1"
              width="100"
              mode="Optional" />
            <field
              path="CONSIGNEEADDRESS2"
              width="100"
              mode="Optional" />
            <field
              path="CONSIGNEECITY"
              width="100"
              mode="Optional" />
            <field
              path="CONSIGNEECONTACT"
              width="100"
              mode="Optional" />
            <field
              path="CONSIGNEEEMAIL"
              width="100"
              mode="Optional" />
            <field
              path="CONSIGNEEFAX"
              width="100"
              mode="Optional" />
            <field
              path="CONSIGNEEINSTRUCTIONS"
              width="100"
              mode="Optional" />
            <field
              path="CONSIGNEEMOBILE"
              width="100"
              mode="Optional" />
            <field
              path="CONSIGNEEPHONE"
              width="100"
              mode="Optional" />
            <field
              path="CONSIGNEEPOSTCODE"
              width="100"
              mode="Optional" />
            <field
              path="CONSIGNEESTATE"
              width="100"
              mode="Optional" />
            <field
              path="CONSIGNMENTID"
              width="100"
              mode="Optional" />
            <field
              path="SHIPPERADDRESS1"
              width="100"
              mode="Optional" />
            <field
              path="SHIPPERADDRESS2"
              width="100"
              mode="Optional" />
            <field
              path="SHIPPERCITY"
              width="100"
              mode="Optional" />
            <field
              path="SHIPPERCONTACT"
              width="100"
              mode="Optional" />
            <field
              path="SHIPPEREMAIL"
              width="100"
              mode="Optional" />
            <field
              path="SHIPPERFAX"
              width="100"
              mode="Optional" />
            <field
              path="SHIPPERMOBILE"
              width="100"
              mode="Optional" />
            <field
              path="SHIPPERPHONE"
              width="100"
              mode="Optional" />
            <field
              path="SHIPPERPOSTCODE"
              width="100"
              mode="Optional" />
            <field
              path="SHIPPERSTATE"
              width="100"
              mode="Optional" />
            <field
              path="ETAILCONSIGNMENTSTATUS"
              width="100"
              mode="Optional" />
            <field
              path="DGCLASS"
              width="100"
              mode="Optional" />
            <field
              path="AUTHORITYTOLEAVE"
              width="100"
              mode="Optional" />
            <field
              path="HAZARDOUS"
              width="100"
              mode="Optional" />
            <field
              path="ISPERISHABLE"
              width="100"
              mode="Optional" />
            <field
              path="ISSELFBOOKED"
              width="100"
              mode="Optional" />
            <field
              path="ISSIGNATUREREQUIRED"
              width="100"
              mode="Optional" />
            <field
              path="ISTIMBER"
              width="100"
              mode="Optional" />
            <field
              path="REQUIRESFUMIGATION"
              width="100"
              mode="Optional" />
            <field
              path="DESTINATIONDEPOT"
              width="100"
              mode="Optional" />
            <field
              path="ISPERSONALEFFECTS"
              width="100"
              mode="Optional" />
            <field
              path="LASTMILECARRIER"
              width="100"
              mode="Optional" />
            <field
              path="ORIGINPORTIATA"
              width="100"
              mode="Optional" />
            <field
              path="ORIGINPORTCOUNTRY"
              width="100"
              mode="Optional" />
            <field
              path="DESTINATIONPORTIATA"
              width="100"
              mode="Optional" />
            <field
              path="DESTINATIONPORTCOUNTRY"
              width="100"
              mode="Optional" />
            <field
              path="TRANSPORTMODE"
              width="100"
              mode="Optional" />
            <field
              path="GOODSVALUE"
              width="100"
              mode="Optional" />
            <field
              path="SHIPMENTID"
              width="100"
              mode="Optional" />
            <field
              path="CONSOLID"
              width="100"
              mode="Optional" />
            <field
              path="MASTERBILLNUMBER"
              width="100"
              mode="Optional" />
            <field
              path="RELEASESTATUS"
              width="100"
              mode="Optional" />
            <field
              path="ETDLOCAL"
              width="100"
              mode="Optional" />
            <field
              path="ETALOCAL"
              width="100"
              mode="Optional" />
            <field
              path="DEPARTUREDATELOCAL"
              width="100"
              mode="Optional" />
            <field
              path="ARRIVALDATELOCAL"
              width="100"
              mode="Optional" />
            <field
              path="SERVICELEVEL"
              width="100"
              mode="Optional" />
            <field
              path="INSURANCEVALUE"
              width="300"
              mode="Optional" />
            <field
              path="TRANSPORTVALUE"
              width="300"
              mode="Optional" />
            <field
              path="ITEMID"
              width="150"
              mode="FilterOnly" />
            <field
              path="ITEMSHIPPERREFERENCE"
              width="150"
              mode="FilterOnly" />
            <field
              path="ITEMBARCODE"
              width="150"
              mode="Optional" />
          </fields>
        </xml>
      </placeholder>
      <placeholder
        name="ShowItemActions"
        value="True" />
      <placeholder
        name="ShowQuickSearch"
        value="True" />
      <placeholder
        name="ItemViewModelExtender"
        value="TrackingWithLocation" />
      <placeholder
        name="ShowAutoRefresh"
        value="True" />
    </control>
  </form>
