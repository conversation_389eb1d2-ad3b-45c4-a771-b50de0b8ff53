#transformationVersion: 70.0
#
VZ_PK: b4de5a7efe5b48abb2263a975f44dc18
VZ_ConfigurationKey: b4de5a7e-fe5b-48ab-b226-3a975f44dc18
VZ_FormID: ETL - VDV3 - Origin Depot - Outer Packages
VZ_Caption:
  resKey: VZ_Caption|b4de5a7e-fe5b-48ab-b226-3a975f44dc18
  text: Outer Packages
VZ_FormFactor: DSK
VZ_FormType: PAG
VZ_RequiresLogin: true
VZ_Dependencies: <dependencies xmlns="http://cargowise.com/glow/2014/11/14/dependencies.xsd" />
VZ_FormData: >-
  <form
    id="cc0da138-0797-4cd2-b813-d721a35753e5" xmlns="http://wisetechglobal.com/glow/2017/05/26/form.xsd">
    <placeholder
      name="BackgroundImagePk"
      value="" />
    <placeholder
      name="BackgroundSize"
      value="Auto" />
    <placeholder
      name="BackgroundImageOverride"
      value="" />
    <placeholder
      name="FormLayout"
      value="Splitview" />
    <placeholder
      name="ExpandPaths"
      value="" />
    <placeholder
      name="ScanInputHandling"
      value="NON" />
    <placeholder
      name="RemovePadding"
      value="false" />
    <placeholder
      name="TurnOnPortableOneToOneDesignMode"
      value="false" />
    <placeholder
      name="ContainsDefaultButton"
      value="False" />
    <control
      code="GRP"
      id="448f065b-f325-4d3e-8c58-43127297d4f4"
      left="0"
      top="0"
      width="6"
      height="4">
      <placeholder
        name="CanBeHidden"
        value="True" />
      <placeholder
        name="VisibilityCondition"
        value="" />
      <placeholder
        name="Header"
        value="New Section"
        resid="cbcd548f-cf6b-4eff-89b4-c1d29843c3fa" />
      <placeholder
        name="HeaderAlignment"
        value="Left" />
      <placeholder
        name="HeaderDisplayMode"
        value="Hidden" />
      <placeholder
        name="BackgroundImagePk"
        value="" />
      <placeholder
        name="BackgroundImageOverride"
        value="" />
      <control
        code="TIL"
        id="e8542b5d-748a-45f2-bdfe-0eebb4a00d05"
        left="0"
        top="0"
        width="2"
        height="2">
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="Size"
          value="Standard" />
        <placeholder
          name="Color"
          value="Default" />
        <placeholder
          name="DisplayMode"
          value="TextAndIcon" />
        <placeholder
          name="Text"
          value="Shipments"
          resid="3a415550-ca15-42e6-b073-eb7d6292e15c" />
        <placeholder
          name="Image"
          value="d4c02b187e9d4e37be59b173a7caa29a" />
        <placeholder
          name="ImageOverride"
          value="" />
        <placeholder
          name="Description"
          value="" />
        <placeholder
          name="Behaviour"
          value="Page" />
        <placeholder
          name="PagePK"
          value="d4f1974737b64b2c9ccfc3fe6214292e" />
        <placeholder
          name="Url"
          value="" />
        <placeholder
          name="FormFlow"
          value="" />
      </control>
      <control
        code="TIL"
        id="a8ee33e9-b91f-46de-875f-e06e3d239f13"
        left="2"
        top="0"
        width="2"
        height="2">
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="Size"
          value="Standard" />
        <placeholder
          name="Color"
          value="Default" />
        <placeholder
          name="DisplayMode"
          value="TextAndIcon" />
        <placeholder
          name="Text"
          value="Consignments"
          resid="c329cddb-b403-46ee-add9-6d89d49a231a" />
        <placeholder
          name="Image"
          value="2c348de953fe4e8db9967317a1a942c4" />
        <placeholder
          name="ImageOverride"
          value="" />
        <placeholder
          name="Description"
          value="" />
        <placeholder
          name="Behaviour"
          value="Page" />
        <placeholder
          name="PagePK"
          value="94d76ffabddb4f35938a7dfb833a35a6" />
        <placeholder
          name="Url"
          value="" />
        <placeholder
          name="FormFlow"
          value="" />
      </control>
      <control
        code="TIL"
        id="08845d9f-55a0-4d76-9577-47b4404758fe"
        left="4"
        top="0"
        width="2"
        height="2">
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="Size"
          value="Standard" />
        <placeholder
          name="Color"
          value="Default" />
        <placeholder
          name="DisplayMode"
          value="TextAndIcon" />
        <placeholder
          name="Text"
          value="Headers"
          resid="d2c0fcad-3939-467c-bfb7-699b0146ff6c" />
        <placeholder
          name="Image"
          value="05e1a4771ad542aeadfef74283eec66b" />
        <placeholder
          name="ImageOverride"
          value="" />
        <placeholder
          name="Description"
          value="" />
        <placeholder
          name="Behaviour"
          value="Page" />
        <placeholder
          name="PagePK"
          value="e4743903bbfd4ad492d5c5cc05d1d08d" />
        <placeholder
          name="Url"
          value="" />
        <placeholder
          name="FormFlow"
          value="" />
      </control>
      <control
        code="TIL"
        id="f8bd585a-3569-43f5-b09b-de2ac01de90b"
        left="0"
        top="2"
        width="2"
        height="2">
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="Size"
          value="Standard" />
        <placeholder
          name="Color"
          value="Primary" />
        <placeholder
          name="DisplayMode"
          value="TextAndIcon" />
        <placeholder
          name="Text"
          value="Outer Packages"
          resid="392c46c1-799f-4611-b8d8-e0109db4e7dc" />
        <placeholder
          name="Image"
          value="3e36f6d7c44842d8b2faa22f952210e2" />
        <placeholder
          name="ImageOverride"
          value="" />
        <placeholder
          name="Description"
          value="" />
        <placeholder
          name="Behaviour"
          value="Page" />
        <placeholder
          name="PagePK"
          value="b4de5a7efe5b48abb2263a975f44dc18" />
        <placeholder
          name="Url"
          value="" />
        <placeholder
          name="FormFlow"
          value="" />
      </control>
      <control
        code="TIL"
        id="79405de5-139b-461f-b604-82246ff32177"
        left="2"
        top="2"
        width="2"
        height="2">
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="Size"
          value="Standard" />
        <placeholder
          name="Color"
          value="Default" />
        <placeholder
          name="DisplayMode"
          value="TextAndIcon" />
        <placeholder
          name="Text"
          value="Load Lists"
          resid="b919aba9-0b60-43b2-b089-62878b0dd1f1" />
        <placeholder
          name="Image"
          value="2da381a135ff46e2a741d7fde6cbdef0" />
        <placeholder
          name="ImageOverride"
          value="" />
        <placeholder
          name="Description"
          value="" />
        <placeholder
          name="Behaviour"
          value="Page" />
        <placeholder
          name="PagePK"
          value="03d9b1e6875d4f968ed81f98579e3ed5" />
        <placeholder
          name="Url"
          value="" />
        <placeholder
          name="FormFlow"
          value="" />
      </control>
      <control
        code="TIL"
        id="1439fe9e-1f36-4faf-a8e1-752cd9ac8963"
        left="4"
        top="2"
        width="2"
        height="2">
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="Size"
          value="Standard" />
        <placeholder
          name="Color"
          value="Default" />
        <placeholder
          name="DisplayMode"
          value="TextAndIcon" />
        <placeholder
          name="Text"
          value="Reports"
          resid="a4d70d83-fa80-4320-b135-e653e9de62d5" />
        <placeholder
          name="Image"
          value="fbcf0fdf52074ddf97ec2ce94d6f47a1" />
        <placeholder
          name="ImageOverride"
          value="" />
        <placeholder
          name="Description"
          value="" />
        <placeholder
          name="Behaviour"
          value="Page" />
        <placeholder
          name="PagePK"
          value="382ebbbe424f43d1bda67e2a5df611ef" />
        <placeholder
          name="Url"
          value="" />
        <placeholder
          name="FormFlow"
          value="" />
      </control>
    </control>
    <control
      code="SRL"
      id="f6a4566e-e90b-4d21-bcc9-5c59869ee3ed"
      left="7"
      top="0"
      right="0"
      bottom="0">
      <placeholder
        name="CanBeHidden"
        value="True" />
      <placeholder
        name="IsInitialFocus"
        value="False" />
      <placeholder
        name="CaptionOverride"
        value="Outer Packages"
        resid="d9e8e9d9-e0f8-4493-98a5-7b4dd5decbea" />
      <placeholder
        name="VisibilityCondition"
        value="" />
      <placeholder
        name="HeaderAlignment"
        value="Left" />
      <placeholder
        name="HeaderDisplayMode"
        value="Visible" />
      <placeholder
        name="FilterType"
        value="Entity" />
      <placeholder
        name="EntityType"
        value="IHVLVOuterPackage" />
      <placeholder
        name="ItemDisplayPath"
        value="" />
      <placeholder
        name="DisabledGridRowActions"
        value="" />
      <placeholder
        name="DefaultFilter">
        <xml>
          <ArrayOfFilterGroup xmlns:a="http://schemas.microsoft.com/2003/10/Serialization/Arrays" xmlns:i="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://cargowise.com/glow/2014/07/16/filters.xsd">
            <FilterGroup>
              <Filters>
                <Filter>
                  <FilterType>StringFilter</FilterType>
                  <Operation>StartsWith</Operation>
                  <PropertyPath>HVO_PackageBarcode</PropertyPath>
                  <Values />
                </Filter>
                <Filter>
                  <FilterType>StringFilter</FilterType>
                  <Operation>StartsWith</Operation>
                  <PropertyPath>HVO_PackageReference</PropertyPath>
                  <Values />
                </Filter>
                <Filter>
                  <FilterType>StringFilter</FilterType>
                  <Operation>StartsWith</Operation>
                  <PropertyPath>HVLVItems.Consignment.HVC_WaybillNumber</PropertyPath>
                  <Values />
                </Filter>
                <Filter>
                  <FilterType>StringFilter</FilterType>
                  <Operation>StartsWith</Operation>
                  <PropertyPath>HVLVItems.Consignment.HVLVItems.HVI_CurrentBarcode</PropertyPath>
                  <Values />
                </Filter>
              </Filters>
              <IsImplicit>false</IsImplicit>
            </FilterGroup>
          </ArrayOfFilterGroup>
        </xml>
      </placeholder>
      <placeholder
        name="DefaultFilterPropertyPath"
        value="" />
      <placeholder
        name="FieldConfiguration">
        <xml>
          <fields xmlns="">
            <field
              path="HVO_PackageBarcode"
              width="250"
              mode="Default" />
            <field
              path="HVO_PackageReference"
              width="250"
              mode="Default" />
            <field
              path="HVO_F3_NKPackageType"
              width="250"
              mode="Default" />
            <field
              path="HVO_Status"
              width="250"
              mode="Default" />
            <field
              path="HVO_OH_LastMileCarrier"
              width="250"
              mode="Default" />
            <field
              path="HVO_HVL_LoadList"
              width="250"
              mode="Default" />
            <field
              path="CountAll"
              width="100"
              mode="Default" />
            <field
              path="LoadList.HVL_MasterBillNumber"
              width="200"
              mode="Default" />
            <field
              path="LoadedOnConsol.JK_UniqueConsignRef"
              width="200"
              mode="Default" />
            <field
              path="LoadList.HVL_E_Dep"
              width="160"
              mode="Default" />
            <field
              path="LoadList.HVL_E_Arv"
              width="160"
              mode="Default" />
            <field
              path="LoadList.HVL_TransportMode"
              width="200"
              mode="Default" />
            <field
              path="LoadList.HVL_OH_Carrier"
              width="250"
              mode="Default" />
            <field
              path="LoadList.HVL_VoyageFlight"
              width="200"
              mode="Default" />
            <field
              path="LoadList.HVL_VesselName"
              width="200"
              mode="Default" />
            <field
              path="HVO_ContainerNumber"
              width="200"
              mode="Default" />
            <field
              path="LoadList.HVL_OA_OriginDepot"
              width="250"
              mode="Default" />
            <field
              path="LoadList.HVL_OA_DestinationDepot"
              width="250"
              mode="Default" />
            <field
              path="HVO_SystemCreateTimeUtc"
              width="180"
              mode="Optional" />
            <field
              path="HVO_SystemLastEditTimeUtc"
              width="180"
              mode="Optional" />
            <field
              path="CountClear"
              width="110"
              mode="Optional" />
            <field
              path="CountHeld"
              width="100"
              mode="Optional" />
            <field
              path="CountNonReported"
              width="180"
              mode="Optional" />
            <field
              path="HVO_Weight"
              width="100"
              mode="Default" />
            <field
              path="HVO_WeightUQ"
              width="90"
              mode="Default" />
            <field
              path="HVO_Volume"
              width="100"
              mode="Default" />
            <field
              path="HVO_VolumeUQ"
              width="90"
              mode="Default" />
            <field
              path="HVO_Length"
              width="100"
              mode="Default" />
            <field
              path="HVO_Width"
              width="100"
              mode="Default" />
            <field
              path="HVO_Height"
              width="100"
              mode="Default" />
            <field
              path="HVO_UnitOfDimension"
              width="170"
              mode="Default" />
            <field
              path="HVO_PL_NKLastMileCarrierServiceLevel"
              width="250"
              mode="Default" />
            <field
              path="HVO_RH_NKCommodityCode"
              width="350"
              mode="Default" />
          </fields>
        </xml>
      </placeholder>
      <placeholder
        name="DefaultSortFields"
        value="" />
      <placeholder
        name="DefaultGroupFields"
        value="" />
      <placeholder
        name="EnableGrouping"
        value="False" />
      <placeholder
        name="AutoRefreshInterval"
        value="0" />
      <placeholder
        name="AllowMultiSelect"
        value="True" />
      <placeholder
        name="ShowWorkflowLabel"
        value="True" />
      <placeholder
        name="RowActionsMode"
        value="RowAndMenu" />
      <placeholder
        name="HideGridActions"
        value="False" />
      <placeholder
        name="MinimizeFilters"
        value="False" />
      <placeholder
        name="WorkflowOpenInNewWindow"
        value="False" />
      <placeholder
        name="SearchOnInit"
        value="True" />
      <placeholder
        name="ShowQuickSearch"
        value="False" />
      <placeholder
        name="QuickSearchCaption"
        value="" />
      <placeholder
        name="ValueField"
        value="" />
      <placeholder
        name="Category"
        value="" />
      <placeholder
        name="ActionMenuItems"
        value="" />
      <placeholder
        name="EditFormFlow"
        value="" />
      <placeholder
        name="ResetFilterOnInit"
        value="False" />
      <placeholder
        name="FilterDisplayMode"
        value="InlineAndPopup" />
    </control>
    <control
      code="GRP"
      id="83d6b0f8-eb72-4611-a27f-e6e71d4da8f7"
      left="0"
      top="4"
      width="6"
      height="5">
      <placeholder
        name="CanBeHidden"
        value="True" />
      <placeholder
        name="VisibilityCondition"
        value="" />
      <placeholder
        name="Header"
        value="Tracking"
        resid="fb3b7fce-b9de-4c4e-9b0f-fc3c2836ed3e" />
      <placeholder
        name="HeaderAlignment"
        value="Left" />
      <placeholder
        name="HeaderDisplayMode"
        value="Visible" />
      <placeholder
        name="BackgroundImagePk"
        value="" />
      <placeholder
        name="BackgroundImageOverride"
        value="" />
      <control
        code="TRC"
        id="dfcd740c-8e7f-4a6e-884c-8dd5e4e8df32"
        left="0"
        top="0"
        width="6"
        height="4">
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="VisibilityCondition"
          value="" />
      </control>
    </control>
    <control
      code="FAV"
      id="7d132d0a-d2d4-4fd3-ab8c-a114cad89004"
      left="0"
      top="9"
      width="6"
      bottom="2">
      <placeholder
        name="CanBeHidden"
        value="True" />
      <placeholder
        name="VisibilityCondition"
        value="" />
      <placeholder
        name="EntityType"
        value="" />
      <placeholder
        name="HeaderDisplayMode"
        value="Visible" />
      <placeholder
        name="ShowFavorites"
        value="True" />
      <placeholder
        name="ShowRecents"
        value="True" />
    </control>
  </form>
