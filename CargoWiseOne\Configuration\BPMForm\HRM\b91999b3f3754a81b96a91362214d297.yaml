#transformationVersion: 70.0
#
VZ_PK: b91999b3f3754a81b96a91362214d297
VZ_ConfigurationKey: b91999b3-f375-4a81-b96a-91362214d297
VZ_FormID: HRM - Hiring Requests
VZ_Caption:
  resKey: VZ_Caption|b91999b3-f375-4a81-b96a-91362214d297
  text: Hiring Requests
VZ_FormFactor: DSK
VZ_FormType: PAG
VZ_RequiresLogin: true
VZ_MaterialDesign: true
VZ_Dependencies: <dependencies xmlns="http://cargowise.com/glow/2014/11/14/dependencies.xsd" />
VZ_FormData: >-
  <form
    id="09cee540-aa26-4801-94fd-dee58d497771" xmlns="http://wisetechglobal.com/glow/2017/05/26/form.xsd">
    <placeholder
      name="Layout"
      value="fill" />
    <control
      code="SDT"
      id="4a629a92-9c83-4a9c-abd0-77bb9a22a654"
      binding="">
      <placeholder
        name="CaptionOverride"
        value="Hiring Requests"
        resid="fce33396-8ff0-405c-a948-9fff8ef9b36e" />
      <placeholder
        name="SearchMode"
        value="Entity" />
      <placeholder
        name="EntityType"
        value="IHRHiringRequest" />
      <placeholder
        name="DefaultFilter">
        <xml>
          <ArrayOfFilterGroup xmlns:a="http://schemas.microsoft.com/2003/10/Serialization/Arrays" xmlns:i="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://cargowise.com/glow/2014/07/16/filters.xsd">
            <FilterGroup>
              <Filters>
                <Filter>
                  <FilterType>SimpleLookupFilter</FilterType>
                  <Operation>Is</Operation>
                  <PropertyPath>HRR_Status</PropertyPath>
                  <Values>
                    <a:string>PEN</a:string>
                  </Values>
                </Filter>
              </Filters>
              <IsImplicit>false</IsImplicit>
            </FilterGroup>
          </ArrayOfFilterGroup>
        </xml>
      </placeholder>
      <placeholder
        name="FieldConfiguration">
        <xml>
          <fields xmlns="">
            <field
              path="HRR_HA_JobApplicant"
              width="200"
              mode="Mandatory" />
            <field
              path="HRR_Status"
              width="200"
              mode="Mandatory" />
            <field
              path="HRR_JobTitle"
              width="250"
              mode="Default" />
            <field
              path="HRR_WorkingBasis"
              width="200"
              mode="Default" />
            <field
              path="HRR_WorkLocationCity"
              width="150"
              mode="Optional" />
            <field
              path="HRR_JobFamily"
              width="150"
              mode="Optional" />
            <field
              path="HRR_SystemCreateTimeUtc"
              width="130"
              mode="Optional" />
            <field
              path="HRR_SystemLastEditTimeUtc"
              width="130"
              mode="Optional" />
            <field
              path="ReportingManager.GS_Code"
              width="120"
              mode="Optional" />
            <field
              path="ReportingManager.GS_FullName"
              width="250"
              mode="Default" />
            <field
              path="Team.GST_Code"
              width="120"
              mode="Optional" />
            <field
              path="Team.GST_Name"
              width="250"
              mode="Optional" />
            <field
              path="WorkLocationCountry.RN_Desc"
              width="250"
              mode="Optional" />
            <field
              path="CreatedByStaff.GS_Code"
              width="120"
              mode="Optional" />
            <field
              path="CreatedByStaff.GS_FullName"
              width="250"
              mode="Optional" />
            <field
              path="LastEditedByStaff.GS_Code"
              width="120"
              mode="Optional" />
            <field
              path="LastEditedByStaff.GS_FullName"
              width="250"
              mode="Default" />
            <field
              path="HRR_StartDate"
              width="130"
              mode="Default" />
            <field
              path="HRR_GS_NKReportingManager"
              width="120"
              mode="FilterOnly" />
            <field
              path="HRR_GST_NKTeam"
              width="150"
              mode="FilterOnly" />
            <field
              path="HRR_RN_NKWorkLocationCountry"
              width="150"
              mode="FilterOnly" />
            <field
              path="HRR_EndDate"
              width="130"
              mode="Optional" />
            <field
              path="HRR_SystemCreateUser"
              width="120"
              mode="FilterOnly" />
            <field
              path="HRR_SystemLastEditUser"
              width="120"
              mode="FilterOnly" />
            <field
              path="WorkflowTasks"
              width="140"
              mode="FilterOnly" />
          </fields>
        </xml>
      </placeholder>
      <placeholder
        name="FullPage"
        value="True" />
      <placeholder
        name="EditFormFlowConfiguration">
        <xml>
          <formFlows xmlns="">
            <formFlow>b9354e9bc87f4fde811648c2545c0a30</formFlow>
          </formFlows>
        </xml>
      </placeholder>
    </control>
  </form>
