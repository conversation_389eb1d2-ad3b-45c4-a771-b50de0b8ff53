#transformationVersion: 70.0
#
VZ_PK: f72fe25dc6f84e64912dfab98db9b130
VZ_ConfigurationKey: f72fe25d-c6f8-4e64-912d-fab98db9b130
VZ_FormID: Quick Do Sub Form-flow Page
VZ_Caption:
  resKey: VZ_Caption|f72fe25dc6f84e64912dfab98db9b130
  text: Quick Do Sub Form-flow Page
VZ_FormFactor: DSK
VZ_FormType: PAG
VZ_RequiresLogin: true
VZ_MaterialDesign: true
VZ_Dependencies: <dependencies xmlns="http://cargowise.com/glow/2014/11/14/dependencies.xsd" />
VZ_FormData: >-
  <form
    id="bf55b78b-6ad4-4db1-ac32-61a68327699a" xmlns="http://wisetechglobal.com/glow/2017/05/26/form.xsd">
    <placeholder
      name="FitToHeight"
      value="True" />
    <placeholder
      name="Layout"
      value="fill" />
    <control
      code="PNL"
      id="aa13b003-652b-486c-aa0a-9aacc8ca1fc7"
      binding="">
      <placeholder
        name="Margin"
        value="mb-3" />
      <placeholder
        name="Class"
        value="blue lighten-4 bg-blue-lighten-4" />
      <control
        code="BOX"
        id="7c25f33c-fbfa-42a7-9bd8-7b0faa0e52c5">
        <placeholder
          name="Layout"
          value="flex" />
        <control
          code="IBT"
          id="0edd015c-b109-43b4-8521-70c9006f0e87"
          binding="">
          <placeholder
            name="Icon"
            value="s-icon-documentation" />
          <placeholder
            name="Tooltip"
            value="Documentation"
            resid="8dc791d1-1d2d-4e9c-a429-7969ca4a785f" />
          <placeholder
            name="Target"
            value="_blank" />
          <placeholder
            name="Hyperlink"
            value="https://wisetechglobal.sharepoint.com/sites/Content-as-Code/Shared%20Documents/GLOW-Content/_permalinks/PB-QuickDoSubFormFlowActivity.aspx" />
        </control>
        <control
          code="IBT"
          id="cd2014ed-9849-496c-a17e-7ef540d3edc6"
          binding="">
          <placeholder
            name="Icon"
            value="s-icon-pt-devtools" />
          <placeholder
            name="Margin"
            value="ml-2" />
          <placeholder
            name="Target"
            value="_blank" />
          <placeholder
            name="Tooltip"
            value="YAML"
            resid="fd431a3e-1b7f-42e0-91a9-bd549eeef06b" />
          <placeholder
            name="Hyperlink"
            value="https://github.com/WiseTechGlobal/Glow/blob/master/CargoWiseOne/Configuration/BPMFormFlow/Internal/Library/5e5471fdbd5341348bca72c8cebf39b7.yaml" />
        </control>
        <control
          code="IBT"
          id="42277ad1-4670-44a3-abcb-871d5ae74b97"
          binding="">
          <placeholder
            name="Icon"
            value="s-icon-settings" />
          <placeholder
            name="Margin"
            value="ml-2" />
          <placeholder
            name="Tooltip"
            value="Platform Builder"
            resid="899e9009-3edb-438c-bd9b-22c143e200f7" />
          <placeholder
            name="Target"
            value="_blank" />
          <placeholder
            name="Hyperlink"
            value="platformbuilder:?target=BPMFormFlow&amp;identifier=5e5471fdbd5341348bca72c8cebf39b7" />
        </control>
        <control
          code="BOX"
          id="ab662a10-26ad-4bf7-b242-41e5fb1e50b1">
          <placeholder
            name="Margin"
            value="ml-3" />
          <placeholder
            name="Layout"
            value="flex" />
          <placeholder
            name="FlexDirection"
            value="flex-column" />
          <placeholder
            name="FlexJustify"
            value="justify-center" />
          <placeholder
            name="FlexAlign"
            value="align-center" />
          <control
            code="LBL"
            id="20fa8069-65fa-4690-b763-b92e9d9c5718"
            binding="">
            <placeholder
              name="Display"
              value="block" />
            <placeholder
              name="Caption"
              value="To have a hands on experience of the example within the Documentation run the Action Menu Item called &quot;Quick Do Sub Form-flow Example&quot;."
              resid="6c2203b5-8c17-40d6-b350-6bf2cb048d27" />
            <placeholder
              name="Typography"
              value="body-strong" />
          </control>
        </control>
      </control>
    </control>
    <control
      code="PNL"
      id="bf89367e-0e3e-4b63-b49a-1869ffe575bd"
      binding="">
      <placeholder
        name="Layout"
        value="fill" />
      <placeholder
        name="FillAvailable"
        value="True" />
      <control
        code="SDT"
        id="db531709-667d-47e2-aa34-65a12615a073"
        binding="">
        <placeholder
          name="EntityType"
          value="IDtbLandTransportConsignment" />
        <placeholder
          name="CaptionOverride"
          value="List to explain the usage of the &quot;Quick Do Sub Form-flow&quot; activity"
          resid="1430c62e-e44c-4851-b83c-79dcd7f3b353" />
        <placeholder
          name="HideActions"
          value="True" />
        <placeholder
          name="HideFilters"
          value="True" />
        <placeholder
          name="ActionMenuItems">
          <xml>
            <formFlows xmlns="">
              <formFlow>5e5471fdbd5341348bca72c8cebf39b7</formFlow>
            </formFlows>
          </xml>
        </placeholder>
        <placeholder
          name="FieldConfiguration">
          <xml>
            <fields xmlns="">
              <field
                path="LTC_JobID"
                width="150"
                mode="Default" />
              <field
                path="LTC_Status"
                width="60"
                mode="Default" />
              <field
                path="LTC_RS_NKServiceLevel"
                width="100"
                mode="Default" />
              <field
                path="Branch.GB_Code"
                width="100"
                mode="Default" />
              <field
                path="LTC_ConnoteNumber"
                width="150"
                mode="Default" />
              <field
                path="CustomerReferenceNumber"
                width="300"
                mode="Default" />
              <field
                path="PackageJob.PkgPackagesSumPackageQty"
                width="50"
                mode="Default" />
              <field
                path="PackageJob.PkgPackagesSumVolume"
                width="50"
                mode="Default" />
              <field
                path="PackageJob.PkgPackagesSumWeight"
                width="50"
                mode="Default" />
              <field
                path="LTC_IsActive"
                width="90"
                mode="Optional" />
              <field
                path="LTC_SystemCreateUser"
                width="100"
                mode="Optional" />
              <field
                path="LTC_SystemCreateTimeUtc"
                width="120"
                mode="Optional" />
              <field
                path="LTC_SystemLastEditTimeUtc"
                width="120"
                mode="Optional" />
              <field
                path="LTC_SystemLastEditUser"
                width="200"
                mode="Optional" />
              <field
                path="LTC_JobType"
                width="250"
                mode="Optional" />
              <field
                path="LTC_IsDirect"
                width="90"
                mode="Optional" />
              <field
                path="LTC_IsHeld"
                width="70"
                mode="Optional" />
              <field
                path="LTC_IsConfirmed"
                width="120"
                mode="Optional" />
              <field
                path="IsHazardous"
                width="70"
                mode="Optional" />
              <field
                path="RequiresRefrigeration"
                width="80"
                mode="Optional" />
              <field
                path="LTC_GoodsValue"
                width="200"
                mode="Optional" />
              <field
                path="LTC_InsuranceValue"
                width="200"
                mode="Optional" />
            </fields>
          </xml>
        </placeholder>
        <placeholder
          name="FitToHeight"
          value="True" />
      </control>
    </control>
  </form>
