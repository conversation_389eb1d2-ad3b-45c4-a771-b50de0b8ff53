#transformationVersion: 70.0
#
VZ_PK: 357173eb892c4f6894ee5a59efedb80a
VZ_ConfigurationKey: 357173eb-892c-4f68-94ee-5a59efedb80a
VZ_FormID: OPP - Select Matching Organization
VZ_Caption:
  resKey: VZ_Caption|357173eb892c4f6894ee5a59efedb80a
  text: Matching organization
VZ_FormFactor: DSK
VZ_EntityType: IOrgHeader
VZ_FormType: FRM
VZ_RequiresLogin: true
VZ_MaterialDesign: true
VZ_ExtenderFunc: CargoWise.Glow.Business.CustomerRelationshipManagement.Opportunity.NewTemporaryOrganisationExtension.ExtendSelectMatchingOrganizationForm
VZ_Dependencies: <dependencies xmlns="http://cargowise.com/glow/2014/11/14/dependencies.xsd" />
VZ_FormData: >-
  <form
    id="ef67be76-9473-4ec6-9dca-610931b923be" xmlns="http://wisetechglobal.com/glow/2017/05/26/form.xsd">
    <placeholder
      name="Layout"
      value="flex" />
    <placeholder
      name="FlexDirection"
      value="flex-column-reverse" />
    <placeholder
      name="FlexJustify"
      value="justify-space-between" />
    <control
      code="PNL"
      id="881078a3-6631-498b-a5dc-141af52703f3">
      <placeholder
        name="Caption"
        value="Caption"
        resid="4b0ebf86-7cb6-4859-bac8-8c4ccbf8f03b" />
      <control
        code="BOX"
        id="84b44c00-6c40-4753-8146-1d4c2b2d0a4b">
        <placeholder
          name="Layout"
          value="grid" />
        <control
          code="TXT"
          id="dcb2ce69-802e-4133-8b7a-2d447267820c"
          binding="OH_FullName">
          <placeholder
            name="CaptionOverride"
            value="Name"
            resid="0de2f519-4745-4746-89eb-c1895da8404a" />
          <placeholder
            name="VisibilityCondition"
            value="TemporaryOrganizationMandatoryFields.Contains(&quot;Name&quot;) || TemporaryOrganizationOptionalFields.Contains(&quot;Name&quot;)" />
          <placeholder
            name="IsReadOnly"
            value="True" />
        </control>
        <control
          code="TXT"
          id="98b97e93-aa82-454c-9268-2d028c6fc55d"
          binding="OrgAddresses/OA_Address1">
          <placeholder
            name="VisibilityCondition"
            value="TemporaryOrganizationMandatoryFields.Contains(&quot;Address1&quot;) || TemporaryOrganizationOptionalFields.Contains(&quot;Address1&quot;)" />
          <placeholder
            name="IsReadOnly"
            value="True" />
          <placeholder
            name="Columns"
            value="col-md-6 col-lg-6 col-xl-6" />
        </control>
        <control
          code="TXT"
          id="b9684b66-8036-4d0e-b012-da76eca6191c"
          binding="OrgAddresses/OA_Address2">
          <placeholder
            name="VisibilityCondition"
            value="TemporaryOrganizationMandatoryFields.Contains(&quot;Address2&quot;) || TemporaryOrganizationOptionalFields.Contains(&quot;Address2&quot;)" />
          <placeholder
            name="IsReadOnly"
            value="True" />
          <placeholder
            name="Columns"
            value="col-md-6 col-lg-6 col-xl-6" />
        </control>
        <control
          code="SRC"
          id="32751ddd-3659-46bd-b23d-95b75ced5ed0"
          binding="OrgAddresses/OA_RN_NKCountryCode">
          <placeholder
            name="VisibilityCondition"
            value="TemporaryOrganizationMandatoryFields.Contains(&quot;Country&quot;) || TemporaryOrganizationOptionalFields.Contains(&quot;Country&quot;)" />
          <placeholder
            name="IsReadOnly"
            value="True" />
          <placeholder
            name="Columns"
            value="col-md-6 col-lg-6 col-xl-6" />
        </control>
        <control
          code="TXT"
          id="0c3f8cc9-5915-4826-8698-528cc6be02f6"
          binding="OrgAddresses/OA_City">
          <placeholder
            name="VisibilityCondition"
            value="TemporaryOrganizationMandatoryFields.Contains(&quot;City&quot;) || TemporaryOrganizationOptionalFields.Contains(&quot;City&quot;)" />
          <placeholder
            name="IsReadOnly"
            value="True" />
          <placeholder
            name="Columns"
            value="col-md-6 col-lg-6 col-xl-6" />
        </control>
        <control
          code="SRC"
          id="8dfb13be-0978-485b-ae95-1ef308a28480"
          binding="OrgAddresses/OA_State">
          <placeholder
            name="VisibilityCondition"
            value="TemporaryOrganizationMandatoryFields.Contains(&quot;State&quot;) || TemporaryOrganizationOptionalFields.Contains(&quot;State&quot;)" />
          <placeholder
            name="IsReadOnly"
            value="True" />
          <placeholder
            name="Columns"
            value="col-md-6 col-lg-6 col-xl-6" />
        </control>
        <control
          code="SRC"
          id="b85fc0f1-14b2-490d-9fbc-56244dfac663"
          binding="OrgAddresses/OA_PostCode">
          <placeholder
            name="VisibilityCondition"
            value="TemporaryOrganizationMandatoryFields.Contains(&quot;Postcode&quot;) || TemporaryOrganizationOptionalFields.Contains(&quot;Postcode&quot;)" />
          <placeholder
            name="CaptionOverride"
            value="Postcode"
            resid="b6d6b850-4126-48d7-93f2-c323dcfb1772" />
          <placeholder
            name="IsReadOnly"
            value="True" />
          <placeholder
            name="Columns"
            value="col-md-6 col-lg-6 col-xl-6" />
        </control>
        <control
          code="SRC"
          id="fbb59e5b-70f4-4946-871f-ef33a57ae59e"
          binding="OH_RL_NKClosestPort">
          <placeholder
            name="VisibilityCondition"
            value="TemporaryOrganizationMandatoryFields.Contains(&quot;UNLOCO&quot;) || TemporaryOrganizationOptionalFields.Contains(&quot;UNLOCO&quot;)" />
          <placeholder
            name="IsReadOnly"
            value="True" />
          <placeholder
            name="Columns"
            value="col-md-6 col-lg-6 col-xl-6" />
        </control>
        <control
          code="BOX"
          id="bc17cfda-5846-4e81-9dad-d822e75b03f4">
          <placeholder
            name="Layout"
            value="grid" />
          <control
            code="SRC"
            id="d74fd9d0-ae53-4fe0-b2ce-698dc2c6f5c6"
            binding="OrgCompanyData/OB_GB_ControllingBranch">
            <placeholder
              name="CaptionOverride"
              value="Controlling branch"
              resid="3ccc94de-3f9a-44c4-8faf-fc90e86f0b0a" />
            <placeholder
              name="VisibilityCondition"
              value="TemporaryOrganizationMandatoryFields.Contains(&quot;Branch&quot;) || TemporaryOrganizationOptionalFields.Contains(&quot;Branch&quot;)" />
            <placeholder
              name="IsReadOnly"
              value="True" />
            <placeholder
              name="Columns"
              value="col-md-6 col-lg-6 col-xl-6" />
          </control>
        </control>
        <control
          code="TXT"
          id="9aebd569-494a-4f83-ac9c-70e86ba3b71d"
          binding="OrgAddresses/OA_Phone">
          <placeholder
            name="VisibilityCondition"
            value="TemporaryOrganizationMandatoryFields.Contains(&quot;Phone&quot;) || TemporaryOrganizationOptionalFields.Contains(&quot;Phone&quot;)" />
          <placeholder
            name="IsReadOnly"
            value="True" />
          <placeholder
            name="Columns"
            value="col-md-6 col-lg-6 col-xl-6" />
        </control>
        <control
          code="TXT"
          id="d1b7067e-5ee2-4b30-a7ae-3f8b621d2e53"
          binding="OrgAddresses/OA_Mobile">
          <placeholder
            name="VisibilityCondition"
            value="TemporaryOrganizationMandatoryFields.Contains(&quot;Mobile&quot;) || TemporaryOrganizationOptionalFields.Contains(&quot;Mobile&quot;)" />
          <placeholder
            name="IsReadOnly"
            value="True" />
          <placeholder
            name="Columns"
            value="col-md-6 col-lg-6 col-xl-6" />
        </control>
        <control
          code="TXT"
          id="a19329c0-7706-4197-bbcd-291ed0e75691"
          binding="OrgAddresses/OA_Email">
          <placeholder
            name="VisibilityCondition"
            value="TemporaryOrganizationMandatoryFields.Contains(&quot;Email&quot;) || TemporaryOrganizationOptionalFields.Contains(&quot;Email&quot;)" />
          <placeholder
            name="IsReadOnly"
            value="True" />
          <placeholder
            name="Columns"
            value="col-md-6 col-lg-6 col-xl-6" />
        </control>
        <control
          code="TXT"
          id="0ffae15f-f6c2-4edd-9ef0-a4fc45224c1d"
          binding="OrgAddresses/OA_Fax">
          <placeholder
            name="VisibilityCondition"
            value="TemporaryOrganizationMandatoryFields.Contains(&quot;Fax&quot;) || TemporaryOrganizationOptionalFields.Contains(&quot;Fax&quot;)" />
          <placeholder
            name="IsReadOnly"
            value="True" />
          <placeholder
            name="Columns"
            value="col-md-6 col-lg-6 col-xl-6" />
        </control>
        <control
          code="TXT"
          id="a3cf4c01-765f-49d9-8dec-4f486d88d673"
          binding="OrgWebURLs/PU_URL">
          <placeholder
            name="VisibilityCondition"
            value="TemporaryOrganizationMandatoryFields.Contains(&quot;Web&quot;) || TemporaryOrganizationOptionalFields.Contains(&quot;Web&quot;)" />
          <placeholder
            name="IsReadOnly"
            value="True" />
          <placeholder
            name="Columns"
            value="col-md-6 col-lg-6 col-xl-6" />
        </control>
      </control>
      <control
        code="BOX"
        id="e7bb5c0d-b956-4939-977f-b320096a2c58">
        <control
          code="RDT"
          id="b619adb5-72e7-4ec7-906e-579f90213384"
          binding="PotentialDuplicates">
          <placeholder
            name="CaptionOverride"
            value="Matching organization"
            resid="f1736393-5519-4824-b03d-b04ccb00a2ae" />
          <placeholder
            name="ShowToolbar"
            value="small" />
          <placeholder
            name="SingleSelect"
            value="True" />
          <placeholder
            name="ShowSelect"
            value="True" />
          <placeholder
            name="HideItemActions"
            value="True" />
          <placeholder
            name="HideDefaultFooter"
            value="True" />
          <placeholder
            name="FieldConfiguration">
            <xml>
              <fields xmlns="">
                <field
                  path="MatchingScore"
                  width="80"
                  mode="Default" />
                <field
                  path="OH_Code"
                  width="80"
                  mode="Default" />
                <field
                  path="OH_FullName"
                  width="200"
                  mode="Default" />
                <field
                  path="MatchingBrandName"
                  width="200"
                  mode="Default" />
                <field
                  path="ClosestPort.RL_Code"
                  width="80"
                  mode="Default" />
                <field
                  path="ClosestPort.RL_PortName"
                  width="80"
                  mode="Default" />
                <field
                  path="MatchingAddress1"
                  width="300"
                  mode="Default" />
                <field
                  path="MatchingAddress2"
                  width="300"
                  mode="Default" />
                <field
                  path="MatchingCity"
                  width="80"
                  mode="Default" />
                <field
                  path="MatchingState"
                  width="80"
                  mode="Default" />
                <field
                  path="MatchingCountryRegion"
                  width="80"
                  mode="Default" />
                <field
                  path="EHubID"
                  width="250"
                  mode="Optional" />
                <field
                  path="OH_Language"
                  width="80"
                  mode="Optional" />
                <field
                  path="OH_Category"
                  width="100"
                  mode="Optional" />
                <field
                  path="OH_ScreeningStatus"
                  width="100"
                  mode="Optional" />
                <field
                  path="OH_IsActive"
                  width="130"
                  mode="Optional" />
                <field
                  path="OH_IsBroker"
                  width="70"
                  mode="Optional" />
                <field
                  path="OH_IsShippingProvider"
                  width="70"
                  mode="Optional" />
                <field
                  path="OH_IsCompetitor"
                  width="100"
                  mode="Optional" />
                <field
                  path="OH_IsConsignee"
                  width="90"
                  mode="Optional" />
                <field
                  path="OH_IsConsignor"
                  width="90"
                  mode="Optional" />
                <field
                  path="OH_IsControllingAgent"
                  width="170"
                  mode="Optional" />
                <field
                  path="OH_IsControllingCustomer"
                  width="200"
                  mode="Optional" />
                <field
                  path="OH_IsForwarder"
                  width="150"
                  mode="Optional" />
                <field
                  path="OH_IsGlobalAccount"
                  width="150"
                  mode="Optional" />
                <field
                  path="OH_IsAirCTO"
                  width="100"
                  mode="Optional" />
                <field
                  path="OH_IsAirLine"
                  width="110"
                  mode="Optional" />
                <field
                  path="OH_IsAirWholesaler"
                  width="170"
                  mode="Optional" />
                <field
                  path="IsCodeReadOnly"
                  width="170"
                  mode="Optional" />
                <field
                  path="OH_IsContainerYard"
                  width="170"
                  mode="Optional" />
                <field
                  path="OH_IsContainerLeasingCompany"
                  width="250"
                  mode="Optional" />
                <field
                  path="OH_IsDistributionCentre"
                  width="220"
                  mode="Optional" />
                <field
                  path="OH_IsFumigationContractor"
                  width="240"
                  mode="Optional" />
                <field
                  path="OH_IsLineHaulProvider"
                  width="210"
                  mode="Optional" />
                <field
                  path="OH_IsLocalTransport"
                  width="180"
                  mode="Optional" />
                <field
                  path="OH_IsPackDepot"
                  width="130"
                  mode="Optional" />
                <field
                  path="OH_IsRailProvider"
                  width="160"
                  mode="Optional" />
                <field
                  path="OH_IsSeaCTO"
                  width="100"
                  mode="Optional" />
                <field
                  path="OH_IsSeaWholesaler"
                  width="170"
                  mode="Optional" />
                <field
                  path="OH_IsShippingLine"
                  width="160"
                  mode="Optional" />
                <field
                  path="OH_IsUnpackDepot"
                  width="150"
                  mode="Optional" />
                <field
                  path="OH_IsNationalAccount"
                  width="80"
                  mode="Optional" />
                <field
                  path="OrgMiscServ"
                  width="130"
                  mode="Optional" />
                <field
                  path="OH_IsPersonalEffectsAccount"
                  width="240"
                  mode="Optional" />
                <field
                  path="OH_IsRailHead"
                  width="90"
                  mode="Optional" />
                <field
                  path="OH_IsRoadFreightDepot"
                  width="180"
                  mode="Optional" />
                <field
                  path="OH_IsSalesLead"
                  width="70"
                  mode="Optional" />
                <field
                  path="OH_IsMiscFreightServices"
                  width="80"
                  mode="Optional" />
                <field
                  path="OH_IsTempAccount"
                  width="140"
                  mode="Optional" />
                <field
                  path="OH_IsTransportClient"
                  width="160"
                  mode="Optional" />
                <field
                  path="OH_IsShippingConsortium"
                  width="170"
                  mode="Optional" />
                <field
                  path="OH_IsWarehouseClient"
                  width="90"
                  mode="Optional" />
                <field
                  path="OH_SystemCreateUser"
                  width="160"
                  mode="Optional" />
                <field
                  path="OH_SystemLastEditUser"
                  width="200"
                  mode="Optional" />
                <field
                  path="MainOfficeAddress"
                  width="190"
                  mode="Optional" />
                <field
                  path="OH_SystemCreateTimeUtc"
                  width="120"
                  mode="Optional" />
                <field
                  path="OH_SystemLastEditTimeUtc"
                  width="120"
                  mode="Optional" />
                <field
                  path="OH_IsUserFlag1"
                  width="100"
                  mode="Optional" />
                <field
                  path="OH_IsUserFlag10"
                  width="100"
                  mode="Optional" />
                <field
                  path="OH_IsUserFlag11"
                  width="100"
                  mode="Optional" />
                <field
                  path="OH_IsUserFlag12"
                  width="100"
                  mode="Optional" />
                <field
                  path="OH_IsUserFlag13"
                  width="100"
                  mode="Optional" />
                <field
                  path="OH_IsUserFlag14"
                  width="100"
                  mode="Optional" />
                <field
                  path="OH_IsUserFlag15"
                  width="100"
                  mode="Optional" />
                <field
                  path="OH_IsUserFlag16"
                  width="100"
                  mode="Optional" />
                <field
                  path="OH_IsUserFlag17"
                  width="100"
                  mode="Optional" />
                <field
                  path="OH_IsUserFlag18"
                  width="100"
                  mode="Optional" />
                <field
                  path="OH_IsUserFlag19"
                  width="100"
                  mode="Optional" />
                <field
                  path="OH_IsUserFlag2"
                  width="100"
                  mode="Optional" />
                <field
                  path="OH_IsUserFlag20"
                  width="100"
                  mode="Optional" />
                <field
                  path="OH_IsUserFlag21"
                  width="100"
                  mode="Optional" />
                <field
                  path="OH_IsUserFlag22"
                  width="100"
                  mode="Optional" />
                <field
                  path="OH_IsUserFlag23"
                  width="100"
                  mode="Optional" />
                <field
                  path="OH_IsUserFlag24"
                  width="100"
                  mode="Optional" />
                <field
                  path="OH_IsUserFlag25"
                  width="100"
                  mode="Optional" />
                <field
                  path="OH_IsUserFlag26"
                  width="100"
                  mode="Optional" />
                <field
                  path="OH_IsUserFlag27"
                  width="100"
                  mode="Optional" />
                <field
                  path="OH_IsUserFlag28"
                  width="100"
                  mode="Optional" />
                <field
                  path="OH_IsUserFlag29"
                  width="100"
                  mode="Optional" />
                <field
                  path="OH_IsUserFlag3"
                  width="100"
                  mode="Optional" />
                <field
                  path="OH_IsUserFlag30"
                  width="100"
                  mode="Optional" />
                <field
                  path="OH_IsUserFlag31"
                  width="100"
                  mode="Optional" />
                <field
                  path="OH_IsUserFlag32"
                  width="100"
                  mode="Optional" />
                <field
                  path="OH_IsUserFlag4"
                  width="100"
                  mode="Optional" />
                <field
                  path="OH_IsUserFlag5"
                  width="100"
                  mode="Optional" />
                <field
                  path="OH_IsUserFlag6"
                  width="100"
                  mode="Optional" />
                <field
                  path="OH_IsUserFlag7"
                  width="100"
                  mode="Optional" />
                <field
                  path="OH_IsUserFlag8"
                  width="100"
                  mode="Optional" />
                <field
                  path="OH_IsUserFlag9"
                  width="100"
                  mode="Optional" />
                <field
                  path="OH_RL_NKClosestPort"
                  width="100"
                  mode="Optional" />
                <field
                  path="OH_RSL_ShippingLine"
                  width="100"
                  mode="FilterOnly" />
                <field
                  path="PartiesRelatedToThisOrg"
                  width="250"
                  mode="FilterOnly" />
                <field
                  path="GlbGroupOrgLinks.GOK_GG_Group"
                  width="250"
                  mode="FilterOnly" />
              </fields>
            </xml>
          </placeholder>
          <placeholder
            name="ShowCustomize"
            value="False" />
          <placeholder
            name="DisabledGridRowActions"
            value="Documents" />
        </control>
      </control>
    </control>
  </form>
