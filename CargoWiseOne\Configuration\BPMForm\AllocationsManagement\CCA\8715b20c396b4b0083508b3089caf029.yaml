#transformationVersion: 70.0
#
VZ_PK: 8715b20c396b4b0083508b3089caf029
VZ_ConfigurationKey: 8715b20c-396b-4b00-8350-8b3089caf029
VZ_FormID: CCA - Edit Contract
VZ_Caption:
  resKey: VZ_Caption|8715b20c-396b-4b00-8350-8b3089caf029
  text: Edit Contract
VZ_FormFactor: DSK
VZ_EntityType: IRatingContract
VZ_FormType: FRM
VZ_RequiresLogin: true
VZ_MaterialDesign: true
VZ_ExtenderFunc: CargoWise.Glow.Business.Forwarding.Contracts.ContractsEditFormExtenderG2.extendContractsEditForm
VZ_Dependencies: <dependencies xmlns="http://cargowise.com/glow/2014/11/14/dependencies.xsd" />
VZ_FormData: >-
  <form
    id="bda3dd07-696d-47d7-a26f-a9474503f709" xmlns="http://wisetechglobal.com/glow/2017/05/26/form.xsd">
    <placeholder
      name="Layout"
      value="grid" />
    <placeholder
      name="Padding"
      value="pa-0" />
    <placeholder
      name="NoGutters"
      value="True" />
    <control
      code="BOX"
      id="4c7b3e81-c72f-40f3-a0f5-eea96aa0bd1b"
      binding="">
      <placeholder
        name="Layout"
        value="grid" />
      <placeholder
        name="NoGutters"
        value="True" />
      <control
        code="BOX"
        id="ff88488d-2ff8-465a-886c-87f1dee36613"
        binding="">
        <placeholder
          name="Columns"
          value="col-8" />
        <placeholder
          name="Margin"
          value="mr-1" />
        <placeholder
          name="Layout"
          value="flex" />
        <placeholder
          name="FlexDirection"
          value="flex-column" />
        <placeholder
          name="Height"
          value="100%" />
        <control
          code="PNL"
          id="2e9581b8-86f3-4e42-9fdc-7800e17d34c1"
          binding="">
          <placeholder
            name="Layout"
            value="grid" />
          <placeholder
            name="Columns"
            value="col-6" />
          <placeholder
            name="Caption"
            value="Contract Information"
            resid="250b1405-d502-48c6-8554-d7c620b7a2be" />
          <placeholder
            name="NoGutters"
            value="False" />
          <placeholder
            name="FlexGrow"
            value="flex-grow-1" />
          <placeholder
            name="FlexDirection"
            value="flex-column" />
          <control
            code="TXT"
            id="80bd17ea-eda2-4d84-a2fc-bb596eacaef7"
            binding="RCT_ContractNumber">
            <placeholder
              name="Columns"
              value="col-3" />
            <placeholder
              name="Padding"
              value="pr-1" />
          </control>
          <control
            code="TXT"
            id="c1cfdd0a-bb0e-446d-862b-c17db106405e"
            binding="RCT_TransportMode">
            <placeholder
              name="Columns"
              value="col-3" />
            <placeholder
              name="Padding"
              value="pl-1 pr-1" />
          </control>
          <control
            code="DAE"
            id="ab351b0b-4fff-4483-8bf4-f777b3127daa"
            binding="RCT_StartDate">
            <placeholder
              name="Columns"
              value="col-2" />
            <placeholder
              name="Padding"
              value="px-1" />
          </control>
          <control
            code="BOX"
            id="324d9f0f-5c67-49cd-84cc-fe4eb0d9dcbe">
            <placeholder
              name="Columns"
              value="col-4" />
            <placeholder
              name="Height"
              value="100%" />
            <placeholder
              name="Layout"
              value="flex" />
            <control
              code="CMB"
              id="2336e6f1-2bf3-4531-b8bd-017fef3eb603"
              binding="RCT_ContainerType">
              <placeholder
                name="Padding"
                value="px-1" />
              <placeholder
                name="VisibilityCondition"
                value="%.CCAIsAllocationsVisible" />
            </control>
            <control
              code="OPT"
              id="f871098c-bb1d-45e0-866f-06f98b57d879"
              binding="RCT_AllowHazardousCommodities">
              <placeholder
                name="Padding"
                value="pt-6 pl-3 pr-10" />
            </control>
          </control>
          <control
            code="TXT"
            id="37e3aee1-59e9-4f39-8b83-db7ecea2397b"
            binding="RCT_Description">
            <placeholder
              name="Columns"
              value="col-3" />
            <placeholder
              name="Padding"
              value="pr-1" />
          </control>
          <control
            code="SRC"
            id="1cdb8ac0-2e68-4fdc-ac83-c756e31b246d"
            binding="RCT_OH">
            <placeholder
              name="Columns"
              value="col-3" />
            <placeholder
              name="Padding"
              value="px-1" />
          </control>
          <control
            code="DAE"
            id="9560bfa8-a328-4b99-af4e-f15fc93d1cab"
            binding="RCT_EndDate">
            <placeholder
              name="Columns"
              value="col-2" />
            <placeholder
              name="Padding"
              value="px-1" />
          </control>
          <control
            code="SRC"
            id="b61261dc-ece6-488f-bd8a-55b96af5cff4"
            binding="RCT_GS_NKContractOwner">
            <placeholder
              name="Columns"
              value="col-4" />
            <placeholder
              name="Padding"
              value="pl-1" />
          </control>
        </control>
        <control
          code="PNL"
          id="7cabab51-f610-4667-8022-76f7520f45b4"
          binding="">
          <placeholder
            name="Caption"
            value="Total Allocation"
            resid="9a69ec16-7f69-4c19-b742-fdec57ba5fbd" />
          <placeholder
            name="Columns"
            value="col-6" />
          <placeholder
            name="Layout"
            value="grid" />
          <placeholder
            name="Dense"
            value="True" />
          <placeholder
            name="VisibilityCondition"
            value="%.CCAIsAllocationsVisible" />
          <placeholder
            name="FlexGrow"
            value="flex-grow-1" />
          <placeholder
            name="Margin"
            value="mt-1" />
          <control
            code="NUM"
            id="6e57cc70-f6f1-4dd0-ab0c-b369a5ed5cef"
            binding="QuantityCN">
            <placeholder
              name="Columns"
              value="col-2" />
          </control>
          <control
            code="NUM"
            id="f18fca2c-32be-456f-a4d4-3ae42042125f"
            binding="CapacityWithVarianceCN">
            <placeholder
              name="Columns"
              value="col-2" />
          </control>
          <control
            code="NUM"
            id="4f2efdc0-9787-48c9-8f16-35fa4d22e549"
            binding="UtilizationCN">
            <placeholder
              name="Columns"
              value="col-2" />
          </control>
          <control
            code="NUM"
            id="61569a6e-7579-4b10-983f-6db716c21ea3"
            binding="OutstandingCommittedCN">
            <placeholder
              name="Columns"
              value="col-3" />
          </control>
          <control
            code="NUM"
            id="8ac62c34-c5d1-4f2e-8654-93b9aea012b3"
            binding="OutstandingWithVarianceCN">
            <placeholder
              name="Columns"
              value="col-3" />
          </control>
          <control
            code="NUM"
            id="0f1555a8-abe2-4aee-a04f-c50c33c05b44"
            binding="QuantityTEU">
            <placeholder
              name="Columns"
              value="col-2" />
          </control>
          <control
            code="NUM"
            id="6b5e9b06-2c4e-471d-9afe-1933ba529eae"
            binding="CapacityWithVarianceTEU">
            <placeholder
              name="Columns"
              value="col-2" />
          </control>
          <control
            code="NUM"
            id="83daf34a-e947-4969-a1f2-cf4aa1baf0d1"
            binding="UtilizationTEU">
            <placeholder
              name="Columns"
              value="col-2" />
          </control>
          <control
            code="NUM"
            id="e9aad4e9-b9fb-47f3-bc22-69aed9ea2b4f"
            binding="OutstandingCommittedTEU">
            <placeholder
              name="Columns"
              value="col-3" />
          </control>
          <control
            code="NUM"
            id="5a0dd450-dd9b-417c-bb7f-890e5ce92a19"
            binding="OutstandingWithVarianceTEU">
            <placeholder
              name="Columns"
              value="col-3" />
          </control>
        </control>
      </control>
      <control
        code="PNL"
        id="9d6e9975-b196-46b9-bfe5-ba0ea79a23dc"
        binding="">
        <placeholder
          name="Columns"
          value="col-4" />
        <placeholder
          name="FitToHeight"
          value="True" />
        <placeholder
          name="Height"
          value="100%" />
        <control
          code="RDT"
          id="7f7ab01f-f9a8-471d-98b9-bd1f907a9041"
          binding="RatingContractNamedAccountPivots">
          <placeholder
            name="AllowAdd"
            value="True" />
          <placeholder
            name="InlineEdit"
            value="cell" />
          <placeholder
            name="FitToHeight"
            value="True" />
          <placeholder
            name="ShowCustomize"
            value="True" />
        </control>
      </control>
    </control>
    <control
      code="PNL"
      id="ec8c11f4-1b29-4f31-82f9-86edd2580f23"
      binding="">
      <placeholder
        name="FitToHeight"
        value="True" />
      <placeholder
        name="Margin"
        value="mt-1" />
      <control
        code="TBS"
        id="297bc14b-1f70-4ad7-bb07-b72564ee22b7"
        binding="">
        <control
          code="TAB"
          id="d67bc392-b6f5-40b5-8fc0-e8fe84e917c6"
          binding="">
          <placeholder
            name="Caption"
            value="Allocation Routes"
            resid="2b7d6009-9830-4a73-adbc-ecf915481725" />
          <placeholder
            name="VisibilityCondition"
            value="%.CCAIsAllocationsVisible" />
        </control>
        <control
          code="TAI"
          id="689148a1-ee07-4e3c-8157-2e2ab98288fd"
          binding="">
          <placeholder
            name="VisibilityCondition"
            value="%.CCAIsAllocationsVisible" />
          <control
            code="RDT"
            id="71bd3bee-fbc4-4015-9f03-64d55cc83087"
            binding="RatingContractAllocationLines">
            <placeholder
              name="InlineEdit"
              value="cell" />
            <placeholder
              name="AllowAdd"
              value="True" />
            <placeholder
              name="Height"
              value="250px" />
            <placeholder
              name="ActionMenuItems">
              <xml>
                <formFlows xmlns="">
                  <formFlow>c69e6db7d230406782908ccd2a2821f6</formFlow>
                  <formFlow>5ddfd356d12149f7aba1cc2acf4e3e0d</formFlow>
                  <formFlow>7cc4f7e8fb344629baf81ac10d136985</formFlow>
                  <formFlow>1e2d89d51ba548c0b91a695b49218c61</formFlow>
                  <formFlow>285468f550c24229ba73cdaf27e0668e</formFlow>
                </formFlows>
              </xml>
            </placeholder>
            <placeholder
              name="ShowSelect"
              value="True" />
            <placeholder
              name="ShowCustomize"
              value="True" />
            <placeholder
              name="ShowFilters"
              value="True" />
            <placeholder
              name="Padding"
              value="pt-1" />
          </control>
          <control
            code="BOX"
            id="45b62f17-d187-45fc-aa36-322c05081949"
            binding="">
            <placeholder
              name="FlexJustify"
              value="justify-end" />
            <placeholder
              name="Layout"
              value="flex" />
            <control
              code="BTN"
              id="828f20a7-03c6-4eb5-85b6-18ee872b2930"
              binding="">
              <placeholder
                name="Caption"
                value="Allocation Route Wizard"
                resid="d6c51482-e078-41c4-8460-0dfcfb748c1d" />
              <placeholder
                name="Transition"
                value="True" />
              <placeholder
                name="Style"
                value="margin-left:auto;" />
            </control>
          </control>
          <control
            code="TBS"
            id="b5246277-73bf-48c3-9cd1-9bea14de68e5">
            <control
              code="TAB"
              id="4516ceec-3fec-4a18-b3cd-a60c48a91877">
              <placeholder
                name="Caption"
                value="Consolidations &amp; Containers"
                resid="274936da-5636-4e08-9521-64c26cf88205" />
            </control>
            <control
              code="TAI"
              id="f76c9f18-906a-43b7-ae61-990a4fa4d6b7">
              <control
                code="BOX"
                id="0bf17d81-90d5-461e-b552-65cab4d595b7">
                <placeholder
                  name="Layout"
                  value="grid" />
                <control
                  code="RDT"
                  id="c064df02-583e-445b-a0d0-139f98c4a238"
                  binding="RatingContractAllocationLines/CCAJobConsolViews">
                  <placeholder
                    name="Columns"
                    value="col-6" />
                  <placeholder
                    name="Height"
                    value="250px" />
                  <placeholder
                    name="CaptionOverride"
                    value="Allocated Consolidations of Route"
                    resid="94859108-d60f-4f9d-9b78-51b00ef3e627" />
                  <placeholder
                    name="AllowAttach"
                    value="False" />
                  <placeholder
                    name="AllowDetach"
                    value="False" />
                  <placeholder
                    name="ShowCustomize"
                    value="True" />
                  <placeholder
                    name="ShowFilters"
                    value="True" />
                  <placeholder
                    name="ActionMenuItems">
                    <xml>
                      <formFlows xmlns="">
                        <formFlow>50390b8477c84011828b5fcbcea17140</formFlow>
                        <formFlow>ca8fb325c4ae4105bef672b10dc05211</formFlow>
                        <formFlow>08d10865453b442295c72da5fbfcfb23</formFlow>
                      </formFlows>
                    </xml>
                  </placeholder>
                  <placeholder
                    name="FieldConfiguration">
                    <xml>
                      <fields xmlns="">
                        <field
                          path="JobConsol.JK_UniqueConsignRef"
                          width="200"
                          mode="Mandatory" />
                        <field
                          path="JobConsol.JK_MasterBillNum"
                          width="200"
                          mode="Default" />
                        <field
                          path="JobConsol.JK_TransportMode"
                          width="200"
                          mode="Default" />
                        <field
                          path="JobConsol.TransportFirstLeg.JW_VoyageFlight"
                          width="250"
                          mode="Optional" />
                        <field
                          path="JobConsol.TransportLastLeg.JW_VoyageFlight"
                          width="250"
                          mode="Optional" />
                        <field
                          path="JobConsol.ArrivalCTOAddress.OrgHeader.OH_FullName"
                          width="250"
                          mode="Default" />
                        <field
                          path="JobConsol.DepartureCTOAddress.OrgHeader.OH_FullName"
                          width="250"
                          mode="Default" />
                        <field
                          path="JobConsol.UnpackDepotAddress.OrgHeader.OH_FullName"
                          width="250"
                          mode="Default" />
                        <field
                          path="JobConsol.ShippingLineAddress.OrgHeader.OH_FullName"
                          width="250"
                          mode="Default" />
                        <field
                          path="JobConsol.JK_IsCancelled"
                          width="120"
                          mode="Optional" />
                        <field
                          path="JobConsol.TransportFirstLeg.JW_ATD"
                          width="120"
                          mode="Default" />
                        <field
                          path="JobConsolFirstTransportLeg.FirstTransport.JW_ETD"
                          width="120"
                          mode="Default" />
                        <field
                          path="JobConsol.TransportLastLeg.JW_ATA"
                          width="120"
                          mode="Default" />
                        <field
                          path="JobConsol.TransportLastLeg.JW_ETA"
                          width="120"
                          mode="Default" />
                        <field
                          path="JobConsol.JK_RL_NKLoadPort"
                          width="80"
                          mode="Default" />
                        <field
                          path="JobConsol.JK_RL_NKDischargePort"
                          width="90"
                          mode="Default" />
                        <field
                          path="JobConsol.JK_AgentsReference"
                          width="100"
                          mode="Optional" />
                        <field
                          path="JobConsol.JK_AgentType"
                          width="100"
                          mode="Optional" />
                        <field
                          path="JobConsol.JK_AWBServiceLevel"
                          width="100"
                          mode="Optional" />
                        <field
                          path="JobConsol.JK_BookingReference"
                          width="100"
                          mode="Optional" />
                        <field
                          path="JobConsol.JK_CoLoadBookingReference"
                          width="100"
                          mode="Optional" />
                        <field
                          path="JobConsol.JK_CoLoadMasterBill"
                          width="100"
                          mode="Optional" />
                        <field
                          path="JobConsol.JK_ConsolChargeable"
                          width="100"
                          mode="Optional" />
                        <field
                          path="JobConsol.JK_ConsolChargeableRate"
                          width="100"
                          mode="Optional" />
                        <field
                          path="JobConsol.JK_ConsolCutOffDate"
                          width="100"
                          mode="Optional" />
                        <field
                          path="JobConsol.JK_ConsolMode"
                          width="100"
                          mode="Optional" />
                        <field
                          path="JobConsol.JK_ConsolStatus"
                          width="100"
                          mode="Optional" />
                        <field
                          path="JobConsol.JK_CorrectedConsolVolume"
                          width="100"
                          mode="Optional" />
                        <field
                          path="JobConsol.JK_CorrectedConsolVolumeUnit"
                          width="100"
                          mode="Optional" />
                        <field
                          path="JobConsol.JK_CorrectedConsolWeight"
                          width="100"
                          mode="Optional" />
                        <field
                          path="JobConsol.JK_CorrectedConsolWeightUnit"
                          width="100"
                          mode="Optional" />
                        <field
                          path="JobConsol.JK_CustomDate1"
                          width="100"
                          mode="Optional" />
                        <field
                          path="JobConsol.JK_CustomDate2"
                          width="100"
                          mode="Optional" />
                        <field
                          path="JobConsol.JK_CustomFlag1"
                          width="100"
                          mode="Optional" />
                        <field
                          path="JobConsol.JK_CustomFlag2"
                          width="100"
                          mode="Optional" />
                        <field
                          path="JobConsol.JK_CustomsReference"
                          width="100"
                          mode="Optional" />
                        <field
                          path="JobConsol.JK_DateFirstForeignPort"
                          width="100"
                          mode="Optional" />
                        <field
                          path="JobConsol.JK_DateLastForeignPort"
                          width="100"
                          mode="Optional" />
                        <field
                          path="JobConsol.JK_DatePortOfFirstArrival"
                          width="100"
                          mode="Optional" />
                        <field
                          path="JobConsol.JK_IsCFS"
                          width="100"
                          mode="Optional" />
                        <field
                          path="JobConsol.JK_IsForwarding"
                          width="100"
                          mode="Optional" />
                        <field
                          path="JobConsol.JK_IsHazardous"
                          width="100"
                          mode="Optional" />
                        <field
                          path="JobConsol.JK_IsNeutralMaster"
                          width="100"
                          mode="Optional" />
                        <field
                          path="JobConsol.JK_MasterBillIssueDate"
                          width="100"
                          mode="Optional" />
                        <field
                          path="JobConsol.JK_MaximumAllowablePackageHeight"
                          width="100"
                          mode="Optional" />
                        <field
                          path="JobConsol.JK_MaximumAllowablePackageLength"
                          width="100"
                          mode="Optional" />
                        <field
                          path="JobConsol.JK_MaximumAllowablePackageUnit"
                          width="100"
                          mode="Optional" />
                        <field
                          path="JobConsol.JK_MaximumAllowablePackageWidth"
                          width="100"
                          mode="Optional" />
                        <field
                          path="JobConsol.JK_MBLAWBChargesDisplay"
                          width="100"
                          mode="Optional" />
                        <field
                          path="JobConsol.JK_NoCopyBills"
                          width="100"
                          mode="Optional" />
                        <field
                          path="JobConsol.JK_NoOriginalBills"
                          width="100"
                          mode="Optional" />
                        <field
                          path="JobConsol.JK_OA_ArrivalCTOAddress"
                          width="100"
                          mode="Optional" />
                        <field
                          path="JobConsol.JK_OA_ArrivalUnpackCFSTransportAddress"
                          width="100"
                          mode="Optional" />
                        <field
                          path="JobConsol.JK_OA_CoLoadAddress"
                          width="100"
                          mode="Optional" />
                        <field
                          path="JobConsol.JK_OA_ContainerYardEmptyPickupAddress"
                          width="100"
                          mode="Optional" />
                        <field
                          path="JobConsol.JK_OA_ContainerYardEmptyReturnAddress"
                          width="100"
                          mode="Optional" />
                        <field
                          path="JobConsol.JK_OA_CreditorAddress"
                          width="100"
                          mode="Optional" />
                        <field
                          path="JobConsol.JK_OA_DepartureCTOAddress"
                          width="100"
                          mode="Optional" />
                        <field
                          path="JobConsol.JK_OA_DeparturePackCFSTransportAddress"
                          width="100"
                          mode="Optional" />
                        <field
                          path="JobConsol.JK_OA_PackDepotAddress"
                          width="100"
                          mode="Optional" />
                        <field
                          path="JobConsol.JK_OA_ReceivingForwarderAddress"
                          width="100"
                          mode="Optional" />
                        <field
                          path="JobConsol.JK_OA_SendingForwarderAddress"
                          width="100"
                          mode="Optional" />
                        <field
                          path="JobConsol.JK_OA_ShippingLineAddress"
                          width="100"
                          mode="Optional" />
                        <field
                          path="JobConsol.JK_OA_UnpackDepotAddress"
                          width="100"
                          mode="Optional" />
                        <field
                          path="JobConsol.JK_OC_ReceivingForwarderContact"
                          width="100"
                          mode="Optional" />
                        <field
                          path="JobConsol.JK_OC_SendingForwarderContact"
                          width="100"
                          mode="Optional" />
                        <field
                          path="JobConsol.JK_OH_ArrivalUnpackCFSTransport"
                          width="100"
                          mode="Optional" />
                        <field
                          path="JobConsol.JK_OH_Creditor"
                          width="100"
                          mode="Optional" />
                        <field
                          path="JobConsol.JK_OH_DeparturePackCFSTransport"
                          width="100"
                          mode="Optional" />
                        <field
                          path="JobConsol.JK_OverrideConsolChargeable"
                          width="100"
                          mode="Optional" />
                        <field
                          path="JobConsol.JK_OverrideWaybillDefaults"
                          width="100"
                          mode="Optional" />
                        <field
                          path="JobConsol.JK_PackDepotDispatchRequested"
                          width="100"
                          mode="Optional" />
                        <field
                          path="JobConsol.JK_PackDepotReceiptRequested"
                          width="100"
                          mode="Optional" />
                        <field
                          path="JobConsol.JK_Phase"
                          width="100"
                          mode="Optional" />
                        <field
                          path="JobConsol.JK_PrepaidCollect"
                          width="100"
                          mode="Optional" />
                        <field
                          path="JobConsol.JK_PrintOptionForColoadsOnManifest"
                          width="100"
                          mode="Optional" />
                        <field
                          path="JobConsol.JK_PrintOptionForColoadsOnOtherDocs"
                          width="100"
                          mode="Optional" />
                        <field
                          path="JobConsol.JK_PrintOptionForPackagesOnAWB"
                          width="100"
                          mode="Optional" />
                        <field
                          path="JobConsol.JK_ReceivingForwarderHandlingType"
                          width="100"
                          mode="Optional" />
                        <field
                          path="JobConsol.JK_ReleaseType"
                          width="100"
                          mode="Optional" />
                        <field
                          path="JobConsol.JK_RequiredTemperatureMaximum"
                          width="100"
                          mode="Optional" />
                        <field
                          path="JobConsol.JK_RequiredTemperatureMinimum"
                          width="100"
                          mode="Optional" />
                        <field
                          path="JobConsol.JK_RequiredTemperatureUnit"
                          width="100"
                          mode="Optional" />
                        <field
                          path="JobConsol.JK_RequiresTemperatureControl"
                          width="100"
                          mode="Optional" />
                        <field
                          path="JobConsol.JK_RL_NKFirstForeignPort"
                          width="100"
                          mode="Optional" />
                        <field
                          path="JobConsol.JK_RL_NKLastForeignPort"
                          width="100"
                          mode="Optional" />
                        <field
                          path="JobConsol.JK_RL_NKMasterBillIssuePlace"
                          width="100"
                          mode="Optional" />
                        <field
                          path="JobConsol.JK_RL_NKPortOfFirstArrival"
                          width="100"
                          mode="Optional" />
                        <field
                          path="JobConsol.JK_ScreeningStatus"
                          width="100"
                          mode="Optional" />
                        <field
                          path="JobConsol.JK_SendingForwarderHandlingType"
                          width="100"
                          mode="Optional" />
                        <field
                          path="JobConsol.JK_ShippedOnBoardDate"
                          width="100"
                          mode="Optional" />
                        <field
                          path="JobConsol.JK_SystemCreateTimeUtc"
                          width="100"
                          mode="Optional" />
                        <field
                          path="JobConsol.JK_SystemCreateUser"
                          width="100"
                          mode="Optional" />
                        <field
                          path="JobConsol.JK_SystemLastEditTimeUtc"
                          width="100"
                          mode="Optional" />
                        <field
                          path="JobConsol.JK_SystemLastEditUser"
                          width="100"
                          mode="Optional" />
                        <field
                          path="JobConsol.JK_TotalShipmentActOtherUnit"
                          width="100"
                          mode="Optional" />
                        <field
                          path="JobConsol.JK_TotalShipmentActVolumeCheck"
                          width="100"
                          mode="Optional" />
                        <field
                          path="JobConsol.JK_TotalShipmentActWeightCheck"
                          width="100"
                          mode="Optional" />
                        <field
                          path="JobConsol.JK_TotalShipmentChargableCheck"
                          width="100"
                          mode="Optional" />
                        <field
                          path="JobConsol.JK_TotalShipmentChargeableUnit"
                          width="100"
                          mode="Optional" />
                        <field
                          path="JobConsol.JK_TotalShipmentCountCheck"
                          width="100"
                          mode="Optional" />
                        <field
                          path="JobConsol.JK_UnpackDepotDispatchRequested"
                          width="100"
                          mode="Optional" />
                        <field
                          path="JobConsol.JK_UnpackDepotReceiptRequested"
                          width="100"
                          mode="Optional" />
                        <field
                          path="JobConsol.ContainerCount"
                          width="80"
                          mode="Optional" />
                        <field
                          path="JobConsol.LoadListInstructionsNote"
                          width="250"
                          mode="Optional" />
                        <field
                          path="JobConsol.ContainerCountPerType"
                          width="250"
                          mode="Optional" />
                        <field
                          path="JobConsol.CTOCutOff"
                          width="250"
                          mode="Optional" />
                        <field
                          path="JobConsol.JK_CarrierContractNumber"
                          width="250"
                          mode="Optional" />
                        <field
                          path="JobConsol.TransportFirstLeg.JW_Vessel"
                          width="250"
                          mode="Optional" />
                        <field
                          path="JobConsol.AllocationLevel"
                          width="250"
                          mode="Optional" />
                        <field
                          path="JobConsol.ContainerCountNumber"
                          width="190"
                          mode="Optional" />
                        <field
                          path="JobConsol.TEU"
                          width="70"
                          mode="Optional" />
                        <field
                          path="JobConsol.AllocatedCN"
                          width="120"
                          mode="Optional" />
                        <field
                          path="JobConsol.AllocatedTEU"
                          width="130"
                          mode="Optional" />
                        <field
                          path="JobConsol.AllocationRoutes"
                          width="250"
                          mode="Optional" />
                        <field
                          path="JobConsol.ContainerTypesSummary"
                          width="250"
                          mode="Optional" />
                        <field
                          path="JobConsol.JK_RL_NKCarrierBookingOffice"
                          width="250"
                          mode="Optional" />
                        <field
                          path="ConsolLatestBookingStatusAndDate.JKS_LatestBookingStatus"
                          width="250"
                          mode="Optional" />
                        <field
                          path="ConsolLatestBookingStatusAndDate.JKS_LatestBookingDate"
                          width="250"
                          mode="Optional" />
                        <field
                          path="JobConsol.TransportRoutings"
                          width="300"
                          caption="Related Transport Legs"
                          resid="25f69743-712c-4c38-8536-e3f80d4b7692"
                          mode="FilterOnly" />
                      </fields>
                    </xml>
                  </placeholder>
                </control>
                <control
                  code="RDT"
                  id="81036a66-71e1-44e0-86f0-0905318bf986"
                  binding="RatingContractAllocationLines/CCAJobContainerViews">
                  <placeholder
                    name="Columns"
                    value="col-6" />
                  <placeholder
                    name="Height"
                    value="250px" />
                  <placeholder
                    name="CaptionOverride"
                    value="Allocated Containers of Route"
                    resid="928ae2c0-018f-4856-baaf-24a024cb8e8c" />
                  <placeholder
                    name="AllowDetach"
                    value="False" />
                  <placeholder
                    name="AllowAttach"
                    value="False" />
                  <placeholder
                    name="ShowCustomize"
                    value="True" />
                  <placeholder
                    name="DefaultFilter">
                    <xml>
                      <ArrayOfFilterGroup xmlns:a="http://schemas.microsoft.com/2003/10/Serialization/Arrays" xmlns:i="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://cargowise.com/glow/2014/07/16/filters.xsd">
                        <FilterGroup>
                          <Filters>
                            <Filter>
                              <FilterType>AdvancedGuidLookupFilter</FilterType>
                              <Operation>IsNotBlank</Operation>
                              <PropertyPath>JobContainer.JC_JK</PropertyPath>
                              <Values />
                            </Filter>
                          </Filters>
                          <IsImplicit>true</IsImplicit>
                        </FilterGroup>
                      </ArrayOfFilterGroup>
                    </xml>
                  </placeholder>
                  <placeholder
                    name="ShowFilters"
                    value="True" />
                  <placeholder
                    name="ActionMenuItems">
                    <xml>
                      <formFlows xmlns="">
                        <formFlow>5c53fbee6ff94d938405ed41e702ac49</formFlow>
                      </formFlows>
                    </xml>
                  </placeholder>
                </control>
              </control>
            </control>
            <control
              code="TAB"
              id="ffb9b45c-34b7-4109-b65f-9b471fab78be">
              <placeholder
                name="Caption"
                value="Bookings &amp; Containers"
                resid="1742698b-90bb-4fa8-8832-658a3c813e81" />
            </control>
            <control
              code="TAI"
              id="4baf5bbf-f696-4360-b125-64196bcc222b">
              <control
                code="BOX"
                id="2c46d477-6cfc-4c2f-b2dd-a3f30ef83fc4">
                <placeholder
                  name="Layout"
                  value="grid" />
                <control
                  code="RDT"
                  id="253de3ed-df34-4914-b5b1-c204092467b3"
                  binding="RatingContractAllocationLines/BookingViews">
                  <placeholder
                    name="Columns"
                    value="col-6" />
                  <placeholder
                    name="Height"
                    value="250px" />
                  <placeholder
                    name="CaptionOverride"
                    value="Allocated Bookings of Route"
                    resid="e746f4b6-d5fe-4e45-96fd-05277d270d17" />
                  <placeholder
                    name="ShowCustomize"
                    value="True" />
                  <placeholder
                    name="AllowAttach"
                    value="False" />
                  <placeholder
                    name="AllowDetach"
                    value="False" />
                  <placeholder
                    name="FieldConfiguration">
                    <xml>
                      <fields xmlns="">
                        <field
                          path="ViewQuotedBooking.VB_QuoteNumber"
                          width="200"
                          mode="Default" />
                        <field
                          path="ViewQuotedBooking.RatingHeader.TH_QuoteDate"
                          width="100"
                          mode="Default" />
                        <field
                          path="ViewQuotedBooking.RatingHeader.TH_QuoteEndDate"
                          width="110"
                          mode="Default" />
                        <field
                          path="ViewQuotedBooking.RatingHeader.TH_GC"
                          width="200"
                          mode="Default" />
                        <field
                          path="ViewQuotedBooking.RatingHeader.OneOffShipment.TT_RL_NKReceivalLocation"
                          width="100"
                          mode="Default" />
                        <field
                          path="ViewQuotedBooking.RatingHeader.OneOffShipment.TT_RL_NKDeliveryLocation"
                          width="100"
                          mode="Default" />
                        <field
                          path="ViewQuotedBooking.RatingHeader.TotalLocalSellAmount"
                          width="200"
                          mode="Default" />
                        <field
                          path="ViewQuotedBooking.VB_TH"
                          width="100"
                          mode="Optional" />
                        <field
                          path="ViewQuotedBooking.VB_JS"
                          width="100"
                          mode="FilterOnly" />
                        <field
                          path="ViewQuotedBooking.RatingHeader.OneOffShipment"
                          width="250"
                          mode="Optional" />
                        <field
                          path="ViewQuotedBooking.RatingHeader.RateOneOffShipments.TT_RL_NKReceivalLocation"
                          width="100"
                          mode="FilterOnly" />
                        <field
                          path="ViewQuotedBooking.RatingHeader.RateOneOffShipments.TT_RL_NKDeliveryLocation"
                          width="100"
                          mode="FilterOnly" />
                        <field
                          path="ViewQuotedBooking.VB_GC"
                          width="100"
                          mode="FilterOnly" />
                        <field
                          path="ViewQuotedBooking.VB_IsCanceled"
                          width="100"
                          mode="Optional" />
                        <field
                          path="ViewQuotedBooking.VB_IsConsolidated"
                          width="100"
                          mode="Optional" />
                        <field
                          path="ViewQuotedBooking.RatingHeader.TH_OneTimeQuote"
                          width="100"
                          mode="FilterOnly" />
                        <field
                          path="ViewQuotedBooking.Booking.JS_UniqueConsignRef"
                          width="200"
                          mode="Default" />
                        <field
                          path="ViewQuotedBooking.Booking.ConsignorDocumentaryAddress.OrganizationCode"
                          width="250"
                          mode="Default" />
                        <field
                          path="ViewQuotedBooking.Booking.ConsignorName"
                          width="250"
                          mode="Default" />
                        <field
                          path="ViewQuotedBooking.Booking.JS_TransportMode"
                          width="240"
                          mode="Default" />
                        <field
                          path="ViewQuotedBooking.Booking.OneTimeQuote.TH_IsLocked"
                          width="250"
                          mode="Default" />
                        <field
                          path="ViewQuotedBooking.Booking.OneTimeQuote.TH_IsOneOffQuoteConsumed"
                          width="250"
                          mode="Default" />
                        <field
                          path="ViewQuotedBooking.Booking.JS_GoodsValue"
                          width="200"
                          mode="Default" />
                        <field
                          path="ViewQuotedBooking.Booking.GoodsValueCurr.RX_Code"
                          width="40"
                          mode="Default" />
                        <field
                          path="ViewQuotedBooking.Booking.JS_CFSReference"
                          width="200"
                          mode="Default" />
                        <field
                          path="ViewQuotedBooking.RatingHeader.OneOffShipment.TT_Status"
                          width="250"
                          mode="Default" />
                        <field
                          path="ViewQuotedBooking.RatingHeader.OneOffShipment.TT_QuoteKPI"
                          width="250"
                          mode="Default" />
                        <field
                          path="ViewQuotedBooking.RatingHeader.OneOffShipment.TT_QuoteSource"
                          width="250"
                          mode="Default" />
                        <field
                          path="ViewQuotedBooking.RatingHeader.OneOffShipment.TT_RevisionReason"
                          width="250"
                          mode="Default" />
                        <field
                          path="ViewQuotedBooking.AllocationLevel"
                          width="250"
                          mode="Optional" />
                        <field
                          path="ViewQuotedBooking.TwentyGPContainerCount"
                          width="70"
                          mode="Optional" />
                        <field
                          path="ViewQuotedBooking.FortyGPContainerCount"
                          width="70"
                          mode="Optional" />
                        <field
                          path="ViewQuotedBooking.AdditionalReference"
                          width="250"
                          mode="Optional" />
                        <field
                          path="ViewQuotedBooking.Booking.BookingAllocationLine.RCA_AllocationLineID"
                          width="250"
                          mode="Optional" />
                        <field
                          path="ViewQuotedBooking.Booking.JS_A_BKD"
                          width="180"
                          mode="Optional" />
                        <field
                          path="ViewQuotedBooking.Booking.JS_E_DEP"
                          width="180"
                          mode="Optional" />
                        <field
                          path="ViewQuotedBooking.Carrier"
                          width="250"
                          mode="Optional" />
                        <field
                          path="ViewQuotedBooking.Consignee"
                          width="250"
                          mode="Optional" />
                        <field
                          path="ViewQuotedBooking.Consignor"
                          width="250"
                          mode="Optional" />
                        <field
                          path="ViewQuotedBooking.Booking.JS_CarrierContractNumber"
                          width="250"
                          mode="Optional" />
                        <field
                          path="ViewQuotedBooking.Destination"
                          width="250"
                          mode="Optional" />
                        <field
                          path="ViewQuotedBooking.DischargePort"
                          width="250"
                          mode="Optional" />
                        <field
                          path="ViewQuotedBooking.LoadPort"
                          width="250"
                          mode="Optional" />
                        <field
                          path="ViewQuotedBooking.Origin"
                          width="250"
                          mode="Optional" />
                        <field
                          path="ViewQuotedBooking.Booking.DocsAndCartageDetail.Parent.OrderReferences"
                          width="250"
                          mode="Optional" />
                        <field
                          path="ViewQuotedBooking.TEUContainerCount"
                          width="70"
                          mode="Optional" />
                        <field
                          path="ViewQuotedBooking.Vessel"
                          width="250"
                          mode="Optional" />
                        <field
                          path="ViewQuotedBooking.Voyage"
                          width="250"
                          mode="Optional" />
                        <field
                          path="ViewQuotedBooking.VB_SystemCreateUser"
                          width="160"
                          mode="Optional" />
                        <field
                          path="BookingCalculatedField.JSBV_PickupClose"
                          width="250"
                          mode="Optional" />
                        <field
                          path="ViewQuotedBooking.Client"
                          width="300"
                          mode="Optional" />
                      </fields>
                    </xml>
                  </placeholder>
                </control>
                <control
                  code="RDT"
                  id="4df4f89d-ce4f-416f-a771-1daeb2904275"
                  binding="RatingContractAllocationLines/JobContainers">
                  <placeholder
                    name="Columns"
                    value="col-6" />
                  <placeholder
                    name="Height"
                    value="250px" />
                  <placeholder
                    name="CaptionOverride"
                    value="Allocated Booking Containers of Route"
                    resid="e75a0bcf-8035-485b-aa3d-db0f0bfb5b4a" />
                  <placeholder
                    name="ShowCustomize"
                    value="True" />
                  <placeholder
                    name="DefaultFilter">
                    <xml>
                      <ArrayOfFilterGroup xmlns:a="http://schemas.microsoft.com/2003/10/Serialization/Arrays" xmlns:i="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://cargowise.com/glow/2014/07/16/filters.xsd">
                        <FilterGroup>
                          <Filters>
                            <Filter>
                              <FilterType>StringFilter</FilterType>
                              <Operation>IsNotBlank</Operation>
                              <PropertyPath>FCLBookingOnlyLink.JS_UniqueConsignRef</PropertyPath>
                              <Values />
                            </Filter>
                            <Filter>
                              <FilterType>BoolFilter</FilterType>
                              <Operation>Is</Operation>
                              <PropertyPath>FCLBookingOnlyLink.JS_IsForwardRegistered</PropertyPath>
                              <Values>
                                <a:string>false</a:string>
                              </Values>
                            </Filter>
                          </Filters>
                          <IsImplicit>true</IsImplicit>
                        </FilterGroup>
                      </ArrayOfFilterGroup>
                    </xml>
                  </placeholder>
                  <placeholder
                    name="AllowDetach"
                    value="False" />
                  <placeholder
                    name="AllowAttach"
                    value="False" />
                </control>
              </control>
            </control>
            <control
              code="TAB"
              id="0bd8c350-5570-4c9e-9180-01764c3131b7">
              <placeholder
                name="Caption"
                value="Customers"
                resid="946b77f0-f287-40c7-8dfe-c5870c4857d9" />
            </control>
            <control
              code="TAI"
              id="ccdd56f1-b701-45dd-8acd-c6d304126bb6">
              <control
                code="RDT"
                id="51c6e72e-1402-4c7a-b43f-796c5d7cceb8"
                binding="RatingContractAllocationLines/RatingContractNamedAccountPivots">
                <placeholder
                  name="CaptionOverride"
                  value="Customers of Route"
                  resid="177eb715-382c-4ecc-a679-5d47b305c5ad" />
                <placeholder
                  name="AllowAdd"
                  value="True" />
                <placeholder
                  name="InlineEdit"
                  value="cell" />
                <placeholder
                  name="Height"
                  value="250px" />
                <placeholder
                  name="ShowCustomize"
                  value="True" />
              </control>
            </control>
            <control
              code="TAB"
              id="0e0fe406-5286-4061-82e3-619137a14bf8">
              <placeholder
                name="Caption"
                value="Agents"
                resid="b26bf9e5-c66e-4222-9132-e937fad38a63" />
            </control>
            <control
              code="TAI"
              id="ee4b399f-4bec-4ec5-808a-1cbcb8064111">
              <control
                code="RDT"
                id="275b70c6-dc22-4d06-b551-18e641a46e65"
                binding="RatingContractAllocationLines/AllocationRouteAgentPivots">
                <placeholder
                  name="FieldConfiguration">
                  <xml>
                    <fields xmlns="">
                      <field
                        path="ARA_OH_Agent"
                        width="300"
                        caption="Organization"
                        resid="b21bbf76-b9d2-4253-835f-10e5e8177d6e"
                        mode="Mandatory" />
                      <field
                        path="Agent.OH_FullName"
                        width="300"
                        mode="Optional" />
                      <field
                        path="Agent.OH_Code"
                        width="300"
                        mode="Optional" />
                    </fields>
                  </xml>
                </placeholder>
                <placeholder
                  name="AllowAdd"
                  value="True" />
                <placeholder
                  name="AllowAttach"
                  value="True" />
                <placeholder
                  name="InlineEdit"
                  value="cell" />
                <placeholder
                  name="CaptionOverride"
                  value="Dedicated Agents of Route"
                  resid="52099c58-7aac-4617-abf2-882898d5e3fd" />
              </control>
            </control>
          </control>
        </control>
        <control
          code="TAB"
          id="708c424d-29b7-4a17-9c45-49a44734f3bd">
          <placeholder
            name="Caption"
            value="Container Penalties"
            resid="f4a815a6-0791-45dd-8201-bd2a66f6ef00" />
        </control>
        <control
          code="TAI"
          id="ddcaa1d7-ec14-4ab4-a72b-5c3379b8f1b2">
          <control
            code="RDT"
            id="8710c10f-b27f-40a4-a671-6739fc77df5e"
            binding="RatingContractContainerDetentions">
            <placeholder
              name="Style"
              value="min-height:300px;" />
            <placeholder
              name="FitToHeight"
              value="True" />
            <placeholder
              name="ShowCustomize"
              value="True" />
            <placeholder
              name="InlineEdit"
              value="cell" />
            <placeholder
              name="AllowAdd"
              value="True" />
            <placeholder
              name="FieldConfiguration">
              <xml>
                <fields xmlns="">
                  <field
                    path="RCD_Direction"
                    width="90"
                    mode="Default" />
                  <field
                    path="RCD_PenaltyType"
                    width="120"
                    mode="Default" />
                  <field
                    path="RCD_DetentionPortOrCountry"
                    width="250"
                    mode="Default" />
                  <field
                    path="RCD_OriginPortOrCountry"
                    width="220"
                    mode="Default" />
                  <field
                    path="RCD_ContainerType"
                    width="140"
                    mode="Default" />
                  <field
                    path="RCD_FreeDays"
                    width="90"
                    mode="Default" />
                  <field
                    path="FirstFreeDay"
                    width="250"
                    mode="Default" />
                  <field
                    path="LastFreeDay"
                    width="250"
                    mode="Default" />
                  <field
                    path="RCD_OH_Client"
                    width="250"
                    mode="Optional" />
                  <field
                    path="RCD_StartDateOverride"
                    width="190"
                    mode="Default" />
                  <field
                    path="RCD_EndDateOverride"
                    width="170"
                    mode="Default" />
                  <field
                    path="DurationExclusionDays"
                    width="300"
                    caption="Duration Exclusions"
                    resid="5b6567a4-2ff9-482e-af23-03f4a9e6d9c9"
                    mode="Default" />
                  <field
                    path="FreeDayExclusionDays"
                    width="300"
                    caption="Free Day Exclusions"
                    resid="7b1626e8-8c8f-4ee5-9eae-e995c307ff84"
                    mode="Default" />
                </fields>
              </xml>
            </placeholder>
            <placeholder
              name="ActionMenuItems">
              <xml>
                <formFlows xmlns="">
                  <formFlow>4ff508673ceb4018871bf70bcb419495</formFlow>
                  <formFlow>8cae285faffc4a4fa8b05fd3166ca429</formFlow>
                </formFlows>
              </xml>
            </placeholder>
          </control>
        </control>
        <control
          code="TAB"
          id="a14aa1fc-0e3c-4649-ad03-ee99654bd277">
          <placeholder
            name="Caption"
            value="Allocated Consolidations"
            resid="a0901bcf-0378-46d3-aee2-f50f628a9bd6" />
        </control>
        <control
          code="TAI"
          id="ab1c69ff-7899-469d-9ee1-6f90d930bcb6">
          <control
            code="RDT"
            id="5d663924-68d1-487f-9034-ed20d48973fc"
            binding="ConsolidationViews">
            <placeholder
              name="Style"
              value="min-height:300px;" />
            <placeholder
              name="FitToHeight"
              value="True" />
            <placeholder
              name="ShowCustomize"
              value="True" />
            <placeholder
              name="CaptionOverride"
              value="Allocated Consolidations"
              resid="66a72be8-bd9b-45a8-8212-f147c3be5f12" />
            <placeholder
              name="ShowSelect"
              value="False" />
            <placeholder
              name="ActionMenuItems">
              <xml>
                <formFlows xmlns="">
                  <formFlow>fc7abfd09b2a4b429fd7c23f7a48a4b2</formFlow>
                  <formFlow>50390b8477c84011828b5fcbcea17140</formFlow>
                </formFlows>
              </xml>
            </placeholder>
            <placeholder
              name="ShowFilters"
              value="True" />
            <placeholder
              name="FieldConfiguration">
              <xml>
                <fields xmlns="">
                  <field
                    path="JobConsol.JK_UniqueConsignRef"
                    width="200"
                    mode="Mandatory" />
                  <field
                    path="JobConsol.JK_MasterBillNum"
                    width="200"
                    mode="Default" />
                  <field
                    path="JobConsol.JK_TransportMode"
                    width="200"
                    mode="Default" />
                  <field
                    path="JobConsol.TransportFirstLeg.JW_VoyageFlight"
                    width="250"
                    mode="Optional" />
                  <field
                    path="JobConsol.TransportLastLeg.JW_VoyageFlight"
                    width="250"
                    mode="Optional" />
                  <field
                    path="JobConsol.ArrivalCTOAddress.OrgHeader.OH_FullName"
                    width="250"
                    mode="Default" />
                  <field
                    path="JobConsol.DepartureCTOAddress.OrgHeader.OH_FullName"
                    width="250"
                    mode="Default" />
                  <field
                    path="JobConsol.UnpackDepotAddress.OrgHeader.OH_FullName"
                    width="250"
                    mode="Default" />
                  <field
                    path="JobConsol.ShippingLineAddress.OrgHeader.OH_FullName"
                    width="250"
                    mode="Default" />
                  <field
                    path="JobConsol.JK_IsCancelled"
                    width="120"
                    mode="Optional" />
                  <field
                    path="JobConsol.TransportFirstLeg.JW_ATD"
                    width="120"
                    mode="Default" />
                  <field
                    path="JobConsolFirstTransportLeg.FirstTransport.JW_ETD"
                    width="120"
                    mode="Default" />
                  <field
                    path="JobConsol.TransportLastLeg.JW_ATA"
                    width="120"
                    mode="Default" />
                  <field
                    path="JobConsol.TransportLastLeg.JW_ETA"
                    width="120"
                    mode="Default" />
                  <field
                    path="JobConsol.JK_RL_NKLoadPort"
                    width="80"
                    mode="Default" />
                  <field
                    path="JobConsol.JK_RL_NKDischargePort"
                    width="90"
                    mode="Default" />
                  <field
                    path="JobConsol.JK_AgentsReference"
                    width="100"
                    mode="Optional" />
                  <field
                    path="JobConsol.JK_AgentType"
                    width="100"
                    mode="Optional" />
                  <field
                    path="JobConsol.JK_AWBServiceLevel"
                    width="100"
                    mode="Optional" />
                  <field
                    path="JobConsol.JK_BookingReference"
                    width="100"
                    mode="Optional" />
                  <field
                    path="JobConsol.JK_CoLoadBookingReference"
                    width="100"
                    mode="Optional" />
                  <field
                    path="JobConsol.JK_CoLoadMasterBill"
                    width="100"
                    mode="Optional" />
                  <field
                    path="JobConsol.JK_ConsolChargeable"
                    width="100"
                    mode="Optional" />
                  <field
                    path="JobConsol.JK_ConsolChargeableRate"
                    width="100"
                    mode="Optional" />
                  <field
                    path="JobConsol.JK_ConsolCutOffDate"
                    width="100"
                    mode="Optional" />
                  <field
                    path="JobConsol.JK_ConsolMode"
                    width="100"
                    mode="Optional" />
                  <field
                    path="JobConsol.JK_ConsolStatus"
                    width="100"
                    mode="Optional" />
                  <field
                    path="JobConsol.JK_CorrectedConsolVolume"
                    width="100"
                    mode="Optional" />
                  <field
                    path="JobConsol.JK_CorrectedConsolVolumeUnit"
                    width="100"
                    mode="Optional" />
                  <field
                    path="JobConsol.JK_CorrectedConsolWeight"
                    width="100"
                    mode="Optional" />
                  <field
                    path="JobConsol.JK_CorrectedConsolWeightUnit"
                    width="100"
                    mode="Optional" />
                  <field
                    path="JobConsol.JK_CustomDate1"
                    width="100"
                    mode="Optional" />
                  <field
                    path="JobConsol.JK_CustomDate2"
                    width="100"
                    mode="Optional" />
                  <field
                    path="JobConsol.JK_CustomFlag1"
                    width="100"
                    mode="Optional" />
                  <field
                    path="JobConsol.JK_CustomFlag2"
                    width="100"
                    mode="Optional" />
                  <field
                    path="JobConsol.JK_CustomsReference"
                    width="100"
                    mode="Optional" />
                  <field
                    path="JobConsol.JK_DateFirstForeignPort"
                    width="100"
                    mode="Optional" />
                  <field
                    path="JobConsol.JK_DateLastForeignPort"
                    width="100"
                    mode="Optional" />
                  <field
                    path="JobConsol.JK_DatePortOfFirstArrival"
                    width="100"
                    mode="Optional" />
                  <field
                    path="JobConsol.JK_IsCFS"
                    width="100"
                    mode="Optional" />
                  <field
                    path="JobConsol.JK_IsForwarding"
                    width="100"
                    mode="Optional" />
                  <field
                    path="JobConsol.JK_IsHazardous"
                    width="100"
                    mode="Optional" />
                  <field
                    path="JobConsol.JK_IsNeutralMaster"
                    width="100"
                    mode="Optional" />
                  <field
                    path="JobConsol.JK_MasterBillIssueDate"
                    width="100"
                    mode="Optional" />
                  <field
                    path="JobConsol.JK_MaximumAllowablePackageHeight"
                    width="100"
                    mode="Optional" />
                  <field
                    path="JobConsol.JK_MaximumAllowablePackageLength"
                    width="100"
                    mode="Optional" />
                  <field
                    path="JobConsol.JK_MaximumAllowablePackageUnit"
                    width="100"
                    mode="Optional" />
                  <field
                    path="JobConsol.JK_MaximumAllowablePackageWidth"
                    width="100"
                    mode="Optional" />
                  <field
                    path="JobConsol.JK_MBLAWBChargesDisplay"
                    width="100"
                    mode="Optional" />
                  <field
                    path="JobConsol.JK_NoCopyBills"
                    width="100"
                    mode="Optional" />
                  <field
                    path="JobConsol.JK_NoOriginalBills"
                    width="100"
                    mode="Optional" />
                  <field
                    path="JobConsol.JK_OA_ArrivalCTOAddress"
                    width="100"
                    mode="Optional" />
                  <field
                    path="JobConsol.JK_OA_ArrivalUnpackCFSTransportAddress"
                    width="100"
                    mode="Optional" />
                  <field
                    path="JobConsol.JK_OA_CoLoadAddress"
                    width="100"
                    mode="Optional" />
                  <field
                    path="JobConsol.JK_OA_ContainerYardEmptyPickupAddress"
                    width="100"
                    mode="Optional" />
                  <field
                    path="JobConsol.JK_OA_ContainerYardEmptyReturnAddress"
                    width="100"
                    mode="Optional" />
                  <field
                    path="JobConsol.JK_OA_CreditorAddress"
                    width="100"
                    mode="Optional" />
                  <field
                    path="JobConsol.JK_OA_DepartureCTOAddress"
                    width="100"
                    mode="Optional" />
                  <field
                    path="JobConsol.JK_OA_DeparturePackCFSTransportAddress"
                    width="100"
                    mode="Optional" />
                  <field
                    path="JobConsol.JK_OA_PackDepotAddress"
                    width="100"
                    mode="Optional" />
                  <field
                    path="JobConsol.JK_OA_ReceivingForwarderAddress"
                    width="100"
                    mode="Optional" />
                  <field
                    path="JobConsol.JK_OA_SendingForwarderAddress"
                    width="100"
                    mode="Optional" />
                  <field
                    path="JobConsol.JK_OA_ShippingLineAddress"
                    width="100"
                    mode="Optional" />
                  <field
                    path="JobConsol.JK_OA_UnpackDepotAddress"
                    width="100"
                    mode="Optional" />
                  <field
                    path="JobConsol.JK_OC_ReceivingForwarderContact"
                    width="100"
                    mode="Optional" />
                  <field
                    path="JobConsol.JK_OC_SendingForwarderContact"
                    width="100"
                    mode="Optional" />
                  <field
                    path="JobConsol.JK_OH_ArrivalUnpackCFSTransport"
                    width="100"
                    mode="Optional" />
                  <field
                    path="JobConsol.JK_OH_Creditor"
                    width="100"
                    mode="Optional" />
                  <field
                    path="JobConsol.JK_OH_DeparturePackCFSTransport"
                    width="100"
                    mode="Optional" />
                  <field
                    path="JobConsol.JK_OverrideConsolChargeable"
                    width="100"
                    mode="Optional" />
                  <field
                    path="JobConsol.JK_OverrideWaybillDefaults"
                    width="100"
                    mode="Optional" />
                  <field
                    path="JobConsol.JK_PackDepotDispatchRequested"
                    width="100"
                    mode="Optional" />
                  <field
                    path="JobConsol.JK_PackDepotReceiptRequested"
                    width="100"
                    mode="Optional" />
                  <field
                    path="JobConsol.JK_Phase"
                    width="100"
                    mode="Optional" />
                  <field
                    path="JobConsol.JK_PrepaidCollect"
                    width="100"
                    mode="Optional" />
                  <field
                    path="JobConsol.JK_PrintOptionForColoadsOnManifest"
                    width="100"
                    mode="Optional" />
                  <field
                    path="JobConsol.JK_PrintOptionForColoadsOnOtherDocs"
                    width="100"
                    mode="Optional" />
                  <field
                    path="JobConsol.JK_PrintOptionForPackagesOnAWB"
                    width="100"
                    mode="Optional" />
                  <field
                    path="JobConsol.JK_ReceivingForwarderHandlingType"
                    width="100"
                    mode="Optional" />
                  <field
                    path="JobConsol.JK_ReleaseType"
                    width="100"
                    mode="Optional" />
                  <field
                    path="JobConsol.JK_RequiredTemperatureMaximum"
                    width="100"
                    mode="Optional" />
                  <field
                    path="JobConsol.JK_RequiredTemperatureMinimum"
                    width="100"
                    mode="Optional" />
                  <field
                    path="JobConsol.JK_RequiredTemperatureUnit"
                    width="100"
                    mode="Optional" />
                  <field
                    path="JobConsol.JK_RequiresTemperatureControl"
                    width="100"
                    mode="Optional" />
                  <field
                    path="JobConsol.JK_RL_NKFirstForeignPort"
                    width="100"
                    mode="Optional" />
                  <field
                    path="JobConsol.JK_RL_NKLastForeignPort"
                    width="100"
                    mode="Optional" />
                  <field
                    path="JobConsol.JK_RL_NKMasterBillIssuePlace"
                    width="100"
                    mode="Optional" />
                  <field
                    path="JobConsol.JK_RL_NKPortOfFirstArrival"
                    width="100"
                    mode="Optional" />
                  <field
                    path="JobConsol.JK_ScreeningStatus"
                    width="100"
                    mode="Optional" />
                  <field
                    path="JobConsol.JK_SendingForwarderHandlingType"
                    width="100"
                    mode="Optional" />
                  <field
                    path="JobConsol.JK_ShippedOnBoardDate"
                    width="100"
                    mode="Optional" />
                  <field
                    path="JobConsol.JK_SystemCreateTimeUtc"
                    width="100"
                    mode="Optional" />
                  <field
                    path="JobConsol.JK_SystemCreateUser"
                    width="100"
                    mode="Optional" />
                  <field
                    path="JobConsol.JK_SystemLastEditTimeUtc"
                    width="100"
                    mode="Optional" />
                  <field
                    path="JobConsol.JK_SystemLastEditUser"
                    width="100"
                    mode="Optional" />
                  <field
                    path="JobConsol.JK_TotalShipmentActOtherUnit"
                    width="100"
                    mode="Optional" />
                  <field
                    path="JobConsol.JK_TotalShipmentActVolumeCheck"
                    width="100"
                    mode="Optional" />
                  <field
                    path="JobConsol.JK_TotalShipmentActWeightCheck"
                    width="100"
                    mode="Optional" />
                  <field
                    path="JobConsol.JK_TotalShipmentChargableCheck"
                    width="100"
                    mode="Optional" />
                  <field
                    path="JobConsol.JK_TotalShipmentChargeableUnit"
                    width="100"
                    mode="Optional" />
                  <field
                    path="JobConsol.JK_TotalShipmentCountCheck"
                    width="100"
                    mode="Optional" />
                  <field
                    path="JobConsol.JK_UnpackDepotDispatchRequested"
                    width="100"
                    mode="Optional" />
                  <field
                    path="JobConsol.JK_UnpackDepotReceiptRequested"
                    width="100"
                    mode="Optional" />
                  <field
                    path="JobConsol.ContainerCount"
                    width="80"
                    mode="Optional" />
                  <field
                    path="JobConsol.LoadListInstructionsNote"
                    width="250"
                    mode="Optional" />
                  <field
                    path="JobConsol.ContainerCountPerType"
                    width="250"
                    mode="Optional" />
                  <field
                    path="JobConsol.CTOCutOff"
                    width="250"
                    mode="Optional" />
                  <field
                    path="JobConsol.JK_CarrierContractNumber"
                    width="250"
                    mode="Optional" />
                  <field
                    path="JobConsol.TransportFirstLeg.JW_Vessel"
                    width="250"
                    mode="Optional" />
                  <field
                    path="JobConsol.AllocationLevel"
                    width="250"
                    mode="Optional" />
                  <field
                    path="JobConsol.ContainerCountNumber"
                    width="190"
                    mode="Optional" />
                  <field
                    path="JobConsol.TEU"
                    width="70"
                    mode="Optional" />
                  <field
                    path="JobConsol.AllocatedCN"
                    width="120"
                    mode="Optional" />
                  <field
                    path="JobConsol.AllocatedTEU"
                    width="130"
                    mode="Optional" />
                  <field
                    path="JobConsol.AllocationRoutes"
                    width="250"
                    mode="Optional" />
                  <field
                    path="JobConsol.ContainerTypesSummary"
                    width="250"
                    mode="Optional" />
                  <field
                    path="JobConsol.JK_RL_NKCarrierBookingOffice"
                    width="250"
                    mode="Optional" />
                  <field
                    path="ConsolLatestBookingStatusAndDate.JKS_LatestBookingStatus"
                    width="250"
                    mode="Optional" />
                  <field
                    path="ConsolLatestBookingStatusAndDate.JKS_LatestBookingDate"
                    width="250"
                    mode="Optional" />
                  <field
                    path="JobConsol.TransportRoutings"
                    width="300"
                    caption="Related Transport Legs"
                    resid="b16fefaa-b71f-4c85-87f7-b5293215759f"
                    mode="FilterOnly" />
                </fields>
              </xml>
            </placeholder>
          </control>
          <control
            code="BTN"
            id="625679f9-9a4a-4fea-82ab-c6957a0da681"
            binding="">
            <placeholder
              name="Caption"
              value="Allocate Consolidations"
              resid="ecbe5953-f60b-4b5f-ae34-69827447b653" />
            <placeholder
              name="Transition"
              value="True" />
            <placeholder
              name="VisibilityCondition"
              value="!%.CCAEnforceConsolAllocationAtRouteLevel" />
          </control>
        </control>
        <control
          code="TAB"
          id="f96f6918-c880-48b8-88b0-8ec037d028bc">
          <placeholder
            name="Caption"
            value="Allocated Bookings"
            resid="d39a6f88-e5c0-47a6-bdcb-e63f0b9542bf" />
        </control>
        <control
          code="TAI"
          id="ca16873a-0b41-4a1c-86fb-d3445d40e5b1">
          <control
            code="RDT"
            id="46bddcbe-62bc-4168-a42d-4e99f026d31e"
            binding="BookingViews">
            <placeholder
              name="CaptionOverride"
              value="Allocated Bookings"
              resid="4c92f28f-6a24-4f02-85ec-0678d478d08c" />
            <placeholder
              name="ShowCustomize"
              value="True" />
            <placeholder
              name="Style"
              value="min-height:300px;" />
            <placeholder
              name="FitToHeight"
              value="True" />
            <placeholder
              name="FieldConfiguration">
              <xml>
                <fields xmlns="">
                  <field
                    path="ViewQuotedBooking.VB_QuoteNumber"
                    width="200"
                    mode="Default" />
                  <field
                    path="ViewQuotedBooking.RatingHeader.TH_QuoteDate"
                    width="100"
                    mode="Default" />
                  <field
                    path="ViewQuotedBooking.RatingHeader.TH_QuoteEndDate"
                    width="110"
                    mode="Default" />
                  <field
                    path="ViewQuotedBooking.RatingHeader.TH_GC"
                    width="200"
                    mode="Default" />
                  <field
                    path="ViewQuotedBooking.RatingHeader.OneOffShipment.TT_RL_NKReceivalLocation"
                    width="100"
                    mode="Default" />
                  <field
                    path="ViewQuotedBooking.RatingHeader.OneOffShipment.TT_RL_NKDeliveryLocation"
                    width="100"
                    mode="Default" />
                  <field
                    path="ViewQuotedBooking.RatingHeader.TotalLocalSellAmount"
                    width="200"
                    mode="Default" />
                  <field
                    path="ViewQuotedBooking.VB_TH"
                    width="100"
                    mode="Optional" />
                  <field
                    path="ViewQuotedBooking.VB_JS"
                    width="100"
                    mode="FilterOnly" />
                  <field
                    path="ViewQuotedBooking.RatingHeader.OneOffShipment"
                    width="250"
                    mode="Optional" />
                  <field
                    path="ViewQuotedBooking.RatingHeader.RateOneOffShipments.TT_RL_NKReceivalLocation"
                    width="100"
                    mode="FilterOnly" />
                  <field
                    path="ViewQuotedBooking.RatingHeader.RateOneOffShipments.TT_RL_NKDeliveryLocation"
                    width="100"
                    mode="FilterOnly" />
                  <field
                    path="ViewQuotedBooking.VB_GC"
                    width="100"
                    mode="FilterOnly" />
                  <field
                    path="ViewQuotedBooking.VB_IsCanceled"
                    width="100"
                    mode="Optional" />
                  <field
                    path="ViewQuotedBooking.VB_IsConsolidated"
                    width="100"
                    mode="Optional" />
                  <field
                    path="ViewQuotedBooking.RatingHeader.TH_OneTimeQuote"
                    width="100"
                    mode="FilterOnly" />
                  <field
                    path="ViewQuotedBooking.Booking.JS_UniqueConsignRef"
                    width="200"
                    mode="Default" />
                  <field
                    path="ViewQuotedBooking.Booking.ConsignorDocumentaryAddress.OrganizationCode"
                    width="250"
                    mode="Default" />
                  <field
                    path="ViewQuotedBooking.Booking.ConsignorName"
                    width="250"
                    mode="Default" />
                  <field
                    path="ViewQuotedBooking.Booking.JS_TransportMode"
                    width="240"
                    mode="Default" />
                  <field
                    path="ViewQuotedBooking.Booking.OneTimeQuote.TH_IsLocked"
                    width="250"
                    mode="Default" />
                  <field
                    path="ViewQuotedBooking.Booking.OneTimeQuote.TH_IsOneOffQuoteConsumed"
                    width="250"
                    mode="Default" />
                  <field
                    path="ViewQuotedBooking.Booking.JS_GoodsValue"
                    width="200"
                    mode="Default" />
                  <field
                    path="ViewQuotedBooking.Booking.GoodsValueCurr.RX_Code"
                    width="40"
                    mode="Default" />
                  <field
                    path="ViewQuotedBooking.Booking.JS_CFSReference"
                    width="200"
                    mode="Default" />
                  <field
                    path="ViewQuotedBooking.RatingHeader.OneOffShipment.TT_Status"
                    width="250"
                    mode="Default" />
                  <field
                    path="ViewQuotedBooking.RatingHeader.OneOffShipment.TT_QuoteKPI"
                    width="250"
                    mode="Default" />
                  <field
                    path="ViewQuotedBooking.RatingHeader.OneOffShipment.TT_QuoteSource"
                    width="250"
                    mode="Default" />
                  <field
                    path="ViewQuotedBooking.RatingHeader.OneOffShipment.TT_RevisionReason"
                    width="250"
                    mode="Default" />
                  <field
                    path="ViewQuotedBooking.AllocationLevel"
                    width="250"
                    mode="Optional" />
                  <field
                    path="ViewQuotedBooking.TwentyGPContainerCount"
                    width="70"
                    mode="Optional" />
                  <field
                    path="ViewQuotedBooking.FortyGPContainerCount"
                    width="70"
                    mode="Optional" />
                  <field
                    path="ViewQuotedBooking.AdditionalReference"
                    width="250"
                    mode="Optional" />
                  <field
                    path="ViewQuotedBooking.Booking.BookingAllocationLine.RCA_AllocationLineID"
                    width="250"
                    mode="Optional" />
                  <field
                    path="ViewQuotedBooking.Booking.JS_A_BKD"
                    width="180"
                    mode="Optional" />
                  <field
                    path="ViewQuotedBooking.Booking.JS_E_DEP"
                    width="180"
                    mode="Optional" />
                  <field
                    path="ViewQuotedBooking.Carrier"
                    width="250"
                    mode="Optional" />
                  <field
                    path="ViewQuotedBooking.Consignee"
                    width="250"
                    mode="Optional" />
                  <field
                    path="ViewQuotedBooking.Consignor"
                    width="250"
                    mode="Optional" />
                  <field
                    path="ViewQuotedBooking.Booking.JS_CarrierContractNumber"
                    width="250"
                    mode="Optional" />
                  <field
                    path="ViewQuotedBooking.Destination"
                    width="250"
                    mode="Optional" />
                  <field
                    path="ViewQuotedBooking.DischargePort"
                    width="250"
                    mode="Optional" />
                  <field
                    path="ViewQuotedBooking.LoadPort"
                    width="250"
                    mode="Optional" />
                  <field
                    path="ViewQuotedBooking.Origin"
                    width="250"
                    mode="Optional" />
                  <field
                    path="ViewQuotedBooking.Booking.DocsAndCartageDetail.Parent.OrderReferences"
                    width="250"
                    mode="Optional" />
                  <field
                    path="ViewQuotedBooking.TEUContainerCount"
                    width="70"
                    mode="Optional" />
                  <field
                    path="ViewQuotedBooking.Vessel"
                    width="250"
                    mode="Optional" />
                  <field
                    path="ViewQuotedBooking.Voyage"
                    width="250"
                    mode="Optional" />
                  <field
                    path="ViewQuotedBooking.VB_SystemCreateUser"
                    width="160"
                    mode="Optional" />
                  <field
                    path="BookingCalculatedField.JSBV_PickupClose"
                    width="250"
                    mode="Optional" />
                  <field
                    path="ViewQuotedBooking.Client"
                    width="300"
                    mode="Optional" />
                </fields>
              </xml>
            </placeholder>
          </control>
        </control>
        <control
          code="TAB"
          id="5b94c225-edb3-4104-afd6-98bb95f61427"
          binding="">
          <placeholder
            name="Caption"
            value="Tariffs &amp; Rates"
            resid="63d8d60e-64d9-4804-9ff9-4de8515f9aa2" />
          <placeholder
            name="VisibilityCondition"
            value="%.CCAIsRatingDateConfigurationVisible" />
        </control>
        <control
          code="TAI"
          id="a45e4043-76ee-4310-90e8-b6b230470b4d"
          binding="">
          <placeholder
            name="VisibilityCondition"
            value="%.CCAIsRatingDateConfigurationVisible" />
          <control
            code="BOX"
            id="088fb53b-c58a-4d65-9bec-a5f71f42957e">
            <placeholder
              name="Layout"
              value="grid" />
            <control
              code="CMB"
              id="39989a28-2d3d-45cd-8143-7c47dd2481d2"
              binding="RCT_AutoratingDateFiltering">
              <placeholder
                name="Columns"
                value="col-6 col-lg-4" />
              <placeholder
                name="CaptionType"
                value="medium" />
            </control>
          </control>
          <control
            code="RDT"
            id="a0c6afcc-0cc7-46cd-a042-134fb6610fe9"
            binding="ChargeGroupContractRatingDates">
            <placeholder
              name="VisibilityCondition"
              value="RCT_AutoratingDateFiltering == &quot;CUS&quot;" />
            <placeholder
              name="Style"
              value="min-height:300px;" />
            <placeholder
              name="AllowAdd"
              value="True" />
            <placeholder
              name="InlineEdit"
              value="cell" />
            <placeholder
              name="FitToHeight"
              value="True" />
            <placeholder
              name="ShowCustomize"
              value="True" />
          </control>
        </control>
      </control>
    </control>
  </form>
