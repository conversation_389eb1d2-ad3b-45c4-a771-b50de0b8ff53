#transformationVersion: 70.0
#
VZ_PK: a5097afaa3b5487fbc08641e8bbe71db
VZ_ConfigurationKey: a5097afa-a3b5-487f-bc08-641e8bbe71db
VZ_FormID: NEO - CYP - View Release Order
VZ_Caption:
  resKey: VZ_Caption|a5097afaa3b5487fbc08641e8bbe71db
  text: View Release Order
VZ_FormFactor: DSK
VZ_EntityType: ICYDReleaseAdvice
VZ_FormType: FRM
VZ_RequiresLogin: true
VZ_MaterialDesign: true
VZ_Dependencies: <dependencies xmlns="http://cargowise.com/glow/2014/11/14/dependencies.xsd" />
VZ_FormData: >-
  <form
    id="bf26aafe-f5cb-450a-a358-acc330d918c4" xmlns="http://wisetechglobal.com/glow/2017/05/26/form.xsd">
    <control
      code="BOX"
      id="b162b600-153b-419b-b343-b6dcbd54ed96">
      <placeholder
        name="Layout"
        value="grid" />
      <control
        code="PNL"
        id="a1ef28fc-4b8e-4a46-80ef-c4a21903f425">
        <placeholder
          name="Columns"
          value="col-md-3 col-lg-3 col-xl-3" />
        <placeholder
          name="Caption"
          value="Release Order"
          resid="7d3f8467-d9b1-4f2a-a954-abaf46d8bd00" />
        <placeholder
          name="Layout"
          value="grid" />
        <control
          code="SRC"
          id="930ea70d-e534-450e-a6b5-f3ba9bd3cfe8"
          binding="YRE_WW_Yard" />
        <control
          code="TXT"
          id="ba04b5fc-4ade-4ee4-8a39-fe92f34da58f"
          binding="AddressTypeBKD.Address.OrgHeader.OH_FullName">
          <placeholder
            name="CaptionOverride"
            value="Client"
            resid="ada0a358-d30f-4709-8b7f-0d32ac37d9a6" />
        </control>
        <control
          code="TXT"
          id="96ade03f-a33b-4b41-9636-9f23208b7cad"
          binding="AddressTypeSCP.Address.OrgHeader.OH_FullName">
          <placeholder
            name="CaptionOverride"
            value="Lessee"
            resid="1434290a-e0d4-4d85-9095-080c184d7f3a" />
        </control>
        <control
          code="TXT"
          id="52b6a5c2-3bed-4856-b24b-bc0fa11bdcae"
          binding="YRE_ReleaseNumber">
          <placeholder
            name="Padding"
            value="pb-2" />
        </control>
        <control
          code="DAE"
          id="bbb0da98-4146-481b-8016-d1ee56570d82"
          binding="YRE_FromDate">
          <placeholder
            name="Padding"
            value="pb-2" />
        </control>
        <control
          code="DAE"
          id="1d7a8b2b-073e-4235-856e-717a3ff1a384"
          binding="YRE_ToDate" />
      </control>
      <control
        code="PNL"
        id="6abaaed2-de4d-4746-b7ee-3b640da33cb1">
        <placeholder
          name="Columns"
          value="col-md-9 col-lg-9 col-xl-9" />
        <control
          code="RDT"
          id="474832c2-2d40-4717-8103-8723bb5e0791"
          binding="CYDReleaseAdviceLines">
          <placeholder
            name="HideItemActions"
            value="True" />
          <placeholder
            name="ShowGrouping"
            value="True" />
          <placeholder
            name="FieldConfiguration">
            <xml>
              <fields xmlns="">
                <field
                  path="UnitID"
                  width="250"
                  mode="Mandatory" />
                <field
                  path="YEL_Quantity"
                  width="80"
                  mode="Mandatory" />
                <field
                  path="AllocatedToPickupButNotGatedOutQuantity"
                  width="250"
                  mode="Mandatory" />
                <field
                  path="AvailableToPickupQuantity"
                  width="250"
                  mode="Mandatory" />
                <field
                  path="QuantityOut"
                  width="120"
                  mode="Mandatory" />
                <field
                  path="BalanceQuantity"
                  width="160"
                  mode="Mandatory" />
                <field
                  path="YEL_Type"
                  width="120"
                  mode="Mandatory" />
                <field
                  path="YEL_RC_ContainerType"
                  width="250"
                  mode="Mandatory" />
                <field
                  path="YEL_OnHireDate"
                  width="120"
                  mode="Optional" />
                <field
                  path="YEL_OffHireDate"
                  width="130"
                  mode="Optional" />
                <field
                  path="YEL_PickupDate"
                  width="130"
                  mode="Mandatory" />
                <field
                  path="YEL_ReadyDate"
                  width="130"
                  mode="Mandatory" />
                <field
                  path="IISOType"
                  width="250"
                  mode="Mandatory" />
                <field
                  path="Size"
                  width="250"
                  mode="Mandatory" />
                <field
                  path="Empty"
                  width="70"
                  mode="Optional" />
                <field
                  path="ManufactureDate"
                  width="160"
                  mode="Optional" />
                <field
                  path="YardInDateTime"
                  width="180"
                  mode="Optional" />
                <field
                  path="YardOutDateTime"
                  width="180"
                  mode="Optional" />
                <field
                  path="YardOutTPUId"
                  width="250"
                  mode="Optional" />
                <field
                  path="YardInTPUId"
                  width="250"
                  mode="Optional" />
                <field
                  path="DaysInYard"
                  width="120"
                  mode="Optional" />
              </fields>
            </xml>
          </placeholder>
          <placeholder
            name="CaptionOverride"
            value="Units"
            resid="2f15cd58-c6a4-445c-900f-bd7898b2011f" />
        </control>
      </control>
    </control>
  </form>
