#transformationVersion: 70.0
#
VZ_PK: b5873fab317242a9bfc2be2f4eceff7b
VZ_ConfigurationKey: b5873fab-3172-42a9-bfc2-be2f4eceff7b
VZ_FormID: HRM - Configuration - Edit Entitlement Rule
VZ_Caption:
  resKey: VZ_Caption|b5873fab-3172-42a9-bfc2-be2f4eceff7b
  text: Entitlement Rule
VZ_FormFactor: DSK
VZ_EntityType: IHrlEntitlement
VZ_FormType: FRM
VZ_RequiresLogin: true
VZ_MaterialDesign: true
VZ_Dependencies: <dependencies xmlns="http://cargowise.com/glow/2014/11/14/dependencies.xsd" />
VZ_FormData: >-
  <form
    id="62410217-b8b1-4bac-be42-1a63118e2125" xmlns="http://wisetechglobal.com/glow/2017/05/26/form.xsd">
    <control
      code="PNL"
      id="d745c583-8056-4605-b247-e983b24b42f8">
      <placeholder
        name="VisibilityCondition"
        value="IsAttachedToABenefit" />
      <control
        code="CLO"
        id="d9becde2-1291-4c06-aa50-eca7148f19b1">
        <placeholder
          name="Sentiment"
          value="info" />
        <placeholder
          name="Description"
          value="This Leave Entitlement rule cannot be edited as it is already attached to a benefit."
          resid="2d49e5f1-fcfe-4a76-9563-b26a743dae26" />
      </control>
    </control>
    <control
      code="BOX"
      id="14bb7dc1-18e6-4cab-ae83-f761005df765">
      <control
        code="PNL"
        id="57a140ab-21d5-42b7-a3a7-d1dc11559893"
        binding="">
        <placeholder
          name="Caption"
          value="Leave Entitlement"
          resid="6b1893b7-7cc5-48aa-88b0-e0a79397a87c" />
        <placeholder
          name="MaxWidth"
          value="1100" />
        <placeholder
          name="Margin"
          value="mx-auto mt-4" />
        <control
          code="TXT"
          id="67477c98-17ca-4bec-95e6-a6a1684db902"
          binding="LPE_Name">
          <placeholder
            name="Padding"
            value="py-2" />
        </control>
        <control
          code="CMB"
          id="367f1e61-e8e8-49b2-a6f6-6d8accfd3d27"
          binding="LPE_Type">
          <placeholder
            name="Padding"
            value="py-2" />
        </control>
        <control
          code="BOX"
          id="5ee5fcec-9b4f-4a5b-8346-e713b2127d32"
          binding="">
          <placeholder
            name="Columns"
            value="col-1 col-sm-1 col-md-1 col-lg-1 col-xl-1" />
          <placeholder
            name="VisibilityCondition"
            value="LPE_Type == &quot;F&quot;" />
          <placeholder
            name="Padding"
            value="py-2" />
          <control
            code="LBL"
            id="5e6857e8-b409-4a36-b360-a62f98a4e0fd">
            <placeholder
              name="Caption"
              value="Default Leave Entitlement"
              resid="090d51da-1142-4b1e-8d9b-ad43a234624c" />
            <placeholder
              name="Typography"
              value="body" />
          </control>
          <control
            code="BOX"
            id="3329cf5d-9ec7-48ea-b118-f4fad1fc0bd4"
            binding="">
            <placeholder
              name="Layout"
              value="grid" />
            <control
              code="NUM"
              id="d3cb1d75-23f7-4523-8f6a-b2c86e40fff7"
              binding="LPE_Entitlement">
              <placeholder
                name="CaptionOverride"
                value="This is the value of the entitlement the staff member receives over the duration of the leave year"
                resid="c6d329d2-a509-4e85-ac1f-4c73cb3359f5" />
              <placeholder
                name="Columns"
                value="col-8" />
            </control>
            <control
              code="CMB"
              id="fcfc2d5d-9a17-488c-84b9-1fb4104c0702"
              binding="LPE_EntitlementTimeUnit">
              <placeholder
                name="CaptionOverride"
                value="Unit"
                resid="06ce30d0-3ed7-4be1-bbd4-fb69efd7c5fc" />
              <placeholder
                name="Columns"
                value="col-4" />
            </control>
          </control>
        </control>
        <control
          code="RDT"
          id="5cc645ac-51db-40a6-8b84-43637030e572"
          binding="HrlEntitlementTenures">
          <placeholder
            name="AllowAdd"
            value="True" />
          <placeholder
            name="CaptionOverride"
            value="The first period starts from the beginning of the leave year as defined in the accrual rule. From the final period, the entitlement value does not change."
            resid="20ffbc8f-f9a9-4327-ae37-4b1543433ce8" />
          <placeholder
            name="VisibilityCondition"
            value="LPE_Type == &quot;P&quot;" />
          <placeholder
            name="CaptionTypography"
            value="label" />
          <placeholder
            name="FieldConfiguration">
            <xml>
              <fields xmlns="">
                <field
                  path="TenureMonthsFrom"
                  width="90"
                  mode="Default" />
                <field
                  path="LPT_TenureMonths"
                  width="90"
                  mode="Default" />
                <field
                  path="LPT_EntitlementValue"
                  width="90"
                  mode="Default" />
                <field
                  path="LPT_EntitlementUnit"
                  width="90"
                  mode="Default" />
              </fields>
            </xml>
          </placeholder>
          <placeholder
            name="InlineEdit"
            value="cell" />
          <placeholder
            name="ShowCustomize"
            value="True" />
          <placeholder
            name="DefaultSortFields">
            <xml>
              <ArrayOfFieldSortDefinition xmlns:i="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://schemas.datacontract.org/2004/07/CargoWise.Glow.Infrastructure.Interfaces">
                <FieldSortDefinition>
                  <FieldName>LPT_TenureMonths</FieldName>
                  <IsAscending>true</IsAscending>
                </FieldSortDefinition>
              </ArrayOfFieldSortDefinition>
            </xml>
          </placeholder>
          <placeholder
            name="ShowSelect"
            value="False" />
          <placeholder
            name="ItemsPerPage"
            value="100" />
        </control>
        <control
          code="BOX"
          id="593dde66-4b0d-4d70-9eb1-a42e66eb9e35">
          <placeholder
            name="Padding"
            value="py-2" />
          <control
            code="LBL"
            id="4dbafa23-a2be-4e17-a60f-bbb309488d42">
            <placeholder
              name="Caption"
              value="Maximum Balance (optional)"
              resid="f8bc2436-d8a2-4c20-abae-67f4237dbb4a" />
            <placeholder
              name="Typography"
              value="body" />
          </control>
          <control
            code="BOX"
            id="51be2341-a869-4df5-a90e-e5865062b771">
            <control
              code="LBL"
              id="a8e0065e-7c35-4342-aa52-a3f759e488d3">
              <placeholder
                name="Caption"
                value="When a balance reaches this limit, no more leave will be granted or accrued. Leave blank if there is no maximum balance."
                resid="35f57feb-b981-4636-8e0b-c567c283c4d8" />
              <placeholder
                name="Typography"
                value="label" />
            </control>
          </control>
          <control
            code="BOX"
            id="f8aae769-e4a6-4680-a85d-9a6218184d05"
            binding="">
            <placeholder
              name="Layout"
              value="grid" />
            <control
              code="NUM"
              id="e7f818f4-6d43-4b1c-8feb-259267f04067"
              binding="LPE_MaximumEntitlement">
              <placeholder
                name="CaptionOverride"
                value="Value"
                resid="c7ceeb79-d503-47d1-a42a-a0a291560a33" />
              <placeholder
                name="Columns"
                value="col-8" />
            </control>
            <control
              code="CMB"
              id="6421d82b-bf14-4189-b532-e24c3b34e226"
              binding="LPE_MaximumEntitlementTimeUnit">
              <placeholder
                name="CaptionOverride"
                value="Unit"
                resid="e942c366-fbdb-4546-a6d8-56f75d663f36" />
              <placeholder
                name="Columns"
                value="col-4" />
            </control>
          </control>
        </control>
      </control>
    </control>
  </form>
