#transformationVersion: 70.0
#
VZ_PK: 09cb04bc6d6d40fb868f5255260a63a0
VZ_ConfigurationKey: 09cb04bc-6d6d-40fb-868f-5255260a63a0
VZ_FormID: GDM - Gate Out
VZ_Caption:
  resKey: Caption|09cb04bc-6d6d-40fb-868f-5255260a63a0
  text: Gate Out Form
VZ_FormFactor: DSK
VZ_EntityType: IGteVehicleMovement
VZ_FormType: FRM
VZ_RequiresLogin: true
VZ_MaterialDesign: true
VZ_Dependencies: <dependencies xmlns="http://cargowise.com/glow/2014/11/14/dependencies.xsd" />
VZ_StatusBindingPath: Status
VZ_EntityStatusDisplayMode: ReadOnly
VZ_FormData: >-
  <form
    id="b422810d-07c9-4789-977b-03ceae0ac055" xmlns="http://wisetechglobal.com/glow/2017/05/26/form.xsd">
    <placeholder
      name="NoGutters"
      value="True" />
    <placeholder
      name="Align"
      value="left" />
    <placeholder
      name="Dense"
      value="True" />
    <placeholder
      name="Layout"
      value="flex" />
    <placeholder
      name="FlexDirection"
      value="flex-column" />
    <control
      code="BOX"
      id="0e0f018a-ee7f-4870-b6a3-00c136f5c50d"
      binding="">
      <placeholder
        name="Layout"
        value="grid" />
      <placeholder
        name="Margin"
        value="mt-2" />
      <placeholder
        name="Height"
        value="100%" />
      <control
        code="BOX"
        id="631b1011-958b-452c-96b6-93d67b56e26b"
        binding="">
        <placeholder
          name="Margin"
          value="mr-4" />
        <placeholder
          name="Height"
          value="100%" />
        <placeholder
          name="Columns"
          value="col-3" />
        <control
          code="PNL"
          id="7add9b6c-b581-413a-ac40-3f465d054a4e"
          binding="">
          <placeholder
            name="Caption"
            value="Transport details"
            resid="212f9fd8-d1eb-49ea-8a5c-50816d031d63" />
          <control
            code="BOX"
            id="200171f2-4d9b-4ac5-a916-b945757216dd">
            <placeholder
              name="Layout"
              value="grid" />
            <placeholder
              name="Margin"
              value="mb-2" />
            <control
              code="TXT"
              id="4a48b1c7-0e24-46e7-8102-9b84d50dff6f"
              binding="VehicleRegistrationForBinding">
              <placeholder
                name="CaptionOverride"
                value="Vehicle registration"
                resid="a74ac95b-1f8d-4cff-837e-0696baf211f3" />
              <placeholder
                name="Columns"
                value="col-6" />
              <placeholder
                name="Margin"
                value="mb-2" />
              <placeholder
                name="Required"
                value="True" />
            </control>
            <control
              code="SRC"
              id="4fd47c2e-8ccc-41aa-be8a-72fc126c72fe"
              binding="GVM_RC_VehicleType">
              <placeholder
                name="Columns"
                value="col-6" />
              <placeholder
                name="Margin"
                value="mb-2" />
            </control>
            <control
              code="TXT"
              id="9db0de0f-79f4-43be-918a-5858a123fb02"
              binding="TransportProvider">
              <placeholder
                name="Margin"
                value="mb-2" />
              <placeholder
                name="IsReadOnly"
                value="True" />
            </control>
            <control
              code="TXT"
              id="a48bd181-7348-43db-96f3-e0c5eb370ade"
              binding="MainBooking.GBK_ReferenceNumber">
              <placeholder
                name="Columns"
                value="col-6" />
              <placeholder
                name="Margin"
                value="mb-2" />
              <placeholder
                name="CaptionOverride"
                value="Gate booking"
                resid="5c60f9c0-23ff-40f1-9753-1ea643e7e3b1" />
              <placeholder
                name="IsReadOnly"
                value="True" />
            </control>
            <control
              code="CMB"
              id="4592fe1d-a816-459d-a274-88d5955214b2"
              binding="MainBooking.GBK_BookingType">
              <placeholder
                name="Columns"
                value="col-6" />
              <placeholder
                name="Margin"
                value="mb-2" />
              <placeholder
                name="CaptionOverride"
                value="Booking type"
                resid="282aee90-f8c6-4bb8-8067-b22677b481c0" />
              <placeholder
                name="IsReadOnly"
                value="True" />
            </control>
            <control
              code="TXA"
              id="938db762-6b82-42bc-b647-e5d014bc08f7"
              binding="GateReferenceCheckGateOut">
              <placeholder
                name="CaptionOverride"
                value="Gate reference check"
                resid="94150ba7-3fbd-41f7-8623-4abab7885a3c" />
              <placeholder
                name="Rows"
                value="3" />
              <placeholder
                name="Margin"
                value="mb-2" />
              <placeholder
                name="IsReadOnly"
                value="True" />
            </control>
            <control
              code="SRC"
              id="ffa9a84e-407e-42f8-b206-70cb6ae7c964"
              binding="MainBooking.GBK_WW_Facility">
              <placeholder
                name="IsReadOnly"
                value="True" />
              <placeholder
                name="CaptionOverride"
                value="Facility"
                resid="40841b55-3286-46c8-adff-769c9c676b24" />
              <placeholder
                name="Columns"
                value="col-6" />
              <placeholder
                name="Margin"
                value="mb-2" />
            </control>
            <control
              code="SRC"
              id="67267771-e56e-45b8-be12-4c16a1656f89"
              binding="GVM_WL_Location">
              <placeholder
                name="IsReadOnly"
                value="True" />
              <placeholder
                name="Columns"
                value="col-6" />
              <placeholder
                name="Margin"
                value="mb-2" />
            </control>
            <control
              code="DVR"
              id="a5cfe8ad-7ae1-4eaa-a95a-9e5adc56d83d">
              <placeholder
                name="Variant"
                value="solid" />
              <placeholder
                name="Margin"
                value="mb-2" />
            </control>
            <control
              code="BOX"
              id="65671b4b-d5f2-4679-b4be-5a52b74b860e">
              <placeholder
                name="Columns"
                value="col-6" />
              <control
                code="TXT"
                id="24104906-7ff7-4314-94db-43065a1b9ac2"
                binding="GateInActionNumber">
                <placeholder
                  name="Columns"
                  value="col-6" />
                <placeholder
                  name="Margin"
                  value="mb-2" />
                <placeholder
                  name="IsReadOnly"
                  value="True" />
                <placeholder
                  name="CaptionOverride"
                  value="Gate in number"
                  resid="122da777-b35e-440a-b447-0c204e652624" />
              </control>
              <control
                code="SRC"
                id="6dbf4110-537d-4d01-830a-8dd61ea3c307"
                binding="GateInVehicleEntry.GVE_SystemCreateUser">
                <placeholder
                  name="CaptionOverride"
                  value="Entry gate operator"
                  resid="2e13a686-7f41-427e-b068-4d084f9f1913" />
                <placeholder
                  name="Columns"
                  value="col-6" />
                <placeholder
                  name="Margin"
                  value="mb-2" />
                <placeholder
                  name="IsReadOnly"
                  value="True" />
              </control>
              <control
                code="TXT"
                id="596b868a-a542-4fc3-a5af-e9a5e4976a9e"
                binding="GateInVehicleEntry.GVE_DriverName">
                <placeholder
                  name="CaptionOverride"
                  value="Entry driver name"
                  resid="cd0c3cd2-4c41-4c2d-9065-da7da306b168" />
                <placeholder
                  name="Columns"
                  value="col-6" />
                <placeholder
                  name="Margin"
                  value="mb-2" />
                <placeholder
                  name="IsReadOnly"
                  value="True" />
              </control>
              <control
                code="TXT"
                id="cd33a84e-b1be-4f9e-9f13-064203fe568c"
                binding="GateInVehicleEntry.GVE_DriverLicenseNumber">
                <placeholder
                  name="CaptionOverride"
                  value="Entry driver license"
                  resid="c977603b-4542-4990-8f34-a8cae26a8591" />
                <placeholder
                  name="Columns"
                  value="col-6" />
                <placeholder
                  name="Margin"
                  value="mb-2" />
                <placeholder
                  name="IsReadOnly"
                  value="True" />
              </control>
              <control
                code="DTE"
                id="447df760-f165-4ec4-9b03-e3113a60b8b5"
                binding="GateInVehicleEntry.GVE_EntryTime">
                <placeholder
                  name="Columns"
                  value="col-6" />
                <placeholder
                  name="Margin"
                  value="mb-2" />
                <placeholder
                  name="IsReadOnly"
                  value="True" />
                <placeholder
                  name="CaptionOverride"
                  value="Entry date and time"
                  resid="5dea5560-aa92-4f86-9727-2d1b8daebbba" />
              </control>
              <control
                code="MHS"
                id="d33595f0-55ee-4493-8f92-18af6d2f27c1"
                binding="GateInVehicleEntry.GVE_WeightMeasure">
                <placeholder
                  name="CaptionOverride"
                  value="Entry weight"
                  resid="3d035c09-bd14-4f3d-9759-6e030163812d" />
                <placeholder
                  name="Columns"
                  value="col-6" />
                <placeholder
                  name="Margin"
                  value="mb-2" />
                <placeholder
                  name="IsReadOnly"
                  value="True" />
              </control>
              <control
                code="SRC"
                id="e032b759-a617-4e42-9362-13b766b39dc1"
                binding="GateInVehicleEntry.Lane.GLN_GTE_Gate">
                <placeholder
                  name="IsReadOnly"
                  value="True" />
                <placeholder
                  name="CaptionOverride"
                  value="Entry gate"
                  resid="5cb9646e-0892-40c5-82e0-6911658592d2" />
                <placeholder
                  name="Columns"
                  value="col-6" />
                <placeholder
                  name="Margin"
                  value="mb-2" />
              </control>
              <control
                code="SRC"
                id="15633d7e-4350-4cfc-a4a7-787c49c3a23d"
                binding="GateInVehicleEntry.GVE_GLN_Lane">
                <placeholder
                  name="CaptionOverride"
                  value="Entry lane"
                  resid="8dd64fca-2161-4eec-b568-42cb8791a59c" />
                <placeholder
                  name="Columns"
                  value="col-6" />
                <placeholder
                  name="Margin"
                  value="mb-2" />
                <placeholder
                  name="IsReadOnly"
                  value="True" />
              </control>
            </control>
            <control
              code="BOX"
              id="d6614fe1-a259-4033-b0a8-504f98954ff7">
              <placeholder
                name="Columns"
                value="col-6" />
              <control
                code="TXT"
                id="cd5ae445-5803-4bcf-95ac-5885d651047e"
                binding="GateOutActionNumber">
                <placeholder
                  name="Columns"
                  value="col-6" />
                <placeholder
                  name="IsReadOnly"
                  value="True" />
                <placeholder
                  name="Margin"
                  value="mb-2" />
                <placeholder
                  name="CaptionOverride"
                  value="Gate out number"
                  resid="0d46a1ef-74a4-46a3-82f0-6b6d04e9c5f1" />
              </control>
              <control
                code="SRC"
                id="b23275ac-c342-4f1d-979d-67a15ad50ac8"
                binding="GateOutVehicleEntry.GVE_SystemCreateUser">
                <placeholder
                  name="IsReadOnly"
                  value="True" />
                <placeholder
                  name="CaptionOverride"
                  value="Exit gate operator"
                  resid="c4240c3b-29f9-4212-9f49-f74ab1cb4d62" />
                <placeholder
                  name="Columns"
                  value="col-6" />
                <placeholder
                  name="Margin"
                  value="mb-2" />
              </control>
              <control
                code="TXT"
                id="0fe23d7c-6090-450b-a4f7-2d8362ea0b25"
                binding="GateOutVehicleEntry.GVE_DriverName">
                <placeholder
                  name="CaptionOverride"
                  value="Exit driver name"
                  resid="b3a574a6-5de8-49c9-94ba-7cb2e0d390e3" />
                <placeholder
                  name="Columns"
                  value="col-6" />
                <placeholder
                  name="Margin"
                  value="mb-2" />
              </control>
              <control
                code="TXT"
                id="807f62b2-0003-445d-8b5c-9e0f0b6db724"
                binding="GateOutVehicleEntry.GVE_DriverLicenseNumber">
                <placeholder
                  name="CaptionOverride"
                  value="Exit driver license"
                  resid="e84747de-9964-4b77-84c3-3b9a77c06e7d" />
                <placeholder
                  name="Columns"
                  value="col-6" />
                <placeholder
                  name="Margin"
                  value="mb-2" />
              </control>
              <control
                code="DTE"
                id="dc2d3372-7836-4c3c-8485-cd920e01e3e8"
                binding="GateOutVehicleEntry.GVE_EntryTime">
                <placeholder
                  name="CaptionOverride"
                  value="Exit date and time"
                  resid="2f39c9c8-3699-4820-95ab-98a6eedfa1e3" />
                <placeholder
                  name="Columns"
                  value="col-6" />
                <placeholder
                  name="Margin"
                  value="mb-2" />
                <placeholder
                  name="Required"
                  value="True" />
              </control>
              <control
                code="MHS"
                id="51efe325-c12f-4cd0-8371-4d2cd4fb0d51"
                binding="GateOutVehicleEntry.GVE_WeightMeasure">
                <placeholder
                  name="CaptionOverride"
                  value="Exit weight"
                  resid="dd3cf131-a552-494b-98e6-10b16efa7290" />
                <placeholder
                  name="Columns"
                  value="col-6" />
                <placeholder
                  name="Margin"
                  value="mb-2" />
                <placeholder
                  name="Required"
                  value="True" />
              </control>
              <control
                code="SRC"
                id="1475f64c-f61d-489a-8591-7045169a9fa8"
                binding="GateOutVehicleEntry.Lane.GLN_GTE_Gate">
                <placeholder
                  name="CaptionOverride"
                  value="Exit gate"
                  resid="49853519-2eb2-487e-8aad-b8e1bd5b44aa" />
                <placeholder
                  name="Columns"
                  value="col-6" />
                <placeholder
                  name="Margin"
                  value="mb-2" />
                <placeholder
                  name="IsReadOnly"
                  value="True" />
                <placeholder
                  name="VisibilityCondition"
                  value="!Exists" />
              </control>
              <control
                code="SRC"
                id="4dd09f90-4616-427b-9568-67251c32f09b"
                binding="GateOutVehicleEntry.EntryGateCode">
                <placeholder
                  name="CaptionOverride"
                  value="Exit gate"
                  resid="96bf3791-fbd7-4196-b04a-a85015e7bdd0" />
                <placeholder
                  name="Columns"
                  value="col-6" />
                <placeholder
                  name="Margin"
                  value="mb-2" />
                <placeholder
                  name="VisibilityCondition"
                  value="Exists" />
                <placeholder
                  name="Required"
                  value="True" />
              </control>
              <control
                code="SRC"
                id="7c46f545-414c-420f-be65-d6bce2dfb32a"
                binding="GateOutVehicleEntry.GVE_GLN_Lane">
                <placeholder
                  name="CaptionOverride"
                  value="Exit lane"
                  resid="780351b3-d7d2-4c82-ae77-47ad36d8edfb" />
                <placeholder
                  name="Columns"
                  value="col-6" />
                <placeholder
                  name="Margin"
                  value="mb-2" />
                <placeholder
                  name="Required"
                  value="True" />
              </control>
            </control>
          </control>
        </control>
      </control>
      <control
        code="PNL"
        id="4082d82b-5f60-4878-9e7b-fdc79798a07f"
        binding="">
        <placeholder
          name="FitToHeight"
          value="True" />
        <placeholder
          name="Columns"
          value="col-9" />
        <placeholder
          name="Height"
          value="100%" />
        <control
          code="RDT"
          id="79f49ba8-1bce-4e0d-a115-3f5d874e243c"
          binding="GteGateMovements">
          <placeholder
            name="AllowAdd"
            value="False" />
          <placeholder
            name="CaptionOverride"
            value="Associated bookings"
            resid="59ea12f7-8072-4c61-9424-da56d23d8c7d" />
          <placeholder
            name="InlineEdit"
            value="cell" />
          <placeholder
            name="DefaultFilter">
            <xml>
              <ArrayOfFilterGroup xmlns:a="http://schemas.microsoft.com/2003/10/Serialization/Arrays" xmlns:i="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://cargowise.com/glow/2014/07/16/filters.xsd">
                <FilterGroup>
                  <Filters>
                    <Filter>
                      <FilterType>StringFilter</FilterType>
                      <Operation>IsBlank</Operation>
                      <PropertyPath>GGM_CancelledReason</PropertyPath>
                      <Values />
                    </Filter>
                  </Filters>
                  <IsImplicit>true</IsImplicit>
                </FilterGroup>
              </ArrayOfFilterGroup>
            </xml>
          </placeholder>
          <placeholder
            name="FieldConfiguration">
            <xml>
              <fields xmlns="">
                <field
                  path="MovementType"
                  width="150"
                  mode="Default"
                  readOnly="true" />
                <field
                  path="MovementBooking.GBM_SourceReferenceNumber"
                  width="150"
                  mode="Default"
                  readOnly="true" />
                <field
                  path="BookingReferenceNumber"
                  width="150"
                  mode="Default"
                  readOnly="true" />
                <field
                  path="GGM_TransportReference"
                  width="180"
                  mode="Default"
                  readOnly="true" />
                <field
                  path="GGM_RH_NKCargoType"
                  width="220"
                  mode="Default"
                  readOnly="true" />
                <field
                  path="GGM_F3_NKPackageType"
                  width="180"
                  mode="Default"
                  readOnly="true" />
                <field
                  path="GGM_RC_UnitType"
                  width="220"
                  mode="Default"
                  readOnly="true" />
                <field
                  path="GGM_UnitNumber"
                  width="120"
                  mode="Default"
                  readOnly="true" />
              </fields>
            </xml>
          </placeholder>
          <placeholder
            name="AllowAttach"
            value="False" />
          <placeholder
            name="AllowDetach"
            value="False" />
          <placeholder
            name="DisabledGridRowActions"
            value="Remove" />
          <placeholder
            name="ActionMenuItems">
            <xml>
              <formFlows xmlns="">
                <formFlow
                  inDrawer="True">af0773685c154e8f91653ebb54b67ad5</formFlow>
              </formFlows>
            </xml>
          </placeholder>
        </control>
      </control>
    </control>
  </form>
