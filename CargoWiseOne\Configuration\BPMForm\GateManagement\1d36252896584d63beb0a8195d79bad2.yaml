#transformationVersion: 70.0
#
VZ_PK: 1d36252896584d63beb0a8195d79bad2
VZ_ConfigurationKey: 1d362528-9658-4d63-beb0-a8195d79bad2
VZ_FormID: GDM - Administration
VZ_Caption:
  resKey: VZ_Caption|1d362528-9658-4d63-beb0-a8195d79bad2
  text: Administration
VZ_FormFactor: DSK
VZ_FormType: PAG
VZ_RequiresLogin: true
VZ_MaterialDesign: true
VZ_Dependencies: <dependencies xmlns="http://cargowise.com/glow/2014/11/14/dependencies.xsd" />
VZ_FormData: >-
  <form
    id="c30da1a6-70b9-4f2c-9eca-ba7089d8d654" xmlns="http://wisetechglobal.com/glow/2017/05/26/form.xsd">
    <placeholder
      name="Layout"
      value="fill" />
    <control
      code="SDT"
      id="c17d8f2f-b560-4a1b-b4f1-4134a59e045d"
      binding="">
      <placeholder
        name="EntityType"
        value="IGteGate" />
      <placeholder
        name="ShowAutoRefresh"
        value="False" />
      <placeholder
        name="AutoRefresh"
        value="fiveMinutes" />
      <placeholder
        name="EditFormFlowConfiguration">
        <xml>
          <formFlows xmlns="">
            <formFlow>85712f5263114d269e70844d3956c05d</formFlow>
          </formFlows>
        </xml>
      </placeholder>
      <placeholder
        name="NewFormFlowConfiguration">
        <xml>
          <formFlows xmlns="">
            <formFlow>01e323347a404e158dfeb3ff8b60b4f0</formFlow>
          </formFlows>
        </xml>
      </placeholder>
      <placeholder
        name="FullPage"
        value="True" />
      <placeholder
        name="DisabledGridRowActions"
        value="Documents,Remove" />
      <placeholder
        name="CaptionOverride"
        value="Gates"
        resid="fd3c2970-a8b3-43b1-8493-fabb82f3b7fc" />
      <placeholder
        name="NewCaption"
        value="Add new" />
      <placeholder
        name="ShowGrouping"
        value="True" />
      <placeholder
        name="DefaultFilter">
        <xml>
          <ArrayOfFilterGroup xmlns:a="http://schemas.microsoft.com/2003/10/Serialization/Arrays" xmlns:i="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://cargowise.com/glow/2014/07/16/filters.xsd">
            <FilterGroup>
              <Filters>
                <Filter>
                  <FilterType>IsActiveFilter</FilterType>
                  <Operation>Is</Operation>
                  <PropertyPath>GTE_IsActive</PropertyPath>
                  <Values>
                    <a:string>true</a:string>
                  </Values>
                </Filter>
              </Filters>
              <IsImplicit>false</IsImplicit>
            </FilterGroup>
          </ArrayOfFilterGroup>
        </xml>
      </placeholder>
    </control>
  </form>
