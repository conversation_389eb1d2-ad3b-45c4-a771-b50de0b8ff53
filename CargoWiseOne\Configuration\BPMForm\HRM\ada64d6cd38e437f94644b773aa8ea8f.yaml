#transformationVersion: 70.0
#
VZ_PK: ada64d6cd38e437f94644b773aa8ea8f
VZ_ConfigurationKey: ada64d6c-d38e-437f-9464-4b773aa8ea8f
VZ_FormID: HRM - Leave Entitlement Template Selector
VZ_Caption:
  resKey: VZ_Caption|ada64d6cd38e437f94644b773aa8ea8f
  text: New leave entitlement
VZ_FormFactor: DSK
VZ_EntityType: IGlbStaffChangeRequest
VZ_FormType: FRM
VZ_RequiresLogin: true
VZ_MaterialDesign: true
VZ_Dependencies: <dependencies xmlns="http://cargowise.com/glow/2014/11/14/dependencies.xsd" />
VZ_FormData: >-
  <form
    id="2ecf81c0-01f1-4a64-a93d-968478997570" xmlns="http://wisetechglobal.com/glow/2017/05/26/form.xsd">
    <placeholder
      name="Width"
      value="900" />
    <placeholder
      name="FlexWrap"
      value="flex-nowrap" />
    <placeholder
      name="FlexDirection"
      value="flex-column" />
    <control
      code="ESB"
      id="c7a91641-8ecc-43f8-829a-e63de724e8a1">
      <placeholder
        name="EntityType"
        value="IGlbStaffChangeRequestTemplate" />
      <placeholder
        name="Transition"
        value="True" />
      <placeholder
        name="CaptionOverride"
        value="What are you applying for?"
        resid="4d9dde44-0c29-4dc1-8341-f69d4660a9fc" />
      <placeholder
        name="DefaultFilter">
        <xml>
          <ArrayOfFilterGroup xmlns:a="http://schemas.microsoft.com/2003/10/Serialization/Arrays" xmlns:i="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://cargowise.com/glow/2014/07/16/filters.xsd">
            <FilterGroup>
              <Filters>
                <Filter>
                  <FilterType>SimpleLookupFilter</FilterType>
                  <Operation>Is</Operation>
                  <PropertyPath>GlbStaffChangeRequestTemplateItems.GGI_PropertyName</PropertyPath>
                  <Values>
                    <a:string>BOD</a:string>
                  </Values>
                </Filter>
              </Filters>
              <IsImplicit>true</IsImplicit>
            </FilterGroup>
          </ArrayOfFilterGroup>
        </xml>
      </placeholder>
      <placeholder
        name="Margin"
        value="mb-4" />
    </control>
    <control
      code="ERR"
      id="146aef13-ad9b-47d8-90f3-d4f323e3c84c">
      <placeholder
        name="Caption"
        value="Some of the required documents are missing for this Leave Entitlement Request"
        resid="e546395c-4da2-4684-aa91-bc208ace871c" />
      <placeholder
        name="VisibilityCondition"
        value="SelectedTemplateValidationResult == &quot;DOC&quot;" />
    </control>
    <control
      code="RTF"
      id="7ecc1bd3-462b-45ad-bfac-0cd68b14569a"
      binding="LeaveInstructions">
      <placeholder
        name="IsReadOnly"
        value="True" />
      <placeholder
        name="CaptionType"
        value="none" />
      <placeholder
        name="Height"
        value="150" />
      <placeholder
        name="VisibilityCondition"
        value="Template != null &amp;&amp; EffectiveDateIsSpecified" />
      <placeholder
        name="Toolbar"
        value="none" />
      <placeholder
        name="Margin"
        value="mt-2" />
    </control>
    <control
      code="DAE"
      id="4d93d83c-e267-44b7-aa0e-b46d7472f0c2"
      binding="EffectiveDate">
      <placeholder
        name="CaptionOverride"
        value="What is the date of the event?"
        resid="c1532457-c724-42c8-8833-965c00abea92" />
      <placeholder
        name="VisibilityCondition"
        value="Template != null" />
      <placeholder
        name="Required"
        value="True" />
      <placeholder
        name="Margin"
        value="mt-4" />
    </control>
    <control
      code="CMB"
      id="56900514-d84e-4ed4-a4c2-fb8c053ece0e"
      binding="Template.GlbStaffChangeRequestTemplateItems/GGI_SubType">
      <placeholder
        name="IsReadOnly"
        value="True" />
      <placeholder
        name="Required"
        value="False" />
      <placeholder
        name="Margin"
        value="mt-2" />
      <placeholder
        name="CaptionOverride"
        value="Leave Type"
        resid="8057dcfb-3dbe-493a-b54e-c1b57efef58c" />
      <placeholder
        name="VisibilityCondition"
        value="Template != null" />
    </control>
    <control
      code="BOX"
      id="490eca5a-6603-4569-b272-6335c21abd05">
      <placeholder
        name="Margin"
        value="mt-2" />
      <placeholder
        name="Layout"
        value="grid" />
      <control
        code="TXT"
        id="2bff7a3b-26c9-48c4-960a-3585ead01604"
        binding="MaximumLeaveEntitlement">
        <placeholder
          name="CaptionOverride"
          value="Estimated maximum leave entitlement"
          resid="3b518d87-8ff3-41f0-b98b-03202eb80ea5" />
        <placeholder
          name="VisibilityCondition"
          value="Template != null &amp;&amp; EffectiveDateIsSpecified" />
        <placeholder
          name="Columns"
          value="col-6" />
      </control>
      <control
        code="DAE"
        id="3e68dedc-cdf3-4e7b-b1d0-6944a142eae9"
        binding="LeaveEarliestEffectiveDate">
        <placeholder
          name="Columns"
          value="col-6" />
        <placeholder
          name="IsReadOnly"
          value="True" />
        <placeholder
          name="CaptionOverride"
          value="Estimated earliest application date"
          resid="4b9c1299-2fc2-4b30-8c45-886ba401c710" />
        <placeholder
          name="VisibilityCondition"
          value="Template != null &amp;&amp; EffectiveDateIsSpecified" />
      </control>
    </control>
    <control
      code="TBS"
      id="efcb6501-1a76-42b2-92d0-0adb4b68d122">
      <placeholder
        name="Margin"
        value="mt-2" />
      <placeholder
        name="VisibilityCondition"
        value="Template != null" />
      <control
        code="TAB"
        id="4294d355-3977-419f-a8db-8a1957eb06e5">
        <placeholder
          name="Caption"
          value="Entitlement"
          resid="d4f38969-1da7-48c3-95a7-128f1f97f32e" />
      </control>
      <control
        code="TAI"
        id="921e272e-708b-4add-860f-b4e2a187efc4">
        <control
          code="TXT"
          id="e0421103-b4f8-49df-aebe-c9d2f8bc52f0"
          binding="HrlBenefitOnDemands/LBD_Comment">
          <placeholder
            name="Margin"
            value="mt-2" />
          <placeholder
            name="Required"
            value="False" />
          <placeholder
            name="CaptionOverride"
            value="Please describe the reason you are applying for this leave"
            resid="9389c8f2-0ac6-4427-9b0e-820564eafdcc" />
        </control>
        <control
          code="BOX"
          id="b956874c-cdce-4094-b7a5-dd8c63a159d8">
          <placeholder
            name="Layout"
            value="grid" />
          <placeholder
            name="Margin"
            value="mt-2" />
          <control
            code="NUM"
            id="acfa1d89-eec0-447d-9875-7465152b311d"
            binding="HrlBenefitOnDemands/LBD_Duration">
            <placeholder
              name="Required"
              value="True" />
            <placeholder
              name="CaptionOverride"
              value="How much are you applying for?"
              resid="eff69872-c9f6-42e3-8e57-4dc833517b7e" />
            <placeholder
              name="Columns"
              value="col-sm-6" />
          </control>
          <control
            code="CMB"
            id="a84039cb-6734-44d0-93b4-bfd773242236"
            binding="HrlBenefitOnDemands/LBD_DurationUnit">
            <placeholder
              name="Required"
              value="True" />
            <placeholder
              name="Margin"
              value="mt-5" />
            <placeholder
              name="CaptionType"
              value="none" />
            <placeholder
              name="Columns"
              value="col-sm-6" />
          </control>
        </control>
        <control
          code="OPT"
          id="886777ed-e53e-4926-bbdf-e6d9cdfc8e7b"
          binding="HrlBenefitOnDemands/LBD_IncludesStatutoryLeave">
          <placeholder
            name="CaptionOverride"
            value="If applicable in your jurisdiction, please confirm whether you plan to receive some or all of this leave via social security"
            resid="99216183-558a-41f9-9932-bd3a03431056" />
          <placeholder
            name="Margin"
            value="mt-1" />
        </control>
      </control>
      <control
        code="TAB"
        id="d989fcc2-35eb-45b4-a444-f161b35c654c">
        <placeholder
          name="Caption"
          value="Documents"
          resid="12060ee1-5fca-4f5e-ae73-45ce55fd2b17" />
      </control>
      <control
        code="TAI"
        id="bdc42bbf-2e78-4997-9de5-b4d4b3c55fea">
        <control
          code="RLS"
          id="3d1f61e3-8e71-4fc3-92a3-8fcd53bde1d6"
          binding="Template.GlbStaffChangeRequestTemplateDocuments">
          <placeholder
            name="HideDefaultFooter"
            value="True" />
          <placeholder
            name="HideItemActions"
            value="True" />
          <placeholder
            name="IsReadOnly"
            value="True" />
          <placeholder
            name="CaptionOverride"
            value="Required documents"
            resid="ec0ca79c-a155-468d-987c-7678fe0179b9" />
          <placeholder
            name="ContentTemplateID"
            value="a1fec9c2cf8343ceb9a49d7a0f67eb58" />
          <placeholder
            name="Caption"
            value="Required documents"
            resid="bf878768-3a63-4110-a26b-7e08118700f5" />
          <placeholder
            name="Margin"
            value="mt-2" />
        </control>
        <control
          code="PNL"
          id="301e4a54-33af-4d8a-bf5b-686024d763b3">
          <placeholder
            name="Margin"
            value="mt-4" />
          <placeholder
            name="VisibilityCondition"
            value="Template.GlbStaffChangeRequestTemplateDocuments.Any()" />
          <control
            code="FIL"
            id="c775d68e-0437-4816-81c3-f7bdb44e6159" />
        </control>
      </control>
    </control>
  </form>
