#transformationVersion: 70.0
#
VZ_PK: 6ae9e4c56c1749368d293262d11b396e
VZ_ConfigurationKey: 6ae9e4c5-6c17-4936-8d29-3262d11b396e
VZ_FormID: HRM - My Change Requests
VZ_Caption:
  resKey: VZ_Caption|6ae9e4c5-6c17-4936-8d29-3262d11b396e
  text: My Change Requests
VZ_FormFactor: DSK
VZ_FormType: PAG
VZ_RequiresLogin: true
VZ_MaterialDesign: true
VZ_Dependencies: <dependencies xmlns="http://cargowise.com/glow/2014/11/14/dependencies.xsd" />
VZ_FormData: >-
  <form
    id="48b2a014-bbd8-4683-8ab9-ede5c290f81e" xmlns="http://wisetechglobal.com/glow/2017/05/26/form.xsd">
    <control
      code="SDT"
      id="18bb0bd0-bf1e-4f2b-8c0d-f7c8d82c354f"
      binding="">
      <placeholder
        name="EntityType"
        value="IGlbStaffChangeRequest" />
      <placeholder
        name="DefaultFilter">
        <xml>
          <ArrayOfFilterGroup xmlns:a="http://schemas.microsoft.com/2003/10/Serialization/Arrays" xmlns:i="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://cargowise.com/glow/2014/07/16/filters.xsd">
            <FilterGroup>
              <Filters>
                <Filter>
                  <FilterType>StringFilter</FilterType>
                  <Operation>Is</Operation>
                  <PropertyPath>GCR_Status</PropertyPath>
                  <Values>
                    <a:string>REQ</a:string>
                  </Values>
                </Filter>
              </Filters>
              <IsImplicit>false</IsImplicit>
            </FilterGroup>
            <FilterGroup>
              <Filters>
                <Filter>
                  <FilterType>StringFilter</FilterType>
                  <Operation>Is</Operation>
                  <PropertyPath>CreatedByStaff.GS_Code</PropertyPath>
                  <Values>
                    <a:string>&lt;%.CurrentStaffCode&gt;</a:string>
                  </Values>
                </Filter>
              </Filters>
              <IsImplicit>true</IsImplicit>
            </FilterGroup>
          </ArrayOfFilterGroup>
        </xml>
      </placeholder>
      <placeholder
        name="EditFormFlowConfiguration">
        <xml>
          <formFlows xmlns="">
            <formFlow>1bcace699bec4374add622247289e926</formFlow>
          </formFlows>
        </xml>
      </placeholder>
      <placeholder
        name="FullPage"
        value="True" />
      <placeholder
        name="CaptionOverride"
        value="My Change Requests"
        resid="cf7379f2-43f6-45ef-9cff-f7dae7d321f4" />
      <placeholder
        name="FieldConfiguration">
        <xml>
          <fields xmlns="">
            <field
              path="Staff.GS_FullName"
              width="250"
              mode="Mandatory" />
            <field
              path="CurrentlyAssignedTo"
              width="250"
              mode="Default" />
            <field
              path="GCR_Status"
              width="60"
              mode="Mandatory" />
            <field
              path="EarliestEffectiveDate"
              width="230"
              mode="Default" />
            <field
              path="GCR_GSG_Template"
              width="250"
              mode="Mandatory" />
            <field
              path="GCR_SystemCreateTimeUtc"
              width="220"
              mode="Mandatory" />
          </fields>
        </xml>
      </placeholder>
      <placeholder
        name="DisabledGridRowActions"
        value="Remove" />
    </control>
  </form>
