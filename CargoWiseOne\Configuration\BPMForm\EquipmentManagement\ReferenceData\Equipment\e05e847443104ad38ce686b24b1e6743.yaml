#transformationVersion: 70.0
#
VZ_PK: e05e847443104ad38ce686b24b1e6743
VZ_ConfigurationKey: e05e8474-4310-4ad3-8ce6-86b24b1e6743
VZ_FormID: EQM - Equipment - Search
VZ_Caption:
  resKey: VZ_Caption|e05e847443104ad38ce686b24b1e6743
  text: Equipment
VZ_FormFactor: DSK
VZ_FormType: PAG
VZ_RequiresLogin: true
VZ_MaterialDesign: true
VZ_Dependencies: <dependencies xmlns="http://cargowise.com/glow/2014/11/14/dependencies.xsd" />
VZ_FormData: >-
  <form
    id="ad6306e3-d523-4e65-910e-bede3d80f6db" xmlns="http://wisetechglobal.com/glow/2017/05/26/form.xsd">
    <placeholder
      name="Layout"
      value="fill" />
    <control
      code="SDT"
      id="b132f8d9-d558-4bfe-92c6-190ae57458f4">
      <placeholder
        name="CaptionOverride"
        value="Equipment"
        resid="720f00c7-802b-433b-9aac-c52d23289f8b" />
      <placeholder
        name="ShowSelect"
        value="True" />
      <placeholder
        name="EntityType"
        value="IRefEquipment" />
      <placeholder
        name="ItemsPerPage"
        value="50" />
      <placeholder
        name="SearchMode"
        value="Entity" />
      <placeholder
        name="FullPage"
        value="True" />
      <placeholder
        name="FieldConfiguration">
        <xml>
          <fields xmlns="">
            <field
              path="RQ_ShortCode"
              width="180"
              caption="Equipment code"
              resid="331062ea-859d-4f41-a931-d0efc03c8d6a"
              mode="Mandatory" />
            <field
              path="RQ_Description"
              width="250"
              caption="Equipment description"
              resid="44152697-a8d1-425b-a350-8fcd4f29acf0"
              mode="Mandatory" />
            <field
              path="RQ_Registration"
              width="200"
              caption="Registration No."
              resid="92184761-e2ec-47af-adea-bd1647687154"
              mode="Default" />
            <field
              path="RQ_IsVehicle"
              width="100"
              caption="Vehicle"
              resid="8f357dc5-ccf9-4453-96bf-7bf1b6f34d77"
              mode="Optional" />
            <field
              path="RQ_IsActive"
              width="90"
              caption="Active"
              resid="25122f4a-c574-43b6-a69e-eca7e953cd74"
              mode="Optional" />
            <field
              path="RQ_DisposalDate"
              width="150"
              mode="Optional" />
            <field
              path="RQ_RegExpiry"
              width="160"
              caption="Expiry date"
              resid="e4367ae6-4cf1-4e92-8c44-f150eae7e28a"
              mode="Optional" />
            <field
              path="RQ_PurchaseDate"
              width="150"
              mode="Optional" />
            <field
              path="RQ_CubicCapacity"
              width="100"
              caption="Cubic capacity"
              resid="f1173e8b-2d29-4fc8-bd24-19b1c13f10ad"
              mode="FilterOnly" />
            <field
              path="RQ_PackCapacity"
              width="150"
              caption="Pack capacity"
              resid="553b5c22-8ec1-4671-a9d7-d1098dff86ce"
              mode="Optional" />
            <field
              path="RQ_TareWeight"
              width="100"
              caption="Tare weight"
              resid="214cec08-d3f8-4820-8b51-4b3ef66948fc"
              mode="FilterOnly" />
            <field
              path="RQ_CapacityInTEU"
              width="100"
              mode="Optional" />
            <field
              path="RQ_WeightCapacity"
              width="100"
              caption="Weight capacity"
              resid="681089ec-b8e5-45db-91d0-7378566b5c0f"
              mode="FilterOnly" />
            <field
              path="RoadContainerType.RC_ContainerType"
              width="200"
              caption="Road equipment type"
              resid="919b9c18-1b56-451b-88bb-f17591ee25c2"
              mode="Optional" />
            <field
              path="RoadContainerType.RC_Description"
              width="260"
              caption="Road equipment type description"
              resid="a2f8d894-cb52-46f2-a3ac-959ee8ae0616"
              mode="Optional" />
            <field
              path="RQ_OH_Owner"
              width="180"
              caption="Equipment owner"
              resid="39c7fc48-6d21-4ee8-be78-e56a33470ca5"
              mode="Optional" />
            <field
              path="RQ_CubicUnit"
              width="40"
              caption="Cubic unit"
              resid="ffe02363-cc5f-4c66-8474-d9b1e048c767"
              mode="FilterOnly" />
            <field
              path="RQ_EquipmentGroup"
              width="200"
              caption="Equipment group"
              resid="bf5dda5e-a0ba-41c1-95a9-46948d5cdd77"
              mode="Default" />
            <field
              path="RQ_OwnerType"
              width="120"
              caption="Owner type"
              resid="27ab76dc-9b08-4f49-9138-ce1df32ab119"
              mode="Optional" />
            <field
              path="RQ_2WayInfo"
              width="150"
              caption="Radio serial No."
              resid="ca2e50da-321d-4481-9155-9cd3ceab96bc"
              mode="Optional" />
            <field
              path="RQ_RegState"
              width="100"
              caption="State"
              resid="b98abe8f-58fe-4996-b2e8-c3084b8eb12e"
              mode="Optional" />
            <field
              path="RQ_TollPass"
              width="120"
              caption="Toll pass"
              resid="4efdb235-9e9a-4b86-866a-a3367d29e631"
              mode="Optional" />
            <field
              path="RQ_GateTransponder1"
              width="150"
              mode="Optional" />
            <field
              path="RQ_GateTransponder2"
              width="150"
              mode="Optional" />
            <field
              path="RQ_GateTransponder3"
              width="150"
              mode="Optional" />
            <field
              path="RQ_EquipmentType"
              width="150"
              caption="Equipment type"
              resid="7b1e8f81-5f66-49bf-9558-1bb752f1d7e4"
              mode="Default" />
            <field
              path="RQ_VIN"
              width="200"
              caption="VIN No."
              resid="f3ee2d28-6336-4a86-99fe-17fce4b61288"
              mode="Default" />
            <field
              path="RQ_WeightUnit"
              width="40"
              caption="Weight unit"
              resid="ccfc2620-1b69-44ba-b137-c355daae15c6"
              mode="FilterOnly" />
            <field
              path="RQ_PackType"
              width="150"
              caption="Package type"
              resid="ca5c734b-dcd8-45ff-928d-43ed5845b3ea"
              mode="Optional" />
            <field
              path="RQ_RN_NKRegistrationCountry"
              width="250"
              caption="Registration country / region"
              resid="c5e60ac7-edec-4c9c-a7e4-0c7864d4108d"
              mode="Default" />
            <field
              path="RQ_GS_NKPreferredDriver"
              width="120"
              mode="Optional" />
            <field
              path="RQ_F3_NKPackType"
              width="150"
              caption="Pack type"
              resid="533d7546-9d59-4838-9fae-eded0e89fee1"
              mode="Optional" />
            <field
              path="RQ_GeoProviderID"
              width="180"
              caption="GEO provider id"
              resid="9f8211e2-7cdc-4bb6-8db0-f54530028c0e"
              mode="Optional" />
            <field
              path="RQ_GeoProviderType"
              width="180"
              caption="GEO provider type"
              resid="8ea13e0e-50fa-4ede-8796-47e263b12bea"
              mode="Optional" />
            <field
              path="RQ_CubicCapacityMeasure"
              width="180"
              caption="Cubic capacity"
              resid="c2e440a6-8e19-44f1-9685-e0b8b1721ccd"
              mode="Optional" />
            <field
              path="RQ_TareWeightMeasure"
              width="150"
              caption="Tare weight"
              resid="cce8908e-1c01-4d80-9a59-283144e0ce49"
              mode="Optional" />
            <field
              path="RQ_WeightCapacityMeasure"
              width="180"
              caption="Weight capacity"
              resid="560379e4-75d3-451a-89ba-5ea7193d7066"
              mode="Optional" />
            <field
              path="RQ_DoorOpeningHeight"
              width="200"
              caption="Door opening height"
              resid="964f80c6-2f83-40c9-9790-2d6e888c7f14"
              mode="FilterOnly" />
            <field
              path="RQ_DoorOpeningWidth"
              width="200"
              caption="Door opening width"
              resid="73d6d080-02e6-4978-b2aa-74d948cc2d61"
              mode="FilterOnly" />
            <field
              path="RQ_InsideHeight"
              width="150"
              caption="Inside height"
              resid="a0076a9f-b011-4b3c-9b03-9fd67dceff7a"
              mode="FilterOnly" />
            <field
              path="RQ_InsideLength"
              width="150"
              caption="Inside length"
              resid="e78e51df-2656-4ab2-b1e5-60f22270f4f6"
              mode="FilterOnly" />
            <field
              path="RQ_InsideWidth"
              width="150"
              caption="Inside width"
              resid="857f44fe-e50d-407c-9085-43457ce58381"
              mode="FilterOnly" />
            <field
              path="RQ_OutsideHeight"
              width="150"
              caption="Outside height"
              resid="f1e9b8f4-8ee5-40d9-9475-491736cb5605"
              mode="FilterOnly" />
            <field
              path="RQ_OutsideLength"
              width="150"
              caption="Outside length"
              resid="b92549c5-e74e-436b-8ba1-a0ee88fcd3d4"
              mode="FilterOnly" />
            <field
              path="RQ_OutsideWidth"
              width="150"
              caption="Outside width"
              resid="53d68889-195e-4695-9daa-e5706cc2c8f7"
              mode="FilterOnly" />
            <field
              path="RQ_ReeferHumidityPercentMinimum"
              width="220"
              caption="Humidity percent minimum"
              resid="b2d95ff8-3d28-41f0-b6c2-fe82947095a7"
              mode="Optional" />
            <field
              path="RQ_ReeferHumidityPercentMaximum"
              width="220"
              caption="Humidity percent maximum"
              resid="68fd0e1d-a1b5-4117-b17a-aa48e61d7856"
              mode="Optional" />
            <field
              path="RQ_ReeferTemperatureMinimum"
              width="200"
              caption="Temperature minimum"
              resid="3440988f-df44-42f4-b14b-954c7c971067"
              mode="FilterOnly" />
            <field
              path="RQ_ReeferTemperatureMaximum"
              width="200"
              caption="Temperature maximum"
              resid="80eb0f5d-9c0e-49c2-a8e8-0f4e607615c4"
              mode="FilterOnly" />
            <field
              path="RQ_ReeferVentilationMinimum"
              width="180"
              caption="Ventilation minimum"
              resid="a63fe7b7-a49e-401f-92df-29b00eadf73f"
              mode="FilterOnly" />
            <field
              path="RQ_ReeferVentilationMaximum"
              width="180"
              caption="Ventilation maximum"
              resid="58df1c61-839b-4d15-a20a-11a48560f7d1"
              mode="FilterOnly" />
            <field
              path="RQ_ControlledAtmosphere"
              width="200"
              caption="Controlled atmosphere"
              resid="976ce878-3a9d-49fa-a2c1-149a02f735f2"
              mode="Optional" />
            <field
              path="RQ_DoorOpeningDimensionUQ"
              width="250"
              caption="Door opening dimension unit"
              resid="e33b70b6-23d7-4209-b95d-1730465986c2"
              mode="FilterOnly" />
            <field
              path="RQ_InsideDimensionUQ"
              width="180"
              caption="Inside dimension unit"
              resid="e3dd14b4-9723-43c0-98da-a6b3cc79c56d"
              mode="FilterOnly" />
            <field
              path="RQ_OutsideDimensionUQ"
              width="200"
              caption="Outside dimension unit"
              resid="ab416b4d-b025-401e-b903-0ef341e9f6d7"
              mode="FilterOnly" />
            <field
              path="RQ_ReeferTemperatureUQ"
              width="180"
              caption="Temperature unit"
              resid="b69b812d-ff13-4a8e-a481-767b4e98f684"
              mode="FilterOnly" />
            <field
              path="RQ_ReeferVentilationUQ"
              width="150"
              caption="Ventilation unit"
              resid="bd2c9eea-e4f1-4d9a-9f7c-0eddf28bd4fc"
              mode="FilterOnly" />
            <field
              path="RQ_HasArgonControl"
              width="180"
              caption="Has argon control"
              resid="7a6c84f9-21d6-4a50-bcfc-9c9947a2be93"
              mode="Optional" />
            <field
              path="RQ_HasCarbonDioxideControl"
              width="220"
              caption="Has carbon dioxide control"
              resid="f3dedb1d-7360-44fd-8759-29990c0e0fe9"
              mode="Optional" />
            <field
              path="RQ_HasEthyleneControl"
              width="180"
              caption="Has ethylene control"
              resid="ea059685-80fd-4399-a464-2401aaf1c9fe"
              mode="Optional" />
            <field
              path="RQ_HasNitrogenControl"
              width="180"
              caption="Has nitrogen control"
              resid="8c4ad05d-c915-4f22-bfc0-e392ebe157ad"
              mode="Optional" />
            <field
              path="RQ_HasOxygenControl"
              width="180"
              caption="Has oxygen control"
              resid="8972a298-c152-40d8-bb25-14ac8f177bdd"
              mode="Optional" />
            <field
              path="RQ_IsBulbMode"
              width="120"
              caption="Bulb mode"
              resid="f539e5b0-4d15-4d6c-84d7-833b64a95189"
              mode="Optional" />
            <field
              path="RQ_IsRemoteMonitoring"
              width="180"
              caption="Remote monitoring"
              resid="5cc4ff2c-aae3-447a-9fae-d60e4f98e8b7"
              mode="Optional" />
            <field
              path="RQ_DoorOpeningHeightMeasure"
              width="200"
              caption="Door opening height"
              resid="cf11077f-2b2a-4d48-a9fe-cd6c34900889"
              mode="Optional" />
            <field
              path="RQ_DoorOpeningWidthMeasure"
              width="200"
              caption="Door opening width"
              resid="998ce1c2-65f0-4bbe-90dc-a9c3d101f5a2"
              mode="Optional" />
            <field
              path="RQ_InsideHeightMeasure"
              width="150"
              caption="Inside height"
              resid="363545cf-3040-4746-ab14-4426bbc6da09"
              mode="Optional" />
            <field
              path="RQ_InsideLengthMeasure"
              width="150"
              caption="Inside length"
              resid="70a3c90e-341b-4d97-b663-5532f17909fb"
              mode="Optional" />
            <field
              path="RQ_InsideWidthMeasure"
              width="150"
              caption="Inside width"
              resid="7cf07753-178b-47e4-95e5-a45562b751e3"
              mode="Optional" />
            <field
              path="RQ_OutsideHeightMeasure"
              width="150"
              caption="Outside height"
              resid="dc9587cf-9859-47d8-a455-539569832494"
              mode="Optional" />
            <field
              path="RQ_OutsideLengthMeasure"
              width="150"
              caption="Outside length"
              resid="a3dbdc2a-e4c1-401f-86fb-37c03ff25cb3"
              mode="Optional" />
            <field
              path="RQ_OutsideWidthMeasure"
              width="150"
              caption="Outside width"
              resid="10cd693f-615a-4450-96d0-e9f54699f5d1"
              mode="Optional" />
            <field
              path="ReeferTemperatureMaximumMeasureForDisplay"
              width="200"
              caption="Temperature maximum"
              resid="51c7f6b2-50d9-448a-b883-41a6af52953c"
              mode="Optional" />
            <field
              path="ReeferTemperatureMinimumMeasureForDisplay"
              width="200"
              caption="Temperature minimum"
              resid="9297c056-08d6-4d64-9b92-5ad5953f47a5"
              mode="Optional" />
            <field
              path="RQ_ReeferVentilationMaximumMeasure"
              width="180"
              caption="Ventilation maximum"
              resid="83287b3f-9b3b-4787-8fab-af4baf2a5c8f"
              mode="Optional" />
            <field
              path="RQ_ReeferVentilationMinimumMeasure"
              width="180"
              caption="Ventilation minimum"
              resid="bf5a0166-5bda-44da-b409-04314e6451d2"
              mode="Optional" />
          </fields>
        </xml>
      </placeholder>
      <placeholder
        name="DefaultSortFields">
        <xml>
          <ArrayOfFieldSortDefinition xmlns:i="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://schemas.datacontract.org/2004/07/CargoWise.Glow.Infrastructure.Interfaces">
            <FieldSortDefinition>
              <FieldName>RQ_ShortCode</FieldName>
              <IsAscending>true</IsAscending>
            </FieldSortDefinition>
          </ArrayOfFieldSortDefinition>
        </xml>
      </placeholder>
      <placeholder
        name="ShowAutoRefresh"
        value="True" />
      <placeholder
        name="EditFormFlowConfiguration">
        <xml>
          <formFlows xmlns="">
            <formFlow>1a3e9e609ace433da0a927e5f4483714</formFlow>
          </formFlows>
        </xml>
      </placeholder>
      <placeholder
        name="ShowAddActions"
        value="True" />
      <placeholder
        name="ActionMenuItems">
        <xml>
          <formFlows xmlns="">
            <formFlow>1a3e9e609ace433da0a927e5f4483714</formFlow>
          </formFlows>
        </xml>
      </placeholder>
      <placeholder
        name="NewFormFlowConfiguration">
        <xml>
          <formFlows xmlns="">
            <formFlow
              newSession="True">4ea80bae85b34a4a8c63c1e969b1ef35</formFlow>
          </formFlows>
        </xml>
      </placeholder>
    </control>
  </form>
