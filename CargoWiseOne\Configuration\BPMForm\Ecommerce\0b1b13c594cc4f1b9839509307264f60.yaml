#transformationVersion: 70.0
#
VZ_PK: 0b1b13c594cc4f1b9839509307264f60
VZ_ConfigurationKey: 0b1b13c5-94cc-4f1b-9839-509307264f60
VZ_FormID: ETL - VDV3 - Destination Depot - Scan Item To Package With Labels
VZ_Caption:
  resKey: VZ_Caption|0b1b13c594cc4f1b9839509307264f60
  text: Scan Item To Package With Labels
VZ_FormFactor: DSK
VZ_EntityType: IHVLVItem
VZ_FormType: FRM
VZ_RequiresLogin: true
VZ_Dependencies: >-
  <dependencies xmlns="http://cargowise.com/glow/2014/11/14/dependencies.xsd">
    <expandPath
      path="OuterPackageSelectByThisItem" />
    <expandPath
      path="OuterPackageSelectByThisItem.LastMileCarrier" />
    <expandPath
      path="Consignment" />
    <calculatedProperty
      path="OuterPackageSelectByThisItem" />
    <calculatedProperty
      path="OuterPackageSelectByThisItem.CountTotalItemsLabel" />
    <calculatedProperty
      path="OuterPackageSelectByThisItem.CountHeldItemsLabel" />
    <calculatedProperty
      path="OuterPackageSelectByThisItem.CountClearedItemsLabel" />
    <calculatedProperty
      path="OuterPackageSelectByThisItem.CountSurplusItemsLabel" />
    <calculatedProperty
      path="Consignment.DetailedImportStatusWithBrackets" />
    <calculatedProperty
      path="ReturnReleaseStatus" />
  </dependencies>
VZ_FormData: >-
  <form
    id="c7f253b2-de59-48a2-869f-6f30f85b74b5" xmlns="http://wisetechglobal.com/glow/2017/05/26/form.xsd">
    <placeholder
      name="BackgroundImagePk"
      value="" />
    <placeholder
      name="BackgroundSize"
      value="Auto" />
    <placeholder
      name="BackgroundImageOverride"
      value="" />
    <placeholder
      name="FormLayout"
      value="Splitview" />
    <placeholder
      name="ExpandPaths"
      value="" />
    <placeholder
      name="ScanInputHandling"
      value="NON" />
    <placeholder
      name="RemovePadding"
      value="false" />
    <placeholder
      name="TurnOnPortableOneToOneDesignMode"
      value="false" />
    <placeholder
      name="ContainsDefaultButton"
      value="False" />
    <control
      code="GRP"
      id="3557aea7-a4eb-45fa-86cf-aef1a640d1de"
      top="0"
      width="26"
      height="8">
      <placeholder
        name="CanBeHidden"
        value="True" />
      <placeholder
        name="VisibilityCondition"
        value="9108ed2d-4c17-4c16-a0e7-0d7c35543e0b" />
      <placeholder
        name="Header"
        value="full"
        resid="ce58273e-e8e0-4007-9603-1379c9295fa0" />
      <placeholder
        name="HeaderAlignment"
        value="Center" />
      <placeholder
        name="HeaderDisplayMode"
        value="Hidden" />
      <placeholder
        name="BackgroundImagePk"
        value="" />
      <placeholder
        name="BackgroundImageOverride"
        value="" />
      <control
        code="LBL"
        id="a57b57cd-a8ec-495b-ae4a-f6624bc9c2c5"
        left="10"
        top="3"
        width="6"
        height="1"
        binding="OuterPackageSelectByThisItem.CountTotalItemsLabel">
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="LabelType"
          value="Notification" />
        <placeholder
          name="LabelNotificationType"
          value="Information" />
        <placeholder
          name="ForegroundThemeColor"
          value="GeneralText" />
        <placeholder
          name="TextSize"
          value="Medium" />
        <placeholder
          name="FontWeight"
          value="Normal" />
        <placeholder
          name="LabelAlignment"
          value="Center" />
        <placeholder
          name="BackgroundColor"
          value="" />
        <placeholder
          name="Padding"
          value="None" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="LBL"
        id="d234f93e-1113-4f30-b5dc-80675064bc4e"
        left="10"
        top="4"
        width="6"
        height="1"
        binding="OuterPackageSelectByThisItem.CountHeldItemsLabel">
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="LabelType"
          value="Notification" />
        <placeholder
          name="LabelNotificationType"
          value="Error" />
        <placeholder
          name="ForegroundThemeColor"
          value="GeneralText" />
        <placeholder
          name="TextSize"
          value="Medium" />
        <placeholder
          name="FontWeight"
          value="Normal" />
        <placeholder
          name="LabelAlignment"
          value="Center" />
        <placeholder
          name="BackgroundColor"
          value="" />
        <placeholder
          name="Padding"
          value="None" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="LBL"
        id="781c9c82-49e8-4756-9c58-b24eca8c636d"
        left="10"
        top="5"
        width="6"
        height="1"
        binding="OuterPackageSelectByThisItem.CountClearedItemsLabel">
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="LabelType"
          value="Notification" />
        <placeholder
          name="LabelNotificationType"
          value="Success" />
        <placeholder
          name="ForegroundThemeColor"
          value="GeneralText" />
        <placeholder
          name="TextSize"
          value="Medium" />
        <placeholder
          name="FontWeight"
          value="Normal" />
        <placeholder
          name="LabelAlignment"
          value="Center" />
        <placeholder
          name="BackgroundColor"
          value="" />
        <placeholder
          name="Padding"
          value="None" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="LBL"
        id="10bbcc69-da39-4e55-823b-a71af22b7293"
        left="10"
        top="6"
        width="6"
        height="1"
        binding="OuterPackageSelectByThisItem.CountSurplusItemsLabel">
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="LabelType"
          value="Notification" />
        <placeholder
          name="LabelNotificationType"
          value="Warning" />
        <placeholder
          name="ForegroundThemeColor"
          value="GeneralText" />
        <placeholder
          name="TextSize"
          value="Medium" />
        <placeholder
          name="FontWeight"
          value="Normal" />
        <placeholder
          name="LabelAlignment"
          value="Center" />
        <placeholder
          name="BackgroundColor"
          value="" />
        <placeholder
          name="Padding"
          value="None" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
    </control>
    <control
      code="GRP"
      id="431c7191-e08b-4ae1-b100-d036f9e15696"
      top="0"
      width="26"
      height="3">
      <placeholder
        name="CanBeHidden"
        value="True" />
      <placeholder
        name="VisibilityCondition"
        value="313c748d-fe27-41c3-961f-9d0e398b255d" />
      <placeholder
        name="Header"
        value="two"
        resid="6049de64-4336-4ef2-a83b-cc9d7818c1ce" />
      <placeholder
        name="HeaderAlignment"
        value="Left" />
      <placeholder
        name="HeaderDisplayMode"
        value="Hidden" />
      <placeholder
        name="BackgroundImagePk"
        value="" />
      <placeholder
        name="BackgroundImageOverride"
        value="" />
      <control
        code="LBL"
        id="6f2df8c7-bf90-4314-b1a1-73f7c83178a7"
        left="5"
        top="0"
        width="16"
        height="1"
        binding="OuterPackageSelectByThisItem.HVO_PackageBarcode">
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="LabelType"
          value="Notification" />
        <placeholder
          name="LabelNotificationType"
          value="Information" />
        <placeholder
          name="ForegroundThemeColor"
          value="GeneralText" />
        <placeholder
          name="TextSize"
          value="Large" />
        <placeholder
          name="FontWeight"
          value="Normal" />
        <placeholder
          name="LabelAlignment"
          value="Center" />
        <placeholder
          name="BackgroundColor"
          value="" />
        <placeholder
          name="Padding"
          value="None" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="LBL"
        id="84820004-98a1-4563-94a9-cb2c7ce14ce0"
        left="5"
        top="1.5"
        width="16"
        height="1"
        binding="OuterPackageSelectByThisItem.LastMileCarrier.OH_FullName">
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="LabelType"
          value="Notification" />
        <placeholder
          name="LabelNotificationType"
          value="Information" />
        <placeholder
          name="ForegroundThemeColor"
          value="GeneralText" />
        <placeholder
          name="TextSize"
          value="Large" />
        <placeholder
          name="FontWeight"
          value="Normal" />
        <placeholder
          name="LabelAlignment"
          value="Center" />
        <placeholder
          name="BackgroundColor"
          value="" />
        <placeholder
          name="Padding"
          value="None" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
    </control>
    <control
      code="GRP"
      id="848c749d-618d-4c02-8097-ba7b3dc37b34"
      top="0"
      width="26"
      height="3">
      <placeholder
        name="CanBeHidden"
        value="True" />
      <placeholder
        name="VisibilityCondition"
        value="ea9c60ab-8655-41dd-a070-14c2570c0904" />
      <placeholder
        name="Header"
        value="one"
        resid="c055780c-e3f4-483c-a7d9-ae6f21558b6f" />
      <placeholder
        name="HeaderAlignment"
        value="Left" />
      <placeholder
        name="HeaderDisplayMode"
        value="Hidden" />
      <placeholder
        name="BackgroundImagePk"
        value="" />
      <placeholder
        name="BackgroundImageOverride"
        value="" />
      <control
        code="LBL"
        id="489efe0f-e8c5-4cb8-a882-71402e0eaf27"
        left="5"
        top="0"
        width="16"
        height="2"
        binding="OuterPackageSelectByThisItem.HVO_PackageBarcode">
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="LabelType"
          value="Notification" />
        <placeholder
          name="LabelNotificationType"
          value="Information" />
        <placeholder
          name="ForegroundThemeColor"
          value="GeneralText" />
        <placeholder
          name="TextSize"
          value="ExtraExtraLarge" />
        <placeholder
          name="FontWeight"
          value="Normal" />
        <placeholder
          name="LabelAlignment"
          value="Center" />
        <placeholder
          name="BackgroundColor"
          value="" />
        <placeholder
          name="Padding"
          value="None" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
    </control>
    <control
      code="GRP"
      id="cfb6e003-1815-4cea-8cd2-60df7be49eeb"
      top="8"
      width="26"
      height="6">
      <placeholder
        name="CanBeHidden"
        value="True" />
      <placeholder
        name="VisibilityCondition"
        value="e67327a0-a34a-4bb9-98c6-8aff378666cc" />
      <placeholder
        name="Header"
        value="Held"
        resid="2d795523-3dd7-4f48-bb06-1c35540483dd" />
      <placeholder
        name="HeaderAlignment"
        value="Left" />
      <placeholder
        name="HeaderDisplayMode"
        value="Hidden" />
      <placeholder
        name="BackgroundImagePk"
        value="" />
      <placeholder
        name="BackgroundImageOverride"
        value="" />
      <control
        code="FLB"
        id="93148c25-7ce7-4ae4-9745-b8250eb4505d"
        left="5"
        top="0"
        width="16"
        height="2">
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="LabelText"
          value="HELD - SURPLUS"
          resid="b9018b2e-05ac-4143-b44f-d51d2fc1e570" />
        <placeholder
          name="LabelType"
          value="Notification" />
        <placeholder
          name="LabelNotificationType"
          value="Warning" />
        <placeholder
          name="ForegroundThemeColor"
          value="GeneralText" />
        <placeholder
          name="TextSize"
          value="ExtraExtraLarge" />
        <placeholder
          name="FontWeight"
          value="Normal" />
        <placeholder
          name="LabelAlignment"
          value="Center" />
        <placeholder
          name="BackgroundColor"
          value="" />
        <placeholder
          name="Padding"
          value="None" />
      </control>
      <control
        code="LBL"
        id="4b21d625-d025-457a-8545-ca2c39038159"
        left="5"
        top="2"
        width="16"
        height="1"
        binding="Consignment.DetailedImportStatusWithBrackets">
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="LabelType"
          value="Notification" />
        <placeholder
          name="LabelNotificationType"
          value="Warning" />
        <placeholder
          name="ForegroundThemeColor"
          value="GeneralText" />
        <placeholder
          name="TextSize"
          value="Medium" />
        <placeholder
          name="FontWeight"
          value="Normal" />
        <placeholder
          name="LabelAlignment"
          value="Center" />
        <placeholder
          name="BackgroundColor"
          value="" />
        <placeholder
          name="Padding"
          value="None" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="LBL"
        id="b3f965e0-8040-4657-92e8-a2c8df16d693"
        left="5"
        top="4"
        width="16"
        height="1"
        binding="HVI_CurrentBarcode">
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="LabelType"
          value="Notification" />
        <placeholder
          name="LabelNotificationType"
          value="Warning" />
        <placeholder
          name="ForegroundThemeColor"
          value="GeneralText" />
        <placeholder
          name="TextSize"
          value="Large" />
        <placeholder
          name="FontWeight"
          value="Normal" />
        <placeholder
          name="LabelAlignment"
          value="Center" />
        <placeholder
          name="BackgroundColor"
          value="" />
        <placeholder
          name="Padding"
          value="None" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
    </control>
    <control
      code="GRP"
      id="2952e493-1995-499d-b072-c84be08b4635"
      top="8"
      width="26"
      height="6">
      <placeholder
        name="CanBeHidden"
        value="True" />
      <placeholder
        name="VisibilityCondition"
        value="589f8807-04c0-424a-ad64-47667a9149e3" />
      <placeholder
        name="Header"
        value="Clear"
        resid="05cb7637-2799-4c58-8a0c-2d0ab7f912b4" />
      <placeholder
        name="HeaderAlignment"
        value="Left" />
      <placeholder
        name="HeaderDisplayMode"
        value="Hidden" />
      <placeholder
        name="BackgroundImagePk"
        value="" />
      <placeholder
        name="BackgroundImageOverride"
        value="" />
      <control
        code="FLB"
        id="40eb0e7c-178f-41b8-a1a4-b245d8101c68"
        left="5"
        top="0"
        width="16"
        height="2">
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="LabelText"
          value="CLEAR - TRANSHIPMENT"
          resid="3d2e37ac-95f1-4b5c-baea-fc05229580b4" />
        <placeholder
          name="LabelType"
          value="Notification" />
        <placeholder
          name="LabelNotificationType"
          value="Information" />
        <placeholder
          name="ForegroundThemeColor"
          value="GeneralText" />
        <placeholder
          name="TextSize"
          value="ExtraLarge" />
        <placeholder
          name="FontWeight"
          value="Normal" />
        <placeholder
          name="LabelAlignment"
          value="Center" />
        <placeholder
          name="BackgroundColor"
          value="" />
        <placeholder
          name="Padding"
          value="None" />
      </control>
      <control
        code="LBL"
        id="db87c57b-3a66-4171-8010-764c61a21985"
        left="5"
        top="2"
        width="16"
        height="1"
        binding="Consignment.DetailedImportStatusWithBrackets">
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="LabelType"
          value="Notification" />
        <placeholder
          name="LabelNotificationType"
          value="Information" />
        <placeholder
          name="ForegroundThemeColor"
          value="GeneralText" />
        <placeholder
          name="TextSize"
          value="Medium" />
        <placeholder
          name="FontWeight"
          value="Normal" />
        <placeholder
          name="LabelAlignment"
          value="Center" />
        <placeholder
          name="BackgroundColor"
          value="" />
        <placeholder
          name="Padding"
          value="None" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="LBL"
        id="8fcd5a6e-6830-4831-8510-2e7166f194a3"
        left="5"
        top="4"
        width="16"
        height="1"
        binding="HVI_CurrentBarcode">
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="LabelType"
          value="Notification" />
        <placeholder
          name="LabelNotificationType"
          value="Information" />
        <placeholder
          name="ForegroundThemeColor"
          value="GeneralText" />
        <placeholder
          name="TextSize"
          value="Large" />
        <placeholder
          name="FontWeight"
          value="Normal" />
        <placeholder
          name="LabelAlignment"
          value="Center" />
        <placeholder
          name="BackgroundColor"
          value="" />
        <placeholder
          name="Padding"
          value="None" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
    </control>
    <control
      code="GRP"
      id="7ad5c672-13ef-4c91-9066-894084f094e9"
      top="8"
      width="26"
      height="6">
      <placeholder
        name="CanBeHidden"
        value="True" />
      <placeholder
        name="VisibilityCondition"
        value="8a20448b-1082-4a0c-a3d6-1a0802050858" />
      <placeholder
        name="Header"
        value="green"
        resid="34359268-aa6e-481f-afa0-73cd516507b3" />
      <placeholder
        name="HeaderAlignment"
        value="Left" />
      <placeholder
        name="HeaderDisplayMode"
        value="Hidden" />
      <placeholder
        name="BackgroundImagePk"
        value="" />
      <placeholder
        name="BackgroundImageOverride"
        value="" />
      <control
        code="LBL"
        id="30eac107-77e3-424b-a950-2a38fc2db33c"
        left="5"
        top="0"
        width="16"
        height="2"
        binding="ReturnReleaseStatus">
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="LabelType"
          value="Notification" />
        <placeholder
          name="LabelNotificationType"
          value="Success" />
        <placeholder
          name="ForegroundThemeColor"
          value="GeneralText" />
        <placeholder
          name="TextSize"
          value="ExtraExtraLarge" />
        <placeholder
          name="FontWeight"
          value="Normal" />
        <placeholder
          name="LabelAlignment"
          value="Center" />
        <placeholder
          name="BackgroundColor"
          value="" />
        <placeholder
          name="Padding"
          value="None" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="LBL"
        id="d19bbc88-8245-4d7f-95c9-924c1dd7938a"
        left="5"
        top="2"
        width="16"
        height="1"
        binding="Consignment.DetailedImportStatusWithBrackets">
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="LabelType"
          value="Notification" />
        <placeholder
          name="LabelNotificationType"
          value="Success" />
        <placeholder
          name="ForegroundThemeColor"
          value="GeneralText" />
        <placeholder
          name="TextSize"
          value="Medium" />
        <placeholder
          name="FontWeight"
          value="Normal" />
        <placeholder
          name="LabelAlignment"
          value="Center" />
        <placeholder
          name="BackgroundColor"
          value="" />
        <placeholder
          name="Padding"
          value="None" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="LBL"
        id="08f2a302-4e05-4178-86b0-72bd67c78ecc"
        left="5"
        top="4"
        width="16"
        height="1"
        binding="HVI_CurrentBarcode">
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="LabelType"
          value="Notification" />
        <placeholder
          name="LabelNotificationType"
          value="Success" />
        <placeholder
          name="ForegroundThemeColor"
          value="GeneralText" />
        <placeholder
          name="TextSize"
          value="Large" />
        <placeholder
          name="FontWeight"
          value="Normal" />
        <placeholder
          name="LabelAlignment"
          value="Center" />
        <placeholder
          name="BackgroundColor"
          value="" />
        <placeholder
          name="Padding"
          value="None" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
    </control>
    <control
      code="GRP"
      id="f9968cc4-1f64-43e5-adba-ab54d19cc686"
      top="8"
      width="26"
      height="6">
      <placeholder
        name="CanBeHidden"
        value="True" />
      <placeholder
        name="VisibilityCondition"
        value="7f416997-9504-44cc-8a40-cc117979184e" />
      <placeholder
        name="Header"
        value="Red"
        resid="4dfe06a8-1914-4605-a9f3-ebc49ab96119" />
      <placeholder
        name="HeaderAlignment"
        value="Left" />
      <placeholder
        name="HeaderDisplayMode"
        value="Hidden" />
      <placeholder
        name="BackgroundImagePk"
        value="" />
      <placeholder
        name="BackgroundImageOverride"
        value="" />
      <control
        code="LBL"
        id="57f15ded-b7f0-45bd-8411-3b77469aca49"
        left="5"
        top="0"
        width="16"
        height="2"
        binding="ReturnReleaseStatus">
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="LabelType"
          value="Notification" />
        <placeholder
          name="LabelNotificationType"
          value="Error" />
        <placeholder
          name="ForegroundThemeColor"
          value="GeneralText" />
        <placeholder
          name="TextSize"
          value="ExtraExtraLarge" />
        <placeholder
          name="FontWeight"
          value="Normal" />
        <placeholder
          name="LabelAlignment"
          value="Center" />
        <placeholder
          name="BackgroundColor"
          value="" />
        <placeholder
          name="Padding"
          value="None" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="LBL"
        id="60d6c0b0-49fc-4d71-8e24-5f56a7c14c73"
        left="5"
        top="2"
        width="16"
        height="1"
        binding="Consignment.DetailedImportStatusWithBrackets">
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="LabelType"
          value="Notification" />
        <placeholder
          name="LabelNotificationType"
          value="Error" />
        <placeholder
          name="ForegroundThemeColor"
          value="GeneralText" />
        <placeholder
          name="TextSize"
          value="Medium" />
        <placeholder
          name="FontWeight"
          value="Normal" />
        <placeholder
          name="LabelAlignment"
          value="Center" />
        <placeholder
          name="BackgroundColor"
          value="" />
        <placeholder
          name="Padding"
          value="None" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="LBL"
        id="046bdbaf-ea07-4258-91c0-558355b78db5"
        left="5"
        top="4"
        width="16"
        height="1"
        binding="HVI_CurrentBarcode">
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="LabelType"
          value="Notification" />
        <placeholder
          name="LabelNotificationType"
          value="Error" />
        <placeholder
          name="ForegroundThemeColor"
          value="GeneralText" />
        <placeholder
          name="TextSize"
          value="Large" />
        <placeholder
          name="FontWeight"
          value="Normal" />
        <placeholder
          name="LabelAlignment"
          value="Center" />
        <placeholder
          name="BackgroundColor"
          value="" />
        <placeholder
          name="Padding"
          value="None" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
    </control>
    <control
      code="GRP"
      id="f28cd5a8-5456-41e5-a54f-c3a2fe0320ec"
      top="8"
      width="26"
      height="6">
      <placeholder
        name="CanBeHidden"
        value="True" />
      <placeholder
        name="VisibilityCondition"
        value="5fe39259-e3b8-4ae4-96e8-abc70ee01ca6" />
      <placeholder
        name="Header"
        value="Held Red"
        resid="d16cf480-dfb5-45bd-8135-83d85f8ee0be" />
      <placeholder
        name="HeaderAlignment"
        value="Left" />
      <placeholder
        name="HeaderDisplayMode"
        value="Hidden" />
      <placeholder
        name="BackgroundImagePk"
        value="" />
      <placeholder
        name="BackgroundImageOverride"
        value="" />
      <control
        code="FLB"
        id="7dd0f31a-fe7b-45a3-b302-ec6875eaf622"
        left="5"
        top="0"
        width="16"
        height="3">
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="LabelText"
          value="HELD - NO STATUS AVAILABLE"
          resid="b70efd5d-f02a-4ef5-8e1a-de4a57d8c6f4" />
        <placeholder
          name="LabelType"
          value="Notification" />
        <placeholder
          name="LabelNotificationType"
          value="Error" />
        <placeholder
          name="ForegroundThemeColor"
          value="GeneralText" />
        <placeholder
          name="TextSize"
          value="ExtraExtraLarge" />
        <placeholder
          name="FontWeight"
          value="Normal" />
        <placeholder
          name="LabelAlignment"
          value="Center" />
        <placeholder
          name="BackgroundColor"
          value="" />
        <placeholder
          name="Padding"
          value="None" />
      </control>
      <control
        code="LBL"
        id="399efe9b-f13e-4ddc-8a2f-27a36dc66261"
        left="5"
        top="4"
        width="16"
        height="1"
        binding="HVI_CurrentBarcode">
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="LabelType"
          value="Notification" />
        <placeholder
          name="LabelNotificationType"
          value="Error" />
        <placeholder
          name="ForegroundThemeColor"
          value="GeneralText" />
        <placeholder
          name="TextSize"
          value="Large" />
        <placeholder
          name="FontWeight"
          value="Normal" />
        <placeholder
          name="LabelAlignment"
          value="Center" />
        <placeholder
          name="BackgroundColor"
          value="" />
        <placeholder
          name="Padding"
          value="None" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
    </control>
    <control
      code="GRP"
      id="fcd8a5aa-8a25-4e36-8a54-631a9326e781"
      top="14"
      width="26"
      height="4">
      <placeholder
        name="CanBeHidden"
        value="True" />
      <placeholder
        name="VisibilityCondition"
        value="" />
      <placeholder
        name="Header"
        value="New Section"
        resid="a16ac821-f0e7-41fe-8752-82d34a3180be" />
      <placeholder
        name="HeaderAlignment"
        value="Left" />
      <placeholder
        name="HeaderDisplayMode"
        value="Hidden" />
      <placeholder
        name="BackgroundImagePk"
        value="" />
      <placeholder
        name="BackgroundImageOverride"
        value="" />
      <control
        code="FFI"
        id="98057c35-1f25-4cc9-8a2b-f40d64e2707b"
        top="0.5"
        width="9.6"
        height="1">
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="True" />
        <placeholder
          name="CaptionOverride"
          value="Barcode"
          resid="95f2a899-1796-4fdd-816f-1b7956ed52de" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="EntityType"
          value="IHVLVItem" />
        <placeholder
          name="DefaultFilter">
          <xml>
            <ArrayOfFilterGroup xmlns:a="http://schemas.microsoft.com/2003/10/Serialization/Arrays" xmlns:i="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://cargowise.com/glow/2014/07/16/filters.xsd">
              <FilterGroup>
                <Filters>
                  <Filter>
                    <FilterType>IsActiveFilter</FilterType>
                    <Operation>Is</Operation>
                    <PropertyPath>HVI_IsActive</PropertyPath>
                    <Values>
                      <a:string>true</a:string>
                    </Values>
                  </Filter>
                </Filters>
                <IsImplicit>false</IsImplicit>
              </FilterGroup>
            </ArrayOfFilterGroup>
          </xml>
        </placeholder>
        <placeholder
          name="ItemTemplateID"
          value="" />
        <placeholder
          name="FormFlowPK"
          value="" />
        <placeholder
          name="ItemPosition"
          value="Bottom" />
      </control>
      <control
        code="FLB"
        id="086d88e9-954f-4be5-9d63-a3dbe1b67693"
        top="1.5"
        width="12"
        height="1">
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="LabelText"
          value="Please Scan Next Item"
          resid="6ce5c0ce-f798-45a7-bc4b-c2d853a06586" />
        <placeholder
          name="LabelType"
          value="NormalLabel" />
        <placeholder
          name="LabelNotificationType"
          value="Information" />
        <placeholder
          name="ForegroundThemeColor"
          value="DefaultButton" />
        <placeholder
          name="TextSize"
          value="Medium" />
        <placeholder
          name="FontWeight"
          value="Bold" />
        <placeholder
          name="LabelAlignment"
          value="Center" />
        <placeholder
          name="BackgroundColor"
          value="" />
        <placeholder
          name="Padding"
          value="None" />
      </control>
      <control
        code="TBT"
        id="d9007d56-611d-428a-8f0c-b1db3c55c889"
        top="2.5"
        width="9.4"
        height="1">
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="Content"
          value="Edit Item Details"
          resid="08c7d694-2bc0-48a4-b6e9-406c36234069" />
        <placeholder
          name="IsDefault"
          value="False" />
      </control>
    </control>
  </form>
