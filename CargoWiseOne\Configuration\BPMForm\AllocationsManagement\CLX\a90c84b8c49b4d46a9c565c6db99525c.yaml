#transformationVersion: 70.0
#
VZ_PK: a90c84b8c49b4d46a9c565c6db99525c
VZ_ConfigurationKey: a90c84b8-c49b-4d46-a9c5-65c6db99525c
VZ_FormID: CLX - Client Contract Details
VZ_Caption:
  resKey: VZ_Caption|a90c84b8-c49b-4d46-a9c5-65c6db99525c
  text: Client Contract & Allocations
VZ_FormFactor: DSK
VZ_EntityType: IClientContract
VZ_FormType: FRM
VZ_RequiresLogin: true
VZ_Dependencies: >-
  <dependencies
    xmlns="http://cargowise.com/glow/2014/11/14/dependencies.xsd">
    <expandPath
      path="ServiceProvider" />
    <expandPath
      path="ClientContractContainerDetentions" />
    <datagrid
      path="ClientContractContainerDetentions">
      <expandPath
        path="Client" />
      <expandPath
        path="CTO" />
    </datagrid>
  </dependencies>
VZ_FormData: >-
  <form
    id="8cf8e945-6c83-4437-b7ee-c87441bb31cf"
    xmlns="http://wisetechglobal.com/glow/2017/05/26/form.xsd">
    <placeholder
      name="BackgroundImagePk"
      value="" />
    <placeholder
      name="BackgroundSize"
      value="Auto" />
    <placeholder
      name="BackgroundImageOverride"
      value="" />
    <placeholder
      name="FormLayout"
      value="Splitview" />
    <placeholder
      name="ExpandPaths"
      value="" />
    <placeholder
      name="ScanInputHandling"
      value="NON" />
    <placeholder
      name="RemovePadding"
      value="false" />
    <placeholder
      name="TurnOnPortableOneToOneDesignMode"
      value="false" />
    <placeholder
      name="ContainsDefaultButton"
      value="False" />
    <control
      code="GRP"
      id="d1531f92-fb87-4936-b035-12d4fef4280b"
      left="0"
      top="0"
      height="12"
      right="0">
      <placeholder
        name="CanBeHidden"
        value="True" />
      <placeholder
        name="VisibilityCondition"
        value="" />
      <placeholder
        name="Header"
        value="New Section"
        resid="7aac25ff-b4a0-4394-9c08-5062905707a5" />
      <placeholder
        name="HeaderAlignment"
        value="Left" />
      <placeholder
        name="HeaderDisplayMode"
        value="Hidden" />
      <placeholder
        name="BackgroundImagePk"
        value="" />
      <placeholder
        name="BackgroundImageOverride"
        value="" />
      <control
        code="FLB"
        id="18c86b4f-81fd-4b53-a576-a2c051520978"
        left="1"
        top="0"
        width="4"
        height="1">
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="LabelText"
          value="Contract Information"
          resid="a2ea7f28-37d9-4fe4-9a9e-e4b1c4659cba" />
        <placeholder
          name="LabelType"
          value="NormalLabel" />
        <placeholder
          name="LabelNotificationType"
          value="Information" />
        <placeholder
          name="ForegroundThemeColor"
          value="DefaultButton" />
        <placeholder
          name="TextSize"
          value="Medium" />
        <placeholder
          name="FontWeight"
          value="Bold" />
        <placeholder
          name="LabelAlignment"
          value="CenterLeft" />
        <placeholder
          name="BackgroundColor"
          value="" />
        <placeholder
          name="Padding"
          value="None" />
      </control>
      <control
        code="TXT"
        id="8352c2da-8b9e-493c-a656-1bfcb98909b2"
        left="1"
        top="1"
        width="4"
        height="1"
        binding="RCT_ContractNumber">
        <placeholder
          name="IsReadOnly"
          value="True" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="OnSubmit"
          value="" />
        <placeholder
          name="MultiLine"
          value="False" />
        <placeholder
          name="EnableAutoComplete"
          value="False" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="SRC"
        id="31f40054-1995-4588-8424-7884460b8353"
        left="6"
        top="1"
        width="4"
        height="1"
        binding="RCT_OH">
        <placeholder
          name="IsReadOnly"
          value="False" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="Client"
          resid="c05c46b7-732f-44a1-bf8a-fae46e2932d3" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="DisplayMode"
          value="Unspecified" />
        <placeholder
          name="FieldConfiguration"
          value="" />
        <placeholder
          name="DefaultFilter"
          value="" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="DTE"
        id="23facb95-13b2-412f-a09c-d42fea5fedaf"
        left="11"
        top="1"
        width="4"
        height="1"
        binding="RCT_StartDate">
        <placeholder
          name="IsReadOnly"
          value="False" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="DefaultValueExpression"
          value="" />
        <placeholder
          name="IncludeTime"
          value="True" />
        <placeholder
          name="ShowNowButton"
          value="True" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="TXT"
        id="2e9ec3a0-69ed-4689-8996-cce998540005"
        left="1"
        top="2"
        width="4"
        height="1"
        binding="RCT_Description">
        <placeholder
          name="IsReadOnly"
          value="False" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="OnSubmit"
          value="" />
        <placeholder
          name="MultiLine"
          value="False" />
        <placeholder
          name="EnableAutoComplete"
          value="False" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="DTE"
        id="d5b58ad6-7fd4-462c-b5da-fbf4eddaa0c9"
        left="11"
        top="2"
        width="4"
        height="1"
        binding="RCT_EndDate">
        <placeholder
          name="IsReadOnly"
          value="False" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="DefaultValueExpression"
          value="" />
        <placeholder
          name="IncludeTime"
          value="True" />
        <placeholder
          name="ShowNowButton"
          value="True" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="GRD"
        id="2a7bdc9e-6431-4a2f-bd16-70ed1c331116"
        left="1"
        top="3"
        height="8"
        right="1"
        binding="ClientContractContainerDetentions">
        <placeholder
          name="IsReadOnly"
          value="False" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="CaptionOverride"
          value="Container Penalty"
          resid="5f0b2e7c-87b9-4a5e-959e-9d439f101456" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="HeaderAlignment"
          value="Left" />
        <placeholder
          name="HeaderDisplayMode"
          value="Visible" />
        <placeholder
          name="AllowAdd"
          value="True" />
        <placeholder
          name="AllowInlineEdit"
          value="True" />
        <placeholder
          name="FieldConfiguration">
          <xml>
            <fields
              xmlns="">
              <field
                path="RCD_Direction"
                width="90"
                mode="Default" />
              <field
                path="RCD_PenaltyType"
                width="120"
                mode="Default" />
              <field
                path="RCD_DetentionPortOrCountry"
                width="250"
                mode="Default" />
              <field
                path="RCD_OriginPortOrCountry"
                width="220"
                mode="Default" />
              <field
                path="RCD_ContainerType"
                width="140"
                mode="Default" />
              <field
                path="RCD_FreeDays"
                width="90"
                mode="Default" />
              <field
                path="RCD_OH_Client"
                width="250"
                mode="Default" />
              <field
                path="RCD_StartDateOverride"
                width="190"
                mode="Default" />
              <field
                path="RCD_EndDateOverride"
                width="170"
                mode="Default" />
              <field
                path="FirstFreeDay"
                width="250"
                mode="Default" />
              <field
                path="LastFreeDay"
                width="250"
                mode="Default" />
            </fields>
          </xml>
        </placeholder>
        <placeholder
          name="DefaultSortFields"
          value="" />
        <placeholder
          name="ShowDocumentsIcon"
          value="True" />
        <placeholder
          name="RowActionsMode"
          value="" />
        <placeholder
          name="HideGridActions"
          value="False" />
        <placeholder
          name="HideFilters"
          value="False" />
        <placeholder
          name="EditFormFlow"
          value="" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
    </control>
  </form>
