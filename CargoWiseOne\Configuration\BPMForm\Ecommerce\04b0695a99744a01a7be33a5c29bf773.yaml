#transformationVersion: 70.0
#
VZ_PK: 04b0695a99744a01a7be33a5c29bf773
VZ_ConfigurationKey: 04b0695a-9974-4a01-a7be-33a5c29bf773
VZ_FormID: ETL - VDV3 - Destination Depot - Scan to Receive Items on Shipment (Optimized Flow)
VZ_Caption:
  resKey: VZ_Caption|04b0695a-9974-4a01-a7be-33a5c29bf773
  text: Scan Surplus Items
VZ_FormFactor: DSK
VZ_EntityType: IJobShipment
VZ_FormType: FRM
VZ_RequiresLogin: true
VZ_Dependencies: >-
  <dependencies xmlns="http://cargowise.com/glow/2014/11/14/dependencies.xsd">
    <expandPath
      path="CurrentItem" />
    <expandPath
      path="CurrentItem.Consignment" />
    <calculatedProperty
      path="ItemCount" />
    <calculatedProperty
      path="HeldCount" />
    <calculatedProperty
      path="ClearCount" />
    <calculatedProperty
      path="SurplusCount" />
    <calculatedProperty
      path="CurrentItem" />
    <calculatedProperty
      path="CurrentItem.Consignment.DetailedImportStatusWithBrackets" />
    <calculatedProperty
      path="CurrentSearchTerm" />
    <calculatedProperty
      path="CurrentItem.ReturnReleaseStatus" />
    <calculatedProperty
      path="CurrentItem.ReturnLastMileCarrierAndBarcode" />
  </dependencies>
VZ_FormData: >-
  <form
    id="c6466453-5ae1-4a8c-b1a2-6e357ff69154" xmlns="http://wisetechglobal.com/glow/2017/05/26/form.xsd">
    <placeholder
      name="BackgroundImagePk"
      value="" />
    <placeholder
      name="BackgroundSize"
      value="Auto" />
    <placeholder
      name="BackgroundImageOverride"
      value="" />
    <placeholder
      name="FormLayout"
      value="Splitview" />
    <placeholder
      name="ExpandPaths"
      value="" />
    <placeholder
      name="ScanInputHandling"
      value="NON" />
    <placeholder
      name="RemovePadding"
      value="false" />
    <placeholder
      name="TurnOnPortableOneToOneDesignMode"
      value="false" />
    <placeholder
      name="ContainsDefaultButton"
      value="False" />
    <control
      code="GRP"
      id="85658af3-aa5b-4787-8827-057d87d8d1d3"
      top="0"
      width="32"
      height="22.9">
      <placeholder
        name="CanBeHidden"
        value="True" />
      <placeholder
        name="VisibilityCondition"
        value="b0316001-e70d-494f-9f1f-4e0dd3055fe8" />
      <placeholder
        name="Header"
        value="New Section"
        resid="c851d215-2e69-4d26-a4cb-edd6dad40225" />
      <placeholder
        name="HeaderAlignment"
        value="Left" />
      <placeholder
        name="HeaderDisplayMode"
        value="Hidden" />
      <placeholder
        name="BackgroundImagePk"
        value="" />
      <placeholder
        name="BackgroundImageOverride"
        value="" />
      <control
        code="FLB"
        id="94ca5386-2292-4d41-8b7e-d2f5527fb15e"
        left="6"
        top="7"
        width="20"
        height="3">
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="VisibilityCondition"
          value="138f4f14-fb80-4f75-ae47-7cf5c9a62179" />
        <placeholder
          name="LabelText"
          value="Receipt at CFS before scanning can commence"
          resid="86086913-7cfd-4a5c-98c6-1af4abffbd17" />
        <placeholder
          name="LabelType"
          value="Notification" />
        <placeholder
          name="LabelNotificationType"
          value="Error" />
        <placeholder
          name="ForegroundThemeColor"
          value="GeneralText" />
        <placeholder
          name="TextSize"
          value="Large" />
        <placeholder
          name="FontWeight"
          value="Normal" />
        <placeholder
          name="LabelAlignment"
          value="Center" />
        <placeholder
          name="BackgroundColor"
          value="" />
        <placeholder
          name="Padding"
          value="None" />
      </control>
      <control
        code="FLB"
        id="60096d1d-736a-40a7-8798-4184229f7ba1"
        left="11.5"
        top="10"
        width="9.3"
        height="1">
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="VisibilityCondition"
          value="44ff7cc7-f820-4165-9a87-30a0f2855cd5" />
        <placeholder
          name="LabelText"
          value="Please Scan Next Item"
          resid="18e71c45-e97f-48ad-9fdf-9f010dfc9230" />
        <placeholder
          name="LabelType"
          value="NormalLabel" />
        <placeholder
          name="LabelNotificationType"
          value="Information" />
        <placeholder
          name="ForegroundThemeColor"
          value="DefaultButton" />
        <placeholder
          name="TextSize"
          value="Medium" />
        <placeholder
          name="FontWeight"
          value="Bold" />
        <placeholder
          name="LabelAlignment"
          value="Center" />
        <placeholder
          name="BackgroundColor"
          value="" />
        <placeholder
          name="Padding"
          value="None" />
      </control>
      <control
        code="TBT"
        id="d7042e58-d1dc-450c-8b66-d3b7feaddc4d"
        left="11.5"
        top="11"
        width="9.3"
        height="1">
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="VisibilityCondition"
          value="35cf9df9-2d3a-4543-9d41-7fca57d2f9b9" />
        <placeholder
          name="Content"
          value="Edit Last Scanned Item"
          resid="615d8f36-dd28-4208-b406-f9eb79877e80" />
        <placeholder
          name="IsDefault"
          value="False" />
      </control>
      <control
        code="FLB"
        id="fc0e9a52-fd4e-44dc-8900-6060863e770d"
        left="11.8"
        top="12.2"
        width="8.8"
        height="1">
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="LabelText"
          value="Item Counts"
          resid="3b3baf6e-25c9-4fbe-b402-4d3a84bf7473" />
        <placeholder
          name="LabelType"
          value="NormalLabel" />
        <placeholder
          name="LabelNotificationType"
          value="Information" />
        <placeholder
          name="ForegroundThemeColor"
          value="DefaultButton" />
        <placeholder
          name="TextSize"
          value="Medium" />
        <placeholder
          name="FontWeight"
          value="Bold" />
        <placeholder
          name="LabelAlignment"
          value="Center" />
        <placeholder
          name="BackgroundColor"
          value="" />
        <placeholder
          name="Padding"
          value="None" />
      </control>
      <control
        code="FLB"
        id="885a5059-764c-4114-b4ff-4fcb1c41ea2a"
        left="11.8"
        top="13.2"
        width="4"
        height="2">
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="LabelText"
          value="Total"
          resid="6a3de76f-fc6d-4d3d-b394-66dc35030393" />
        <placeholder
          name="LabelType"
          value="Notification" />
        <placeholder
          name="LabelNotificationType"
          value="Information" />
        <placeholder
          name="ForegroundThemeColor"
          value="GeneralText" />
        <placeholder
          name="TextSize"
          value="ExtraLarge" />
        <placeholder
          name="FontWeight"
          value="Bold" />
        <placeholder
          name="LabelAlignment"
          value="Center" />
        <placeholder
          name="BackgroundColor"
          value="" />
        <placeholder
          name="Padding"
          value="None" />
      </control>
      <control
        code="LBL"
        id="e1d99a85-33c9-46a3-be46-5b0788d9576f"
        left="15.8"
        top="13.2"
        width="4.8"
        height="2"
        binding="ItemCount">
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="LabelType"
          value="Notification" />
        <placeholder
          name="LabelNotificationType"
          value="Information" />
        <placeholder
          name="ForegroundThemeColor"
          value="GeneralText" />
        <placeholder
          name="TextSize"
          value="ExtraLarge" />
        <placeholder
          name="FontWeight"
          value="Bold" />
        <placeholder
          name="LabelAlignment"
          value="CenterLeft" />
        <placeholder
          name="BackgroundColor"
          value="" />
        <placeholder
          name="Padding"
          value="None" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="FLB"
        id="744dda17-5382-43ab-b154-604cbc4897b7"
        left="11.8"
        top="15.2"
        width="4"
        height="2">
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="LabelText"
          value="Held"
          resid="f0d342f7-37ba-4cd3-a15f-c39ecfbe6f81" />
        <placeholder
          name="LabelType"
          value="Notification" />
        <placeholder
          name="LabelNotificationType"
          value="Error" />
        <placeholder
          name="ForegroundThemeColor"
          value="GeneralText" />
        <placeholder
          name="TextSize"
          value="ExtraLarge" />
        <placeholder
          name="FontWeight"
          value="Bold" />
        <placeholder
          name="LabelAlignment"
          value="Center" />
        <placeholder
          name="BackgroundColor"
          value="" />
        <placeholder
          name="Padding"
          value="None" />
      </control>
      <control
        code="LBL"
        id="7a1ed285-99ef-4b9f-a552-94a43c4d3c6a"
        left="15.8"
        top="15.2"
        width="4.8"
        height="2"
        binding="HeldCount">
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="LabelType"
          value="Notification" />
        <placeholder
          name="LabelNotificationType"
          value="Error" />
        <placeholder
          name="ForegroundThemeColor"
          value="GeneralText" />
        <placeholder
          name="TextSize"
          value="ExtraLarge" />
        <placeholder
          name="FontWeight"
          value="Bold" />
        <placeholder
          name="LabelAlignment"
          value="CenterLeft" />
        <placeholder
          name="BackgroundColor"
          value="" />
        <placeholder
          name="Padding"
          value="None" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="FLB"
        id="6f85417c-4661-4a0e-9a6f-73e0b181930d"
        left="11.8"
        top="17.2"
        width="4"
        height="2">
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="LabelText"
          value="Clear"
          resid="db204d3d-5572-4970-9e78-0b6096b1c419" />
        <placeholder
          name="LabelType"
          value="Notification" />
        <placeholder
          name="LabelNotificationType"
          value="Success" />
        <placeholder
          name="ForegroundThemeColor"
          value="GeneralText" />
        <placeholder
          name="TextSize"
          value="ExtraLarge" />
        <placeholder
          name="FontWeight"
          value="Bold" />
        <placeholder
          name="LabelAlignment"
          value="Center" />
        <placeholder
          name="BackgroundColor"
          value="" />
        <placeholder
          name="Padding"
          value="None" />
      </control>
      <control
        code="LBL"
        id="3dd2d2ec-f080-469c-841e-45a33e08dec4"
        left="15.8"
        top="17.2"
        width="4.8"
        height="2"
        binding="ClearCount">
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="LabelType"
          value="Notification" />
        <placeholder
          name="LabelNotificationType"
          value="Success" />
        <placeholder
          name="ForegroundThemeColor"
          value="GeneralText" />
        <placeholder
          name="TextSize"
          value="ExtraLarge" />
        <placeholder
          name="FontWeight"
          value="Bold" />
        <placeholder
          name="LabelAlignment"
          value="CenterLeft" />
        <placeholder
          name="BackgroundColor"
          value="" />
        <placeholder
          name="Padding"
          value="None" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="FLB"
        id="4d58e55e-7479-4a4b-a87f-15d93258bb65"
        left="11.8"
        top="19.2"
        width="4"
        height="2">
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="LabelText"
          value="Surplus"
          resid="1e81fd9b-8911-4ece-b96c-7406c251e844" />
        <placeholder
          name="LabelType"
          value="Notification" />
        <placeholder
          name="LabelNotificationType"
          value="MessageError" />
        <placeholder
          name="ForegroundThemeColor"
          value="GeneralText" />
        <placeholder
          name="TextSize"
          value="ExtraLarge" />
        <placeholder
          name="FontWeight"
          value="Bold" />
        <placeholder
          name="LabelAlignment"
          value="Center" />
        <placeholder
          name="BackgroundColor"
          value="" />
        <placeholder
          name="Padding"
          value="None" />
      </control>
      <control
        code="LBL"
        id="bcaf573e-dd27-4728-b91d-fd7004de6cdb"
        left="15.8"
        top="19.2"
        width="4.8"
        height="2"
        binding="SurplusCount">
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="LabelType"
          value="Notification" />
        <placeholder
          name="LabelNotificationType"
          value="MessageError" />
        <placeholder
          name="ForegroundThemeColor"
          value="GeneralText" />
        <placeholder
          name="TextSize"
          value="ExtraLarge" />
        <placeholder
          name="FontWeight"
          value="Bold" />
        <placeholder
          name="LabelAlignment"
          value="CenterLeft" />
        <placeholder
          name="BackgroundColor"
          value="" />
        <placeholder
          name="Padding"
          value="None" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
    </control>
    <control
      code="GRP"
      id="1081ccc9-aa6a-48ba-8558-4a7c30fdb4a9"
      top="1"
      width="30"
      height="8">
      <placeholder
        name="CanBeHidden"
        value="True" />
      <placeholder
        name="VisibilityCondition"
        value="59d0928e-122f-43c6-8f74-d4738049103e" />
      <placeholder
        name="Header"
        value="New Section"
        resid="7a4410cc-37e4-4299-9363-b7dee6b37b7a" />
      <placeholder
        name="HeaderAlignment"
        value="Left" />
      <placeholder
        name="HeaderDisplayMode"
        value="Hidden" />
      <placeholder
        name="BackgroundImagePk"
        value="" />
      <placeholder
        name="BackgroundImageOverride"
        value="" />
      <control
        code="FLB"
        id="1e4c311b-ce7d-48f3-9e35-4a9188dc7573"
        left="0"
        top="0"
        width="30"
        height="3">
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="LabelText"
          value="HELD - NO STATUS AVAILABLE"
          resid="18f402ab-c76b-4b77-9d9e-60d9be502db9" />
        <placeholder
          name="LabelType"
          value="Notification" />
        <placeholder
          name="LabelNotificationType"
          value="Error" />
        <placeholder
          name="ForegroundThemeColor"
          value="GeneralText" />
        <placeholder
          name="TextSize"
          value="ExtraExtraLarge" />
        <placeholder
          name="FontWeight"
          value="Normal" />
        <placeholder
          name="LabelAlignment"
          value="Center" />
        <placeholder
          name="BackgroundColor"
          value="" />
        <placeholder
          name="Padding"
          value="None" />
      </control>
      <control
        code="LBL"
        id="f3958642-b857-4a74-9ca2-be58c187dbbc"
        left="0"
        top="4"
        width="30"
        height="3"
        binding="CurrentItem.HVI_ItemId">
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="LabelType"
          value="Notification" />
        <placeholder
          name="LabelNotificationType"
          value="Error" />
        <placeholder
          name="ForegroundThemeColor"
          value="GeneralText" />
        <placeholder
          name="TextSize"
          value="ExtraExtraLarge" />
        <placeholder
          name="FontWeight"
          value="Normal" />
        <placeholder
          name="LabelAlignment"
          value="Center" />
        <placeholder
          name="BackgroundColor"
          value="" />
        <placeholder
          name="Padding"
          value="None" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
    </control>
    <control
      code="GRP"
      id="1fd12222-4b51-45db-ba19-19297ea80b59"
      top="1"
      width="30"
      height="8">
      <placeholder
        name="CanBeHidden"
        value="True" />
      <placeholder
        name="VisibilityCondition"
        value="238a3018-1d23-48eb-b771-618f7ba1f2e7" />
      <placeholder
        name="Header"
        value="New Section"
        resid="96647e79-7373-4ca9-81ff-2af5d34cab15" />
      <placeholder
        name="HeaderAlignment"
        value="Left" />
      <placeholder
        name="HeaderDisplayMode"
        value="Hidden" />
      <placeholder
        name="BackgroundImagePk"
        value="" />
      <placeholder
        name="BackgroundImageOverride"
        value="" />
      <control
        code="FLB"
        id="793bfa0b-75d4-4404-84e7-63157deb33c0"
        left="0"
        top="0"
        width="30"
        height="2">
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="LabelText"
          value="HELD - SURPLUS"
          resid="7ef2615b-2e84-49c9-9a0c-488707b9e408" />
        <placeholder
          name="LabelType"
          value="Notification" />
        <placeholder
          name="LabelNotificationType"
          value="Warning" />
        <placeholder
          name="ForegroundThemeColor"
          value="GeneralText" />
        <placeholder
          name="TextSize"
          value="ExtraExtraLarge" />
        <placeholder
          name="FontWeight"
          value="Normal" />
        <placeholder
          name="LabelAlignment"
          value="Center" />
        <placeholder
          name="BackgroundColor"
          value="" />
        <placeholder
          name="Padding"
          value="None" />
      </control>
      <control
        code="LBL"
        id="f7c22743-44c4-4f58-bedd-5bfc5b048242"
        left="0"
        top="2"
        width="30"
        height="1.7"
        binding="CurrentItem.Consignment.DetailedImportStatusWithBrackets">
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="LabelType"
          value="Notification" />
        <placeholder
          name="LabelNotificationType"
          value="Warning" />
        <placeholder
          name="ForegroundThemeColor"
          value="GeneralText" />
        <placeholder
          name="TextSize"
          value="Large" />
        <placeholder
          name="FontWeight"
          value="Normal" />
        <placeholder
          name="LabelAlignment"
          value="Center" />
        <placeholder
          name="BackgroundColor"
          value="" />
        <placeholder
          name="Padding"
          value="None" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="LBL"
        id="9569a265-4ec7-4ba7-86fa-f69eed2b15a2"
        left="0"
        top="4"
        width="30"
        height="3"
        binding="CurrentSearchTerm">
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="LabelType"
          value="Notification" />
        <placeholder
          name="LabelNotificationType"
          value="Warning" />
        <placeholder
          name="ForegroundThemeColor"
          value="GeneralText" />
        <placeholder
          name="TextSize"
          value="ExtraLarge" />
        <placeholder
          name="FontWeight"
          value="Normal" />
        <placeholder
          name="LabelAlignment"
          value="Center" />
        <placeholder
          name="BackgroundColor"
          value="" />
        <placeholder
          name="Padding"
          value="None" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
    </control>
    <control
      code="GRP"
      id="ae336027-e42c-407d-a53a-ec6dcd980eed"
      top="1"
      width="30"
      height="8">
      <placeholder
        name="CanBeHidden"
        value="True" />
      <placeholder
        name="VisibilityCondition"
        value="e0e70e79-5005-48f3-9079-8a31dece438c" />
      <placeholder
        name="Header"
        value="New Section"
        resid="09de1d9b-0fbf-4d8a-87ad-2254a57dae53" />
      <placeholder
        name="HeaderAlignment"
        value="Left" />
      <placeholder
        name="HeaderDisplayMode"
        value="Hidden" />
      <placeholder
        name="BackgroundImagePk"
        value="" />
      <placeholder
        name="BackgroundImageOverride"
        value="" />
      <control
        code="LBL"
        id="20260deb-b1c3-464a-8c75-8a566037a068"
        left="0"
        top="0"
        width="30"
        height="2"
        binding="CurrentItem.ReturnReleaseStatus">
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="LabelType"
          value="Notification" />
        <placeholder
          name="LabelNotificationType"
          value="Error" />
        <placeholder
          name="ForegroundThemeColor"
          value="GeneralText" />
        <placeholder
          name="TextSize"
          value="ExtraExtraLarge" />
        <placeholder
          name="FontWeight"
          value="Normal" />
        <placeholder
          name="LabelAlignment"
          value="Center" />
        <placeholder
          name="BackgroundColor"
          value="" />
        <placeholder
          name="Padding"
          value="None" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="LBL"
        id="6a1e434f-9a60-4123-b8f0-baa3856d30fd"
        left="0"
        top="2"
        width="30"
        height="1.7"
        binding="CurrentItem.Consignment.DetailedImportStatusWithBrackets">
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="LabelType"
          value="Notification" />
        <placeholder
          name="LabelNotificationType"
          value="Error" />
        <placeholder
          name="ForegroundThemeColor"
          value="GeneralText" />
        <placeholder
          name="TextSize"
          value="Large" />
        <placeholder
          name="FontWeight"
          value="Normal" />
        <placeholder
          name="LabelAlignment"
          value="Center" />
        <placeholder
          name="BackgroundColor"
          value="" />
        <placeholder
          name="Padding"
          value="None" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="LBL"
        id="d85907ac-2ea3-404b-a17e-5b23f0d37f61"
        left="0"
        top="4"
        width="30"
        height="3"
        binding="CurrentItem.HVI_ItemId">
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="LabelType"
          value="Notification" />
        <placeholder
          name="LabelNotificationType"
          value="Error" />
        <placeholder
          name="ForegroundThemeColor"
          value="GeneralText" />
        <placeholder
          name="TextSize"
          value="ExtraExtraLarge" />
        <placeholder
          name="FontWeight"
          value="Normal" />
        <placeholder
          name="LabelAlignment"
          value="Center" />
        <placeholder
          name="BackgroundColor"
          value="" />
        <placeholder
          name="Padding"
          value="None" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
    </control>
    <control
      code="GRP"
      id="511b35a4-b9f2-4857-9e7d-e5435d2311e1"
      top="1"
      width="30"
      height="8">
      <placeholder
        name="CanBeHidden"
        value="True" />
      <placeholder
        name="VisibilityCondition"
        value="e3633a27-818c-469a-a23a-bab2918a7fce" />
      <placeholder
        name="Header"
        value="New Section"
        resid="932d1cb2-c522-486c-a31e-c927761c41a3" />
      <placeholder
        name="HeaderAlignment"
        value="Left" />
      <placeholder
        name="HeaderDisplayMode"
        value="Hidden" />
      <placeholder
        name="BackgroundImagePk"
        value="" />
      <placeholder
        name="BackgroundImageOverride"
        value="" />
      <control
        code="FLB"
        id="87ce4dd9-5b89-42d5-b1fa-ed48ced64eb6"
        left="0"
        top="0"
        width="29.5"
        height="2">
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="LabelText"
          value="CLEAR - TRANSHIPMENT"
          resid="30d664ba-804c-4493-a884-166bf20731fb" />
        <placeholder
          name="LabelType"
          value="Notification" />
        <placeholder
          name="LabelNotificationType"
          value="Information" />
        <placeholder
          name="ForegroundThemeColor"
          value="GeneralText" />
        <placeholder
          name="TextSize"
          value="ExtraExtraLarge" />
        <placeholder
          name="FontWeight"
          value="Normal" />
        <placeholder
          name="LabelAlignment"
          value="Center" />
        <placeholder
          name="BackgroundColor"
          value="" />
        <placeholder
          name="Padding"
          value="None" />
      </control>
      <control
        code="LBL"
        id="5d3617f4-df06-4057-9f40-2ed8ababe386"
        left="0"
        top="2"
        width="29.5"
        height="1.6"
        binding="CurrentItem.Consignment.DetailedImportStatusWithBrackets">
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="LabelType"
          value="Notification" />
        <placeholder
          name="LabelNotificationType"
          value="Information" />
        <placeholder
          name="ForegroundThemeColor"
          value="GeneralText" />
        <placeholder
          name="TextSize"
          value="Large" />
        <placeholder
          name="FontWeight"
          value="Normal" />
        <placeholder
          name="LabelAlignment"
          value="Center" />
        <placeholder
          name="BackgroundColor"
          value="" />
        <placeholder
          name="Padding"
          value="None" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="LBL"
        id="19aa75b7-776a-43a2-960c-8539d948c9d9"
        left="0"
        top="4"
        width="29.5"
        height="3"
        binding="CurrentItem.ReturnLastMileCarrierAndBarcode">
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="LabelType"
          value="Notification" />
        <placeholder
          name="LabelNotificationType"
          value="Information" />
        <placeholder
          name="ForegroundThemeColor"
          value="GeneralText" />
        <placeholder
          name="TextSize"
          value="ExtraLarge" />
        <placeholder
          name="FontWeight"
          value="Normal" />
        <placeholder
          name="LabelAlignment"
          value="Center" />
        <placeholder
          name="BackgroundColor"
          value="" />
        <placeholder
          name="Padding"
          value="None" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
    </control>
    <control
      code="GRP"
      id="97551f1c-403c-4159-8302-0bd2594ad519"
      top="1"
      width="30"
      height="8">
      <placeholder
        name="CanBeHidden"
        value="True" />
      <placeholder
        name="VisibilityCondition"
        value="90e26c01-3ceb-4646-a15a-01bf79131e92" />
      <placeholder
        name="Header"
        value="New Section"
        resid="d559ba0a-a331-4330-99c6-bce623d5f775" />
      <placeholder
        name="HeaderAlignment"
        value="Left" />
      <placeholder
        name="HeaderDisplayMode"
        value="Hidden" />
      <placeholder
        name="BackgroundImagePk"
        value="" />
      <placeholder
        name="BackgroundImageOverride"
        value="" />
      <control
        code="LBL"
        id="470d0bf3-ab1b-4639-851d-b7eb196830fc"
        left="0"
        top="0"
        width="30"
        height="2"
        binding="CurrentItem.ReturnReleaseStatus">
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="LabelType"
          value="Notification" />
        <placeholder
          name="LabelNotificationType"
          value="Success" />
        <placeholder
          name="ForegroundThemeColor"
          value="GeneralText" />
        <placeholder
          name="TextSize"
          value="ExtraExtraLarge" />
        <placeholder
          name="FontWeight"
          value="Normal" />
        <placeholder
          name="LabelAlignment"
          value="Center" />
        <placeholder
          name="BackgroundColor"
          value="" />
        <placeholder
          name="Padding"
          value="None" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="LBL"
        id="b7a95337-acbe-47f6-af80-7635eb5a6b6b"
        left="0"
        top="2"
        width="30"
        height="1.6"
        binding="CurrentItem.Consignment.DetailedImportStatusWithBrackets">
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="LabelType"
          value="Notification" />
        <placeholder
          name="LabelNotificationType"
          value="Success" />
        <placeholder
          name="ForegroundThemeColor"
          value="GeneralText" />
        <placeholder
          name="TextSize"
          value="Large" />
        <placeholder
          name="FontWeight"
          value="Normal" />
        <placeholder
          name="LabelAlignment"
          value="Center" />
        <placeholder
          name="BackgroundColor"
          value="" />
        <placeholder
          name="Padding"
          value="None" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="LBL"
        id="6de25615-78f1-42b9-b4a6-94f0133dacb1"
        left="0"
        top="4"
        width="30"
        height="3"
        binding="CurrentItem.ReturnLastMileCarrierAndBarcode">
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="LabelType"
          value="Notification" />
        <placeholder
          name="LabelNotificationType"
          value="Success" />
        <placeholder
          name="ForegroundThemeColor"
          value="GeneralText" />
        <placeholder
          name="TextSize"
          value="ExtraLarge" />
        <placeholder
          name="FontWeight"
          value="Normal" />
        <placeholder
          name="LabelAlignment"
          value="Center" />
        <placeholder
          name="BackgroundColor"
          value="" />
        <placeholder
          name="Padding"
          value="None" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
    </control>
    <control
      code="GRP"
      id="76cac658-9227-4aa4-b6aa-d3493212e8ef"
      top="1"
      width="29"
      height="8">
      <placeholder
        name="CanBeHidden"
        value="True" />
      <placeholder
        name="VisibilityCondition"
        value="02d17d71-e70a-4507-82ff-eb6570c12862" />
      <placeholder
        name="Header"
        value="New Section"
        resid="f53b277b-d6cf-481a-825e-a54ea692c8d9" />
      <placeholder
        name="HeaderAlignment"
        value="Left" />
      <placeholder
        name="HeaderDisplayMode"
        value="Hidden" />
      <placeholder
        name="BackgroundImagePk"
        value="" />
      <placeholder
        name="BackgroundImageOverride"
        value="" />
      <control
        code="FLB"
        id="c858f86c-e4af-49e2-b7d9-30d219a7ad9e"
        left="0"
        top="0"
        width="28.9"
        height="2">
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="LabelText"
          value="INTERCEPTED"
          resid="ceecbf07-51ee-4369-949d-514f6728c449" />
        <placeholder
          name="LabelType"
          value="Notification" />
        <placeholder
          name="LabelNotificationType"
          value="Error" />
        <placeholder
          name="ForegroundThemeColor"
          value="GeneralText" />
        <placeholder
          name="TextSize"
          value="ExtraExtraLarge" />
        <placeholder
          name="FontWeight"
          value="Normal" />
        <placeholder
          name="LabelAlignment"
          value="Center" />
        <placeholder
          name="BackgroundColor"
          value="" />
        <placeholder
          name="Padding"
          value="None" />
      </control>
      <control
        code="LBL"
        id="b68c5fd0-6181-493f-9f41-343dff73a62c"
        left="0"
        top="2"
        width="28.9"
        height="1.7"
        binding="CurrentItem.Consignment.DetailedImportStatusWithBrackets">
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="LabelType"
          value="Notification" />
        <placeholder
          name="LabelNotificationType"
          value="Error" />
        <placeholder
          name="ForegroundThemeColor"
          value="GeneralText" />
        <placeholder
          name="TextSize"
          value="Large" />
        <placeholder
          name="FontWeight"
          value="Normal" />
        <placeholder
          name="LabelAlignment"
          value="Center" />
        <placeholder
          name="BackgroundColor"
          value="" />
        <placeholder
          name="Padding"
          value="None" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="LBL"
        id="10db484c-e4a1-4217-b505-74a700393351"
        left="0"
        top="4"
        width="28.9"
        height="3"
        binding="CurrentItem.HVI_ItemId">
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="LabelType"
          value="Notification" />
        <placeholder
          name="LabelNotificationType"
          value="Error" />
        <placeholder
          name="ForegroundThemeColor"
          value="GeneralText" />
        <placeholder
          name="TextSize"
          value="ExtraExtraLarge" />
        <placeholder
          name="FontWeight"
          value="Normal" />
        <placeholder
          name="LabelAlignment"
          value="Center" />
        <placeholder
          name="BackgroundColor"
          value="" />
        <placeholder
          name="Padding"
          value="None" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
    </control>
    <control
      code="GRP"
      id="f61f8880-a7c6-4b1c-b36b-af103a3f52db"
      left="12.6"
      top="9"
      height="1.5"
      right="12.4">
      <placeholder
        name="CanBeHidden"
        value="True" />
      <placeholder
        name="VisibilityCondition"
        value="44ff7cc7-f820-4165-9a87-30a0f2855cd5" />
      <placeholder
        name="Header"
        value="New Section"
        resid="0e6ea405-67cc-4b6b-8995-c851d619e6d7" />
      <placeholder
        name="HeaderAlignment"
        value="Left" />
      <placeholder
        name="HeaderDisplayMode"
        value="Hidden" />
      <placeholder
        name="BackgroundImagePk"
        value="" />
      <placeholder
        name="BackgroundImageOverride"
        value="" />
      <control
        code="TTX"
        id="215667da-b859-4b9b-9e2e-d6a5e950326a"
        left="0"
        top="0"
        height="1"
        right="0">
        <placeholder
          name="IsInitialFocus"
          value="True" />
        <placeholder
          name="CaptionOverride"
          value="Barcode"
          resid="cfdc5f2f-46a4-42dd-aee2-8ac84b674964" />
      </control>
    </control>
  </form>
