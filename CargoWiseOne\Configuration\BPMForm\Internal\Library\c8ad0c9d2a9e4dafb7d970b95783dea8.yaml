#transformationVersion: 70.0
#
VZ_PK: c8ad0c9d2a9e4dafb7d970b95783dea8
VZ_ConfigurationKey: c8ad0c9d-2a9e-4daf-b7d9-70b95783dea8
VZ_FormID: Show Form - General
VZ_Caption:
  resKey: VZ_Caption|c8ad0c9d2a9e4dafb7d970b95783dea8
  text: Show Form - General
VZ_FormFactor: DSK
VZ_FormType: PAG
VZ_RequiresLogin: true
VZ_MaterialDesign: true
VZ_Dependencies: <dependencies xmlns="http://cargowise.com/glow/2014/11/14/dependencies.xsd" />
VZ_FormData: >-
  <form
    id="bf55b78b-6ad4-4db1-ac32-61a68327699a" xmlns="http://wisetechglobal.com/glow/2017/05/26/form.xsd">
    <placeholder
      name="FitToHeight"
      value="True" />
    <placeholder
      name="Layout"
      value="fill" />
    <control
      code="PNL"
      id="aa13b003-652b-486c-aa0a-9aacc8ca1fc7"
      binding="">
      <placeholder
        name="Margin"
        value="mb-3" />
      <placeholder
        name="Class"
        value="blue lighten-4 bg-blue-lighten-4" />
      <control
        code="BOX"
        id="7c25f33c-fbfa-42a7-9bd8-7b0faa0e52c5">
        <placeholder
          name="Layout"
          value="flex" />
        <control
          code="IBT"
          id="1e66cfaf-9f0c-4139-8619-865e1dc1ede5"
          binding="">
          <placeholder
            name="Icon"
            value="s-icon-documentation" />
          <placeholder
            name="Tooltip"
            value="Documentation"
            resid="1c82d486-06a5-443a-bc28-ad81fd7f6d99" />
          <placeholder
            name="Target"
            value="_blank" />
          <placeholder
            name="Hyperlink"
            value="https://wisetechglobal.sharepoint.com/sites/Content-as-Code/Shared%20Documents/GLOW-Content/_permalinks/PB-ShowFormActivity.aspx" />
        </control>
        <control
          code="IBT"
          id="c923fa67-728c-4a64-a17c-c5f1cf6177f6"
          binding="">
          <placeholder
            name="Icon"
            value="s-icon-pt-devtools" />
          <placeholder
            name="Margin"
            value="ml-2" />
          <placeholder
            name="Tooltip"
            value="YAML"
            resid="ff85dd8f-a254-48b9-8f03-279db7954863" />
          <placeholder
            name="Target"
            value="_blank" />
          <placeholder
            name="Hyperlink"
            value="https://github.com/WiseTechGlobal/Glow/blob/master/CargoWiseOne/Configuration/BPMFormFlow/Internal/Library/1646d401ae264d71a7f0a7e0f1158a6b.yaml" />
        </control>
        <control
          code="IBT"
          id="66c6523a-a586-4f80-bf15-5fe88e4c188d"
          binding="">
          <placeholder
            name="Icon"
            value="s-icon-settings" />
          <placeholder
            name="Tooltip"
            value="Platform Builder"
            resid="8eee51ba-1625-412e-ac60-ee8bf1e9d540" />
          <placeholder
            name="Target"
            value="_blank" />
          <placeholder
            name="Margin"
            value="ml-2" />
          <placeholder
            name="Hyperlink"
            value="platformbuilder:?target=BPMFormFlow&amp;identifier=1646d401ae264d71a7f0a7e0f1158a6b" />
        </control>
        <control
          code="BOX"
          id="ab662a10-26ad-4bf7-b242-41e5fb1e50b1">
          <placeholder
            name="Margin"
            value="ml-3" />
          <placeholder
            name="Layout"
            value="flex" />
          <placeholder
            name="FlexDirection"
            value="flex-column" />
          <placeholder
            name="FlexJustify"
            value="justify-center" />
          <placeholder
            name="FlexAlign"
            value="align-center" />
          <control
            code="LBL"
            id="20fa8069-65fa-4690-b763-b92e9d9c5718"
            binding="">
            <placeholder
              name="Display"
              value="block" />
            <placeholder
              name="Caption"
              value="To have a hands on experience of the example within the Documentation, select a record in the data table below then check out the Action Menu Item called &quot;Show Form - General&quot;."
              resid="7b4e38d3-71fb-41ee-9b3a-0dcaff2523af" />
            <placeholder
              name="Typography"
              value="body-strong" />
          </control>
        </control>
      </control>
    </control>
    <control
      code="PNL"
      id="bf89367e-0e3e-4b63-b49a-1869ffe575bd"
      binding="">
      <placeholder
        name="FillAvailable"
        value="True" />
      <placeholder
        name="Layout"
        value="fill" />
      <control
        code="SDT"
        id="db531709-667d-47e2-aa34-65a12615a073"
        binding="">
        <placeholder
          name="EntityType"
          value="IDtbLandTransportConsignment" />
        <placeholder
          name="CaptionOverride"
          value="List to explain the usage of the &quot;Show Form&quot; activity"
          resid="483aa4a2-ec4d-4db5-ae56-e0c2f746938d" />
        <placeholder
          name="HideActions"
          value="True" />
        <placeholder
          name="ShowSelect"
          value="True" />
        <placeholder
          name="DisabledGridRowActions"
          value="eDocs,Notes,Logs,Remove,Documents,Workflow" />
        <placeholder
          name="ActionMenuItems">
          <xml>
            <formFlows xmlns="">
              <formFlow>1646d401ae264d71a7f0a7e0f1158a6b</formFlow>
            </formFlows>
          </xml>
        </placeholder>
        <placeholder
          name="FitToHeight"
          value="True" />
        <placeholder
          name="HideFilters"
          value="True" />
      </control>
    </control>
  </form>
