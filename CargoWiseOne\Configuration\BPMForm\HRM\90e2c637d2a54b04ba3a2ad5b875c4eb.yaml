#transformationVersion: 70.0
#
VZ_PK: 90e2c637d2a54b04ba3a2ad5b875c4eb
VZ_ConfigurationKey: 90e2c637-d2a5-4b04-ba3a-2ad5b875c4eb
VZ_FormID: HRM - My Leave Requests
VZ_Caption:
  resKey: VZ_Caption|90e2c637-d2a5-4b04-ba3a-2ad5b875c4eb
  text: My Leave Requests
VZ_FormFactor: DSK
VZ_EntityType: IGlbStaff
VZ_FormType: FRM
VZ_RequiresLogin: true
VZ_MaterialDesign: true
VZ_Dependencies: <dependencies xmlns="http://cargowise.com/glow/2014/11/14/dependencies.xsd" />
VZ_FormData: >-
  <form
    id="d8074ecf-6526-4f24-a1ce-8e22d000f3ef" xmlns="http://wisetechglobal.com/glow/2017/05/26/form.xsd">
    <placeholder
      name="FitToHeight"
      value="true" />
    <control
      code="PNL"
      id="c71b96c7-ae66-466f-ba51-d0feec717ad5"
      binding="">
      <placeholder
        name="Caption"
        value="Balance Projection"
        resid="77447bef-dd76-45c2-94bd-ab2c158d1e5c" />
      <control
        code="DAE"
        id="998ba37a-c6de-4673-9595-0267afe3d42a"
        binding="LeaveProjectionSelectedDate">
        <placeholder
          name="CaptionOverride"
          value="Projection Date"
          resid="c3b6934a-f2fd-432f-859b-be5118dae1f0" />
      </control>
      <control
        code="CMP"
        id="5cead302-0818-4993-8700-356a00bfb158"
        binding="">
        <placeholder
          name="Component"
          value="cargoWiseOne.productHrm.components.ProcessedBalancesBucket" />
      </control>
    </control>
    <control
      code="TBS"
      id="aaa922f7-8256-43ba-aa78-00d861ae198f">
      <placeholder
        name="Application"
        value="True" />
      <control
        code="TAB"
        id="415700e6-51a9-41eb-98f0-eed133e044bd">
        <placeholder
          name="Caption"
          value="Pending"
          resid="db465273-6da5-4d81-b4db-34f7c59aec93" />
      </control>
      <control
        code="TAI"
        id="c7fd2ed6-2613-40e5-b129-1abc78af7781">
        <control
          code="SDT"
          id="6465c914-8a67-48c6-b0ce-4cb38b7142a4"
          binding="">
          <placeholder
            name="Margin"
            value="mt-2" />
          <placeholder
            name="CaptionOverride"
            value="Pending Approval Leave Requests"
            resid="7dae4769-1024-4f3f-9360-d1b04794ef7f" />
          <placeholder
            name="FullPage"
            value="True" />
          <placeholder
            name="EntityType"
            value="IGlbStaffHoliday" />
          <placeholder
            name="NewCaption"
            value="New Leave Request" />
          <placeholder
            name="NewFormFlowConfiguration">
            <xml>
              <formFlows xmlns="">
                <formFlow>3a804387c11c4a53801c5819d48ac26c</formFlow>
              </formFlows>
            </xml>
          </placeholder>
          <placeholder
            name="DefaultFilter">
            <xml>
              <ArrayOfFilterGroup xmlns:a="http://schemas.microsoft.com/2003/10/Serialization/Arrays" xmlns:i="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://cargowise.com/glow/2014/07/16/filters.xsd">
                <FilterGroup>
                  <Filters>
                    <Filter>
                      <FilterType>StringFilter</FilterType>
                      <Operation>Is</Operation>
                      <PropertyPath>GA_RecordType</PropertyPath>
                      <Values>
                        <a:string>LEV</a:string>
                      </Values>
                    </Filter>
                    <Filter>
                      <FilterType>BoolFilter</FilterType>
                      <Operation>Is</Operation>
                      <PropertyPath>IsSelf</PropertyPath>
                      <Values>
                        <a:string>true</a:string>
                      </Values>
                    </Filter>
                    <Filter>
                      <FilterType>SimpleLookupFilter</FilterType>
                      <Operation>IsNot</Operation>
                      <PropertyPath>GA_ApprovalStatus</PropertyPath>
                      <Values>
                        <a:string>APP</a:string>
                      </Values>
                    </Filter>
                    <Filter>
                      <FilterType>SimpleLookupFilter</FilterType>
                      <Operation>IsNot</Operation>
                      <PropertyPath>GA_ApprovalStatus</PropertyPath>
                      <Values>
                        <a:string>DEC</a:string>
                      </Values>
                    </Filter>
                    <Filter>
                      <FilterType>SimpleLookupFilter</FilterType>
                      <Operation>IsNot</Operation>
                      <PropertyPath>GA_ApprovalStatus</PropertyPath>
                      <Values>
                        <a:string>CAN</a:string>
                      </Values>
                    </Filter>
                    <Filter>
                      <FilterType>SimpleLookupFilter</FilterType>
                      <Operation>IsNot</Operation>
                      <PropertyPath>GA_ApprovalStatus</PropertyPath>
                      <Values>
                        <a:string>APC</a:string>
                      </Values>
                    </Filter>
                  </Filters>
                  <IsImplicit>true</IsImplicit>
                </FilterGroup>
              </ArrayOfFilterGroup>
            </xml>
          </placeholder>
          <placeholder
            name="FieldConfiguration">
            <xml>
              <fields xmlns="">
                <field
                  path="GA_StartTime"
                  width="180"
                  mode="Default" />
                <field
                  path="GA_EndTime"
                  width="180"
                  mode="Default" />
                <field
                  path="GA_DaysLeaveTaken"
                  width="90"
                  mode="Default" />
                <field
                  path="LeaveHours"
                  width="90"
                  mode="Default" />
                <field
                  path="GA_ApprovalStatus"
                  width="180"
                  mode="Default" />
                <field
                  path="GA_WorkHolidayType"
                  width="200"
                  mode="Default" />
                <field
                  path="LeaveTypeCode"
                  width="80"
                  mode="Default" />
                <field
                  path="LeaveTypeName"
                  width="120"
                  mode="Default" />
                <field
                  path="GA_LeaveComment"
                  width="250"
                  mode="Optional" />
                <field
                  path="ApprovalManagerName"
                  width="200"
                  mode="Default" />
                <field
                  path="LastApprovalManager"
                  width="300"
                  mode="Optional" />
                <field
                  path="LatestStatusChangeAt"
                  width="120"
                  mode="Default" />
                <field
                  path="GA_GS"
                  width="200"
                  mode="Default" />
                <field
                  path="GlbStaff.GS_Code"
                  width="80"
                  mode="Default" />
                <field
                  path="GlbStaff.GS_FullName"
                  width="120"
                  mode="Default" />
                <field
                  path="LastUpdatedBy"
                  width="250"
                  mode="Default" />
                <field
                  path="ApprovalTime"
                  width="130"
                  mode="Optional" />
                <field
                  path="ProcessingStatus"
                  width="250"
                  mode="Optional" />
                <field
                  path="ProcessingDate"
                  width="150"
                  mode="Optional" />
                <field
                  path="ProcessingBy"
                  width="250"
                  mode="Optional" />
                <field
                  path="GlbStaff.GS_RN_NKCountryCode"
                  width="250"
                  mode="Default" />
                <field
                  path="GlbStaff.GS_FullNameInMotherLanguage"
                  width="250"
                  mode="Optional" />
                <field
                  path="GlbStaff.GS_GivenName"
                  width="250"
                  mode="Optional" />
                <field
                  path="GlbStaff.GS_Surname"
                  width="250"
                  mode="Optional" />
              </fields>
            </xml>
          </placeholder>
          <placeholder
            name="DisabledGridRowActions"
            value="Remove,Workflow" />
          <placeholder
            name="DefaultSortFields">
            <xml>
              <ArrayOfFieldSortDefinition xmlns:i="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://schemas.datacontract.org/2004/07/CargoWise.Glow.Infrastructure.Interfaces">
                <FieldSortDefinition>
                  <FieldName>GA_StartTime</FieldName>
                  <IsAscending>true</IsAscending>
                </FieldSortDefinition>
              </ArrayOfFieldSortDefinition>
            </xml>
          </placeholder>
          <placeholder
            name="ShowCustomize"
            value="True" />
        </control>
      </control>
      <control
        code="TAB"
        id="3d4ac3d2-59ae-44dc-9f66-d35673571448">
        <placeholder
          name="Caption"
          value="Upcoming"
          resid="0763f746-ce44-4dd4-a87e-ba839b5bb3c0" />
      </control>
      <control
        code="TAI"
        id="c22d623b-a5f6-42bf-98d6-917ebd89e0aa">
        <control
          code="SDT"
          id="7b4bda78-3434-4aea-b47e-4033e8c33514"
          binding="">
          <placeholder
            name="Margin"
            value="mt-2" />
          <placeholder
            name="CaptionOverride"
            value="Upcoming Leave Requests"
            resid="fe79bacb-d73d-46ed-b95f-ba5b6682c742" />
          <placeholder
            name="FullPage"
            value="True" />
          <placeholder
            name="EntityType"
            value="IGlbStaffHoliday" />
          <placeholder
            name="NewCaption"
            value="New Leave Request" />
          <placeholder
            name="NewFormFlowConfiguration">
            <xml>
              <formFlows xmlns="">
                <formFlow>3a804387c11c4a53801c5819d48ac26c</formFlow>
              </formFlows>
            </xml>
          </placeholder>
          <placeholder
            name="DefaultFilter">
            <xml>
              <ArrayOfFilterGroup xmlns:a="http://schemas.microsoft.com/2003/10/Serialization/Arrays" xmlns:i="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://cargowise.com/glow/2014/07/16/filters.xsd">
                <FilterGroup>
                  <Filters>
                    <Filter>
                      <FilterType>StringFilter</FilterType>
                      <Operation>Is</Operation>
                      <PropertyPath>GA_RecordType</PropertyPath>
                      <Values>
                        <a:string>LEV</a:string>
                      </Values>
                    </Filter>
                    <Filter>
                      <FilterType>BoolFilter</FilterType>
                      <Operation>Is</Operation>
                      <PropertyPath>IsSelf</PropertyPath>
                      <Values>
                        <a:string>true</a:string>
                      </Values>
                    </Filter>
                    <Filter>
                      <FilterType>DateTimeFilter</FilterType>
                      <Operation>IsInTheFuture</Operation>
                      <PropertyPath>GA_StartTime</PropertyPath>
                      <Values />
                    </Filter>
                    <Filter>
                      <FilterType>SimpleLookupFilter</FilterType>
                      <Operation>Is</Operation>
                      <PropertyPath>GA_ApprovalStatus</PropertyPath>
                      <Values>
                        <a:string>APP</a:string>
                        <a:string>APC</a:string>
                      </Values>
                    </Filter>
                  </Filters>
                  <IsImplicit>true</IsImplicit>
                </FilterGroup>
              </ArrayOfFilterGroup>
            </xml>
          </placeholder>
          <placeholder
            name="FieldConfiguration">
            <xml>
              <fields xmlns="">
                <field
                  path="GA_StartTime"
                  width="180"
                  mode="Default" />
                <field
                  path="GA_EndTime"
                  width="180"
                  mode="Default" />
                <field
                  path="GA_DaysLeaveTaken"
                  width="90"
                  mode="Default" />
                <field
                  path="LeaveHours"
                  width="90"
                  mode="Default" />
                <field
                  path="GA_ApprovalStatus"
                  width="180"
                  mode="Default" />
                <field
                  path="GA_WorkHolidayType"
                  width="200"
                  mode="Default" />
                <field
                  path="LeaveTypeCode"
                  width="80"
                  mode="Default" />
                <field
                  path="LeaveTypeName"
                  width="120"
                  mode="Default" />
                <field
                  path="GA_LeaveComment"
                  width="250"
                  mode="Optional" />
                <field
                  path="ApprovalManagerName"
                  width="200"
                  mode="Default" />
                <field
                  path="LastApprovalManager"
                  width="300"
                  mode="Optional" />
                <field
                  path="LatestStatusChangeAt"
                  width="120"
                  mode="Default" />
                <field
                  path="GA_GS"
                  width="200"
                  mode="Default" />
                <field
                  path="GlbStaff.GS_Code"
                  width="80"
                  mode="Default" />
                <field
                  path="GlbStaff.GS_FullName"
                  width="120"
                  mode="Default" />
                <field
                  path="LastUpdatedBy"
                  width="250"
                  mode="Default" />
                <field
                  path="ApprovalTime"
                  width="130"
                  mode="Optional" />
                <field
                  path="ProcessingStatus"
                  width="250"
                  mode="Optional" />
                <field
                  path="ProcessingDate"
                  width="150"
                  mode="Optional" />
                <field
                  path="ProcessingBy"
                  width="250"
                  mode="Optional" />
                <field
                  path="GlbStaff.GS_RN_NKCountryCode"
                  width="250"
                  mode="Default" />
                <field
                  path="GlbStaff.GS_FullNameInMotherLanguage"
                  width="250"
                  mode="Optional" />
                <field
                  path="GlbStaff.GS_GivenName"
                  width="250"
                  mode="Optional" />
                <field
                  path="GlbStaff.GS_Surname"
                  width="250"
                  mode="Optional" />
              </fields>
            </xml>
          </placeholder>
          <placeholder
            name="DisabledGridRowActions"
            value="Remove" />
          <placeholder
            name="DefaultSortFields">
            <xml>
              <ArrayOfFieldSortDefinition xmlns:i="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://schemas.datacontract.org/2004/07/CargoWise.Glow.Infrastructure.Interfaces">
                <FieldSortDefinition>
                  <FieldName>GA_StartTime</FieldName>
                  <IsAscending>true</IsAscending>
                </FieldSortDefinition>
              </ArrayOfFieldSortDefinition>
            </xml>
          </placeholder>
        </control>
      </control>
      <control
        code="TAB"
        id="36d931a2-67cc-4bb1-bc8d-9ee41eb27b5e">
        <placeholder
          name="Caption"
          value="Activity"
          resid="08037078-b825-4527-845c-76c3e5504bc6" />
      </control>
      <control
        code="TAI"
        id="0da34f3e-4a7a-4aed-9b74-51fffe086c14">
        <control
          code="SDT"
          id="3941f13c-6705-41cf-a8a7-ee665831271a"
          binding="">
          <placeholder
            name="Margin"
            value="mt-2" />
          <placeholder
            name="CaptionOverride"
            value="Active Leave Requests"
            resid="e64a4860-7a65-4de8-b22b-6f163ac1f42f" />
          <placeholder
            name="FullPage"
            value="True" />
          <placeholder
            name="NewCaption"
            value="New Leave Request" />
          <placeholder
            name="NewFormFlowConfiguration">
            <xml>
              <formFlows xmlns="">
                <formFlow>3a804387c11c4a53801c5819d48ac26c</formFlow>
              </formFlows>
            </xml>
          </placeholder>
          <placeholder
            name="DefaultFilter">
            <xml>
              <ArrayOfFilterGroup xmlns:a="http://schemas.microsoft.com/2003/10/Serialization/Arrays" xmlns:i="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://cargowise.com/glow/2014/07/16/filters.xsd">
                <FilterGroup>
                  <Filters>
                    <Filter>
                      <FilterType>StringFilter</FilterType>
                      <Operation>Is</Operation>
                      <PropertyPath>GA_RecordType</PropertyPath>
                      <Values>
                        <a:string>LEV</a:string>
                      </Values>
                    </Filter>
                    <Filter>
                      <FilterType>BoolFilter</FilterType>
                      <Operation>Is</Operation>
                      <PropertyPath>IsSelf</PropertyPath>
                      <Values>
                        <a:string>true</a:string>
                      </Values>
                    </Filter>
                  </Filters>
                  <IsImplicit>true</IsImplicit>
                </FilterGroup>
              </ArrayOfFilterGroup>
            </xml>
          </placeholder>
          <placeholder
            name="EntityType"
            value="IGlbStaffHoliday" />
          <placeholder
            name="FieldConfiguration">
            <xml>
              <fields xmlns="">
                <field
                  path="GA_StartTime"
                  width="180"
                  mode="Default" />
                <field
                  path="GA_EndTime"
                  width="180"
                  mode="Default" />
                <field
                  path="GA_DaysLeaveTaken"
                  width="90"
                  mode="Default" />
                <field
                  path="LeaveHours"
                  width="90"
                  mode="Default" />
                <field
                  path="GA_ApprovalStatus"
                  width="180"
                  mode="Default" />
                <field
                  path="GA_WorkHolidayType"
                  width="200"
                  mode="Default" />
                <field
                  path="LeaveTypeCode"
                  width="80"
                  mode="Default" />
                <field
                  path="LeaveTypeName"
                  width="120"
                  mode="Default" />
                <field
                  path="GA_LeaveComment"
                  width="250"
                  mode="Optional" />
                <field
                  path="ApprovalManagerName"
                  width="200"
                  mode="Default" />
                <field
                  path="LastApprovalManager"
                  width="300"
                  mode="Optional" />
                <field
                  path="LatestStatusChangeAt"
                  width="120"
                  mode="Default" />
                <field
                  path="GA_GS"
                  width="200"
                  mode="Default" />
                <field
                  path="GlbStaff.GS_Code"
                  width="80"
                  mode="Default" />
                <field
                  path="GlbStaff.GS_FullName"
                  width="120"
                  mode="Default" />
                <field
                  path="LastUpdatedBy"
                  width="250"
                  mode="Default" />
                <field
                  path="ApprovalTime"
                  width="130"
                  mode="Optional" />
                <field
                  path="ProcessingStatus"
                  width="250"
                  mode="Optional" />
                <field
                  path="ProcessingDate"
                  width="150"
                  mode="Optional" />
                <field
                  path="ProcessingBy"
                  width="250"
                  mode="Optional" />
                <field
                  path="GlbStaff.GS_RN_NKCountryCode"
                  width="250"
                  mode="Default" />
                <field
                  path="GlbStaff.GS_FullNameInMotherLanguage"
                  width="250"
                  mode="Optional" />
                <field
                  path="GlbStaff.GS_GivenName"
                  width="250"
                  mode="Optional" />
                <field
                  path="GlbStaff.GS_Surname"
                  width="250"
                  mode="Optional" />
              </fields>
            </xml>
          </placeholder>
          <placeholder
            name="DisabledGridRowActions"
            value="Remove" />
          <placeholder
            name="DefaultSortFields">
            <xml>
              <ArrayOfFieldSortDefinition xmlns:i="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://schemas.datacontract.org/2004/07/CargoWise.Glow.Infrastructure.Interfaces">
                <FieldSortDefinition>
                  <FieldName>GA_StartTime</FieldName>
                  <IsAscending>true</IsAscending>
                </FieldSortDefinition>
              </ArrayOfFieldSortDefinition>
            </xml>
          </placeholder>
        </control>
      </control>
      <control
        code="TAB"
        id="884f2cdc-14ea-42d1-b141-be820a7e29d4"
        binding="">
        <placeholder
          name="Caption"
          value="Calendar"
          resid="2b810bcb-df52-495f-8c1c-f4c961b63846" />
      </control>
      <control
        code="TAI"
        id="9588c596-e912-47d8-a0b6-dfdf11c9c531">
        <placeholder
          name="Layout"
          value="fill" />
        <placeholder
          name="FlexDirection"
          value="flex-column" />
        <placeholder
          name="FitToHeight"
          value="True" />
        <control
          code="CMP"
          id="ca59a571-ac08-4373-bbf0-555d51294abd"
          binding="">
          <placeholder
            name="Component"
            value="cargoWiseOne.productHrm.components.LeaveCalendar" />
        </control>
      </control>
    </control>
  </form>
