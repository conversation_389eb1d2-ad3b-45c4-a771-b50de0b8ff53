#transformationVersion: 70.0
#
VZ_PK: f51908f8b28443039dc46217ff4566f7
VZ_ConfigurationKey: f51908f8-b284-4303-9dc4-6217ff4566f7
VZ_FormID: CCA - Allocate Consolidations & Containers (Row Action)
VZ_Caption:
  resKey: VZ_Caption|f51908f8-b284-4303-9dc4-6217ff4566f7
  text: Allocate Consolidations & Containers
VZ_FormFactor: DSK
VZ_EntityType: IRatingContractAllocationLine
VZ_FormType: FRM
VZ_RequiresLogin: true
VZ_MaterialDesign: true
VZ_ExtenderFunc: CargoWise.Glow.Business.Forwarding.Contracts.AllocateConsolidationsAndContainersFormExtenderG2.Extend
VZ_Dependencies: <dependencies xmlns="http://cargowise.com/glow/2014/11/14/dependencies.xsd" />
VZ_FormData: >-
  <form
    id="6b985f15-2148-45c0-8ab4-728866aefc9a" xmlns="http://wisetechglobal.com/glow/2017/05/26/form.xsd">
    <placeholder
      name="FlexDirection"
      value="flex-column" />
    <placeholder
      name="Layout"
      value="fill" />
    <placeholder
      name="FitToHeight"
      value="True" />
    <control
      code="PNL"
      id="9eaa3f2e-09ca-434d-9645-f3845a1f84c4"
      binding="">
      <placeholder
        name="Caption"
        value="Selected Route"
        resid="bd1e00a4-f904-43e5-927d-89e0a602898b" />
      <placeholder
        name="Layout"
        value="grid" />
      <placeholder
        name="FlexWrap"
        value="flex-wrap" />
      <placeholder
        name="Margin"
        value="mb-5" />
      <placeholder
        name="MinWidth"
        value="85vw" />
      <placeholder
        name="Columns"
        value="col-12" />
      <control
        code="TXT"
        id="92ecda9b-7909-4852-87ba-0c97b8d598b3"
        binding="RCA_AllocationLineID">
        <placeholder
          name="IsReadOnly"
          value="True" />
        <placeholder
          name="Columns"
          value="col-2" />
      </control>
      <control
        code="DTE"
        id="fd187172-79da-41b4-a896-488fc559c303"
        binding="RCA_StartDate">
        <placeholder
          name="IsReadOnly"
          value="True" />
        <placeholder
          name="Columns"
          value="col-2" />
      </control>
      <control
        code="DTE"
        id="7c5378cf-4352-4a31-9245-29366e3cfc83"
        binding="RCA_ExpiryDate">
        <placeholder
          name="IsReadOnly"
          value="True" />
        <placeholder
          name="Columns"
          value="col-2" />
      </control>
      <control
        code="SRC"
        id="ae87c7f1-ef34-4e44-9f57-d3b49a24a824"
        binding="LoadLocationWithProxy">
        <placeholder
          name="IsReadOnly"
          value="True" />
        <placeholder
          name="Columns"
          value="col-3" />
      </control>
      <control
        code="SRC"
        id="d5ea160f-20ed-44c8-a214-cc0444eef5fb"
        binding="DischargeLocationWithProxy">
        <placeholder
          name="IsReadOnly"
          value="True" />
        <placeholder
          name="Columns"
          value="col-3" />
      </control>
      <control
        code="TXT"
        id="3cbfe536-5564-400d-84ae-f4d804df629b"
        binding="VoyageWithProxy">
        <placeholder
          name="IsReadOnly"
          value="True" />
        <placeholder
          name="Columns"
          value="col-3" />
      </control>
      <control
        code="SRC"
        id="273691bd-7521-4049-906a-4c62a352149b"
        binding="VesselWithProxy">
        <placeholder
          name="IsReadOnly"
          value="True" />
        <placeholder
          name="Columns"
          value="col-3" />
      </control>
      <control
        code="TXT"
        id="1f410f10-a62b-4efc-93f0-3f589506e487"
        binding="ServiceStringWithProxy">
        <placeholder
          name="IsReadOnly"
          value="True" />
        <placeholder
          name="Columns"
          value="col-3" />
      </control>
      <control
        code="OPT"
        id="84db78ce-9548-4614-a859-03b94272a930"
        binding="RCA_AllowRelatedPorts">
        <placeholder
          name="Columns"
          value="col-3" />
        <placeholder
          name="IsReadOnly"
          value="True" />
      </control>
    </control>
    <control
      code="SDT"
      id="b1f38ece-f115-4506-a21b-1f08967cf08a"
      binding="">
      <placeholder
        name="CaptionOverride"
        value="Search Consolidations with/without Containers for Allocation"
        resid="8869a33d-11f9-4046-9100-dd956130d4b2" />
      <placeholder
        name="EntityType"
        value="IJobConsolAndContainer" />
      <placeholder
        name="FieldConfiguration">
        <xml>
          <fields xmlns="">
            <field
              path="JCK_ConsolNumber"
              width="140"
              mode="Default" />
            <field
              path="JCK_MasterBillNumber"
              width="140"
              mode="Default" />
            <field
              path="JCK_AgentType"
              width="100"
              mode="Default" />
            <field
              path="JCK_TransportMode"
              width="140"
              mode="Default" />
            <field
              path="JCK_ConsolMode"
              width="110"
              mode="Default" />
            <field
              path="JCK_LoadPort"
              width="90"
              mode="Default" />
            <field
              path="JCK_DischargePort"
              width="110"
              mode="Default" />
            <field
              path="SendingForwarderAddress.OrgHeader.OH_Code"
              width="169"
              mode="Default" />
            <field
              path="ReceivingForwarderAddress.OrgHeader.OH_Code"
              width="1699"
              mode="Default" />
            <field
              path="JCK_ContainerNumber"
              width="150"
              mode="Default" />
            <field
              path="JCK_ContainerCount"
              width="150"
              mode="Default" />
            <field
              path="JCK_ContainerMode"
              width="140"
              mode="Default" />
            <field
              path="JCK_CommodityCode"
              width="140"
              mode="Default" />
            <field
              path="JCK_ContractNumber"
              width="250"
              mode="Optional" />
            <field
              path="JobContainer.JC_RC"
              width="250"
              mode="Default" />
            <field
              path="JobContainer.RefContainer.RC_StorageClass"
              width="250"
              mode="Default" />
            <field
              path="JobContainer.RefContainer.RC_FreightRateClass"
              width="250"
              mode="Default" />
            <field
              path="JobContainer.RefContainer.RC_TEU"
              width="60"
              mode="Default" />
            <field
              path="ConsolAllocationLine.RCA_AllocationLineID"
              width="250"
              mode="Optional" />
            <field
              path="ContainerAllocationLine.RCA_AllocationLineID"
              width="250"
              mode="Optional" />
            <field
              path="JobConsol.ShippingLineAddress.OA_OH"
              width="250"
              mode="Default" />
            <field
              path="Vessel"
              width="250"
              mode="Default" />
            <field
              path="ETD"
              width="250"
              mode="Default" />
            <field
              path="VoyageFlight"
              width="250"
              mode="Default" />
            <field
              path="JobConsol.TransportRoutings.JW_Vessel"
              width="250"
              mode="FilterOnly" />
            <field
              path="JobConsol.TransportRoutings.JW_ETD"
              width="180"
              mode="FilterOnly" />
            <field
              path="JobConsol.TransportRoutings.JW_VoyageFlight"
              width="250"
              mode="FilterOnly" />
            <field
              path="JobConsol.JK_RL_NKCarrierBookingOffice"
              width="300"
              mode="Optional" />
            <field
              path="ConsolLatestBookingStatusAndDate.JKS_LatestBookingStatus"
              width="300"
              caption="Ocean Booking Status"
              resid="eae3a4d7-3db2-4a58-933d-ae133126d8cf"
              mode="Optional" />
            <field
              path="ConsolLatestBookingStatusAndDate.JKS_LatestBookingDate"
              width="300"
              caption="Ocean Booking Status Date"
              resid="af2960df-d0c6-4b14-af98-29dbf76991c4"
              mode="Optional" />
            <field
              path="JobConsol.TransportRoutings"
              width="300"
              caption="Related Transport Legs"
              resid="e08a66b4-038b-4535-9dc9-c2b6337abd5d"
              mode="FilterOnly" />
          </fields>
        </xml>
      </placeholder>
      <placeholder
        name="ShowAddActions"
        value="False" />
      <placeholder
        name="ShowSelect"
        value="True" />
      <placeholder
        name="ItemsPerPage"
        value="100" />
      <placeholder
        name="FitToHeight"
        value="True" />
      <placeholder
        name="DefaultFilter">
        <xml>
          <ArrayOfFilterGroup xmlns:a="http://schemas.microsoft.com/2003/10/Serialization/Arrays" xmlns:i="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://cargowise.com/glow/2014/07/16/filters.xsd">
            <FilterGroup>
              <Filters>
                <Filter>
                  <FilterType>StringFilter</FilterType>
                  <Operation>Is</Operation>
                  <PropertyPath>JCK_TransportMode</PropertyPath>
                  <Values>
                    <a:string>&lt;RatingContract.RCT_TransportMode&gt;</a:string>
                  </Values>
                </Filter>
                <Filter>
                  <FilterType>AdvancedGuidLookupFilter</FilterType>
                  <Operation>Is</Operation>
                  <PropertyPath>JobConsol.ShippingLineAddress.OA_OH</PropertyPath>
                  <Values>
                    <a:string>&lt;RatingContract.RCT_OH&gt;</a:string>
                  </Values>
                </Filter>
                <Filter>
                  <FilterType>DateTimeFilter</FilterType>
                  <Operation>IsInTheDateRange</Operation>
                  <PropertyPath>JobConsol.TransportRoutings.JW_ETD</PropertyPath>
                  <Values>
                    <a:string>&lt;StartDateWithContractFallback&gt;</a:string>
                    <a:string>&lt;ExpiryDateWithContractFallback&gt;</a:string>
                  </Values>
                </Filter>
                <Filter>
                  <FilterType>EntityCustomFilter</FilterType>
                  <Operation>Is</Operation>
                  <PropertyPath>LoadDischargeAndShowRelatedUNLOCOsFilter</PropertyPath>
                  <Values>
                    <a:string>&lt;LoadLocationWithProxy&gt;</a:string>
                    <a:string>&lt;DischargeLocationWithProxy&gt;</a:string>
                    <a:string>&lt;RCA_AllowRelatedPorts&gt;</a:string>
                  </Values>
                </Filter>
                <Filter>
                  <FilterType>StringFilter</FilterType>
                  <Operation>Is</Operation>
                  <PropertyPath>JobConsol.TransportRoutings.JW_Vessel</PropertyPath>
                  <Values>
                    <a:string>&lt;VesselWithProxy&gt;</a:string>
                  </Values>
                </Filter>
                <Filter>
                  <FilterType>StringFilter</FilterType>
                  <Operation>Is</Operation>
                  <PropertyPath>JobConsol.TransportRoutings.JW_VoyageFlight</PropertyPath>
                  <Values>
                    <a:string>&lt;VoyageWithProxy&gt;</a:string>
                  </Values>
                </Filter>
                <Filter>
                  <FilterType>AdvancedGuidLookupFilter</FilterType>
                  <Operation>Is</Operation>
                  <PropertyPath>JobContainer.JC_RC</PropertyPath>
                  <Values>
                    <a:string>&lt;RCA_RC_ContainerType&gt;</a:string>
                  </Values>
                </Filter>
                <Filter>
                  <FilterType>StringFilter</FilterType>
                  <Operation>IsBlank</Operation>
                  <PropertyPath>ContainerAllocationLine.RCA_AllocationLineID</PropertyPath>
                  <Values />
                </Filter>
                <Filter>
                  <FilterType>StringFilter</FilterType>
                  <Operation>IsBlank</Operation>
                  <PropertyPath>ConsolAllocationLine.RCA_AllocationLineID</PropertyPath>
                  <Values />
                </Filter>
              </Filters>
              <IsImplicit>false</IsImplicit>
            </FilterGroup>
            <FilterGroup>
              <Filters>
                <Filter>
                  <FilterType>StringFilter</FilterType>
                  <Operation>IsBlank</Operation>
                  <PropertyPath>JCK_ContractNumber</PropertyPath>
                  <Values />
                </Filter>
              </Filters>
              <IsImplicit>true</IsImplicit>
            </FilterGroup>
            <FilterGroup>
              <Filters>
                <Filter>
                  <FilterType>StringFilter</FilterType>
                  <Operation>Is</Operation>
                  <PropertyPath>JCK_ContractNumber</PropertyPath>
                  <Values>
                    <a:string>&lt;RatingContract.RCT_ContractNumber&gt;</a:string>
                  </Values>
                </Filter>
              </Filters>
              <IsImplicit>true</IsImplicit>
            </FilterGroup>
          </ArrayOfFilterGroup>
        </xml>
      </placeholder>
      <placeholder
        name="ResetFilterOnInit"
        value="True" />
      <placeholder
        name="ActionMenuItems">
        <xml>
          <formFlows xmlns="">
            <formFlow>e2418038dc184dc28d450c0064eb8fe4</formFlow>
          </formFlows>
        </xml>
      </placeholder>
    </control>
    <control
      code="BOX"
      id="786b9c0e-486c-49c4-8aaa-ff68a3b1f780"
      binding="">
      <placeholder
        name="Layout"
        value="flex" />
      <placeholder
        name="FlexJustify"
        value="justify-end" />
      <control
        code="BTN"
        id="54aa8a2f-cf76-4dbb-b089-4761ec1bf60b"
        binding="">
        <placeholder
          name="Caption"
          value="Allocate Consol(s) to Contract"
          resid="6cd727a6-25ab-440f-bb94-d752c43cc701" />
        <placeholder
          name="Margin"
          value="mr-2" />
        <placeholder
          name="Transition"
          value="True" />
        <placeholder
          name="VisibilityCondition"
          value="!%.CCAEnforceConsolAllocationAtRouteLevel" />
      </control>
      <control
        code="BTN"
        id="0472c0a6-7a51-448d-8319-907eae93f437"
        binding="">
        <placeholder
          name="Caption"
          value="Allocate Consol(s) to Route"
          resid="fb6ce1a9-d2a6-45c7-be86-ff5cd1140542" />
        <placeholder
          name="Margin"
          value="mr-2" />
        <placeholder
          name="Transition"
          value="True" />
      </control>
      <control
        code="BTN"
        id="343b692c-d1d2-4c21-a386-3884ebd63c3c"
        binding="">
        <placeholder
          name="Caption"
          value="Allocate Container(s) to Route"
          resid="09dc2891-c176-4a24-8910-596535eed689" />
        <placeholder
          name="Transition"
          value="True" />
      </control>
    </control>
  </form>
