#transformationVersion: 70.0
#
VZ_PK: 7bf58a6a403f4e49b6a6af37c4c1e892
VZ_ConfigurationKey: 7bf58a6a-403f-4e49-b6a6-af37c4c1e892
VZ_FormID: EQM - Equipment Type - Search
VZ_Caption:
  resKey: VZ_Caption|7bf58a6a403f4e49b6a6af37c4c1e892
  text: Equipment types
VZ_FormFactor: DSK
VZ_FormType: PAG
VZ_RequiresLogin: true
VZ_MaterialDesign: true
VZ_Dependencies: <dependencies xmlns="http://cargowise.com/glow/2014/11/14/dependencies.xsd" />
VZ_FormData: >-
  <form
    id="be56f05b-e39e-4b6a-95ea-796eeb902ab1" xmlns="http://wisetechglobal.com/glow/2017/05/26/form.xsd">
    <placeholder
      name="Layout"
      value="fill" />
    <control
      code="SDT"
      id="593140f6-af36-478d-a919-b865877a37f0"
      binding="">
      <placeholder
        name="FullPage"
        value="True" />
      <placeholder
        name="SearchMode"
        value="Entity" />
      <placeholder
        name="CaptionOverride"
        value="Equipment types"
        resid="65e1c50e-6a49-44c9-bf06-3be999f20569" />
      <placeholder
        name="ShowSelect"
        value="True" />
      <placeholder
        name="ShowAutoRefresh"
        value="True" />
      <placeholder
        name="EntityType"
        value="IRefContainer" />
      <placeholder
        name="FieldConfiguration">
        <xml>
          <fields xmlns="">
            <field
              path="RC_Code"
              width="180"
              caption="Equipment type"
              resid="9c1d5679-881a-4674-b340-9bb28107113b"
              mode="Mandatory" />
            <field
              path="RC_Description"
              width="300"
              mode="Mandatory" />
            <field
              path="RC_StorageClass"
              width="200"
              caption="Container storage class"
              resid="c501908a-974f-4128-b677-95d29dda3720"
              mode="Optional" />
            <field
              path="RC_ShippingMode"
              width="100"
              caption="Mode"
              resid="48eec689-5dcb-463c-8cb9-3521bab2d138"
              mode="Default" />
            <field
              path="RC_ContainerType"
              width="280"
              caption="Equipment category"
              resid="07da08b4-653c-462f-a7a2-0f87244f015d"
              mode="Default" />
            <field
              path="RC_ISOEquipmentSizeTypeCode"
              width="200"
              caption="Equipment size type code"
              resid="17a24bf2-77fb-4116-aa3c-a84019e4b4fd"
              mode="Optional" />
            <field
              path="RC_FreightRateClass"
              width="250"
              caption="Freight charges rate class"
              resid="0cc684c5-76f1-4f2c-b36b-df784d56ecec"
              mode="Optional" />
            <field
              path="RC_HandlingRateClass"
              width="250"
              caption="Handling charges rate class"
              resid="462febc4-5ce6-4619-81cd-aff4a48c0fa0"
              mode="Optional" />
            <field
              path="RC_IsIso"
              width="80"
              caption="ISO"
              resid="6fb5f98b-8071-4ee0-b8b7-bc6d7e1bc5df"
              mode="Default" />
            <field
              path="RC_ISOType"
              width="120"
              caption="ISO type"
              resid="27490800-2cc0-4040-bccf-d3b7b64a7c40"
              mode="Default" />
            <field
              path="RC_IATARateClass"
              width="160"
              caption="ULD IATA rate class"
              resid="ade762ce-0546-4fa1-8da3-74e44f672aa3"
              mode="Optional" />
            <field
              path="RC_USContainerCode"
              width="170"
              caption="US container code"
              resid="f6419838-cd52-4117-82a5-dac28af011c0"
              mode="Optional" />
            <field
              path="RC_HasTynes"
              width="120"
              caption="Tynes"
              resid="a2a637ac-11f0-4917-8da1-aaf6673e9195"
              mode="Optional" />
            <field
              path="RC_HasVents"
              width="120"
              caption="Vents"
              resid="53a138a7-5389-44de-8c0d-c43314a8138c"
              mode="Optional" />
            <field
              path="RC_IsActive"
              width="120"
              caption="Active"
              resid="6c413384-b12c-49be-a4a1-dc7312f4bc2a"
              mode="Default" />
            <field
              path="RC_IsHighCube"
              width="150"
              caption="High cube"
              resid="03100e25-6871-42d3-8560-2afedb23feb8"
              mode="Optional" />
            <field
              path="RC_LengthMeasure"
              width="160"
              caption="Length"
              resid="0037577b-9397-46e4-a9c8-bd67853e032f"
              mode="Default" />
            <field
              path="RC_WidthMeasure"
              width="160"
              caption="Width"
              resid="0a83e274-f71d-4cdf-adac-d92bf82dde17"
              mode="Default" />
            <field
              path="RC_HeightMeasure"
              width="160"
              caption="Height"
              resid="a7c85f1f-ba24-44a8-820f-a86903d05dc2"
              mode="Default" />
            <field
              path="RC_CubicCapacityMeasure"
              width="160"
              caption="Cubic capacity"
              resid="3bb22089-497b-4807-90fc-a8c3a87054f2"
              mode="Optional" />
            <field
              path="RC_GrossWeightMeasure"
              width="160"
              caption="Gross weight"
              resid="cdd3e3c1-00a6-4e6b-9f50-b8b29fe5c76a"
              mode="Default" />
            <field
              path="RC_TareWeightMeasure"
              width="160"
              caption="Tare weight"
              resid="7fa001c1-bf38-4784-b8f6-18937bb3bfc1"
              mode="Optional" />
            <field
              path="RC_TEU"
              width="100"
              caption="TEU"
              resid="9f4df4ec-349d-40e1-8ce2-5df2af15636e"
              mode="Default" />
            <field
              path="RC_InsideLengthMeasure"
              width="160"
              caption="Inside length"
              resid="12d36e6e-a616-472c-8186-68a265891762"
              mode="Default" />
            <field
              path="RC_InsideWidthMeasure"
              width="180"
              caption="Inside width"
              resid="8ae34192-d040-4903-afda-5fb02d6230de"
              mode="Default" />
            <field
              path="RC_InsideHeightMeasure"
              width="180"
              caption="Inside height"
              resid="bfd97f9e-4a01-4e87-8363-58133ef3500b"
              mode="Default" />
            <field
              path="RC_NetWeightMeasure"
              width="180"
              caption="Net weight measure"
              resid="2be8ffbe-238e-4db0-83cb-1ef6d4da2e47"
              mode="Optional" />
            <field
              path="RC_DoorOpeningWidthMeasure"
              width="180"
              caption="Door opening width"
              resid="ba574b4c-5426-488f-831f-3ec9167569d5"
              mode="Optional" />
            <field
              path="RC_DoorOpeningHeightMeasure"
              width="180"
              caption="Door opening height"
              resid="3434b1b7-7903-4bb6-8d73-6a34e05155f8"
              mode="Optional" />
            <field
              path="RC_IsControlledAtmosphere"
              width="120"
              caption="Controlled atmosphere"
              resid="7591fc60-622d-459a-8ff4-d7d719e60fdb"
              mode="Optional" />
            <field
              path="RC_IsSystem"
              width="120"
              caption="System defined"
              resid="556f542d-6eb9-4d22-94e5-7ea5517beb61"
              mode="Optional" />
            <field
              path="RC_Height"
              width="150"
              caption="Height"
              resid="f223612a-ae80-4261-80cc-557c1fe1bf4d"
              mode="FilterOnly" />
            <field
              path="RC_Length"
              width="150"
              caption="Length"
              resid="1246a589-47f8-4b7b-939b-f7ace13111fc"
              mode="FilterOnly" />
            <field
              path="RC_Width"
              width="150"
              caption="Width"
              resid="2dbe8d1c-d842-4f6c-890f-4c897f0482c3"
              mode="FilterOnly" />
            <field
              path="RC_CubicCapacity"
              width="150"
              caption="Cubic capacity"
              resid="5fdf7659-04c5-45a4-aaf8-d681c4ef3f87"
              mode="FilterOnly" />
            <field
              path="RC_DoorOpeningHeight"
              width="150"
              caption="Door opening height"
              resid="a217c8b6-60b9-486d-9245-1480be8786a7"
              mode="FilterOnly" />
            <field
              path="RC_DoorOpeningWidth"
              width="150"
              caption="Door opening width"
              resid="c71b6a79-864a-465b-97df-54a4a0eb93c9"
              mode="FilterOnly" />
            <field
              path="RC_GrossWeight"
              width="150"
              caption="Gross weight"
              resid="e1df9858-8d25-44e9-a560-7257963bf8b9"
              mode="FilterOnly" />
            <field
              path="RC_InsideHeight"
              width="150"
              caption="Inside height"
              resid="0d2b145f-176e-4941-9bee-1312e43482ca"
              mode="FilterOnly" />
            <field
              path="RC_InsideLength"
              width="150"
              caption="Inside length"
              resid="fa883ede-1f29-425a-8a79-cd7ea61525fb"
              mode="FilterOnly" />
            <field
              path="RC_InsideWidth"
              width="150"
              caption="Inside width"
              resid="b84b59fa-6d7d-492c-bf4d-1ec372c09cd8"
              mode="FilterOnly" />
            <field
              path="RC_NetWeight"
              width="150"
              caption="Net weight"
              resid="1839f4a1-5138-44a4-8256-8dc0d47d983e"
              mode="FilterOnly" />
            <field
              path="RC_TareWeight"
              width="150"
              caption="Tare weight"
              resid="0445d033-7afc-4e62-ba94-4a3701e6078f"
              mode="FilterOnly" />
            <field
              path="RC_Contour"
              width="150"
              mode="Optional" />
            <field
              path="RC_CubicCapacityUQ"
              width="120"
              caption="Cubic capacity unit"
              resid="96da1edb-c47b-4f95-b532-69aa7f9cf5dd"
              mode="FilterOnly" />
            <field
              path="RC_DimensionUQ"
              width="120"
              caption="Dimension unit"
              resid="6717218e-ce7d-451b-8509-c3783bd88b57"
              mode="FilterOnly" />
            <field
              path="RC_DoorOpeningUQ"
              width="120"
              caption="Door opening unit"
              resid="f0333f5d-a420-455a-ae6b-ca8e0aac7331"
              mode="FilterOnly" />
            <field
              path="RC_InsideUQ"
              width="120"
              caption="Inside unit"
              resid="1775c522-655d-4d78-ba1a-8eae980b3ce6"
              mode="FilterOnly" />
            <field
              path="RC_WeightUQ"
              width="120"
              caption="Weight unit"
              resid="58acca37-d53c-46f1-b1a8-5c2496c5fdc5"
              mode="FilterOnly" />
            <field
              path="ISODescriptionForDisplay"
              width="300"
              caption="ISO description"
              resid="399f96e6-734c-4c67-8ce6-028e42d15eff"
              mode="Optional" />
            <field
              path="ISOSizeForDisplay"
              width="180"
              caption="ISO size"
              resid="eaa7a758-306f-4b2a-b8b8-c9b1784e5b98"
              mode="Default" />
          </fields>
        </xml>
      </placeholder>
      <placeholder
        name="ItemsPerPage"
        value="50" />
      <placeholder
        name="DefaultSortFields">
        <xml>
          <ArrayOfFieldSortDefinition xmlns:i="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://schemas.datacontract.org/2004/07/CargoWise.Glow.Infrastructure.Interfaces">
            <FieldSortDefinition>
              <FieldName>RC_Code</FieldName>
              <IsAscending>true</IsAscending>
            </FieldSortDefinition>
          </ArrayOfFieldSortDefinition>
        </xml>
      </placeholder>
      <placeholder
        name="FitToHeight"
        value="False" />
      <placeholder
        name="NewFormFlowConfiguration">
        <xml>
          <formFlows xmlns="">
            <formFlow
              newSession="True">fc443e37cff74cdbb258ba3488964937</formFlow>
          </formFlows>
        </xml>
      </placeholder>
      <placeholder
        name="ActionMenuItems">
        <xml>
          <formFlows xmlns="">
            <formFlow>c47dd517ef9642d29d211932182f9158</formFlow>
          </formFlows>
        </xml>
      </placeholder>
      <placeholder
        name="EditFormFlowConfiguration">
        <xml>
          <formFlows xmlns="">
            <formFlow>c47dd517ef9642d29d211932182f9158</formFlow>
          </formFlows>
        </xml>
      </placeholder>
    </control>
  </form>
