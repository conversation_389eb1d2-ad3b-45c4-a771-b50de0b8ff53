#transformationVersion: 70.0
#
VZ_PK: 880d02343d5e4bfe9947c23d2643d6b9
VZ_ConfigurationKey: 880d0234-3d5e-4bfe-9947-c23d2643d6b9
VZ_FormID: Billing - Edit Job Header
VZ_Caption:
  resKey: VZ_Caption|880d0234-3d5e-4bfe-9947-c23d2643d6b9
  text: Billing
VZ_FormFactor: DSK
VZ_EntityType: IJobHeader
VZ_FormType: FRM
VZ_RequiresLogin: true
VZ_Dependencies: >-
  <dependencies xmlns="http://cargowise.com/glow/2014/11/14/dependencies.xsd">
    <expandPath
      path="JobCharges" />
    <expandPath
      path="GlbBranch" />
    <expandPath
      path="RepSales" />
    <expandPath
      path="GlbDepartment" />
    <expandPath
      path="RepOps" />
    <expandPath
      path="JobCharges/SellAccount" />
    <expandPath
      path="JobCharges/RevenueLine" />
    <expandPath
      path="JobCharges/RevenueLine.SellGSTRate" />
    <expandPath
      path="JobCharges/CostAccount" />
    <expandPath
      path="JobCharges/AccBankAccount" />
    <expandPath
      path="JobCharges/AccChequeBook" />
    <expandPath
      path="JobCharges/AccChargeCode" />
    <expandPath
      path="JobCharges/InternalJob" />
    <expandPath
      path="JobCharges/GlbDepartment" />
    <expandPath
      path="JobCharges/InternalDept" />
    <expandPath
      path="JobCharges/GlbBranch" />
    <expandPath
      path="JobCharges/InternalBranch" />
    <calculatedProperty
      path="TotalRevenue" />
    <calculatedProperty
      path="TotalCost" />
    <calculatedProperty
      path="TotalProfit" />
    <calculatedProperty
      path="AutoRateCost" />
    <calculatedProperty
      path="AutoRateRevenue" />
    <datagrid
      path="JobCharges">
      <expandPath
        path="AccChargeCode" />
      <expandPath
        path="SellCurrency" />
      <expandPath
        path="SellAccount" />
      <expandPath
        path="CostCurrency" />
      <expandPath
        path="CostAccount" />
      <expandPath
        path="GlbDepartment" />
      <expandPath
        path="GlbBranch" />
      <expandPath
        path="InternalBranch" />
      <expandPath
        path="InternalDept" />
      <expandPath
        path="InternalJob" />
      <expandPath
        path="CostVATClass" />
      <expandPath
        path="SellVATClass" />
      <expandPath
        path="AccBankAccount" />
      <expandPath
        path="AccChequeBook" />
      <expandPath
        path="APLine" />
      <expandPath
        path="ARLine" />
      <expandPath
        path="CFXLine" />
      <expandPath
        path="CostGSTRate" />
      <expandPath
        path="SellGSTRate" />
      <expandPath
        path="JobConsolCost" />
      <expandPath
        path="GatewaySellHeader" />
      <expandPath
        path="GlbCompany" />
      <expandPath
        path="RevenueLine" />
      <expandPath
        path="SellInvoiceAddress" />
      <expandPath
        path="SellInvoiceContact" />
      <expandPath
        path="Product" />
      <expandPath
        path="SellInvoiceCurrency" />
    </datagrid>
  </dependencies>
VZ_FormData: >-
  <form
    id="98bd6ffc-ad66-46fb-8d4a-a681bf0eb8d6" xmlns="http://wisetechglobal.com/glow/2017/05/26/form.xsd">
    <placeholder
      name="BackgroundImagePk"
      value="" />
    <placeholder
      name="BackgroundSize"
      value="Auto" />
    <placeholder
      name="BackgroundImageOverride"
      value="" />
    <placeholder
      name="FormLayout"
      value="Splitview" />
    <placeholder
      name="ExpandPaths"
      value="" />
    <placeholder
      name="ScanInputHandling"
      value="NON" />
    <placeholder
      name="RemovePadding"
      value="false" />
    <placeholder
      name="TurnOnPortableOneToOneDesignMode"
      value="False" />
    <placeholder
      name="ContainsDefaultButton"
      value="False" />
    <control
      code="ADD"
      id="a930d2bc-6e9d-4f47-a1fe-68ef2446cc97"
      left="0"
      top="0"
      width="8"
      height="3"
      binding="JH_OA_LocalChargesAddr">
      <placeholder
        name="IsReadOnly"
        value="False" />
      <placeholder
        name="CanBeHidden"
        value="True" />
      <placeholder
        name="IsInitialFocus"
        value="False" />
      <placeholder
        name="CaptionOverride"
        value="" />
      <placeholder
        name="VisibilityCondition"
        value="" />
      <placeholder
        name="IsUNLOCOVisible"
        value="True" />
      <placeholder
        name="SuppressDependencyContributions"
        value="False" />
    </control>
    <control
      code="GRP"
      id="a81ea13c-fea8-47d9-a499-075fa0eca8af"
      left="8.2"
      top="0"
      width="18.8"
      height="4">
      <placeholder
        name="CanBeHidden"
        value="True" />
      <placeholder
        name="VisibilityCondition"
        value="" />
      <placeholder
        name="Header"
        value="References"
        resid="9ef0bb54-78b9-4f56-b05d-cc50dda2230f" />
      <placeholder
        name="HeaderAlignment"
        value="Left" />
      <placeholder
        name="HeaderDisplayMode"
        value="Hidden" />
      <placeholder
        name="BackgroundImagePk"
        value="" />
      <placeholder
        name="BackgroundImageOverride"
        value="" />
      <control
        code="TXT"
        id="db9e58a2-ef8e-4e52-9dc6-d1109d0091d1"
        left="0"
        top="0"
        width="8"
        height="1"
        binding="JH_Description">
        <placeholder
          name="IsReadOnly"
          value="True" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="OnSubmit"
          value="" />
        <placeholder
          name="MultiLine"
          value="False" />
        <placeholder
          name="EnableAutoComplete"
          value="False" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="OPT"
        id="18cee4e2-b65c-4680-9d1b-4bba177e4276"
        left="9"
        top="0"
        width="4"
        height="1"
        binding="AutoRateCost">
        <placeholder
          name="IsReadOnly"
          value="False" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="Auto Rate Cost"
          resid="56bef956-effe-45cb-9176-30324270d8a0" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="TXT"
        id="4496183f-1e95-4711-b883-1a7b87cfb892"
        left="14"
        top="0"
        width="4"
        height="1"
        binding="JH_JobNum">
        <placeholder
          name="IsReadOnly"
          value="True" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="OnSubmit"
          value="" />
        <placeholder
          name="MultiLine"
          value="False" />
        <placeholder
          name="EnableAutoComplete"
          value="False" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="DTE"
        id="dd85b139-f361-46ed-a123-7147e2b03a63"
        left="0"
        top="1"
        width="4"
        height="1"
        binding="JH_A_JOP">
        <placeholder
          name="IsReadOnly"
          value="True" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="DefaultValueExpression"
          value="" />
        <placeholder
          name="IncludeTime"
          value="True" />
        <placeholder
          name="ShowNowButton"
          value="True" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="DTE"
        id="e2a5d98f-dfd3-48f6-8d09-dd84c97966a0"
        left="4"
        top="1"
        width="4"
        height="1"
        binding="JH_A_JCL">
        <placeholder
          name="IsReadOnly"
          value="True" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="DefaultValueExpression"
          value="" />
        <placeholder
          name="IncludeTime"
          value="True" />
        <placeholder
          name="ShowNowButton"
          value="True" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="OPT"
        id="cc429b46-6676-4714-a488-64c047a1510f"
        left="9"
        top="1"
        width="4"
        height="1"
        binding="AutoRateRevenue">
        <placeholder
          name="IsReadOnly"
          value="False" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="Auto Rate Revenue"
          resid="7183c46c-9b88-45d3-abbf-8b773444565e" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="TXT"
        id="c2b7b68f-6f1d-4845-b097-b886f5e89bf0"
        left="14"
        top="1"
        width="4"
        height="1"
        binding="JH_JobLocalReference">
        <placeholder
          name="IsReadOnly"
          value="True" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="OnSubmit"
          value="" />
        <placeholder
          name="MultiLine"
          value="False" />
        <placeholder
          name="EnableAutoComplete"
          value="False" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="SRC"
        id="2a8ff7c2-bbb3-4cc2-8d38-5a60675020f1"
        left="0"
        top="2"
        width="4"
        height="1"
        binding="JH_GB">
        <placeholder
          name="IsReadOnly"
          value="False" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="DisplayMode"
          value="CodeOnly" />
        <placeholder
          name="FieldConfiguration"
          value="" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="SRC"
        id="99ad98ae-6232-4b62-b4aa-6fee0dd118cd"
        left="4"
        top="2"
        width="4"
        height="1"
        binding="JH_GS_NKRepSales">
        <placeholder
          name="IsReadOnly"
          value="False" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="DisplayMode"
          value="Unspecified" />
        <placeholder
          name="FieldConfiguration"
          value="" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="TBT"
        id="7cec461b-83d2-4385-958d-b4a105cef3cb"
        left="9"
        top="2"
        width="4"
        height="1">
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="Content"
          value="Calculate Charges"
          resid="c1663cfb-4417-4993-a7bb-a0166f2d7bf1" />
        <placeholder
          name="IsDefault"
          value="False" />
      </control>
      <control
        code="CMB"
        id="a1b37d18-c582-4c53-aae7-cd0c3099eaed"
        left="14"
        top="2"
        width="4"
        height="1"
        binding="JH_Status">
        <placeholder
          name="IsReadOnly"
          value="False" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="DisplayMode"
          value="CodeDesc" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="SRC"
        id="1f287798-ce34-4e92-b110-c675840bf947"
        left="0"
        top="3"
        width="4"
        height="1"
        binding="JH_GE">
        <placeholder
          name="IsReadOnly"
          value="False" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="DisplayMode"
          value="CodeOnly" />
        <placeholder
          name="FieldConfiguration"
          value="" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="SRC"
        id="9d103d2c-30cc-4687-b170-1591563cd030"
        left="4"
        top="3"
        width="4"
        height="1"
        binding="JH_GS_NKRepOps">
        <placeholder
          name="IsReadOnly"
          value="False" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="DisplayMode"
          value="Unspecified" />
        <placeholder
          name="FieldConfiguration"
          value="" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="TBT"
        id="667186be-5a54-4961-a9c4-b1437d549c9e"
        left="9"
        top="3"
        width="4"
        height="1">
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="Content"
          value="Edit Charges"
          resid="50d0ca76-a03d-4597-951d-96e78e8a0160" />
        <placeholder
          name="IsDefault"
          value="False" />
      </control>
      <control
        code="TXT"
        id="54c04c87-278d-4a4a-ba83-1ef0f028460f"
        left="14"
        top="3"
        width="4"
        height="1"
        binding="JH_HoldReason">
        <placeholder
          name="IsReadOnly"
          value="False" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="" />
        <placeholder
          name="VisibilityCondition"
          value="052f6536-2baa-45de-90b5-b35583122b3d" />
        <placeholder
          name="OnSubmit"
          value="" />
        <placeholder
          name="MultiLine"
          value="False" />
        <placeholder
          name="EnableAutoComplete"
          value="False" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
    </control>
    <control
      code="GRD"
      id="49e787f7-d411-4a67-99d4-5922a4c6ae49"
      left="0"
      top="4"
      right="0"
      bottom="5"
      binding="JobCharges">
      <placeholder
        name="IsReadOnly"
        value="True" />
      <placeholder
        name="CanBeHidden"
        value="True" />
      <placeholder
        name="CaptionOverride"
        value="Charges"
        resid="34108e78-509a-44ab-ae44-7d31a79f5bd3" />
      <placeholder
        name="VisibilityCondition"
        value="" />
      <placeholder
        name="HeaderAlignment"
        value="Left" />
      <placeholder
        name="AllowAdd"
        value="False" />
      <placeholder
        name="AllowInlineEdit"
        value="False" />
      <placeholder
        name="FieldConfiguration">
        <xml>
          <fields xmlns="">
            <field
              path="JR_AC"
              width="100"
              mode="Mandatory" />
            <field
              path="JR_DisplaySequence"
              width="50"
              mode="Default" />
            <field
              path="JR_Desc"
              width="250"
              mode="Default" />
            <field
              path="JR_OSSellAmt"
              width="100"
              mode="Mandatory" />
            <field
              path="JR_RX_NKSellCurrency"
              width="100"
              mode="Default" />
            <field
              path="JR_LocalSellAmt"
              width="100"
              mode="Default" />
            <field
              path="JR_EstimatedRevenue"
              width="100"
              mode="Default" />
            <field
              path="JR_OH_SellAccount"
              width="100"
              mode="Mandatory" />
            <field
              path="JR_SellReference"
              width="80"
              mode="Default" />
            <field
              path="JR_OSCostAmt"
              width="100"
              mode="Default" />
            <field
              path="JR_RX_NKCostCurrency"
              width="100"
              mode="Default" />
            <field
              path="JR_LocalCostAmt"
              width="100"
              mode="Default" />
            <field
              path="JR_EstimatedCost"
              width="100"
              mode="Default" />
            <field
              path="JR_OH_CostAccount"
              width="100"
              mode="Default" />
            <field
              path="JR_OSCostExRate"
              width="100"
              mode="Default" />
            <field
              path="JR_OSSellExRate"
              width="100"
              mode="Default" />
            <field
              path="JR_GE"
              width="80"
              mode="Default" />
            <field
              path="JR_GB"
              width="80"
              mode="Default" />
            <field
              path="JR_InvoiceType"
              width="50"
              mode="Default" />
            <field
              path="JR_ARLinePostingStatus"
              width="220"
              mode="Optional" />
            <field
              path="JR_APLinePostingStatus"
              width="220"
              mode="Optional" />
            <field
              path="JR_GB_InternalBranch"
              width="250"
              mode="Optional" />
            <field
              path="JR_GE_InternalDept"
              width="250"
              mode="Optional" />
            <field
              path="JR_JH_InternalJob"
              width="250"
              mode="Optional" />
            <field
              path="JR_SellRatingOverrideComment"
              width="250"
              mode="Optional" />
            <field
              path="JR_CostRatingOverrideComment"
              width="250"
              mode="Optional" />
            <field
              path="JR_A9_CostVATClass"
              width="100"
              mode="Optional" />
            <field
              path="JR_A9_SellVATClass"
              width="100"
              mode="Optional" />
            <field
              path="JR_AB"
              width="100"
              mode="Optional" />
            <field
              path="JR_AgentDeclaredCostAmt"
              width="100"
              mode="Optional" />
            <field
              path="JR_AgentDeclaredSellAmt"
              width="100"
              mode="Optional" />
            <field
              path="JR_AK"
              width="100"
              mode="Optional" />
            <field
              path="JR_AL_APLine"
              width="100"
              mode="Optional" />
            <field
              path="JR_AL_ARLine"
              width="100"
              mode="Optional" />
            <field
              path="JR_AL_CFXLine"
              width="100"
              mode="Optional" />
            <field
              path="JR_APDocumentReceivedDate"
              width="100"
              mode="Optional" />
            <field
              path="JR_APInvoiceDate"
              width="100"
              mode="Optional" />
            <field
              path="JR_APInvoiceNum"
              width="100"
              mode="Optional" />
            <field
              path="JR_APNumberOfSupportingDocuments"
              width="100"
              mode="Optional" />
            <field
              path="JR_ARNumberOfSupportingDocuments"
              width="100"
              mode="Optional" />
            <field
              path="JR_AT_CostGSTRate"
              width="100"
              mode="Optional" />
            <field
              path="JR_AT_SellGSTRate"
              width="100"
              mode="Optional" />
            <field
              path="JR_ChargeType"
              width="100"
              mode="Optional" />
            <field
              path="JR_ChequeNo"
              width="100"
              mode="Optional" />
            <field
              path="JR_CostGovtChargeCode"
              width="100"
              mode="Optional" />
            <field
              path="JR_CostPlaceOfSupply"
              width="100"
              mode="Optional" />
            <field
              path="JR_CostPlaceOfSupplyType"
              width="100"
              mode="Optional" />
            <field
              path="JR_CostRated"
              width="100"
              mode="Optional" />
            <field
              path="JR_CostRatingOverride"
              width="100"
              mode="Optional" />
            <field
              path="JR_CostReference"
              width="100"
              mode="Optional" />
            <field
              path="JR_CostTaxDate"
              width="100"
              mode="Optional" />
            <field
              path="JR_DeclaredOSCostAmt"
              width="100"
              mode="Optional" />
            <field
              path="JR_E6"
              width="100"
              mode="Optional" />
            <field
              path="JR_E6_GatewaySellHeader"
              width="100"
              mode="Optional" />
            <field
              path="JR_GC"
              width="100"
              mode="Optional" />
            <field
              path="JR_IsCostTaxAmountOverridden"
              width="100"
              mode="Optional" />
            <field
              path="JR_IsIncludedInProfitShare"
              width="100"
              mode="Optional" />
            <field
              path="JR_JR_RevenueLine"
              width="100"
              mode="Optional" />
            <field
              path="JR_LineCFX"
              width="100"
              mode="Optional" />
            <field
              path="JR_LineType"
              width="100"
              mode="Optional" />
            <field
              path="JR_MarginPercentage"
              width="100"
              mode="Optional" />
            <field
              path="JR_OA_SellInvoiceAddress"
              width="100"
              mode="Optional" />
            <field
              path="JR_OC_SellInvoiceContact"
              width="100"
              mode="Optional" />
            <field
              path="JR_OP_Product"
              width="100"
              mode="Optional" />
            <field
              path="JR_OrderReference"
              width="100"
              mode="Optional" />
            <field
              path="JR_OSCostGSTAmt"
              width="100"
              mode="Optional" />
            <field
              path="JR_OSCostWHTAmt"
              width="100"
              mode="Optional" />
            <field
              path="JR_OSSellWHTAmt"
              width="100"
              mode="Optional" />
            <field
              path="JR_PaymentDate"
              width="100"
              mode="Optional" />
            <field
              path="JR_PaymentType"
              width="100"
              mode="Optional" />
            <field
              path="JR_PreventInvoicePrintGrouping"
              width="100"
              mode="Optional" />
            <field
              path="JR_ProductQuantity"
              width="100"
              mode="Optional" />
            <field
              path="JR_ProFormaCost"
              width="100"
              mode="Optional" />
            <field
              path="JR_ProFormaRevenue"
              width="100"
              mode="Optional" />
            <field
              path="JR_RX_NKSellInvoiceCurrency"
              width="100"
              mode="Optional" />
            <field
              path="JR_SellGovtChargeCode"
              width="100"
              mode="Optional" />
            <field
              path="JR_SellPlaceOfSupply"
              width="100"
              mode="Optional" />
            <field
              path="JR_SellPlaceOfSupplyType"
              width="100"
              mode="Optional" />
            <field
              path="JR_SellRated"
              width="100"
              mode="Optional" />
            <field
              path="JR_SellRatingOverride"
              width="100"
              mode="Optional" />
            <field
              path="JR_SellTaxDate"
              width="100"
              mode="Optional" />
          </fields>
        </xml>
      </placeholder>
      <placeholder
        name="UseFieldConfigurationAsMasterList"
        value="True" />
      <placeholder
        name="DefaultSortFields">
        <xml>
          <ArrayOfFieldSortDefinition xmlns:i="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://schemas.datacontract.org/2004/07/CargoWise.Glow.Infrastructure.Interfaces" />
        </xml>
      </placeholder>
      <placeholder
        name="ShowDocumentsIcon"
        value="True" />
      <placeholder
        name="RowActionsMode"
        value="None" />
      <placeholder
        name="HideGridActions"
        value="False" />
      <placeholder
        name="ShowTaskAsDialog"
        value="False" />
      <placeholder
        name="EditFormFlow"
        value="" />
      <placeholder
        name="SuppressDependencyContributions"
        value="False" />
    </control>
    <control
      code="RBT"
      id="096200f6-2edc-4c65-8d10-50365942bcee"
      left="0"
      width="4"
      height="1"
      bottom="4"
      binding="SetDetailsTab">
      <placeholder
        name="CanBeHidden"
        value="True" />
      <placeholder
        name="IsInitialFocus"
        value="False" />
      <placeholder
        name="VisibilityCondition"
        value="2f91d30f-4171-45dd-b575-511c91cfdf61" />
      <placeholder
        name="Content"
        value="Details"
        resid="b092f0dc-e5b6-49bd-b823-83f2ab330cd9" />
      <placeholder
        name="IsDefault"
        value="False" />
    </control>
    <control
      code="GRP"
      id="88888e32-b79f-4b6c-85d4-194433d9926e"
      left="0"
      width="4"
      height="1"
      bottom="4">
      <placeholder
        name="CanBeHidden"
        value="True" />
      <placeholder
        name="VisibilityCondition"
        value="5ed342c2-8a9f-4eb2-aa7e-6211f8ef7dcc" />
      <placeholder
        name="Header"
        value="New Section"
        resid="a12e35dc-c3ea-41b2-bbc9-37a20ff631cb" />
      <placeholder
        name="HeaderAlignment"
        value="Left" />
      <placeholder
        name="HeaderDisplayMode"
        value="Hidden" />
      <placeholder
        name="BackgroundImagePk"
        value="de48d21662314d9b9d37d598add8d5a5" />
      <placeholder
        name="BackgroundImageOverride"
        value="" />
      <control
        code="FLB"
        id="c365f092-ebb3-4bf3-b9d2-967b2803b514"
        left="0"
        top="0"
        width="4"
        height="1">
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="LabelText"
          value="Details"
          resid="211b831d-5638-4c27-bc14-baadd07f929f" />
        <placeholder
          name="LabelType"
          value="NormalLabel" />
        <placeholder
          name="LabelNotificationType"
          value="Information" />
        <placeholder
          name="ForegroundThemeColor"
          value="Background" />
        <placeholder
          name="TextSize"
          value="Medium" />
        <placeholder
          name="FontWeight"
          value="Bold" />
        <placeholder
          name="LabelAlignment"
          value="Center" />
        <placeholder
          name="BackgroundColor"
          value="" />
      </control>
    </control>
    <control
      code="RBT"
      id="3ae32dac-d63c-4655-8e9e-d07a479915bb"
      left="4"
      width="4"
      height="1"
      bottom="4"
      binding="SetRevenueTab">
      <placeholder
        name="CanBeHidden"
        value="True" />
      <placeholder
        name="IsInitialFocus"
        value="False" />
      <placeholder
        name="VisibilityCondition"
        value="579abaa5-1612-4874-b609-1f617275d49e" />
      <placeholder
        name="Content"
        value="Revenue"
        resid="81fbec9b-644c-4adf-9b28-f5af59f7d95e" />
      <placeholder
        name="IsDefault"
        value="False" />
    </control>
    <control
      code="GRP"
      id="0a40a2e2-91d4-40ca-a3e8-0e250465a5ba"
      left="4"
      width="4"
      height="1"
      bottom="4">
      <placeholder
        name="CanBeHidden"
        value="True" />
      <placeholder
        name="VisibilityCondition"
        value="36c917cc-d6e6-4a7e-855f-8beea11e4912" />
      <placeholder
        name="Header"
        value="New Section"
        resid="148b6efc-8e45-4e0b-b55a-99198b68f1ba" />
      <placeholder
        name="HeaderAlignment"
        value="Left" />
      <placeholder
        name="HeaderDisplayMode"
        value="Hidden" />
      <placeholder
        name="BackgroundImagePk"
        value="de48d21662314d9b9d37d598add8d5a5" />
      <placeholder
        name="BackgroundImageOverride"
        value="" />
      <control
        code="FLB"
        id="b0d0331e-f443-4f66-9912-1de2e6880af1"
        left="0"
        top="0"
        width="4"
        height="1">
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="LabelText"
          value="Revenue"
          resid="b45b243c-3586-4c7f-8057-3af98cf8f110" />
        <placeholder
          name="LabelType"
          value="NormalLabel" />
        <placeholder
          name="LabelNotificationType"
          value="Information" />
        <placeholder
          name="ForegroundThemeColor"
          value="Background" />
        <placeholder
          name="TextSize"
          value="Medium" />
        <placeholder
          name="FontWeight"
          value="Bold" />
        <placeholder
          name="LabelAlignment"
          value="Center" />
        <placeholder
          name="BackgroundColor"
          value="" />
      </control>
    </control>
    <control
      code="RBT"
      id="fb8773b6-7abe-47d8-80b2-59aea1828eed"
      left="8"
      width="4"
      height="1"
      bottom="4"
      binding="SetCostTab">
      <placeholder
        name="CanBeHidden"
        value="True" />
      <placeholder
        name="IsInitialFocus"
        value="False" />
      <placeholder
        name="VisibilityCondition"
        value="e66d6ae5-1c92-4494-81b0-f715a8ea6c62" />
      <placeholder
        name="Content"
        value="Cost"
        resid="32b2a836-0684-4ca2-8e15-4be3c0482c93" />
      <placeholder
        name="IsDefault"
        value="False" />
    </control>
    <control
      code="GRP"
      id="785f7833-3a74-42cf-a82a-8e5ab6bb73de"
      left="8"
      width="4"
      height="1"
      bottom="4">
      <placeholder
        name="CanBeHidden"
        value="True" />
      <placeholder
        name="VisibilityCondition"
        value="a8389a2d-34ba-4a3b-b492-b7948ccb389e" />
      <placeholder
        name="Header"
        value="New Section"
        resid="6bbe3500-3d52-4423-aeed-f70222fa3246" />
      <placeholder
        name="HeaderAlignment"
        value="Left" />
      <placeholder
        name="HeaderDisplayMode"
        value="Hidden" />
      <placeholder
        name="BackgroundImagePk"
        value="de48d21662314d9b9d37d598add8d5a5" />
      <placeholder
        name="BackgroundImageOverride"
        value="" />
      <control
        code="FLB"
        id="1bcf8ede-612c-4491-8be6-0ab97bdba857"
        left="0"
        top="0"
        width="4"
        height="1">
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="LabelText"
          value="Cost"
          resid="5f0fc2df-0b99-4d2d-a15f-46cdb178da8a" />
        <placeholder
          name="LabelType"
          value="NormalLabel" />
        <placeholder
          name="LabelNotificationType"
          value="Information" />
        <placeholder
          name="ForegroundThemeColor"
          value="Background" />
        <placeholder
          name="TextSize"
          value="Medium" />
        <placeholder
          name="FontWeight"
          value="Bold" />
        <placeholder
          name="LabelAlignment"
          value="Center" />
        <placeholder
          name="BackgroundColor"
          value="" />
      </control>
    </control>
    <control
      code="NUM"
      id="76b5f6bf-a205-4d55-9c49-4981c33c6c48"
      width="4"
      height="1"
      right="8"
      bottom="4"
      binding="TotalRevenue">
      <placeholder
        name="IsReadOnly"
        value="True" />
      <placeholder
        name="CanBeHidden"
        value="True" />
      <placeholder
        name="IsInitialFocus"
        value="False" />
      <placeholder
        name="CaptionOverride"
        value="" />
      <placeholder
        name="VisibilityCondition"
        value="" />
      <placeholder
        name="NumericFieldLeftAlignment"
        value="False" />
      <placeholder
        name="SuppressDependencyContributions"
        value="False" />
    </control>
    <control
      code="NUM"
      id="1f9b6e61-1f7a-43ad-943f-4b369faaef34"
      width="4"
      height="1"
      right="4"
      bottom="4"
      binding="TotalCost">
      <placeholder
        name="IsReadOnly"
        value="True" />
      <placeholder
        name="CanBeHidden"
        value="True" />
      <placeholder
        name="IsInitialFocus"
        value="False" />
      <placeholder
        name="CaptionOverride"
        value="" />
      <placeholder
        name="VisibilityCondition"
        value="" />
      <placeholder
        name="NumericFieldLeftAlignment"
        value="False" />
      <placeholder
        name="SuppressDependencyContributions"
        value="False" />
    </control>
    <control
      code="NUM"
      id="3ca55617-0086-4520-83b5-9d151b2b13d2"
      width="4"
      height="1"
      right="0"
      bottom="4"
      binding="TotalProfit">
      <placeholder
        name="IsReadOnly"
        value="True" />
      <placeholder
        name="CanBeHidden"
        value="True" />
      <placeholder
        name="IsInitialFocus"
        value="False" />
      <placeholder
        name="CaptionOverride"
        value="" />
      <placeholder
        name="VisibilityCondition"
        value="" />
      <placeholder
        name="NumericFieldLeftAlignment"
        value="False" />
      <placeholder
        name="SuppressDependencyContributions"
        value="False" />
    </control>
    <control
      code="GRP"
      id="0a8ea364-234d-4c7c-9e18-335dd120dea0"
      left="0"
      height="4"
      right="0"
      bottom="0">
      <placeholder
        name="CanBeHidden"
        value="True" />
      <placeholder
        name="VisibilityCondition"
        value="36c917cc-d6e6-4a7e-855f-8beea11e4912" />
      <placeholder
        name="Header"
        value="New Section"
        resid="f48002b0-a201-4334-bf8c-10455ec02110" />
      <placeholder
        name="HeaderAlignment"
        value="Left" />
      <placeholder
        name="HeaderDisplayMode"
        value="Hidden" />
      <placeholder
        name="BackgroundImagePk"
        value="" />
      <placeholder
        name="BackgroundImageOverride"
        value="" />
      <control
        code="NUM"
        id="ba586282-4888-48b3-b00c-010f8859eacf"
        left="0"
        top="0"
        width="4"
        height="1"
        binding="JobCharges/JR_OSSellAmt">
        <placeholder
          name="IsReadOnly"
          value="True" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="Overseas Sell Amount"
          resid="722ecf78-8cb7-414f-bcad-543a1f1cf012" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="NumericFieldLeftAlignment"
          value="False" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="SRC"
        id="4890af70-80ea-4e4c-9ca3-aea68a00a311"
        left="4.2"
        top="0"
        width="8"
        height="1"
        binding="JobCharges/JR_OH_SellAccount">
        <placeholder
          name="IsReadOnly"
          value="True" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="DisplayMode"
          value="DescOnly" />
        <placeholder
          name="FieldConfiguration"
          value="" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="TXT"
        id="4be4682c-9adc-43c6-8384-20c06bc38ee8"
        left="12.4"
        top="0"
        width="4"
        height="1"
        binding="JobCharges/ARLine.AccTransactionHeader.AH_TransactionNum">
        <placeholder
          name="IsReadOnly"
          value="True" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="AR Invoice Reference"
          resid="322d0a69-c5e3-458c-852e-380082367689" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="OnSubmit"
          value="" />
        <placeholder
          name="MultiLine"
          value="False" />
        <placeholder
          name="EnableAutoComplete"
          value="False" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="DTE"
        id="c06c165e-c505-4491-ba28-7627f4554e8c"
        left="16.4"
        top="0"
        width="4"
        height="1"
        binding="JobCharges/RevenueLine.JR_APInvoiceDate">
        <placeholder
          name="IsReadOnly"
          value="True" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="DefaultValueExpression"
          value="" />
        <placeholder
          name="IncludeTime"
          value="True" />
        <placeholder
          name="ShowNowButton"
          value="True" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="NUM"
        id="62b5f163-6e14-4186-9a71-e1a33f0ad70b"
        left="0"
        top="1"
        width="4"
        height="1"
        binding="JobCharges/JR_EstimatedRevenue">
        <placeholder
          name="IsReadOnly"
          value="True" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="Overseas Estimated Revenue"
          resid="152dee6c-8db3-4500-a98d-6a052027aa09" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="NumericFieldLeftAlignment"
          value="False" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="CMB"
        id="f12dd042-1d53-4b90-bb4f-bd147d9b0679"
        left="4.2"
        top="1"
        width="8"
        height="1"
        binding="JobCharges/JR_InvoiceType">
        <placeholder
          name="IsReadOnly"
          value="True" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="DisplayMode"
          value="Unspecified" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="SRC"
        id="8886a2f9-fd1e-41be-9e4c-ee7ce50f63d6"
        left="12.4"
        top="1"
        width="4"
        height="1"
        binding="JobCharges/RevenueLine.JR_AT_SellGSTRate">
        <placeholder
          name="IsReadOnly"
          value="True" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="DisplayMode"
          value="CodeOnly" />
        <placeholder
          name="FieldConfiguration"
          value="" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="DTE"
        id="8f54d276-d735-4e14-bb7d-15280987c469"
        left="16.4"
        top="1"
        width="4"
        height="1"
        binding="JobCharges/RevenueLine.JR_SellTaxDate">
        <placeholder
          name="IsReadOnly"
          value="True" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="DefaultValueExpression"
          value="" />
        <placeholder
          name="IncludeTime"
          value="True" />
        <placeholder
          name="ShowNowButton"
          value="True" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="NUM"
        id="19f5d0a0-34e4-4488-aef9-f7898b2c7328"
        left="0"
        top="2"
        width="4"
        height="1"
        binding="JobCharges/JR_LocalSellAmt">
        <placeholder
          name="IsReadOnly"
          value="True" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="NumericFieldLeftAlignment"
          value="False" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="OPT"
        id="2521b415-90b2-458b-b09c-8acff0cae81c"
        left="4.2"
        top="2"
        width="6"
        height="1"
        binding="JobCharges/RevenueLine.JR_PreventInvoicePrintGrouping">
        <placeholder
          name="IsReadOnly"
          value="True" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="NUM"
        id="7852f543-aa5e-4826-b9de-9424a09e89c3"
        left="10.2"
        top="2"
        width="2"
        height="1"
        binding="JobCharges/JR_LineCFX">
        <placeholder
          name="IsReadOnly"
          value="True" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="NumericFieldLeftAlignment"
          value="False" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="OPT"
        id="ea348a18-90ff-47f7-a3a6-58da78efa343"
        left="12.4"
        top="2"
        width="8"
        height="1"
        binding="JobCharges/JR_CostRatingOverride">
        <placeholder
          name="IsReadOnly"
          value="True" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="TXT"
        id="32b33e03-206a-4d77-8526-5610998e3bfd"
        left="12.4"
        top="3"
        width="8"
        height="1"
        binding="JobCharges/JR_CostRatingOverrideComment">
        <placeholder
          name="IsReadOnly"
          value="True" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="Revenue Override Comment"
          resid="1c07ed40-eb92-4d47-bce4-55e4fcb55187" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="OnSubmit"
          value="" />
        <placeholder
          name="MultiLine"
          value="False" />
        <placeholder
          name="EnableAutoComplete"
          value="False" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
    </control>
    <control
      code="GRP"
      id="49bede1a-00d3-443a-9691-bf2e620d30ae"
      left="0"
      height="4"
      right="4"
      bottom="0">
      <placeholder
        name="CanBeHidden"
        value="True" />
      <placeholder
        name="VisibilityCondition"
        value="a8389a2d-34ba-4a3b-b492-b7948ccb389e" />
      <placeholder
        name="Header"
        value="New Section"
        resid="4bc10ed2-ba07-4163-9468-d0ba9ff59acf" />
      <placeholder
        name="HeaderAlignment"
        value="Left" />
      <placeholder
        name="HeaderDisplayMode"
        value="Hidden" />
      <placeholder
        name="BackgroundImagePk"
        value="" />
      <placeholder
        name="BackgroundImageOverride"
        value="" />
      <control
        code="NUM"
        id="65a09376-fdc2-4335-9318-9e1dc2805b4a"
        left="0"
        top="0"
        width="4"
        height="1"
        binding="JobCharges/JR_OSCostAmt">
        <placeholder
          name="IsReadOnly"
          value="True" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="NumericFieldLeftAlignment"
          value="False" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="TXT"
        id="eddd8db7-9cf5-45d8-b7f9-fb9876d2b583"
        left="4"
        top="0"
        width="4"
        height="1"
        binding="JobCharges/JR_APInvoiceNum">
        <placeholder
          name="IsReadOnly"
          value="True" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="Invoice Number"
          resid="36f7bb84-a7cc-4a57-8889-72b04f9621cd" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="OnSubmit"
          value="" />
        <placeholder
          name="MultiLine"
          value="False" />
        <placeholder
          name="EnableAutoComplete"
          value="False" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="TXT"
        id="d56018e5-048a-4127-8c5f-723751f34268"
        left="8.2"
        top="0"
        width="4"
        height="1"
        binding="JobCharges/JR_PaymentType">
        <placeholder
          name="IsReadOnly"
          value="True" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="OnSubmit"
          value="" />
        <placeholder
          name="MultiLine"
          value="False" />
        <placeholder
          name="EnableAutoComplete"
          value="False" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="SRC"
        id="7e78cb33-4ded-48f4-a02c-f355cd86b703"
        left="12.4"
        top="0"
        width="7.8"
        height="1"
        binding="JobCharges/JR_OH_CostAccount">
        <placeholder
          name="IsReadOnly"
          value="True" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="DisplayMode"
          value="DescOnly" />
        <placeholder
          name="FieldConfiguration"
          value="" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="NUM"
        id="0183a2ab-9d64-4572-b5ba-99cc355d7440"
        left="0"
        top="1"
        width="4"
        height="1"
        binding="JobCharges/JR_LocalCostAmt">
        <placeholder
          name="IsReadOnly"
          value="True" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="NumericFieldLeftAlignment"
          value="False" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="DTE"
        id="a67b8130-aa7b-4225-be32-5e06c21cf0be"
        left="4"
        top="1"
        width="4"
        height="1"
        binding="JobCharges/JR_APInvoiceDate">
        <placeholder
          name="IsReadOnly"
          value="True" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="DefaultValueExpression"
          value="" />
        <placeholder
          name="IncludeTime"
          value="True" />
        <placeholder
          name="ShowNowButton"
          value="True" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="SRC"
        id="d524f250-5863-4edb-bbe3-d39b9ebc9035"
        left="8.2"
        top="1"
        width="4"
        height="1"
        binding="JobCharges/JR_AB">
        <placeholder
          name="IsReadOnly"
          value="True" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="DisplayMode"
          value="Unspecified" />
        <placeholder
          name="FieldConfiguration"
          value="" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="OPT"
        id="43c8a3be-a138-4393-bf14-7999795a22c8"
        left="12.4"
        top="1"
        width="7.8"
        height="1"
        binding="JobCharges/JR_CostRatingOverride">
        <placeholder
          name="IsReadOnly"
          value="True" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="NUM"
        id="b2e38e19-d506-4188-90ff-7f5c65c703d0"
        left="0"
        top="2"
        width="4"
        height="1"
        binding="JobCharges/JR_EstimatedCost">
        <placeholder
          name="IsReadOnly"
          value="True" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="NumericFieldLeftAlignment"
          value="False" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="DTE"
        id="e9799a0b-73ac-4f2c-8966-113f9d765abb"
        left="4"
        top="2"
        width="4"
        height="1"
        binding="JobCharges/JR_PaymentDate">
        <placeholder
          name="IsReadOnly"
          value="True" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="Due Date"
          resid="2430eba1-cd02-44e4-8c3a-9a17e64962ed" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="DefaultValueExpression"
          value="" />
        <placeholder
          name="IncludeTime"
          value="True" />
        <placeholder
          name="ShowNowButton"
          value="True" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="SRC"
        id="013abe59-fb34-4534-ac34-70093752509e"
        left="8.2"
        top="2"
        width="4"
        height="1"
        binding="JobCharges/JR_AK">
        <placeholder
          name="IsReadOnly"
          value="True" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="DisplayMode"
          value="Unspecified" />
        <placeholder
          name="FieldConfiguration"
          value="" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="TXT"
        id="af49f21e-3e44-462e-921b-ca7cb4dbfd40"
        left="12.4"
        top="2"
        width="7.8"
        height="1"
        binding="JobCharges/JR_CostRatingOverrideComment">
        <placeholder
          name="IsReadOnly"
          value="True" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="OnSubmit"
          value="" />
        <placeholder
          name="MultiLine"
          value="False" />
        <placeholder
          name="EnableAutoComplete"
          value="False" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="TXT"
        id="a5281f09-92fa-4298-80bd-9df6c6cfe9c6"
        left="0"
        top="3"
        width="4"
        height="1"
        binding="JobCharges/JR_CostReference">
        <placeholder
          name="IsReadOnly"
          value="True" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="OnSubmit"
          value="" />
        <placeholder
          name="MultiLine"
          value="False" />
        <placeholder
          name="EnableAutoComplete"
          value="False" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="TXT"
        id="ac0bb8c8-4adf-4bfd-a029-6c8a8bfa8721"
        left="8.2"
        top="3"
        width="4"
        height="1"
        binding="JobCharges/JR_ChequeNo">
        <placeholder
          name="IsReadOnly"
          value="True" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="OnSubmit"
          value="" />
        <placeholder
          name="MultiLine"
          value="False" />
        <placeholder
          name="EnableAutoComplete"
          value="False" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
    </control>
    <control
      code="GRP"
      id="9580c472-45ee-46cb-89d3-5d3b0c7739b9"
      left="0"
      height="4"
      right="10"
      bottom="0">
      <placeholder
        name="CanBeHidden"
        value="True" />
      <placeholder
        name="VisibilityCondition"
        value="5ed342c2-8a9f-4eb2-aa7e-6211f8ef7dcc" />
      <placeholder
        name="Header"
        value="New Section"
        resid="0cfa804d-f2f0-4f6e-a342-12786ffcfc9f" />
      <placeholder
        name="HeaderAlignment"
        value="Left" />
      <placeholder
        name="HeaderDisplayMode"
        value="Hidden" />
      <placeholder
        name="BackgroundImagePk"
        value="" />
      <placeholder
        name="BackgroundImageOverride"
        value="" />
      <control
        code="SRC"
        id="13fc15fc-f88f-4233-b786-fe5e6c2ce48c"
        left="0"
        top="0"
        width="4"
        height="1"
        binding="JobCharges/JR_AC">
        <placeholder
          name="IsReadOnly"
          value="True" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="DisplayMode"
          value="Unspecified" />
        <placeholder
          name="FieldConfiguration"
          value="" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="SRC"
        id="45af58a1-9941-42b3-ad7e-936a9de5540b"
        left="4"
        top="0"
        width="4"
        height="1"
        binding="JobCharges/JR_JH_InternalJob">
        <placeholder
          name="IsReadOnly"
          value="True" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="DisplayMode"
          value="Unspecified" />
        <placeholder
          name="FieldConfiguration"
          value="" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="OPT"
        id="e3ed71d9-37a6-41f7-9804-cd01d4591057"
        left="8.2"
        top="0"
        width="5"
        height="1"
        binding="JobCharges/JR_IsIncludedInProfitShare">
        <placeholder
          name="IsReadOnly"
          value="True" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="SRC"
        id="c9db4c68-c9ae-415a-8b02-e120fdc2ac94"
        left="0"
        top="1"
        width="4"
        height="1"
        binding="JobCharges/JR_GE">
        <placeholder
          name="IsReadOnly"
          value="True" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="DisplayMode"
          value="Unspecified" />
        <placeholder
          name="FieldConfiguration"
          value="" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="SRC"
        id="da7c0878-1fa7-4a2a-b511-06d393c55285"
        left="4"
        top="1"
        width="4"
        height="1"
        binding="JobCharges/JR_GE_InternalDept">
        <placeholder
          name="IsReadOnly"
          value="True" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="DisplayMode"
          value="Unspecified" />
        <placeholder
          name="FieldConfiguration"
          value="" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="NUM"
        id="8423d06f-6abd-4edb-8368-981a6f42dff3"
        left="8.2"
        top="1"
        width="5"
        height="1"
        binding="JobCharges/JR_AgentDeclaredCostAmt">
        <placeholder
          name="IsReadOnly"
          value="True" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="NumericFieldLeftAlignment"
          value="False" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="SRC"
        id="600438a4-a2b7-4241-9352-3f0acb6603fb"
        left="0"
        top="2"
        width="4"
        height="1"
        binding="JobCharges/JR_GB">
        <placeholder
          name="IsReadOnly"
          value="True" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="DisplayMode"
          value="Unspecified" />
        <placeholder
          name="FieldConfiguration"
          value="" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="SRC"
        id="2dbc04d7-fe59-4e79-9d9c-8e6b944efc59"
        left="4"
        top="2"
        width="4"
        height="1"
        binding="JobCharges/JR_GB_InternalBranch">
        <placeholder
          name="IsReadOnly"
          value="True" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="DisplayMode"
          value="Unspecified" />
        <placeholder
          name="FieldConfiguration"
          value="" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="NUM"
        id="aced8c6c-c2e4-4c6e-bb1c-a7fb4ba30987"
        left="8.2"
        top="2"
        width="5"
        height="1"
        binding="JobCharges/JR_AgentDeclaredSellAmt">
        <placeholder
          name="IsReadOnly"
          value="True" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="NumericFieldLeftAlignment"
          value="False" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
    </control>
  </form>
