#transformationVersion: 70.0
#
VZ_PK: ae57f087708d4285a622f46d9dc7b5a2
VZ_ConfigurationKey: ae57f087-708d-4285-a622-f46d9dc7b5a2
VZ_FormID: ETL - VDV3 - Destination Depot - Scan to Receive Outer Packages on Consol
VZ_Caption:
  resKey: VZ_Caption|ae57f087-708d-4285-a622-f46d9dc7b5a2
  text: Scan to Receive Outer Packages
VZ_FormFactor: DSK
VZ_EntityType: IJobConsol
VZ_FormType: FRM
VZ_RequiresLogin: true
VZ_Dependencies: >-
  <dependencies xmlns="http://cargowise.com/glow/2014/11/14/dependencies.xsd">
    <expandPath
      path="TransportFirstLeg" />
    <expandPath
      path="TransportLastLeg" />
    <expandPath
      path="TransportFirstLeg.LoadPort" />
    <expandPath
      path="TransportLastLeg.DiscPort" />
    <expandPath
      path="ArrivalCTOAddress" />
    <expandPath
      path="TransportLastLeg.JobSailing.JobVoyDestination" />
    <expandPath
      path="UnpackDepotAddress" />
    <expandPath
      path="FirstConsolShipmentLink.JobShipment.DocsAndCartageDetail" />
    <expandPath
      path="FirstScanEventConsol" />
    <expandPath
      path="LastScanEventConsol" />
    <expandPath
      path="FirstConsolShipmentLink.JobShipment.CurrentItem.OuterPackage" />
    <calculatedProperty
      path="TransportFirstLeg" />
    <calculatedProperty
      path="ItemCount" />
    <calculatedProperty
      path="TransportLastLeg" />
    <calculatedProperty
      path="HeldCount" />
    <calculatedProperty
      path="ArrivalCTOAddress.CompanyName" />
    <calculatedProperty
      path="ClearCount" />
    <calculatedProperty
      path="UnpackDepotAddress.CompanyName" />
    <calculatedProperty
      path="FirstConsolShipmentLink" />
    <calculatedProperty
      path="FirstConsolShipmentLink.JobShipment.DocsAndCartageDetail" />
    <calculatedProperty
      path="FirstScanEventConsol" />
    <calculatedProperty
      path="LastScanEventConsol" />
    <calculatedProperty
      path="SurplusCount" />
    <calculatedProperty
      path="FirstConsolShipmentLink.JobShipment.CurrentItem" />
    <calculatedProperty
      path="FirstConsolShipmentLink.JobShipment.CurrentItem.OuterPackage.HeldCountLabel" />
    <calculatedProperty
      path="FirstConsolShipmentLink.JobShipment.CurrentItem.OuterPackage.ReturnLMCAndOuterPackageReference" />
    <calculatedProperty
      path="FirstConsolShipmentLink.JobShipment.CurrentItem.OuterPackage.ClearCountLabel" />
  </dependencies>
VZ_FormData: >-
  <form
    id="2bdac500-d124-49ec-97c8-a0e9953b3f84" xmlns="http://wisetechglobal.com/glow/2017/05/26/form.xsd">
    <placeholder
      name="BackgroundImagePk"
      value="" />
    <placeholder
      name="BackgroundSize"
      value="Auto" />
    <placeholder
      name="BackgroundImageOverride"
      value="" />
    <placeholder
      name="FormLayout"
      value="Splitview" />
    <placeholder
      name="ExpandPaths"
      value="" />
    <placeholder
      name="ScanInputHandling"
      value="NON" />
    <placeholder
      name="RemovePadding"
      value="false" />
    <placeholder
      name="TurnOnPortableOneToOneDesignMode"
      value="false" />
    <placeholder
      name="ContainsDefaultButton"
      value="False" />
    <control
      code="GRP"
      id="f929ae72-653e-40bf-8656-f7e8e6d9f768"
      top="0"
      width="28"
      height="22.6">
      <placeholder
        name="CanBeHidden"
        value="True" />
      <placeholder
        name="VisibilityCondition"
        value="9108ed2d-4c17-4c16-a0e7-0d7c35543e0b" />
      <placeholder
        name="Header"
        value="New Section"
        resid="987298c7-6b36-4367-8994-dfda39018dc0" />
      <placeholder
        name="HeaderAlignment"
        value="Left" />
      <placeholder
        name="HeaderDisplayMode"
        value="Hidden" />
      <placeholder
        name="BackgroundImagePk"
        value="" />
      <placeholder
        name="BackgroundImageOverride"
        value="" />
      <control
        code="FLB"
        id="753b6cae-d1ef-4cff-bed2-24e923adca4b"
        left="4.4"
        top="6.9"
        width="19"
        height="2.7">
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="VisibilityCondition"
          value="31721df9-89cb-489c-b481-ed340f72e943" />
        <placeholder
          name="LabelText"
          value="Receipt at CFS before scanning can commence"
          resid="1ef6a843-ddbb-4b8e-ac91-8fb4f42810ef" />
        <placeholder
          name="LabelType"
          value="Notification" />
        <placeholder
          name="LabelNotificationType"
          value="Error" />
        <placeholder
          name="ForegroundThemeColor"
          value="GeneralText" />
        <placeholder
          name="TextSize"
          value="Large" />
        <placeholder
          name="FontWeight"
          value="Normal" />
        <placeholder
          name="LabelAlignment"
          value="Center" />
        <placeholder
          name="BackgroundColor"
          value="" />
        <placeholder
          name="Padding"
          value="None" />
      </control>
      <control
        code="FLB"
        id="324458db-4df4-4dfe-9ddb-8fbe22f9390c"
        left="5"
        top="11.3"
        width="9"
        height="1">
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="LabelText"
          value="Arrival Details"
          resid="192371b9-872a-4351-a969-94297a21efb1" />
        <placeholder
          name="LabelType"
          value="NormalLabel" />
        <placeholder
          name="LabelNotificationType"
          value="Information" />
        <placeholder
          name="ForegroundThemeColor"
          value="DefaultButton" />
        <placeholder
          name="TextSize"
          value="Medium" />
        <placeholder
          name="FontWeight"
          value="Bold" />
        <placeholder
          name="LabelAlignment"
          value="Center" />
        <placeholder
          name="BackgroundColor"
          value="" />
        <placeholder
          name="Padding"
          value="None" />
      </control>
      <control
        code="FLB"
        id="b8973f95-83c9-49a3-9353-9de742f54055"
        left="15"
        top="11.3"
        width="9"
        height="1">
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="LabelText"
          value="Item Counts in Consol"
          resid="aff7752d-e98f-4c42-b2cb-b9c713bbf2ea" />
        <placeholder
          name="LabelType"
          value="NormalLabel" />
        <placeholder
          name="LabelNotificationType"
          value="Information" />
        <placeholder
          name="ForegroundThemeColor"
          value="DefaultButton" />
        <placeholder
          name="TextSize"
          value="Medium" />
        <placeholder
          name="FontWeight"
          value="Bold" />
        <placeholder
          name="LabelAlignment"
          value="Center" />
        <placeholder
          name="BackgroundColor"
          value="" />
        <placeholder
          name="Padding"
          value="None" />
      </control>
      <control
        code="TXT"
        id="933e8a34-b052-4c14-8ad4-a61b02c24cf5"
        left="5"
        top="12.4"
        width="5"
        height="1"
        binding="JK_MasterBillNum">
        <placeholder
          name="IsReadOnly"
          value="True" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="OnSubmit"
          value="" />
        <placeholder
          name="MultiLine"
          value="False" />
        <placeholder
          name="EnableAutoComplete"
          value="False" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="DTE"
        id="5533c749-300e-4896-8a9f-cbc91288b0f1"
        left="10"
        top="12.4"
        width="4"
        height="1"
        binding="TransportFirstLeg.JW_ETD">
        <placeholder
          name="IsReadOnly"
          value="True" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="DefaultValueExpression"
          value="" />
        <placeholder
          name="IncludeTime"
          value="True" />
        <placeholder
          name="ShowNowButton"
          value="True" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="FLB"
        id="ebfda2cc-888a-4812-99ec-bd018f635514"
        left="15"
        top="12.4"
        width="4"
        height="2.1">
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="LabelText"
          value="Total -"
          resid="89b70167-3eeb-4a15-97fc-b9feebf6cc0d" />
        <placeholder
          name="LabelType"
          value="Notification" />
        <placeholder
          name="LabelNotificationType"
          value="Information" />
        <placeholder
          name="ForegroundThemeColor"
          value="GeneralText" />
        <placeholder
          name="TextSize"
          value="ExtraLarge" />
        <placeholder
          name="FontWeight"
          value="Bold" />
        <placeholder
          name="LabelAlignment"
          value="Center" />
        <placeholder
          name="BackgroundColor"
          value="" />
        <placeholder
          name="Padding"
          value="None" />
      </control>
      <control
        code="LBL"
        id="bba3449b-2418-4f0a-a55c-6ade7d1e931f"
        left="19"
        top="12.4"
        width="5"
        height="2.1"
        binding="ItemCount">
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="LabelType"
          value="Notification" />
        <placeholder
          name="LabelNotificationType"
          value="Information" />
        <placeholder
          name="ForegroundThemeColor"
          value="GeneralText" />
        <placeholder
          name="TextSize"
          value="ExtraLarge" />
        <placeholder
          name="FontWeight"
          value="Bold" />
        <placeholder
          name="LabelAlignment"
          value="CenterLeft" />
        <placeholder
          name="BackgroundColor"
          value="" />
        <placeholder
          name="Padding"
          value="None" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="TXT"
        id="86a461fc-0bbc-43a4-ac7b-ab612a03cf6d"
        left="5"
        top="13.4"
        width="5"
        height="1"
        binding="TransportLastLeg.JW_VoyageFlight">
        <placeholder
          name="IsReadOnly"
          value="True" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="OnSubmit"
          value="" />
        <placeholder
          name="MultiLine"
          value="False" />
        <placeholder
          name="EnableAutoComplete"
          value="False" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="DTE"
        id="4caaf127-0ed8-42ca-b6f3-01cc0acf1828"
        left="10"
        top="13.4"
        width="4"
        height="1"
        binding="TransportLastLeg.JW_ETA">
        <placeholder
          name="IsReadOnly"
          value="True" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="DefaultValueExpression"
          value="" />
        <placeholder
          name="IncludeTime"
          value="True" />
        <placeholder
          name="ShowNowButton"
          value="True" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="SRC"
        id="bd647abb-9024-4762-a9f6-985cd44c06b8"
        left="5"
        top="14.4"
        width="5"
        height="1"
        binding="TransportFirstLeg.JW_RL_NKLoadPort">
        <placeholder
          name="IsReadOnly"
          value="True" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="DisplayMode"
          value="DescOnly" />
        <placeholder
          name="FieldConfiguration"
          value="" />
        <placeholder
          name="DefaultFilter"
          value="" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="DTE"
        id="f44e40ce-e6b6-4cd1-8c50-50b180f74198"
        left="10"
        top="14.4"
        width="4"
        height="1"
        binding="TransportLastLeg.JW_ATD">
        <placeholder
          name="IsReadOnly"
          value="True" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="DefaultValueExpression"
          value="" />
        <placeholder
          name="IncludeTime"
          value="True" />
        <placeholder
          name="ShowNowButton"
          value="True" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="FLB"
        id="7373e290-f923-4e86-8e8e-cc6769c12a6a"
        left="15"
        top="14.5"
        width="4"
        height="2.1">
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="LabelText"
          value="Held -"
          resid="d1a04f3d-0bcd-46c5-a46a-e44a95920fcc" />
        <placeholder
          name="LabelType"
          value="Notification" />
        <placeholder
          name="LabelNotificationType"
          value="Error" />
        <placeholder
          name="ForegroundThemeColor"
          value="GeneralText" />
        <placeholder
          name="TextSize"
          value="ExtraLarge" />
        <placeholder
          name="FontWeight"
          value="Bold" />
        <placeholder
          name="LabelAlignment"
          value="Center" />
        <placeholder
          name="BackgroundColor"
          value="" />
        <placeholder
          name="Padding"
          value="None" />
      </control>
      <control
        code="LBL"
        id="1170df76-032a-428a-bcbf-7e866a297bf2"
        left="19"
        top="14.5"
        width="5"
        height="2.1"
        binding="HeldCount">
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="LabelType"
          value="Notification" />
        <placeholder
          name="LabelNotificationType"
          value="Error" />
        <placeholder
          name="ForegroundThemeColor"
          value="GeneralText" />
        <placeholder
          name="TextSize"
          value="ExtraLarge" />
        <placeholder
          name="FontWeight"
          value="Bold" />
        <placeholder
          name="LabelAlignment"
          value="CenterLeft" />
        <placeholder
          name="BackgroundColor"
          value="" />
        <placeholder
          name="Padding"
          value="None" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="SRC"
        id="680b9c8a-10b6-40ce-8376-1aa787292c8e"
        left="5"
        top="15.4"
        width="5"
        height="1"
        binding="TransportLastLeg.JW_RL_NKDiscPort">
        <placeholder
          name="IsReadOnly"
          value="True" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="DisplayMode"
          value="DescOnly" />
        <placeholder
          name="FieldConfiguration"
          value="" />
        <placeholder
          name="DefaultFilter"
          value="" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="DTE"
        id="87a75ef5-d9e1-4b0f-9874-e50bd605a280"
        left="10"
        top="15.4"
        width="4"
        height="1"
        binding="TransportLastLeg.JW_ATA">
        <placeholder
          name="IsReadOnly"
          value="True" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="DefaultValueExpression"
          value="" />
        <placeholder
          name="IncludeTime"
          value="True" />
        <placeholder
          name="ShowNowButton"
          value="True" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="TXT"
        id="5eec50b9-78af-4a04-b9ad-de88057cbef2"
        left="5"
        top="16.4"
        width="5"
        height="1"
        binding="ArrivalCTOAddress.CompanyName">
        <placeholder
          name="IsReadOnly"
          value="True" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="Arrival CTO"
          resid="b6d2ecfa-e8f3-4879-8f95-90efb1653918" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="OnSubmit"
          value="" />
        <placeholder
          name="MultiLine"
          value="False" />
        <placeholder
          name="EnableAutoComplete"
          value="False" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="DTE"
        id="be6cbd7a-4ef5-4cbd-88b7-3e315bd07c3b"
        left="10"
        top="16.4"
        width="4"
        height="1"
        binding="TransportLastLeg.JobSailing.JobVoyDestination.JB_AvailabilityDate">
        <placeholder
          name="IsReadOnly"
          value="True" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="CTO Available"
          resid="7108ead6-36c4-4c7b-afa7-4dafbe167461" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="DefaultValueExpression"
          value="" />
        <placeholder
          name="IncludeTime"
          value="True" />
        <placeholder
          name="ShowNowButton"
          value="True" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="FLB"
        id="58fa4c66-dd48-4360-9ce7-54c9a5867d93"
        left="15"
        top="16.6"
        width="4"
        height="2.1">
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="LabelText"
          value="Clear -"
          resid="8b2d6e48-4970-42b6-98d6-f8209be98e53" />
        <placeholder
          name="LabelType"
          value="Notification" />
        <placeholder
          name="LabelNotificationType"
          value="Success" />
        <placeholder
          name="ForegroundThemeColor"
          value="GeneralText" />
        <placeholder
          name="TextSize"
          value="ExtraLarge" />
        <placeholder
          name="FontWeight"
          value="Bold" />
        <placeholder
          name="LabelAlignment"
          value="Center" />
        <placeholder
          name="BackgroundColor"
          value="" />
        <placeholder
          name="Padding"
          value="None" />
      </control>
      <control
        code="LBL"
        id="e6c97573-2809-4f35-8896-484ab5556845"
        left="19"
        top="16.6"
        width="5"
        height="2.1"
        binding="ClearCount">
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="LabelType"
          value="Notification" />
        <placeholder
          name="LabelNotificationType"
          value="Success" />
        <placeholder
          name="ForegroundThemeColor"
          value="GeneralText" />
        <placeholder
          name="TextSize"
          value="ExtraLarge" />
        <placeholder
          name="FontWeight"
          value="Bold" />
        <placeholder
          name="LabelAlignment"
          value="CenterLeft" />
        <placeholder
          name="BackgroundColor"
          value="" />
        <placeholder
          name="Padding"
          value="None" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="TXT"
        id="9b24d24a-1a75-47d8-b458-f48f5f74ca01"
        left="5"
        top="17.4"
        width="5"
        height="1"
        binding="UnpackDepotAddress.CompanyName">
        <placeholder
          name="IsReadOnly"
          value="True" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="Arrival CFS"
          resid="8193b62d-1a8c-4c4b-a9a7-534d4c5db2b0" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="OnSubmit"
          value="" />
        <placeholder
          name="MultiLine"
          value="False" />
        <placeholder
          name="EnableAutoComplete"
          value="False" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="DTE"
        id="a3c9f4c2-2828-4f0e-8dd1-add8fa8872e4"
        left="10"
        top="17.4"
        width="4"
        height="1"
        binding="FirstConsolShipmentLink.JobShipment.DocsAndCartageDetail.JP_LCLAvailable">
        <placeholder
          name="IsReadOnly"
          value="True" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="CFS Available"
          resid="99211262-32c8-4e7d-93b7-b5b7638fd80d" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="DefaultValueExpression"
          value="" />
        <placeholder
          name="IncludeTime"
          value="True" />
        <placeholder
          name="ShowNowButton"
          value="True" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="DTE"
        id="e72e8a60-d343-430d-89f8-9c5cd18611d7"
        left="5"
        top="18.4"
        width="5"
        height="1"
        binding="FirstScanEventConsol.SL_PostedTimeUtc">
        <placeholder
          name="IsReadOnly"
          value="True" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="First Scan Time"
          resid="101fae90-95ab-4c08-b80c-f1cc77bc1e45" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="DefaultValueExpression"
          value="" />
        <placeholder
          name="IncludeTime"
          value="True" />
        <placeholder
          name="ShowNowButton"
          value="True" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="DTE"
        id="59e9d55a-46cb-4a2b-84fa-8057c5fed9e4"
        left="10"
        top="18.4"
        width="4"
        height="1"
        binding="LastScanEventConsol.SL_PostedTimeUtc">
        <placeholder
          name="IsReadOnly"
          value="True" />
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="Last Scan Time"
          resid="c2298ded-ad96-4656-b377-b7f05b213689" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="DefaultValueExpression"
          value="" />
        <placeholder
          name="IncludeTime"
          value="True" />
        <placeholder
          name="ShowNowButton"
          value="True" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="FLB"
        id="b55738e3-ecbb-42c6-a66b-73b362f0a28a"
        left="15"
        top="18.7"
        width="5"
        height="2.1">
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="LabelText"
          value="Surplus -"
          resid="c723308d-912f-400d-b074-59ec8cda65e7" />
        <placeholder
          name="LabelType"
          value="Notification" />
        <placeholder
          name="LabelNotificationType"
          value="MessageError" />
        <placeholder
          name="ForegroundThemeColor"
          value="GeneralText" />
        <placeholder
          name="TextSize"
          value="ExtraLarge" />
        <placeholder
          name="FontWeight"
          value="Bold" />
        <placeholder
          name="LabelAlignment"
          value="Center" />
        <placeholder
          name="BackgroundColor"
          value="" />
        <placeholder
          name="Padding"
          value="None" />
      </control>
      <control
        code="LBL"
        id="994bf585-4f11-400c-ad5f-c97036366734"
        left="20"
        top="18.7"
        width="4"
        height="2.1"
        binding="SurplusCount">
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="LabelType"
          value="Notification" />
        <placeholder
          name="LabelNotificationType"
          value="MessageError" />
        <placeholder
          name="ForegroundThemeColor"
          value="GeneralText" />
        <placeholder
          name="TextSize"
          value="ExtraLarge" />
        <placeholder
          name="FontWeight"
          value="Bold" />
        <placeholder
          name="LabelAlignment"
          value="CenterLeft" />
        <placeholder
          name="BackgroundColor"
          value="" />
        <placeholder
          name="Padding"
          value="None" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
    </control>
    <control
      code="GRP"
      id="7567d825-797c-4da4-9308-9ecbb72b12fd"
      top="0.4"
      width="26.7"
      height="7">
      <placeholder
        name="CanBeHidden"
        value="True" />
      <placeholder
        name="VisibilityCondition"
        value="ebae7464-b4a3-4c0a-9cd8-6893aabf6295" />
      <placeholder
        name="Header"
        value="New Section"
        resid="a42ad484-b175-412d-a791-efc188551daf" />
      <placeholder
        name="HeaderAlignment"
        value="Left" />
      <placeholder
        name="HeaderDisplayMode"
        value="Hidden" />
      <placeholder
        name="BackgroundImagePk"
        value="" />
      <placeholder
        name="BackgroundImageOverride"
        value="" />
      <control
        code="LBL"
        id="e7a86fdf-cf05-4c04-8683-faad8080049b"
        left="0.1"
        top="0.4"
        width="26.5"
        height="2.7"
        binding="FirstConsolShipmentLink.JobShipment.CurrentItem.OuterPackage.HeldCountLabel">
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="LabelType"
          value="Notification" />
        <placeholder
          name="LabelNotificationType"
          value="Error" />
        <placeholder
          name="ForegroundThemeColor"
          value="GeneralText" />
        <placeholder
          name="TextSize"
          value="ExtraExtraLarge" />
        <placeholder
          name="FontWeight"
          value="Normal" />
        <placeholder
          name="LabelAlignment"
          value="Center" />
        <placeholder
          name="BackgroundColor"
          value="" />
        <placeholder
          name="Padding"
          value="None" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="LBL"
        id="4a0c4d2a-52af-4144-a647-7eb1a3e4fbea"
        left="0.1"
        top="3.7"
        width="26.5"
        height="2.6"
        binding="FirstConsolShipmentLink.JobShipment.CurrentItem.OuterPackage.ReturnLMCAndOuterPackageReference">
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="LabelType"
          value="Notification" />
        <placeholder
          name="LabelNotificationType"
          value="Error" />
        <placeholder
          name="ForegroundThemeColor"
          value="GeneralText" />
        <placeholder
          name="TextSize"
          value="ExtraLarge" />
        <placeholder
          name="FontWeight"
          value="Normal" />
        <placeholder
          name="LabelAlignment"
          value="Center" />
        <placeholder
          name="BackgroundColor"
          value="" />
        <placeholder
          name="Padding"
          value="None" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
    </control>
    <control
      code="GRP"
      id="0073c67f-6cf1-46c1-9c71-fa1fafaace04"
      top="0.4"
      width="27"
      height="7">
      <placeholder
        name="CanBeHidden"
        value="True" />
      <placeholder
        name="VisibilityCondition"
        value="9ce00f24-1385-43c8-9159-ea8b2a761b30" />
      <placeholder
        name="Header"
        value="New Section"
        resid="3056ea04-28f8-469e-bf94-49ca2e198317" />
      <placeholder
        name="HeaderAlignment"
        value="Left" />
      <placeholder
        name="HeaderDisplayMode"
        value="Hidden" />
      <placeholder
        name="BackgroundImagePk"
        value="" />
      <placeholder
        name="BackgroundImageOverride"
        value="" />
      <control
        code="LBL"
        id="1e077ac6-e4d0-4c9b-967e-a8a2087783e2"
        left="0.1"
        top="0.5"
        width="26.4"
        height="2.7"
        binding="FirstConsolShipmentLink.JobShipment.CurrentItem.OuterPackage.ClearCountLabel">
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="LabelType"
          value="Notification" />
        <placeholder
          name="LabelNotificationType"
          value="Success" />
        <placeholder
          name="ForegroundThemeColor"
          value="GeneralText" />
        <placeholder
          name="TextSize"
          value="ExtraExtraLarge" />
        <placeholder
          name="FontWeight"
          value="Normal" />
        <placeholder
          name="LabelAlignment"
          value="Center" />
        <placeholder
          name="BackgroundColor"
          value="" />
        <placeholder
          name="Padding"
          value="None" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
      <control
        code="LBL"
        id="67534870-5744-4e80-a835-14e418140985"
        left="0.1"
        top="3.8"
        width="26.4"
        height="2.5"
        binding="FirstConsolShipmentLink.JobShipment.CurrentItem.OuterPackage.ReturnLMCAndOuterPackageReference">
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="LabelType"
          value="Notification" />
        <placeholder
          name="LabelNotificationType"
          value="Success" />
        <placeholder
          name="ForegroundThemeColor"
          value="GeneralText" />
        <placeholder
          name="TextSize"
          value="ExtraLarge" />
        <placeholder
          name="FontWeight"
          value="Normal" />
        <placeholder
          name="LabelAlignment"
          value="Center" />
        <placeholder
          name="BackgroundColor"
          value="" />
        <placeholder
          name="Padding"
          value="None" />
        <placeholder
          name="SuppressDependencyContributions"
          value="False" />
      </control>
    </control>
    <control
      code="GRP"
      id="d68d5e62-e441-4bc0-b883-6c184cf2d4fc"
      top="7.9"
      width="15"
      height="2">
      <placeholder
        name="CanBeHidden"
        value="True" />
      <placeholder
        name="VisibilityCondition"
        value="f839c6c7-efac-470d-a9c9-d8a417f1dd2a" />
      <placeholder
        name="Header"
        value="New Section"
        resid="3e0ecac6-5695-4e62-b25f-aa4e27cda9c5" />
      <placeholder
        name="HeaderAlignment"
        value="Left" />
      <placeholder
        name="HeaderDisplayMode"
        value="Hidden" />
      <placeholder
        name="BackgroundImagePk"
        value="" />
      <placeholder
        name="BackgroundImageOverride"
        value="" />
      <control
        code="FFI"
        id="db355277-ccbd-4f48-9ced-844108cd560e"
        left="3.4"
        top="0"
        width="8.9"
        height="1">
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="IsInitialFocus"
          value="False" />
        <placeholder
          name="CaptionOverride"
          value="Outer Package Barcode"
          resid="46ceac94-9c02-4093-aa0d-3c916e2a1831" />
        <placeholder
          name="VisibilityCondition"
          value="" />
        <placeholder
          name="EntityType"
          value="IHVLVOuterPackage" />
        <placeholder
          name="DefaultFilter">
          <xml>
            <ArrayOfFilterGroup xmlns:a="http://schemas.microsoft.com/2003/10/Serialization/Arrays" xmlns:i="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://cargowise.com/glow/2014/07/16/filters.xsd">
              <FilterGroup>
                <Filters>
                  <Filter>
                    <FilterType>AdvancedGuidLookupFilter</FilterType>
                    <Operation>Is</Operation>
                    <PropertyPath>HVO_JK_LoadedOnConsol</PropertyPath>
                    <Values>
                      <a:string>&lt;JK_PK&gt;</a:string>
                    </Values>
                  </Filter>
                </Filters>
                <IsImplicit>false</IsImplicit>
              </FilterGroup>
            </ArrayOfFilterGroup>
          </xml>
        </placeholder>
        <placeholder
          name="ItemTemplateID"
          value="" />
        <placeholder
          name="FormFlowPK"
          value="" />
        <placeholder
          name="ItemPosition"
          value="Bottom" />
      </control>
      <control
        code="FLB"
        id="aabefaaa-7060-4bb5-abd4-3d0d66b321e5"
        left="3.9"
        top="1"
        width="8"
        height="1">
        <placeholder
          name="CanBeHidden"
          value="True" />
        <placeholder
          name="VisibilityCondition"
          value="71fb496b-ac71-4727-b4ee-76d5ba34de17" />
        <placeholder
          name="LabelText"
          value="Please Scan Next Item"
          resid="589219ba-db01-43be-b625-8c0685f492d0" />
        <placeholder
          name="LabelType"
          value="NormalLabel" />
        <placeholder
          name="LabelNotificationType"
          value="Information" />
        <placeholder
          name="ForegroundThemeColor"
          value="DefaultButton" />
        <placeholder
          name="TextSize"
          value="Medium" />
        <placeholder
          name="FontWeight"
          value="Bold" />
        <placeholder
          name="LabelAlignment"
          value="Center" />
        <placeholder
          name="BackgroundColor"
          value="" />
        <placeholder
          name="Padding"
          value="None" />
      </control>
    </control>
  </form>
