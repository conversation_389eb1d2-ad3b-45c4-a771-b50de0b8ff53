#transformationVersion: 70.0
#
VZ_PK: 67e9b9687978400882822c9822044a46
VZ_ConfigurationKey: 67e9b968-7978-4008-8282-2c9822044a46
VZ_FormID: OPP - New Temporary Organization
VZ_Caption:
  resKey: VZ_Caption|67e9b968-7978-4008-8282-2c9822044a46
  text: New organization
VZ_FormFactor: DSK
VZ_EntityType: IOrgHeader
VZ_FormType: FRM
VZ_RequiresLogin: true
VZ_MaterialDesign: true
VZ_ExtenderFunc: CargoWise.Glow.Business.CustomerRelationshipManagement.Opportunity.NewTemporaryOrganisationExtension.ExtendNewTemporaryOrganizationForm
VZ_Dependencies: <dependencies xmlns="http://cargowise.com/glow/2014/11/14/dependencies.xsd" />
VZ_FormData: >-
  <form
    id="f8d54f56-af69-4f00-b993-c23fd1cae7e5" xmlns="http://wisetechglobal.com/glow/2017/05/26/form.xsd">
    <placeholder
      name="Width"
      value="950" />
    <control
      code="PNL"
      id="3c82eaee-0376-43d4-989b-721fc2376aa5">
      <placeholder
        name="Width"
        value="950" />
      <control
        code="BOX"
        id="3e087bee-409c-4cff-8fa3-c806a841a068">
        <placeholder
          name="Layout"
          value="grid" />
        <control
          code="LBL"
          id="8a045092-9ec9-4e10-8fd2-1f0630580288">
          <placeholder
            name="Caption"
            value="Organization details"
            resid="0a831fd0-d7c7-44c1-96da-83893ef8d7a5" />
          <placeholder
            name="Typography"
            value="title-sm-default" />
          <placeholder
            name="Margin"
            value="mb-1" />
        </control>
        <control
          code="BOX"
          id="e2f796d4-867d-4cc2-a749-d675b940adce">
          <placeholder
            name="VisibilityCondition"
            value="HasEnabledDuplicateDetectionFields &amp;&amp; OH_FullName != &quot;&quot;" />
          <control
            code="CLO"
            id="27062504-9055-496c-b19a-15f6b9adc5d8">
            <placeholder
              name="Sentiment"
              value="success" />
            <placeholder
              name="VisibilityCondition"
              value="OH_FullName != &quot;&quot; &amp;&amp; (OrgAddresses.First().OA_Phone != &quot;&quot; || OrgAddresses.First().OA_Mobile != &quot;&quot; || OrgAddresses.First().OA_Email != &quot;&quot; || OrgWebURLs.First().PU_URL != &quot;&quot; || (AddressValidationStatus == &quot;VAD&quot; || AddressValidationStatus == &quot;MAN&quot;)) &amp;&amp; !(AddressValidationStatus != &quot;VAD&quot; &amp;&amp; AddressValidationStatus != &quot;MAN&quot; &amp;&amp; HasEnabledAddressFields)" />
            <placeholder
              name="Description"
              value="Sufficient details to check for any existing organizations"
              resid="fca2ad69-defd-49e8-b367-c183a91212d7" />
          </control>
          <control
            code="CLO"
            id="5fe17e54-6687-42f4-a6f0-580cf0c0f0e8">
            <placeholder
              name="Sentiment"
              value="info" />
            <placeholder
              name="VisibilityCondition"
              value="OH_FullName == &quot;&quot; || (OrgAddresses.First().OA_Phone == &quot;&quot; &amp;&amp; OrgAddresses.First().OA_Mobile == &quot;&quot; &amp;&amp; OrgAddresses.First().OA_Email == &quot;&quot; &amp;&amp; OrgWebURLs.First().PU_URL == &quot;&quot; &amp;&amp; (AddressValidationStatus != &quot;VAD&quot; &amp;&amp; AddressValidationStatus != &quot;MAN&quot;)) &amp;&amp; !(AddressValidationStatus != &quot;VAD&quot; &amp;&amp; AddressValidationStatus != &quot;MAN&quot; &amp;&amp; HasEnabledAddressFields)" />
            <placeholder
              name="Description"
              value="Enter more details to check for any existing organizations"
              resid="a98c32e8-5b78-47f7-b402-c70a1b124c87" />
          </control>
        </control>
        <control
          code="CLO"
          id="23983a57-254f-475c-a106-30bbe842cd0f">
          <placeholder
            name="Sentiment"
            value="warning" />
          <placeholder
            name="VisibilityCondition"
            value="AddressValidationStatus != &quot;VAD&quot; &amp;&amp; AddressValidationStatus != &quot;MAN&quot; &amp;&amp; HasEnabledAddressFields" />
          <placeholder
            name="Margin"
            value="mt-n2" />
          <placeholder
            name="Description"
            value="Address needs to be validated before checking for existing organizations"
            resid="332484b7-6b2f-4ab2-b126-26f5a8b5a175" />
        </control>
        <control
          code="TXT"
          id="70ded09f-8c7b-4ebe-b410-31dae506c0cf"
          binding="OH_FullName">
          <placeholder
            name="CaptionOverride"
            value="Organization name"
            resid="23d82c07-2d9a-4d16-bf0e-12354ada93d3" />
          <placeholder
            name="VisibilityCondition"
            value="TemporaryOrganizationMandatoryFields.Contains(&quot;Name&quot;) || TemporaryOrganizationOptionalFields.Contains(&quot;Name&quot;)" />
          <placeholder
            name="Required"
            value="True" />
        </control>
        <control
          code="BOX"
          id="6dd796ba-0bbe-4c5f-b576-71ac3d4f814e">
          <placeholder
            name="Layout"
            value="grid" />
          <control
            code="SRC"
            id="fd8ff6b5-806e-4867-aa94-a9296ed60d4b"
            binding="OrgCompanyData/OB_GB_ControllingBranch">
            <placeholder
              name="CaptionOverride"
              value="Controlling branch"
              resid="8cfbb8ac-b8ce-4dfb-92a2-d8104a6828e3" />
            <placeholder
              name="VisibilityCondition"
              value="TemporaryOrganizationMandatoryFields.Contains(&quot;Branch&quot;) || TemporaryOrganizationOptionalFields.Contains(&quot;Branch&quot;)" />
            <placeholder
              name="Required"
              value="calc(BranchFieldIsRequired)" />
            <placeholder
              name="Columns"
              value="col-md-6 col-lg-6 col-xl-6" />
          </control>
          <control
            code="TXT"
            id="b4f6a350-ca79-45ad-aac1-7e3872b0283c"
            binding="OrgAddresses/OA_Phone">
            <placeholder
              name="VisibilityCondition"
              value="TemporaryOrganizationMandatoryFields.Contains(&quot;Phone&quot;) || TemporaryOrganizationOptionalFields.Contains(&quot;Phone&quot;)" />
            <placeholder
              name="Required"
              value="calc(PhoneFieldIsRequired)" />
            <placeholder
              name="Columns"
              value="col-md-6 col-lg-6 col-xl-6" />
          </control>
        </control>
        <control
          code="TXT"
          id="acf25cc9-6093-48a7-9281-d7046269db61"
          binding="OrgAddresses/OA_Mobile">
          <placeholder
            name="VisibilityCondition"
            value="TemporaryOrganizationMandatoryFields.Contains(&quot;Mobile&quot;) || TemporaryOrganizationOptionalFields.Contains(&quot;Mobile&quot;)" />
          <placeholder
            name="Required"
            value="calc(MobileFieldIsRequired)" />
          <placeholder
            name="Columns"
            value="col-md-6 col-lg-6 col-xl-6" />
        </control>
        <control
          code="TXT"
          id="7d1bec88-e8b9-48ab-888b-16dfbce5a996"
          binding="OrgAddresses/OA_Email">
          <placeholder
            name="VisibilityCondition"
            value="TemporaryOrganizationMandatoryFields.Contains(&quot;Email&quot;) || TemporaryOrganizationOptionalFields.Contains(&quot;Email&quot;)" />
          <placeholder
            name="Required"
            value="calc(EmailFieldIsRequired)" />
          <placeholder
            name="Columns"
            value="col-md-6 col-lg-6 col-xl-6" />
        </control>
        <control
          code="TXT"
          id="c55cef80-9a86-4f1a-bdb5-51dcc7165605"
          binding="OrgAddresses/OA_Fax">
          <placeholder
            name="VisibilityCondition"
            value="TemporaryOrganizationMandatoryFields.Contains(&quot;Fax&quot;) || TemporaryOrganizationOptionalFields.Contains(&quot;Fax&quot;)" />
          <placeholder
            name="Required"
            value="calc(FaxFieldIsRequired)" />
          <placeholder
            name="Columns"
            value="col-md-6 col-lg-6 col-xl-6" />
        </control>
        <control
          code="TXT"
          id="df3770ad-54c2-467a-a587-c72b8bb42ad5"
          binding="OrgWebURLs/PU_URL">
          <placeholder
            name="VisibilityCondition"
            value="TemporaryOrganizationMandatoryFields.Contains(&quot;Web&quot;) || TemporaryOrganizationOptionalFields.Contains(&quot;Web&quot;)" />
          <placeholder
            name="Required"
            value="calc(WebsiteURLFieldIsRequired)" />
          <placeholder
            name="Columns"
            value="col-md-6 col-lg-6 col-xl-6" />
        </control>
        <control
          code="LBL"
          id="f6d9df1d-c37c-4d60-b73d-7b2afd398a87">
          <placeholder
            name="Caption"
            value="Address"
            resid="83951c01-c05e-4a48-8fcb-ed727edc2246" />
          <placeholder
            name="Typography"
            value="title-sm-default" />
        </control>
        <control
          code="TXT"
          id="dac749fe-0a40-4ff7-8591-247f83c23d03"
          binding="OrgAddresses/OA_Address1">
          <placeholder
            name="VisibilityCondition"
            value="TemporaryOrganizationMandatoryFields.Contains(&quot;Address1&quot;) || TemporaryOrganizationOptionalFields.Contains(&quot;Address1&quot;)" />
          <placeholder
            name="Required"
            value="calc(Address1FieldIsRequired)" />
          <placeholder
            name="Columns"
            value="col-md-6 col-lg-6 col-xl-6" />
        </control>
        <control
          code="TXT"
          id="a7523196-abf7-4c94-b495-102a1cf44a90"
          binding="OrgAddresses/OA_Address2">
          <placeholder
            name="VisibilityCondition"
            value="TemporaryOrganizationMandatoryFields.Contains(&quot;Address2&quot;) || TemporaryOrganizationOptionalFields.Contains(&quot;Address2&quot;)" />
          <placeholder
            name="Columns"
            value="col-md-6 col-lg-6 col-xl-6" />
        </control>
        <control
          code="SRC"
          id="c00643b6-978a-468b-aa45-293611e3304e"
          binding="OrgAddresses/OA_RN_NKCountryCode">
          <placeholder
            name="VisibilityCondition"
            value="TemporaryOrganizationMandatoryFields.Contains(&quot;Country&quot;) || TemporaryOrganizationOptionalFields.Contains(&quot;Country&quot;)" />
          <placeholder
            name="Required"
            value="calc(CountryRegionFieldIsRequired)" />
          <placeholder
            name="Columns"
            value="col-md-6 col-lg-6 col-xl-6" />
        </control>
        <control
          code="TXT"
          id="792248ec-d657-485b-a85c-3aabc025b8d1"
          binding="OrgAddresses/OA_City">
          <placeholder
            name="VisibilityCondition"
            value="TemporaryOrganizationMandatoryFields.Contains(&quot;City&quot;) || TemporaryOrganizationOptionalFields.Contains(&quot;City&quot;)" />
          <placeholder
            name="Required"
            value="calc(CityFieldIsRequired)" />
          <placeholder
            name="Columns"
            value="col-md-6 col-lg-6 col-xl-6" />
        </control>
        <control
          code="SRC"
          id="30d8d6f2-8d2d-4f56-96bd-a4823a07ac97"
          binding="OrgAddresses/OA_State">
          <placeholder
            name="VisibilityCondition"
            value="TemporaryOrganizationMandatoryFields.Contains(&quot;State&quot;) || TemporaryOrganizationOptionalFields.Contains(&quot;State&quot;)" />
          <placeholder
            name="Required"
            value="calc(StateFieldIsRequired)" />
          <placeholder
            name="Columns"
            value="col-md-6 col-lg-6 col-xl-6" />
        </control>
        <control
          code="SRC"
          id="503650b5-4555-43b0-9166-6b0da1d52ee2"
          binding="OrgAddresses/OA_PostCode">
          <placeholder
            name="VisibilityCondition"
            value="TemporaryOrganizationMandatoryFields.Contains(&quot;Postcode&quot;) || TemporaryOrganizationOptionalFields.Contains(&quot;Postcode&quot;)" />
          <placeholder
            name="CaptionOverride"
            value="Postcode"
            resid="1cfa6db0-b0db-4c55-bbbd-24e12f27e618" />
          <placeholder
            name="Required"
            value="calc(PostcodeFieldIsRequired)" />
          <placeholder
            name="Columns"
            value="col-md-6 col-lg-6 col-xl-6" />
        </control>
        <control
          code="SRC"
          id="88c08cf9-695b-486f-82c8-b78c35cbc196"
          binding="OH_RL_NKClosestPort">
          <placeholder
            name="VisibilityCondition"
            value="TemporaryOrganizationMandatoryFields.Contains(&quot;UNLOCO&quot;) || TemporaryOrganizationOptionalFields.Contains(&quot;UNLOCO&quot;)" />
          <placeholder
            name="Required"
            value="calc(UNLOCOFieldIsRequired)" />
          <placeholder
            name="Columns"
            value="col-md-6 col-lg-6 col-xl-6" />
        </control>
      </control>
      <control
        code="CLO"
        id="b6b563b9-62f7-4689-8bfa-5fe3986b774a">
        <placeholder
          name="Sentiment"
          value="info" />
        <placeholder
          name="Margin"
          value="mt-3" />
        <placeholder
          name="VisibilityCondition"
          value="AddressValidationStatus != &quot;VAD&quot; &amp;&amp; AddressValidationStatus != &quot;MAN&quot; &amp;&amp; !HasEnabledAddressFields &amp;&amp; (DefaultAddress.OA_Address1 != &quot;&quot; || DefaultAddress.OA_Address2 != &quot;&quot; || DefaultAddress.OA_RN_NKCountryCode != &quot;&quot; || DefaultAddress.OA_City != &quot;&quot; || DefaultAddress.OA_State != &quot;&quot; || DefaultAddress.OA_PostCode != &quot;&quot; || OH_RL_NKClosestPort != &quot;&quot;)" />
        <placeholder
          name="Description"
          value="More address details required to validate address"
          resid="7aabd8f1-93d9-4a5f-a80c-595ad72d3db4" />
      </control>
      <control
        code="BOX"
        id="9014925c-41aa-4913-9993-198b5ce0426b"
        binding="">
        <placeholder
          name="Layout"
          value="flex" />
        <placeholder
          name="FlexAlign"
          value="align-start" />
        <placeholder
          name="VisibilityCondition"
          value="HasEnabledAddressFields" />
        <control
          code="BTN"
          id="fd319c33-87b5-4934-af51-57aade50c132"
          binding="">
          <placeholder
            name="Caption"
            value="Validate address"
            resid="8b8c9199-d2ae-451d-b666-8c018cb2c565" />
          <placeholder
            name="VisibilityCondition"
            value="AddressValidationStatus == &quot;NYV&quot; || AddressValidationStatus == &quot;NYC&quot;" />
          <placeholder
            name="Sentiment"
            value="primary" />
          <placeholder
            name="Variant"
            value="fill" />
          <placeholder
            name="Margin"
            value="mt-3" />
          <placeholder
            name="Transition"
            value="True" />
          <placeholder
            name="Disabled"
            value="calc(AddressValidationStatus == &quot;NYC&quot;)" />
        </control>
        <control
          code="BOX"
          id="f7c6b172-7be9-493f-a1c7-bc3d4e44c9b3"
          binding="">
          <placeholder
            name="FlexAlign"
            value="align-start" />
          <placeholder
            name="Margin"
            value="mt-5" />
          <placeholder
            name="VisibilityCondition"
            value="AddressValidationStatus == &quot;MAN&quot; || AddressValidationStatus == &quot;VAD&quot;" />
          <control
            code="STA"
            id="2abe12a7-3119-4750-abb8-b63ddb40f4e0"
            binding="AddressValidationStatus">
            <placeholder
              name="IsReadOnly"
              value="True" />
          </control>
        </control>
      </control>
      <control
        code="PNL"
        id="ca7f3f2c-21ba-4927-82a6-73f0a02f978b"
        binding="">
        <placeholder
          name="Style"
          value="border: 0px" />
        <placeholder
          name="VisibilityCondition"
          value="AddressValidationStatus == &quot;NYC&quot;" />
        <placeholder
          name="Layout"
          value="grid" />
        <control
          code="BOX"
          id="69cc3eae-3cc2-459b-a681-ff4bdc4c6a20">
          <placeholder
            name="Layout"
            value="grid" />
          <placeholder
            name="VisibilityCondition"
            value="AddressValidationResult.Count()&gt;0" />
          <control
            code="LBL"
            id="a591adad-b9b4-487a-8c85-d6620454ebc1">
            <placeholder
              name="Caption"
              value="Matching address"
              resid="190898c4-114f-483e-81ba-6741eadb4ccc" />
            <placeholder
              name="Typography"
              value="title-small" />
          </control>
          <control
            code="RDT"
            id="04331ee0-09f1-43db-881d-b649bfce30bc"
            binding="AddressValidationResult">
            <placeholder
              name="HideItemActions"
              value="True" />
            <placeholder
              name="HideDefaultFooter"
              value="True" />
            <placeholder
              name="FieldConfiguration">
              <xml>
                <fields xmlns="">
                  <field
                    path="OA_Address1"
                    width="300"
                    mode="Mandatory" />
                  <field
                    path="OA_Address2"
                    width="300"
                    mode="Default" />
                  <field
                    path="OA_RN_NKCountryCode"
                    width="100"
                    mode="Default" />
                  <field
                    path="OA_City"
                    width="150"
                    mode="Default" />
                  <field
                    path="OA_PostCode"
                    width="150"
                    mode="Default" />
                  <field
                    path="OA_State"
                    width="150"
                    mode="Default" />
                  <field
                    path="OA_Code"
                    width="300"
                    mode="Optional" />
                  <field
                    path="CompanyName"
                    width="300"
                    mode="Optional" />
                  <field
                    path="OrganizationType"
                    width="250"
                    mode="FilterOnly" />
                  <field
                    path="OA_AuthorityToLeave"
                    width="40"
                    mode="Optional" />
                  <field
                    path="OA_Email"
                    width="250"
                    mode="Optional" />
                  <field
                    path="OA_Fax"
                    width="200"
                    mode="Optional" />
                  <field
                    path="OA_Mobile"
                    width="200"
                    mode="Optional" />
                  <field
                    path="OA_Phone"
                    width="200"
                    mode="Optional" />
                  <field
                    path="OA_SystemCreateTimeUtc"
                    width="220"
                    mode="Optional" />
                  <field
                    path="OA_SystemLastEditTimeUtc"
                    width="250"
                    mode="Optional" />
                  <field
                    path="OrgAddressCapabilities.PZ_AddressType"
                    width="250"
                    mode="FilterOnly" />
                  <field
                    path="OA_AccessPoint"
                    width="120"
                    mode="Optional" />
                  <field
                    path="OA_IsActive"
                    width="70"
                    mode="Optional" />
                  <field
                    path="OA_AdditionalAddressInformation"
                    width="250"
                    mode="Optional" />
                  <field
                    path="OA_AddressMap"
                    width="250"
                    mode="Optional" />
                  <field
                    path="OA_AIREquipmentNeeded"
                    width="110"
                    mode="Optional" />
                  <field
                    path="OA_CommunicationRequired"
                    width="130"
                    mode="Optional" />
                  <field
                    path="OA_ContainerHandling"
                    width="180"
                    mode="Optional" />
                  <field
                    path="OA_SystemCreateUser"
                    width="160"
                    mode="Optional" />
                  <field
                    path="OA_DeliverFromTimeOnly"
                    width="220"
                    mode="Optional" />
                  <field
                    path="OA_DeliverToTimeOnly"
                    width="200"
                    mode="Optional" />
                  <field
                    path="OA_DeliveryRoute"
                    width="140"
                    mode="Optional" />
                  <field
                    path="OA_DeliveryRouteSequence"
                    width="230"
                    mode="Optional" />
                  <field
                    path="OA_DoNotAttendFrom"
                    width="180"
                    mode="Optional" />
                  <field
                    path="OA_DoNotAttendTo"
                    width="180"
                    mode="Optional" />
                  <field
                    path="OA_Dock_Height"
                    width="110"
                    mode="Optional" />
                  <field
                    path="OA_DockLeveler"
                    width="120"
                    mode="Optional" />
                  <field
                    path="OA_FCLEquipmentNeeded"
                    width="110"
                    mode="Optional" />
                  <field
                    path="OA_ForkLift"
                    width="90"
                    mode="Optional" />
                  <field
                    path="OA_GeoLocation"
                    width="250"
                    mode="Optional" />
                  <field
                    path="OA_GeofencePolygon"
                    width="250"
                    mode="Optional" />
                  <field
                    path="OA_GroupNumber"
                    width="120"
                    mode="Optional" />
                  <field
                    path="HasMainAddress"
                    width="160"
                    mode="Optional" />
                  <field
                    path="OA_JobLoadingDuration"
                    width="200"
                    mode="Optional" />
                  <field
                    path="OA_LCLEquipmentNeeded"
                    width="110"
                    mode="Optional" />
                  <field
                    path="OA_LabourRequired"
                    width="140"
                    mode="Optional" />
                  <field
                    path="OA_Language"
                    width="80"
                    mode="Optional" />
                  <field
                    path="OA_SystemLastEditUser"
                    width="200"
                    mode="Optional" />
                  <field
                    path="OA_PalletJack"
                    width="110"
                    mode="Optional" />
                  <field
                    path="OA_PickupFromTimeOnly"
                    width="210"
                    mode="Optional" />
                  <field
                    path="OA_PickupToTimeOnly"
                    width="190"
                    mode="Optional" />
                  <field
                    path="OA_RL_NKRelatedPortCode"
                    width="60"
                    mode="Optional" />
                  <field
                    path="OA_SuppressAddressValidationError"
                    width="250"
                    mode="Optional" />
                  <field
                    path="OA_UseCumulativeFreeWaitingTime"
                    width="140"
                    mode="Optional" />
                  <field
                    path="OA_ValidationStatus"
                    width="170"
                    mode="Optional" />
                  <field
                    path="OA_VerifiesContainerGrossWeight"
                    width="250"
                    mode="Optional" />
                  <field
                    path="OA_CompanyNameOverride"
                    width="100"
                    mode="Optional" />
                </fields>
              </xml>
            </placeholder>
            <placeholder
              name="HideActions"
              value="True" />
            <placeholder
              name="ShowToolbar"
              value="none" />
            <placeholder
              name="SingleSelect"
              value="True" />
            <placeholder
              name="ShowSelect"
              value="True" />
            <placeholder
              name="Margin"
              value="mt-3" />
            <placeholder
              name="DisabledGridRowActions"
              value="Documents" />
          </control>
        </control>
        <control
          code="EST"
          id="3da646bc-668a-4fbd-9032-3e7a5140d116">
          <placeholder
            name="Variant"
            value="default" />
          <placeholder
            name="Header"
            value="No valid matching address found"
            resid="a7ac2dbd-e69e-4fea-907e-2f04003ff102" />
          <placeholder
            name="BodyCopy"
            value="Verify the entered details. You can either keep them as entered or make changes to the address and validate again."
            resid="15511f83-aacc-471f-8f0f-646f6c9df94c" />
          <placeholder
            name="VisibilityCondition"
            value="AddressValidationResult.Count()==0" />
          <placeholder
            name="Style"
            value="text-align: center" />
          <placeholder
            name="Margin"
            value="ma-auto" />
          <control
            code="BTN"
            id="42074d52-b51b-45dd-8c56-84c017ef69c5"
            binding="">
            <placeholder
              name="Caption"
              value="Keep as entered"
              resid="52e41d98-7340-48a4-b19a-11d78ca4f0ee" />
            <placeholder
              name="Transition"
              value="True" />
            <placeholder
              name="Margin"
              value="mr-3" />
            <placeholder
              name="VisibilityCondition"
              value="AddressValidationResult.Count()==0" />
            <placeholder
              name="Variant"
              value="fill" />
          </control>
        </control>
        <control
          code="BOX"
          id="0cea33ba-e183-46d6-95cf-fb25e37e4f27"
          binding="">
          <placeholder
            name="Layout"
            value="flex" />
          <placeholder
            name="VisibilityCondition"
            value="AddressValidationStatus == &quot;NYC&quot;" />
          <placeholder
            name="Margin"
            value="mt-3" />
          <control
            code="BTN"
            id="1dd56963-98c1-46e6-8fcf-63b7ef083abc"
            binding="">
            <placeholder
              name="Caption"
              value="Update address"
              resid="d2f6bf56-80f7-45ae-9898-b79ceb4f31ad" />
            <placeholder
              name="Margin"
              value="mr-3" />
            <placeholder
              name="Transition"
              value="True" />
            <placeholder
              name="VisibilityCondition"
              value="AddressValidationResult.Count()&gt;0" />
            <placeholder
              name="Sentiment"
              value="primary" />
            <placeholder
              name="Variant"
              value="fill" />
            <placeholder
              name="Disabled"
              value="calc(SelectedAddress == null)" />
          </control>
          <control
            code="BTN"
            id="284707e8-5fca-4630-b121-692a0c80bc11"
            binding="">
            <placeholder
              name="Caption"
              value="Keep as entered"
              resid="80133024-6c7a-442e-bd5f-d5a2cb4d4bf7" />
            <placeholder
              name="Transition"
              value="True" />
            <placeholder
              name="VisibilityCondition"
              value="AddressValidationResult.Count()&gt;0" />
          </control>
        </control>
      </control>
    </control>
  </form>
