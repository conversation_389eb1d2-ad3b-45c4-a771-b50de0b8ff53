#transformationVersion: 70.0
#
VZ_PK: a46ade19b3304155abb2b9cb9c433c0a
VZ_ConfigurationKey: a46ade19-b330-4155-abb2-b9cb9c433c0a
VZ_FormID: ETL - VDV3 - Shipper Portal - View Short Items on Shipment
VZ_Caption:
  resKey: VZ_Caption|a46ade19b3304155abb2b9cb9c433c0a
  text: View Short Items on Shipment
VZ_FormFactor: DSK
VZ_EntityType: IJobShipment
VZ_FormType: FRM
VZ_RequiresLogin: true
VZ_MaterialDesign: true
VZ_Dependencies: <dependencies xmlns="http://cargowise.com/glow/2014/11/14/dependencies.xsd" />
VZ_FormData: >-
  <form
    id="934132c0-a6f2-4cb9-a255-398bfa2885de" xmlns="http://wisetechglobal.com/glow/2017/05/26/form.xsd">
    <placeholder
      name="Layout"
      value="fill" />
    <control
      code="SDT"
      id="fc8f7528-01ec-4cf4-8e67-3ebc6d14bf04">
      <placeholder
        name="FullPage"
        value="True" />
      <placeholder
        name="EntityType"
        value="IHVLVItem" />
      <placeholder
        name="CaptionOverride"
        value="Short Items"
        resid="d4b9b409-69f2-4020-a375-a8518357de50" />
      <placeholder
        name="DefaultFilter">
        <xml>
          <ArrayOfFilterGroup xmlns:a="http://schemas.microsoft.com/2003/10/Serialization/Arrays" xmlns:i="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://cargowise.com/glow/2014/07/16/filters.xsd">
            <FilterGroup>
              <Filters>
                <Filter>
                  <FilterType>StringFilter</FilterType>
                  <Operation>StartsWith</Operation>
                  <PropertyPath>HVI_CurrentBarcode</PropertyPath>
                  <Values />
                </Filter>
                <Filter>
                  <FilterType>StringFilter</FilterType>
                  <Operation>StartsWith</Operation>
                  <PropertyPath>HVI_ShipperReference</PropertyPath>
                  <Values />
                </Filter>
                <Filter>
                  <FilterType>IsActiveFilter</FilterType>
                  <Operation>Is</Operation>
                  <PropertyPath>HVI_IsActive</PropertyPath>
                  <Values>
                    <a:string>true</a:string>
                  </Values>
                </Filter>
              </Filters>
              <IsImplicit>false</IsImplicit>
            </FilterGroup>
            <FilterGroup>
              <Filters>
                <Filter>
                  <FilterType>AdvancedGuidLookupFilter</FilterType>
                  <Operation>Is</Operation>
                  <PropertyPath>HVI_JS_LoadedOnShipment</PropertyPath>
                  <Values>
                    <a:string>&lt;JS_PK&gt;</a:string>
                  </Values>
                </Filter>
                <Filter>
                  <FilterType>SimpleLookupFilter</FilterType>
                  <Operation>Is</Operation>
                  <PropertyPath>HVI_Status</PropertyPath>
                  <Values>
                    <a:string>SSD</a:string>
                  </Values>
                </Filter>
              </Filters>
              <IsImplicit>true</IsImplicit>
            </FilterGroup>
          </ArrayOfFilterGroup>
        </xml>
      </placeholder>
    </control>
  </form>
