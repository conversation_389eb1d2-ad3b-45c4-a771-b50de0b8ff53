#transformationVersion: 70.0
#
VZ_PK: 88dfa6994fd544bd989f8b6d37305208
VZ_ConfigurationKey: 88dfa699-4fd5-44bd-989f-8b6d37305208
VZ_FormID: CLA - Client Contracts Details
VZ_Caption:
  resKey: VZ_Caption|88dfa6994fd544bd989f8b6d37305208
  text: Client Contracts Details
VZ_FormFactor: DSK
VZ_EntityType: IClientContract
VZ_FormType: FRM
VZ_RequiresLogin: true
VZ_MaterialDesign: true
VZ_Dependencies: <dependencies xmlns="http://cargowise.com/glow/2014/11/14/dependencies.xsd" />
VZ_FormData: >-
  <form
    id="4a92500f-600c-444a-8e1a-3abde8aad83b" xmlns="http://wisetechglobal.com/glow/2017/05/26/form.xsd">
    <placeholder
      name="Layout"
      value="fill" />
    <control
      code="PNL"
      id="d5df0525-3e2c-4d1b-968c-84773ddfd37f"
      binding="">
      <placeholder
        name="Caption"
        value="Contract Information"
        resid="926713d3-4424-402a-bc4e-98f4065b4667" />
      <placeholder
        name="Margin"
        value="mb-3" />
      <placeholder
        name="Layout"
        value="grid" />
      <control
        code="TXT"
        id="03fb2d34-0657-42f6-92f8-7d176683b10e"
        binding="RCT_ContractNumber">
        <placeholder
          name="Columns"
          value="col-4 col-lg-2" />
        <placeholder
          name="IsReadOnly"
          value="True" />
      </control>
      <control
        code="SRC"
        id="e557c868-1c64-4937-b4bd-ea524aa94f0c"
        binding="RCT_OH">
        <placeholder
          name="Columns"
          value="col-4 col-lg-2" />
      </control>
      <control
        code="DAE"
        id="4a7a4d9c-99cd-4c0a-849b-086f6717d3c6"
        binding="RCT_StartDate">
        <placeholder
          name="Columns"
          value="col-4 col-lg-1" />
      </control>
      <control
        code="DAE"
        id="55eda8c9-a71e-4a62-874b-178590ef9560"
        binding="RCT_EndDate">
        <placeholder
          name="Columns"
          value="col-4 col-lg-1" />
      </control>
      <control
        code="TXT"
        id="ed9b50b0-6455-4e79-8a86-efe75e00a937"
        binding="RCT_Description">
        <placeholder
          name="Columns"
          value="col-4 col-lg-2" />
      </control>
    </control>
    <control
      code="PNL"
      id="0e019ff6-4c66-4181-be44-4a56386f56c9">
      <placeholder
        name="FitToHeight"
        value="True" />
      <control
        code="RDT"
        id="95f84f63-ad52-4c47-88ef-a7301e59cbf8"
        binding="ClientContractContainerDetentions">
        <placeholder
          name="FitToHeight"
          value="True" />
        <placeholder
          name="InlineEdit"
          value="table" />
        <placeholder
          name="AllowAdd"
          value="True" />
        <placeholder
          name="CaptionOverride"
          value="Container Penalty"
          resid="f8ce419c-691e-4c92-9968-ffc0c3ce1227" />
        <placeholder
          name="CaptionType"
          value="short" />
        <placeholder
          name="ShowGrouping"
          value="True" />
        <placeholder
          name="ShowSelect"
          value="True" />
        <placeholder
          name="ShowToolbar"
          value="large" />
        <placeholder
          name="ShowFilters"
          value="True" />
        <placeholder
          name="ShowAddActions"
          value="True" />
        <placeholder
          name="ShowCustomize"
          value="True" />
        <placeholder
          name="AllowUpdateOnReadonlyParent"
          value="True" />
        <placeholder
          name="FieldConfiguration">
          <xml>
            <fields xmlns="">
              <field
                path="RCD_Direction"
                width="90"
                mode="Default" />
              <field
                path="RCD_PenaltyType"
                width="120"
                mode="Default" />
              <field
                path="RCD_DetentionPortOrCountry"
                width="250"
                mode="Default" />
              <field
                path="RCD_OriginPortOrCountry"
                width="220"
                mode="Default" />
              <field
                path="RCD_ContainerType"
                width="140"
                mode="Default" />
              <field
                path="RCD_FreeDays"
                width="90"
                mode="Default" />
              <field
                path="FirstFreeDay"
                width="250"
                mode="Default" />
              <field
                path="LastFreeDay"
                width="250"
                mode="Default" />
              <field
                path="RCD_OH_Client"
                width="250"
                mode="Optional" />
              <field
                path="RCD_StartDateOverride"
                width="190"
                mode="Default" />
              <field
                path="RCD_EndDateOverride"
                width="170"
                mode="Default" />
              <field
                path="DurationExclusionDays"
                width="300"
                caption="Duration Exclusions"
                resid="8ceef745-eb89-25be-48cb-51edd48eb2ac"
                mode="Default" />
              <field
                path="FreeDayExclusionDays"
                width="300"
                caption="Free Day Exclusions"
                resid="cb132f57-8f51-17bc-457d-88cda6d39291"
                mode="Default" />
            </fields>
          </xml>
        </placeholder>
        <placeholder
          name="ActionMenuItems">
          <xml>
            <formFlows xmlns="">
              <formFlow>8d0e671fb9a5489187ff5ad2b5f339ee</formFlow>
              <formFlow>0d377d06c7e14fcd9e0025adecf2ff34</formFlow>
            </formFlows>
          </xml>
        </placeholder>
      </control>
    </control>
  </form>
